<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="bis_device_item" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\bis_device_item.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/bis_device_item_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="99" endOffset="14"/></Target><Target id="@+id/name" view="TextView"><Expressions/><location startLine="19" startOffset="8" endLine="26" endOffset="13"/></Target><Target id="@+id/status" view="TextView"><Expressions/><location startLine="32" startOffset="12" endLine="39" endOffset="17"/></Target><Target id="@+id/join_mark_text" view="TextView"><Expressions/><location startLine="40" startOffset="12" endLine="46" endOffset="39"/></Target><Target id="@+id/join_mark_button" view="Button"><Expressions/><location startLine="47" startOffset="12" endLine="53" endOffset="17"/></Target><Target id="@+id/bis_icon_exit" view="ImageView"><Expressions/><location startLine="65" startOffset="8" endLine="71" endOffset="41"/></Target><Target id="@+id/bis_icon_need_code" view="ImageView"><Expressions/><location startLine="73" startOffset="8" endLine="79" endOffset="41"/></Target><Target id="@+id/bis_icon_sound" view="ImageView"><Expressions/><location startLine="81" startOffset="8" endLine="87" endOffset="41"/></Target><Target id="@+id/bis_btn_info" view="ImageView"><Expressions/><location startLine="89" startOffset="8" endLine="96" endOffset="13"/></Target></Targets></Layout>