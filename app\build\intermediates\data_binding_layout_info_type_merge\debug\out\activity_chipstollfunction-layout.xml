<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_chipstollfunction" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_chipstollfunction.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_chipstollfunction_0" view="LinearLayout"><Expressions/><location startLine="8" startOffset="4" endLine="357" endOffset="18"/></Target><Target id="@+id/tool" tag="layout/activity_chipstollfunction_0" include="toolbar"><Expressions/><location startLine="16" startOffset="4" endLine="19" endOffset="9"/></Target><Target id="@+id/avslwa" view="Button"><Expressions/><location startLine="37" startOffset="12" endLine="48" endOffset="17"/></Target><Target id="@+id/smartvoice" view="Button"><Expressions/><location startLine="56" startOffset="12" endLine="67" endOffset="17"/></Target><Target id="@+id/rssi" view="Button"><Expressions/><location startLine="76" startOffset="12" endLine="87" endOffset="17"/></Target><Target id="@+id/rssi_extend" view="Button"><Expressions/><location startLine="96" startOffset="12" endLine="108" endOffset="17"/></Target><Target id="@+id/audio_dump" view="Button"><Expressions/><location startLine="117" startOffset="12" endLine="129" endOffset="17"/></Target><Target id="@+id/log_dump" view="Button"><Expressions/><location startLine="138" startOffset="12" endLine="150" endOffset="17"/></Target><Target id="@+id/crash_dump" view="Button"><Expressions/><location startLine="159" startOffset="12" endLine="172" endOffset="17"/></Target><Target id="@+id/custom_command" view="Button"><Expressions/><location startLine="181" startOffset="12" endLine="193" endOffset="17"/></Target><Target id="@+id/EQ" view="Button"><Expressions/><location startLine="195" startOffset="12" endLine="208" endOffset="17"/></Target><Target id="@+id/capsensor" view="Button"><Expressions/><location startLine="217" startOffset="12" endLine="228" endOffset="17"/></Target><Target id="@+id/ble_wifi" view="Button"><Expressions/><location startLine="249" startOffset="12" endLine="261" endOffset="17"/></Target><Target id="@+id/throughput" view="Button"><Expressions/><location startLine="269" startOffset="12" endLine="282" endOffset="17"/></Target><Target id="@+id/check_crc" view="Button"><Expressions/><location startLine="290" startOffset="12" endLine="302" endOffset="17"/></Target><Target id="@+id/command_set" view="Button"><Expressions/><location startLine="311" startOffset="12" endLine="323" endOffset="17"/></Target><Target id="@+id/auracast_assistant" view="Button"><Expressions/><location startLine="332" startOffset="12" endLine="344" endOffset="17"/></Target></Targets></Layout>