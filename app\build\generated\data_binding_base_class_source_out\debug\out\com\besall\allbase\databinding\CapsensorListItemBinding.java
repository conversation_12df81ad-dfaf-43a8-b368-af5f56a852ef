// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import com.besall.allbase.view.activity.chipstoollevel4.capsensor.CapsensorChartView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class CapsensorListItemBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final CapsensorChartView capsensorChartView;

  private CapsensorListItemBinding(@NonNull ConstraintLayout rootView,
      @NonNull CapsensorChartView capsensorChartView) {
    this.rootView = rootView;
    this.capsensorChartView = capsensorChartView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static CapsensorListItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static CapsensorListItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.capsensor_list_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static CapsensorListItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.capsensor_chart_view;
      CapsensorChartView capsensorChartView = rootView.findViewById(id);
      if (capsensorChartView == null) {
        break missingId;
      }

      return new CapsensorListItemBinding((ConstraintLayout) rootView, capsensorChartView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
