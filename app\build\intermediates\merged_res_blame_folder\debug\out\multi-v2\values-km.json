{"logs": [{"outputFile": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-km\\values-km.xml", "map": [{"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\7ad5be15223e864e4511b3f7157ae4d3\\appcompat-1.2.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "274,376,475,585,672,775,896,974,1050,1141,1234,1326,1420,1520,1613,1708,1802,1893,1984,2067,2171,2275,2375,2484,2593,2702,2864,7519", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "371,470,580,667,770,891,969,1045,1136,1229,1321,1415,1515,1608,1703,1797,1888,1979,2062,2166,2270,2370,2479,2588,2697,2859,2957,7598"}}, {"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\85d353c95dd38846b16194b262acf36b\\material-1.4.0-beta01\\res\\values-km\\values-km.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,304,404,516,596,661,755,825,887,974,1039,1098,1163,1224,1281,1400,1458,1519,1576,1647,1777,1863,1941,2049,2124,2195,2292,2359,2425,2505,2595,2681,2760,2837,2907,2982,3070,3140,3240,3339,3413,3489,3596,3650,3723,3814,3910,3972,4036,4099,4198,4296,4388,4488", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,79,99,111,79,64,93,69,61,86,64,58,64,60,56,118,57,60,56,70,129,85,77,107,74,70,96,66,65,79,89,85,78,76,69,74,87,69,99,98,73,75,106,53,72,90,95,61,63,62,98,97,91,99,82", "endOffsets": "219,299,399,511,591,656,750,820,882,969,1034,1093,1158,1219,1276,1395,1453,1514,1571,1642,1772,1858,1936,2044,2119,2190,2287,2354,2420,2500,2590,2676,2755,2832,2902,2977,3065,3135,3235,3334,3408,3484,3591,3645,3718,3809,3905,3967,4031,4094,4193,4291,4383,4483,4566"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2962,3042,3142,3254,3334,3399,3493,3563,3625,3712,3777,3836,3901,3962,4019,4138,4196,4257,4314,4385,4515,4601,4679,4787,4862,4933,5030,5097,5163,5243,5333,5419,5498,5575,5645,5720,5808,5878,5978,6077,6151,6227,6334,6388,6461,6552,6648,6710,6774,6837,6936,7034,7126,7436", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,88", "endColumns": "12,79,99,111,79,64,93,69,61,86,64,58,64,60,56,118,57,60,56,70,129,85,77,107,74,70,96,66,65,79,89,85,78,76,69,74,87,69,99,98,73,75,106,53,72,90,95,61,63,62,98,97,91,99,82", "endOffsets": "269,3037,3137,3249,3329,3394,3488,3558,3620,3707,3772,3831,3896,3957,4014,4133,4191,4252,4309,4380,4510,4596,4674,4782,4857,4928,5025,5092,5158,5238,5328,5414,5493,5570,5640,5715,5803,5873,5973,6072,6146,6222,6329,6383,6456,6547,6643,6705,6769,6832,6931,7029,7121,7221,7514"}}, {"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\3a6c3290bf94c2fdaee81cec79dba243\\core-1.5.0-rc01\\res\\values-km\\values-km.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "90", "startColumns": "4", "startOffsets": "7603", "endColumns": "100", "endOffsets": "7699"}}, {"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\ec1a13a01684407bf169e0545c40db84\\navigation-ui-2.4.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,104", "endOffsets": "155,260"}, "to": {"startLines": "86,87", "startColumns": "4,4", "startOffsets": "7226,7331", "endColumns": "104,104", "endOffsets": "7326,7431"}}]}]}