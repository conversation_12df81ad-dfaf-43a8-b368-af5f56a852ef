<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6"
    >

    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <include
        android:id="@+id/loginfo"
        layout="@layout/logview"
        android:visibility="gone"/>

    <ImageView
        android:layout_width="94dp"
        android:layout_height="94dp"
        android:layout_marginTop="40dp"
        android:layout_gravity="center"
        android:background="@drawable/ota_product_jpg"/>


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:layout_marginTop="18dp"
        android:text="@string/current_device"
        android:textColor="#ff2c4662"
        android:textSize="16sp"
        android:layout_gravity="center"
        />

    <TextView
        android:id="@+id/device_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="4dp"
        android:text="--"
        android:textColor="#ff2c4662"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/device_address"
        android:layout_width="125dp"
        android:layout_height="20dp"
        android:layout_marginTop="4dp"
        android:text=""
        android:textColor="#ffafb8c3"
        android:textSize="14sp"
        android:layout_gravity="center"
        />

    <ImageView
        android:id="@+id/loading"
        android:layout_gravity="center"
        android:layout_marginTop="19dp"
        android:layout_width="129dp"
        android:layout_height="129dp"
        android:visibility="gone"

        />

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="350dp">

    </View>

    <TextView
        android:id="@+id/customer_uuid_tips"
        android:layout_width="match_parent"
        android:layout_height="25dp"
        android:layout_marginTop="-350dp"
        android:text="Format in firmware:{0x88, 0x09, 0x6f, 0x00, 0x99, 0x99, 0x99, 0x99,\n 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99}\nYou need to input:88,09,6f,00,99,99,99,99,99,99,99,99,99,99,99,99"
        android:textAlignment="center"
        android:textSize="8sp"
        android:visibility="invisible"
        />

    <EditText
        android:id="@+id/customer_uuid"
        android:layout_width="320dp"
        android:layout_gravity="center"
        android:layout_height="25dp"
        android:background="@drawable/rnd_rect_border_bg"
        android:digits="0123456789abcdefABCDEF,"
        android:maxLength="48"
        android:maxLines="1"
        android:padding="5dp"
        android:textSize="14sp"
        android:text="99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,"
        android:visibility="invisible"/>

    <Button
        android:id="@+id/edit_cutomercmd"
        android:layout_width="242dp"
        android:layout_height="68dp"
        android:layout_marginTop="10dp"
        android:layout_gravity="center"
        android:background="@drawable/ota_button_bg"
        android:text="@string/EDIT"
        android:textColor="@color/white"
        android:textAllCaps="false"
        android:visibility="invisible"
        >
    </Button>

    <Button
        android:id="@+id/pick_device_ble"
        android:layout_width="242dp"
        android:layout_height="68dp"
        android:layout_gravity="center"
        android:background="@drawable/ota_button_bg"
        android:text="@string/change_device_ble"
        android:textColor="@color/white"
        android:textAllCaps="false"
        android:visibility="invisible"
        >
    </Button>

    <Button
        android:id="@+id/pick_device"
        android:layout_width="242dp"
        android:layout_height="68dp"

        android:layout_gravity="center"
        android:background="@drawable/ota_button_bg"
        android:text="@string/change_device"
        android:textColor="@color/white"
        android:textAllCaps="false"
        >
    </Button>

    <Button
        android:id="@+id/connect_device"
        android:layout_width="242dp"
        android:layout_height="68dp"

        android:layout_gravity="center"
        android:background="@drawable/ota_button_bg"
        android:text="@string/connect_device"
        android:textColor="@color/white"
        android:textAllCaps="false"
        >
    </Button>


</LinearLayout>
