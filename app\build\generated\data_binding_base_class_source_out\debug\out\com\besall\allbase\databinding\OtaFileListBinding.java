// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class OtaFileListBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ListView otaFileList;

  @NonNull
  public final LinearLayout otaFileSteps;

  @NonNull
  public final TextView otaFileSteps2;

  @NonNull
  public final Button otaFileStepsBtn;

  @NonNull
  public final ToolbarBinding tool;

  private OtaFileListBinding(@NonNull LinearLayout rootView, @NonNull ListView otaFileList,
      @NonNull LinearLayout otaFileSteps, @NonNull TextView otaFileSteps2,
      @NonNull Button otaFileStepsBtn, @NonNull ToolbarBinding tool) {
    this.rootView = rootView;
    this.otaFileList = otaFileList;
    this.otaFileSteps = otaFileSteps;
    this.otaFileSteps2 = otaFileSteps2;
    this.otaFileStepsBtn = otaFileStepsBtn;
    this.tool = tool;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static OtaFileListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static OtaFileListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.ota_file_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static OtaFileListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ota_file_list;
      ListView otaFileList = rootView.findViewById(id);
      if (otaFileList == null) {
        break missingId;
      }

      id = R.id.ota_file_steps;
      LinearLayout otaFileSteps = rootView.findViewById(id);
      if (otaFileSteps == null) {
        break missingId;
      }

      id = R.id.ota_file_steps_2;
      TextView otaFileSteps2 = rootView.findViewById(id);
      if (otaFileSteps2 == null) {
        break missingId;
      }

      id = R.id.ota_file_steps_btn;
      Button otaFileStepsBtn = rootView.findViewById(id);
      if (otaFileStepsBtn == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      return new OtaFileListBinding((LinearLayout) rootView, otaFileList, otaFileSteps,
          otaFileSteps2, otaFileStepsBtn, binding_tool);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
