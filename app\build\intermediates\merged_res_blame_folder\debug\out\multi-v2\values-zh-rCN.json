{"logs": [{"outputFile": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-zh-rCN\\values-zh-rCN.xml", "map": [{"source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\values-zh-rCN\\strings.xml", "from": {"startLines": "212,166,160,161,192,191,159,162,213,142,179,180,265,266,264,12,3,5,7,10,11,9,4,6,8,14,17,284,235,196,2,148,134,135,136,138,137,132,133,189,114,115,78,64,65,67,66,186,216,246,256,245,250,252,253,257,248,249,251,107,127,128,79,238,150,155,156,270,269,271,272,63,255,275,68,73,109,168,151,247,261,263,262,201,231,197,15,108,175,174,129,170,167,187,273,268,267,154,94,169,104,106,103,105,182,181,283,237,178,112,198,149,232,229,230,126,285,102,110,225,224,76,86,97,96,100,99,98,101,95,88,55,54,56,61,62,57,60,58,59,77,75,81,92,85,90,84,83,82,87,80,91,89,202,51,70,71,69,72,185,183,184,199,286,16,13,280,294,289,233,113,146,147,236,194,141,143,293,292,200,239,240,241,242,193,287,291,74,281,260,259,258,173,117,120,118,121,119,122,116,290,282,223,188,195,206,205,208,176,190,274,211,210,209,277,234,217,218,219,221,220,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11300,9395,9174,9238,10473,10429,9126,9302,11360,8411,9869,9912,14059,14109,14009,640,103,209,329,503,564,449,156,269,389,751,879,14927,12464,10642,55,8709,8073,8135,8195,8304,8246,7970,8021,10340,7137,7203,5228,4424,4474,4594,4528,10203,11438,13120,13595,13075,13325,13431,13491,13647,13225,13277,13378,6815,7800,7848,5266,12773,8805,8983,9045,14303,14245,14364,14421,4374,13549,14597,4663,4937,6914,9473,8855,13177,13878,13967,13925,10857,12017,10685,788,6863,9724,9677,7897,9561,9431,10241,14470,14202,14159,8933,6081,9517,6646,6759,6589,6705,9997,9956,14878,12724,9822,7034,10723,8759,12218,11911,11975,7766,14977,6555,6964,11830,11774,5136,5674,6212,6168,6417,6347,6279,6487,6123,5785,3862,3796,3922,4232,4304,3986,4172,4053,4113,5172,5037,5374,6030,5615,5902,5556,5497,5439,5731,5322,5980,5842,10907,3688,4775,4824,4715,4880,10144,10037,10090,10767,15020,829,695,14733,15344,15134,12332,7085,8619,8657,12688,10554,8373,8479,15300,15253,10810,12829,12886,12945,12994,10514,15075,15216,4994,14787,13816,13761,13705,9630,7319,7439,7359,7523,7399,7633,7270,15180,14833,11728,10301,10596,11031,10970,11092,9774,10376,14538,11246,11198,11148,14652,12388,11485,11530,11579,11675,11625,7731", "endLines": "212,166,160,161,192,191,159,162,213,142,179,180,265,266,264,12,3,5,7,10,11,9,4,6,8,14,50,284,235,196,2,148,134,135,136,138,137,132,133,189,114,115,78,64,65,67,66,186,216,246,256,245,250,252,253,257,248,249,251,107,127,128,79,238,150,155,156,270,269,271,272,63,255,275,68,73,109,168,151,247,261,263,262,201,231,197,15,108,175,174,129,170,167,187,273,268,267,154,94,169,104,106,103,105,182,181,283,237,178,112,198,149,232,229,230,126,285,102,110,225,224,76,86,97,96,100,99,98,101,95,88,55,54,56,61,62,57,60,58,59,77,75,81,92,85,90,84,83,82,87,80,91,89,202,51,70,71,69,72,185,183,184,199,286,16,13,280,294,289,233,113,146,147,236,194,141,143,293,292,200,239,240,241,242,193,287,291,74,281,260,259,258,173,117,120,118,121,119,122,116,290,282,223,188,195,206,205,208,176,190,274,211,210,209,277,234,217,218,219,221,220,125", "endColumns": "59,35,63,63,40,43,47,62,56,67,42,43,49,49,49,54,52,59,59,60,75,53,52,59,59,36,13,49,223,42,47,49,61,59,50,48,57,50,51,35,65,66,37,49,53,68,65,37,46,56,51,44,52,59,56,57,51,47,52,47,47,48,55,55,49,61,59,60,57,56,48,49,45,53,51,56,49,43,55,47,46,41,41,49,200,37,40,50,49,46,46,43,41,59,67,42,42,49,41,43,58,55,56,53,39,40,48,48,46,50,43,45,113,63,41,33,42,33,68,55,55,35,56,66,43,69,69,67,67,44,56,59,65,63,71,69,66,59,59,58,55,98,64,49,58,77,58,58,57,53,51,49,59,35,88,48,55,59,56,58,52,53,42,54,49,55,53,38,45,55,51,37,51,35,41,37,113,43,46,46,56,58,48,50,39,57,36,42,45,61,54,55,46,39,83,39,109,39,72,48,35,44,45,38,45,59,60,55,46,52,58,53,47,49,54,75,44,48,45,51,49,34", "endOffsets": "11355,9426,9233,9297,10509,10468,9169,9360,11412,8474,9907,9951,14104,14154,14054,690,151,264,384,559,635,498,204,324,444,783,3683,14972,12683,10680,98,8754,8130,8190,8241,8348,8299,8016,8068,10371,7198,7265,5261,4469,4523,4658,4589,10236,11480,13172,13642,13115,13373,13486,13543,13700,13272,13320,13426,6858,7843,7892,5317,12824,8850,9040,9100,14359,14298,14416,14465,4419,13590,14646,4710,4989,6959,9512,8906,13220,13920,14004,13962,10902,12213,10718,824,6909,9769,9719,7939,9600,9468,10296,14533,14240,14197,8978,6118,9556,6700,6810,6641,6754,10032,9992,14922,12768,9864,7080,10762,8800,12327,11970,12012,7795,15015,6584,7028,11881,11825,5167,5726,6274,6207,6482,6412,6342,6550,6163,5837,3917,3857,3981,4299,4369,4048,4227,4108,4167,5223,5131,5434,6075,5669,5975,5610,5551,5492,5780,5369,6025,5897,10938,3772,4819,4875,4770,4932,10198,10085,10139,10805,15070,874,746,14782,15378,15175,12383,7132,8652,8704,12719,10591,8406,8588,15339,15295,10852,12881,12940,12989,13040,10549,15128,15248,5032,14828,13873,13811,13756,9672,7354,7518,7394,7628,7434,7701,7314,15211,14873,11769,10335,10637,11086,11026,11143,9816,10424,14592,11295,11241,11193,14702,12459,11525,11574,11620,11722,11670,7761"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,109,110,111,112,113,116,117,118,119,120,121,122,123,124,125,126,127,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,161,162,164,165,166,167,168,169,170,172,173,174,175,176,177,178,179,181,182,183,184,185,186,187,188,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,302,303,304,305,306,307,308,309,310,311,312,313,314,315,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "252,312,348,412,476,517,561,609,672,3286,3354,3397,3441,3491,3541,3591,3646,3699,3759,3819,3880,3956,4010,4063,4123,4183,4220,7029,7079,7303,7346,7394,7444,7506,7566,7617,7666,7724,7769,7815,7851,7917,8054,8092,8142,8196,8265,8528,8566,8613,8670,8722,8767,8820,8880,8937,8995,9047,9095,9221,9269,9317,9366,9422,9478,9528,9590,9650,9711,9769,9826,9875,9925,9971,10025,10077,10134,10184,10228,10284,10332,10379,10421,10463,10513,10714,10752,10793,10844,10894,11003,11050,11171,11213,11273,11341,11384,11427,11477,11578,11622,11681,11737,11794,11848,11888,11929,12037,12086,12133,12184,12228,12274,12388,12452,15813,15847,15890,15924,15993,16049,16105,16141,16198,16265,16309,16379,16449,16517,16585,16630,16687,16747,16813,16877,16949,17019,17086,17146,17206,17265,17321,17420,17485,17535,17594,17672,17731,17790,17848,17902,17954,18004,18133,18169,18258,18307,18363,18423,18480,18539,18592,18646,18689,18744,18794,18850,18904,18943,18989,19045,19097,19135,19187,19223,19265,19303,19417,19461,19587,19634,19691,19750,19799,19850,19890,19948,19985,20028,20074,20136,20191,20247,20395,20435,20519,20559,20669,20709,20782,20831,20867,20912,20958,20997,21043,21103,21164,21220,21267,21320,21379,21433,21481,21531,21586,21662,21707,21756,21802,21854,21904", "endLines": "6,7,8,9,10,11,12,13,14,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,109,110,111,112,113,116,117,118,119,120,121,122,123,124,125,126,127,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,161,162,164,165,166,167,168,169,170,172,173,174,175,176,177,178,179,181,182,183,184,185,186,187,188,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,302,303,304,305,306,307,308,309,310,311,312,313,314,315,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345", "endColumns": "59,35,63,63,40,43,47,62,56,67,42,43,49,49,49,54,52,59,59,60,75,53,52,59,59,36,13,49,223,42,47,49,61,59,50,48,57,44,45,35,65,66,37,49,53,68,65,37,46,56,51,44,52,59,56,57,51,47,52,47,47,48,55,55,49,61,59,60,57,56,48,49,45,53,51,56,49,43,55,47,46,41,41,49,200,37,40,50,49,46,46,43,41,59,67,42,42,49,41,43,58,55,56,53,39,40,48,48,46,50,43,45,113,63,41,33,42,33,68,55,55,35,56,66,43,69,69,67,67,44,56,59,65,63,71,69,66,59,59,58,55,98,64,49,58,77,58,58,57,53,51,49,59,35,88,48,55,59,56,58,52,53,42,54,49,55,53,38,45,55,51,37,51,35,41,37,113,43,46,46,56,58,48,50,39,57,36,42,45,61,54,55,46,39,83,39,109,39,72,48,35,44,45,38,45,59,60,55,46,52,58,53,47,49,54,75,44,48,45,51,49,34", "endOffsets": "307,343,407,471,512,556,604,667,724,3349,3392,3436,3486,3536,3586,3641,3694,3754,3814,3875,3951,4005,4058,4118,4178,4215,7024,7074,7298,7341,7389,7439,7501,7561,7612,7661,7719,7764,7810,7846,7912,7979,8087,8137,8191,8260,8326,8561,8608,8665,8717,8762,8815,8875,8932,8990,9042,9090,9143,9264,9312,9361,9417,9473,9523,9585,9645,9706,9764,9821,9870,9920,9966,10020,10072,10129,10179,10223,10279,10327,10374,10416,10458,10508,10709,10747,10788,10839,10889,10936,11045,11089,11208,11268,11336,11379,11422,11472,11514,11617,11676,11732,11789,11843,11883,11924,11973,12081,12128,12179,12223,12269,12383,12447,12489,15842,15885,15919,15988,16044,16100,16136,16193,16260,16304,16374,16444,16512,16580,16625,16682,16742,16808,16872,16944,17014,17081,17141,17201,17260,17316,17415,17480,17530,17589,17667,17726,17785,17843,17897,17949,17999,18059,18164,18253,18302,18358,18418,18475,18534,18587,18641,18684,18739,18789,18845,18899,18938,18984,19040,19092,19130,19182,19218,19260,19298,19412,19456,19503,19629,19686,19745,19794,19845,19885,19943,19980,20023,20069,20131,20186,20242,20289,20430,20514,20554,20664,20704,20777,20826,20862,20907,20953,20992,21038,21098,21159,21215,21262,21315,21374,21428,21476,21526,21581,21657,21702,21751,21797,21849,21899,21934"}}, {"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\ec1a13a01684407bf169e0545c40db84\\navigation-ui-2.4.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,156", "endColumns": "100,102", "endOffsets": "151,254"}, "to": {"startLines": "234,235", "startColumns": "4,4", "startOffsets": "15609,15710", "endColumns": "100,102", "endOffsets": "15705,15808"}}, {"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\0a678c11412547be3dd78d75dd3cb66f\\jetified-zxing-android-embedded-4.1.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,104,151,238", "endColumns": "48,46,86,68", "endOffsets": "99,146,233,302"}, "to": {"startLines": "346,347,348,349", "startColumns": "4,4,4,4", "startOffsets": "21939,21988,22035,22122", "endColumns": "48,46,86,68", "endOffsets": "21983,22030,22117,22186"}}, {"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\85d353c95dd38846b16194b262acf36b\\material-1.4.0-beta01\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,272,363,469,542,604,681,740,799,877,934,990,1049,1107,1161,1246,1302,1360,1414,1479,1571,1645,1721,1813,1875,1937,2016,2083,2149,2213,2282,2360,2421,2492,2559,2619,2698,2765,2848,2933,3007,3072,3148,3196,3260,3336,3414,3476,3540,3603,3683,3759,3837,3914", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,69,90,105,72,61,76,58,58,77,56,55,58,57,53,84,55,57,53,64,91,73,75,91,61,61,78,66,65,63,68,77,60,70,66,59,78,66,82,84,73,64,75,47,63,75,77,61,63,62,79,75,77,76,68", "endOffsets": "197,267,358,464,537,599,676,735,794,872,929,985,1044,1102,1156,1241,1297,1355,1409,1474,1566,1640,1716,1808,1870,1932,2011,2078,2144,2208,2277,2355,2416,2487,2554,2614,2693,2760,2843,2928,3002,3067,3143,3191,3255,3331,3409,3471,3535,3598,3678,3754,3832,3909,3978"}, "to": {"startLines": "2,108,114,115,128,160,163,171,180,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,274", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,7984,8331,8422,9148,10941,11094,11519,11978,12494,12572,12629,12685,12744,12802,12856,12941,12997,13055,13109,13174,13266,13340,13416,13508,13570,13632,13711,13778,13844,13908,13977,14055,14116,14187,14254,14314,14393,14460,14543,14628,14702,14767,14843,14891,14955,15031,15109,15171,15235,15298,15378,15454,15532,18064", "endLines": "5,108,114,115,128,160,163,171,180,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,274", "endColumns": "12,69,90,105,72,61,76,58,58,77,56,55,58,57,53,84,55,57,53,64,91,73,75,91,61,61,78,66,65,63,68,77,60,70,66,59,78,66,82,84,73,64,75,47,63,75,77,61,63,62,79,75,77,76,68", "endOffsets": "247,8049,8417,8523,9216,10998,11166,11573,12032,12567,12624,12680,12739,12797,12851,12936,12992,13050,13104,13169,13261,13335,13411,13503,13565,13627,13706,13773,13839,13903,13972,14050,14111,14182,14249,14309,14388,14455,14538,14623,14697,14762,14838,14886,14950,15026,15104,15166,15230,15293,15373,15449,15527,15604,18128"}}, {"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\7ad5be15223e864e4511b3f7157ae4d3\\appcompat-1.2.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,301", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "729,824,919,1019,1101,1198,1304,1381,1456,1547,1640,1737,1833,1927,2020,2115,2207,2298,2389,2467,2563,2658,2753,2850,2946,3044,3192,19508", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "819,914,1014,1096,1193,1299,1376,1451,1542,1635,1732,1828,1922,2015,2110,2202,2293,2384,2462,2558,2653,2748,2845,2941,3039,3187,3281,19582"}}, {"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\3a6c3290bf94c2fdaee81cec79dba243\\core-1.5.0-rc01\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "316", "startColumns": "4", "startOffsets": "20294", "endColumns": "100", "endOffsets": "20390"}}]}]}