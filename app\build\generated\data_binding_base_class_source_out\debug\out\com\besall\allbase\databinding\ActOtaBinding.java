// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActOtaBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final RadioButton bothearbuddiffbin;

  @NonNull
  public final RadioButton bothearbudsamebin;

  @NonNull
  public final Button connectDevice;

  @NonNull
  public final TextView currentOtaFileTitle;

  @NonNull
  public final TextView currentVersionDetails;

  @NonNull
  public final TextView deviceAddress;

  @NonNull
  public final TextView deviceName;

  @NonNull
  public final RadioButton fastMod;

  @NonNull
  public final RadioGroup oldotaupgradetype;

  @NonNull
  public final RadioButton onlyleftearbud;

  @NonNull
  public final RadioButton onlyrightearbud;

  @NonNull
  public final TextView otaFile;

  @NonNull
  public final TextView otaFileRight;

  @NonNull
  public final TextView otaInfo;

  @NonNull
  public final TextView otaInfoList;

  @NonNull
  public final ProgressBar otaProgress;

  @NonNull
  public final TextView otaStatus;

  @NonNull
  public final Button pickDevice;

  @NonNull
  public final Button pickOtaFile;

  @NonNull
  public final Button pickOtaFileRight;

  @NonNull
  public final RadioButton slowMod;

  @NonNull
  public final Button startOta;

  @NonNull
  public final ToolbarBinding tool;

  @NonNull
  public final RadioGroup transferack;

  @NonNull
  public final TextView transferacktext;

  @NonNull
  public final RadioGroup upgradechoose;

  @NonNull
  public final TextView upgradetext;

  @NonNull
  public final RadioButton userCombo;

  @NonNull
  public final RadioButton userFw;

  @NonNull
  public final RadioButton userLg;

  @NonNull
  public final RadioGroup userchoose;

  @NonNull
  public final TextView usertext;

  @NonNull
  public final RadioButton withack;

  @NonNull
  public final RadioButton withoutack;

  private ActOtaBinding(@NonNull ScrollView rootView, @NonNull RadioButton bothearbuddiffbin,
      @NonNull RadioButton bothearbudsamebin, @NonNull Button connectDevice,
      @NonNull TextView currentOtaFileTitle, @NonNull TextView currentVersionDetails,
      @NonNull TextView deviceAddress, @NonNull TextView deviceName, @NonNull RadioButton fastMod,
      @NonNull RadioGroup oldotaupgradetype, @NonNull RadioButton onlyleftearbud,
      @NonNull RadioButton onlyrightearbud, @NonNull TextView otaFile,
      @NonNull TextView otaFileRight, @NonNull TextView otaInfo, @NonNull TextView otaInfoList,
      @NonNull ProgressBar otaProgress, @NonNull TextView otaStatus, @NonNull Button pickDevice,
      @NonNull Button pickOtaFile, @NonNull Button pickOtaFileRight, @NonNull RadioButton slowMod,
      @NonNull Button startOta, @NonNull ToolbarBinding tool, @NonNull RadioGroup transferack,
      @NonNull TextView transferacktext, @NonNull RadioGroup upgradechoose,
      @NonNull TextView upgradetext, @NonNull RadioButton userCombo, @NonNull RadioButton userFw,
      @NonNull RadioButton userLg, @NonNull RadioGroup userchoose, @NonNull TextView usertext,
      @NonNull RadioButton withack, @NonNull RadioButton withoutack) {
    this.rootView = rootView;
    this.bothearbuddiffbin = bothearbuddiffbin;
    this.bothearbudsamebin = bothearbudsamebin;
    this.connectDevice = connectDevice;
    this.currentOtaFileTitle = currentOtaFileTitle;
    this.currentVersionDetails = currentVersionDetails;
    this.deviceAddress = deviceAddress;
    this.deviceName = deviceName;
    this.fastMod = fastMod;
    this.oldotaupgradetype = oldotaupgradetype;
    this.onlyleftearbud = onlyleftearbud;
    this.onlyrightearbud = onlyrightearbud;
    this.otaFile = otaFile;
    this.otaFileRight = otaFileRight;
    this.otaInfo = otaInfo;
    this.otaInfoList = otaInfoList;
    this.otaProgress = otaProgress;
    this.otaStatus = otaStatus;
    this.pickDevice = pickDevice;
    this.pickOtaFile = pickOtaFile;
    this.pickOtaFileRight = pickOtaFileRight;
    this.slowMod = slowMod;
    this.startOta = startOta;
    this.tool = tool;
    this.transferack = transferack;
    this.transferacktext = transferacktext;
    this.upgradechoose = upgradechoose;
    this.upgradetext = upgradetext;
    this.userCombo = userCombo;
    this.userFw = userFw;
    this.userLg = userLg;
    this.userchoose = userchoose;
    this.usertext = usertext;
    this.withack = withack;
    this.withoutack = withoutack;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActOtaBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActOtaBinding inflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup parent,
      boolean attachToParent) {
    View root = inflater.inflate(R.layout.act_ota, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActOtaBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bothearbuddiffbin;
      RadioButton bothearbuddiffbin = rootView.findViewById(id);
      if (bothearbuddiffbin == null) {
        break missingId;
      }

      id = R.id.bothearbudsamebin;
      RadioButton bothearbudsamebin = rootView.findViewById(id);
      if (bothearbudsamebin == null) {
        break missingId;
      }

      id = R.id.connect_device;
      Button connectDevice = rootView.findViewById(id);
      if (connectDevice == null) {
        break missingId;
      }

      id = R.id.current_ota_file_title;
      TextView currentOtaFileTitle = rootView.findViewById(id);
      if (currentOtaFileTitle == null) {
        break missingId;
      }

      id = R.id.current_version_details;
      TextView currentVersionDetails = rootView.findViewById(id);
      if (currentVersionDetails == null) {
        break missingId;
      }

      id = R.id.device_address;
      TextView deviceAddress = rootView.findViewById(id);
      if (deviceAddress == null) {
        break missingId;
      }

      id = R.id.device_name;
      TextView deviceName = rootView.findViewById(id);
      if (deviceName == null) {
        break missingId;
      }

      id = R.id.fast_mod;
      RadioButton fastMod = rootView.findViewById(id);
      if (fastMod == null) {
        break missingId;
      }

      id = R.id.oldotaupgradetype;
      RadioGroup oldotaupgradetype = rootView.findViewById(id);
      if (oldotaupgradetype == null) {
        break missingId;
      }

      id = R.id.onlyleftearbud;
      RadioButton onlyleftearbud = rootView.findViewById(id);
      if (onlyleftearbud == null) {
        break missingId;
      }

      id = R.id.onlyrightearbud;
      RadioButton onlyrightearbud = rootView.findViewById(id);
      if (onlyrightearbud == null) {
        break missingId;
      }

      id = R.id.ota_file;
      TextView otaFile = rootView.findViewById(id);
      if (otaFile == null) {
        break missingId;
      }

      id = R.id.ota_file_right;
      TextView otaFileRight = rootView.findViewById(id);
      if (otaFileRight == null) {
        break missingId;
      }

      id = R.id.ota_info;
      TextView otaInfo = rootView.findViewById(id);
      if (otaInfo == null) {
        break missingId;
      }

      id = R.id.ota_info_list;
      TextView otaInfoList = rootView.findViewById(id);
      if (otaInfoList == null) {
        break missingId;
      }

      id = R.id.ota_progress;
      ProgressBar otaProgress = rootView.findViewById(id);
      if (otaProgress == null) {
        break missingId;
      }

      id = R.id.ota_status;
      TextView otaStatus = rootView.findViewById(id);
      if (otaStatus == null) {
        break missingId;
      }

      id = R.id.pick_device;
      Button pickDevice = rootView.findViewById(id);
      if (pickDevice == null) {
        break missingId;
      }

      id = R.id.pick_ota_file;
      Button pickOtaFile = rootView.findViewById(id);
      if (pickOtaFile == null) {
        break missingId;
      }

      id = R.id.pick_ota_file_right;
      Button pickOtaFileRight = rootView.findViewById(id);
      if (pickOtaFileRight == null) {
        break missingId;
      }

      id = R.id.slow_mod;
      RadioButton slowMod = rootView.findViewById(id);
      if (slowMod == null) {
        break missingId;
      }

      id = R.id.start_ota;
      Button startOta = rootView.findViewById(id);
      if (startOta == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      id = R.id.transferack;
      RadioGroup transferack = rootView.findViewById(id);
      if (transferack == null) {
        break missingId;
      }

      id = R.id.transferacktext;
      TextView transferacktext = rootView.findViewById(id);
      if (transferacktext == null) {
        break missingId;
      }

      id = R.id.upgradechoose;
      RadioGroup upgradechoose = rootView.findViewById(id);
      if (upgradechoose == null) {
        break missingId;
      }

      id = R.id.upgradetext;
      TextView upgradetext = rootView.findViewById(id);
      if (upgradetext == null) {
        break missingId;
      }

      id = R.id.user_combo;
      RadioButton userCombo = rootView.findViewById(id);
      if (userCombo == null) {
        break missingId;
      }

      id = R.id.user_fw;
      RadioButton userFw = rootView.findViewById(id);
      if (userFw == null) {
        break missingId;
      }

      id = R.id.user_lg;
      RadioButton userLg = rootView.findViewById(id);
      if (userLg == null) {
        break missingId;
      }

      id = R.id.userchoose;
      RadioGroup userchoose = rootView.findViewById(id);
      if (userchoose == null) {
        break missingId;
      }

      id = R.id.usertext;
      TextView usertext = rootView.findViewById(id);
      if (usertext == null) {
        break missingId;
      }

      id = R.id.withack;
      RadioButton withack = rootView.findViewById(id);
      if (withack == null) {
        break missingId;
      }

      id = R.id.withoutack;
      RadioButton withoutack = rootView.findViewById(id);
      if (withoutack == null) {
        break missingId;
      }

      return new ActOtaBinding((ScrollView) rootView, bothearbuddiffbin, bothearbudsamebin,
          connectDevice, currentOtaFileTitle, currentVersionDetails, deviceAddress, deviceName,
          fastMod, oldotaupgradetype, onlyleftearbud, onlyrightearbud, otaFile, otaFileRight,
          otaInfo, otaInfoList, otaProgress, otaStatus, pickDevice, pickOtaFile, pickOtaFileRight,
          slowMod, startOta, binding_tool, transferack, transferacktext, upgradechoose, upgradetext,
          userCombo, userFw, userLg, userchoose, usertext, withack, withoutack);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
