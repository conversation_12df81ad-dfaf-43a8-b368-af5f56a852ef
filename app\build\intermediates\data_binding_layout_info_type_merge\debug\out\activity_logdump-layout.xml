<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_logdump" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_logdump.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_logdump_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="176" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/activity_logdump_0" include="toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="12" endOffset="9"/></Target><Target id="@+id/loginfo" tag="layout/activity_logdump_0" include="logview"><Expressions/><location startLine="14" startOffset="4" endLine="17" endOffset="34"/></Target><Target id="@+id/log_read_start" view="Button"><Expressions/><location startLine="26" startOffset="8" endLine="39" endOffset="61"/></Target><Target id="@+id/log_progress" view="ProgressBar"><Expressions/><location startLine="41" startOffset="8" endLine="52" endOffset="13"/></Target><Target id="@+id/total_log_status" view="TextView"><Expressions/><location startLine="60" startOffset="12" endLine="70" endOffset="17"/></Target><Target id="@+id/total_log_progress" view="TextView"><Expressions/><location startLine="81" startOffset="12" endLine="90" endOffset="48"/></Target><Target id="@+id/tasks_view" view="com.besall.allbase.common.utils.CircleProgressView"><Expressions/><location startLine="107" startOffset="8" endLine="116" endOffset="49"/></Target><Target id="@+id/current_log_pro" view="TextView"><Expressions/><location startLine="127" startOffset="4" endLine="136" endOffset="33"/></Target><Target id="@+id/percent" view="ImageView"><Expressions/><location startLine="138" startOffset="4" endLine="143" endOffset="48"/></Target><Target id="@+id/current_log_status" view="TextView"><Expressions/><location startLine="147" startOffset="4" endLine="157" endOffset="9"/></Target><Target id="@+id/spp_stop" view="Button"><Expressions/><location startLine="163" startOffset="4" endLine="173" endOffset="36"/></Target></Targets></Layout>