[{"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_avslwa_language_select.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_avslwa_language_select.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_cuscmd.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_cuscmd.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\dial_show_bgview.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\dial_show_bgview.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\simple_spinner_item.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\simple_spinner_item.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\ota_config.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\ota_config.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\bis_code_dialog.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\bis_code_dialog.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_cropphoto.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_cropphoto.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\act_setting_lsv.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\act_setting_lsv.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_watchfunction.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_watchfunction.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\make_dial_list_item.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\make_dial_list_item.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_functionchoose.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_functionchoose.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_rssi.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_rssi.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\act_aboutus.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\act_aboutus.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\progress_dialog.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\progress_dialog.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_main.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_main.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\fragment_actions.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\fragment_actions.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_otafunction.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_otafunction.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_blewifi.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_blewifi.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_commandset.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_commandset.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\act_test.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\act_test.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_crashdump.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_crashdump.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\listtest.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\listtest.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_eq_pc.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_eq_pc.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\act_scan.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\act_scan.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\cmdlistview.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\cmdlistview.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\dial_show_home_bgview.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\dial_show_home_bgview.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_audiodump.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_audiodump.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\logview.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\logview.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_throughput.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_throughput.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\item_audio_list.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\item_audio_list.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\view_button.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\view_button.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\ota_file_list.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\ota_file_list.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_auracastassistantinfo.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_auracastassistantinfo.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_capsensor.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_capsensor.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\avs_lwa_dialog.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\avs_lwa_dialog.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_auracastassistant.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_auracastassistant.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_eq.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_eq.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_checkcrc.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_checkcrc.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\item_eq_iireq.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\item_eq_iireq.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_chipstollfunction.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_chipstollfunction.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\wifi_item.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\wifi_item.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\bis_device_item.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\bis_device_item.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\cmd_item.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\cmd_item.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_avslwa_setting.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_avslwa_setting.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\file_list.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\file_list.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\fragment1.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\fragment1.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\avslwa_item.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\avslwa_item.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\act_smartvoice.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\act_smartvoice.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\view_versionpath.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\view_versionpath.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_findmy.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_findmy.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_rssiextend.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_rssiextend.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_launch.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_launch.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\act_otaui.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\act_otaui.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_d3d_main.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_d3d_main.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\fragment_home.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\fragment_home.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_logdump.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_logdump.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_watchavs.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_watchavs.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\view_baseoption.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\view_baseoption.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_health.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_health.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\act_temperature.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\act_temperature.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_avslwa_login.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_avslwa_login.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\fragment_spp.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\fragment_spp.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_customerdial.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_customerdial.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\act_blood.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\act_blood.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\otafilepath_item.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\otafilepath_item.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\item_action_fragment.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\item_action_fragment.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\view_device.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\view_device.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\toolbar.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\toolbar.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_avslwa.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_avslwa.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\device_item.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\device_item.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\act_connect.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\act_connect.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\item_eq_drc.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\item_eq_drc.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\item_file_list.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\item_file_list.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\view_otalog.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\view_otalog.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\act_sports.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\act_sports.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\capsensor_list_item.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\capsensor_list_item.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_avslwa_resize.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_avslwa_resize.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\action_sheet.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\action_sheet.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\act_pulse.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\act_pulse.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\act_sleep.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\act_sleep.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\view_otaing.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\view_otaing.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_avslwa_guide.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_avslwa_guide.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\language_select_item.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\language_select_item.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\act_setting.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\act_setting.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\activity_makedial.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\activity_makedial.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\wifi_activity_main.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\wifi_activity_main.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\confirm_dialog.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\confirm_dialog.xml"}, {"merged": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\stripped.dir\\layout\\act_ota.xml", "source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\layout\\act_ota.xml"}]