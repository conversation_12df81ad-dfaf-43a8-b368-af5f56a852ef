// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class BisDeviceItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView bisBtnInfo;

  @NonNull
  public final ImageView bisIconExit;

  @NonNull
  public final ImageView bisIconNeedCode;

  @NonNull
  public final ImageView bisIconSound;

  @NonNull
  public final Button joinMarkButton;

  @NonNull
  public final TextView joinMarkText;

  @NonNull
  public final TextView name;

  @NonNull
  public final TextView status;

  private BisDeviceItemBinding(@NonNull LinearLayout rootView, @NonNull ImageView bisBtnInfo,
      @NonNull ImageView bisIconExit, @NonNull ImageView bisIconNeedCode,
      @NonNull ImageView bisIconSound, @NonNull Button joinMarkButton,
      @NonNull TextView joinMarkText, @NonNull TextView name, @NonNull TextView status) {
    this.rootView = rootView;
    this.bisBtnInfo = bisBtnInfo;
    this.bisIconExit = bisIconExit;
    this.bisIconNeedCode = bisIconNeedCode;
    this.bisIconSound = bisIconSound;
    this.joinMarkButton = joinMarkButton;
    this.joinMarkText = joinMarkText;
    this.name = name;
    this.status = status;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static BisDeviceItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static BisDeviceItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.bis_device_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static BisDeviceItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bis_btn_info;
      ImageView bisBtnInfo = rootView.findViewById(id);
      if (bisBtnInfo == null) {
        break missingId;
      }

      id = R.id.bis_icon_exit;
      ImageView bisIconExit = rootView.findViewById(id);
      if (bisIconExit == null) {
        break missingId;
      }

      id = R.id.bis_icon_need_code;
      ImageView bisIconNeedCode = rootView.findViewById(id);
      if (bisIconNeedCode == null) {
        break missingId;
      }

      id = R.id.bis_icon_sound;
      ImageView bisIconSound = rootView.findViewById(id);
      if (bisIconSound == null) {
        break missingId;
      }

      id = R.id.join_mark_button;
      Button joinMarkButton = rootView.findViewById(id);
      if (joinMarkButton == null) {
        break missingId;
      }

      id = R.id.join_mark_text;
      TextView joinMarkText = rootView.findViewById(id);
      if (joinMarkText == null) {
        break missingId;
      }

      id = R.id.name;
      TextView name = rootView.findViewById(id);
      if (name == null) {
        break missingId;
      }

      id = R.id.status;
      TextView status = rootView.findViewById(id);
      if (status == null) {
        break missingId;
      }

      return new BisDeviceItemBinding((LinearLayout) rootView, bisBtnInfo, bisIconExit,
          bisIconNeedCode, bisIconSound, joinMarkButton, joinMarkText, name, status);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
