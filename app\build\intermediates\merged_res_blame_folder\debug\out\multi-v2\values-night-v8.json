{"logs": [{"outputFile": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-night-v8\\values-night-v8.xml", "map": [{"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\85d353c95dd38846b16194b262acf36b\\material-1.4.0-beta01\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,267,369,471,587,689,803,931,1047,1169,1305,1425,1559,1679,1791,1917,2041,2171,2293,2431,2565,2681", "endColumns": "87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,123,129,121,137,133,115,119", "endOffsets": "138,262,364,466,582,684,798,926,1042,1164,1300,1420,1554,1674,1786,1912,2036,2166,2288,2426,2560,2676,2796"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "687,775,899,1001,1103,1219,1321,1435,1563,1679,1801,1937,2057,2191,2311,2423,2867,2991,3121,3243,3381,3515,3631", "endColumns": "87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,123,129,121,137,133,115,119", "endOffsets": "770,894,996,1098,1214,1316,1430,1558,1674,1796,1932,2052,2186,2306,2418,2544,2986,3116,3238,3376,3510,3626,3746"}}, {"source": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "17", "endLines": "5", "endColumns": "12", "endOffsets": "241"}, "to": {"startLines": "26", "startColumns": "4", "startOffsets": "2638", "endLines": "29", "endColumns": "12", "endOffsets": "2862"}}, {"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\7ad5be15223e864e4511b3f7157ae4d3\\appcompat-1.2.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "2,3,4,5,6,7,8,25", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,2549", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,2633"}}]}]}