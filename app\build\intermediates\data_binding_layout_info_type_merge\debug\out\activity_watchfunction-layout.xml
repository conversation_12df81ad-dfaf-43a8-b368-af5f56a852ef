<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_watchfunction" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_watchfunction.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_watchfunction_0" view="LinearLayout"><Expressions/><location startLine="8" startOffset="0" endLine="105" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/activity_watchfunction_0" include="toolbar"><Expressions/><location startLine="16" startOffset="4" endLine="19" endOffset="9"/></Target><Target id="@+id/find_my" view="Button"><Expressions/><location startLine="27" startOffset="4" endLine="38" endOffset="9"/></Target><Target id="@+id/watch_dial" view="Button"><Expressions/><location startLine="47" startOffset="4" endLine="58" endOffset="9"/></Target><Target id="@+id/watch_avs" view="Button"><Expressions/><location startLine="67" startOffset="4" endLine="78" endOffset="9"/></Target><Target id="@+id/wearable_device" view="Button"><Expressions/><location startLine="86" startOffset="4" endLine="98" endOffset="9"/></Target></Targets></Layout>