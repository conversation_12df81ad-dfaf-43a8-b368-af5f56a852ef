<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="10dp"
            >

        </LinearLayout>
        <LinearLayout
            android:id="@+id/view_device_support"
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:orientation="horizontal">

        </LinearLayout>

        <LinearLayout
            android:id="@+id/view_device_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:ignore="MissingConstraints"
            android:orientation="vertical"
            >

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/ota_product_jpg"
                android:layout_gravity="center"
                >
            </ImageView>
            <TextView
                android:id="@+id/device_title"
                android:layout_marginTop="18dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/last_device"
                android:textColor="@color/title_color_dark"
                android:textSize="16sp"
                android:fontFamily="@font/sanshan_bold"
                android:layout_gravity="center"
                >
            </TextView>
            <EditText
                android:id="@+id/device_name"
                android:layout_marginTop="4dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:textColor="@color/title_color_light"
                android:text="--"
                android:textSize="16sp"
                android:fontFamily="@font/sanshan_normal"
                android:gravity="center"
                android:focusable="false"
                android:background="#00000000"
                >
            </EditText>
        </LinearLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>