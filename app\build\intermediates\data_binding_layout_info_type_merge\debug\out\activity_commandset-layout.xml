<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_commandset" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_commandset.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_commandset_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="3399" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/activity_commandset_0" include="toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="12" endOffset="9"/></Target><Target id="@+id/loginfo" tag="layout/activity_commandset_0" include="logview"><Expressions/><location startLine="14" startOffset="4" endLine="17" endOffset="34"/></Target><Target id="@+id/command_set_receive_data" view="EditText"><Expressions/><location startLine="31" startOffset="12" endLine="38" endOffset="17"/></Target><Target id="@+id/command_set_current_version" view="TextView"><Expressions/><location startLine="40" startOffset="12" endLine="48" endOffset="17"/></Target><Target id="@+id/command_set_current_product_model" view="TextView"><Expressions/><location startLine="49" startOffset="12" endLine="57" endOffset="17"/></Target><Target id="@+id/seekbar_eq_2_bg" view="LinearLayout"><Expressions/><location startLine="59" startOffset="12" endLine="418" endOffset="26"/></Target><Target id="@+id/seekbar_eq_2_32" view="SeekBar"><Expressions/><location startLine="80" startOffset="20" endLine="88" endOffset="25"/></Target><Target id="@+id/text_eq_2_32" view="TextView"><Expressions/><location startLine="90" startOffset="20" endLine="98" endOffset="25"/></Target><Target id="@+id/seekbar_eq_2_64" view="SeekBar"><Expressions/><location startLine="115" startOffset="20" endLine="123" endOffset="25"/></Target><Target id="@+id/text_eq_2_64" view="TextView"><Expressions/><location startLine="125" startOffset="20" endLine="133" endOffset="25"/></Target><Target id="@+id/seekbar_eq_2_125" view="SeekBar"><Expressions/><location startLine="150" startOffset="20" endLine="158" endOffset="25"/></Target><Target id="@+id/text_eq_2_125" view="TextView"><Expressions/><location startLine="160" startOffset="20" endLine="168" endOffset="25"/></Target><Target id="@+id/seekbar_eq_2_250" view="SeekBar"><Expressions/><location startLine="185" startOffset="20" endLine="193" endOffset="25"/></Target><Target id="@+id/text_eq_2_250" view="TextView"><Expressions/><location startLine="195" startOffset="20" endLine="203" endOffset="25"/></Target><Target id="@+id/seekbar_eq_2_500" view="SeekBar"><Expressions/><location startLine="220" startOffset="20" endLine="228" endOffset="25"/></Target><Target id="@+id/text_eq_2_500" view="TextView"><Expressions/><location startLine="230" startOffset="20" endLine="238" endOffset="25"/></Target><Target id="@+id/seekbar_eq_2_1k" view="SeekBar"><Expressions/><location startLine="255" startOffset="20" endLine="263" endOffset="25"/></Target><Target id="@+id/text_eq_2_1k" view="TextView"><Expressions/><location startLine="265" startOffset="20" endLine="273" endOffset="25"/></Target><Target id="@+id/seekbar_eq_2_2k" view="SeekBar"><Expressions/><location startLine="290" startOffset="20" endLine="298" endOffset="25"/></Target><Target id="@+id/text_eq_2_2k" view="TextView"><Expressions/><location startLine="300" startOffset="20" endLine="308" endOffset="25"/></Target><Target id="@+id/seekbar_eq_2_4k" view="SeekBar"><Expressions/><location startLine="325" startOffset="20" endLine="333" endOffset="25"/></Target><Target id="@+id/text_eq_2_4k" view="TextView"><Expressions/><location startLine="335" startOffset="20" endLine="343" endOffset="25"/></Target><Target id="@+id/seekbar_eq_2_8k" view="SeekBar"><Expressions/><location startLine="361" startOffset="20" endLine="369" endOffset="25"/></Target><Target id="@+id/text_eq_2_8k" view="TextView"><Expressions/><location startLine="371" startOffset="20" endLine="379" endOffset="25"/></Target><Target id="@+id/seekbar_eq_2_16k" view="SeekBar"><Expressions/><location startLine="396" startOffset="20" endLine="404" endOffset="25"/></Target><Target id="@+id/text_eq_2_16k" view="TextView"><Expressions/><location startLine="406" startOffset="20" endLine="414" endOffset="25"/></Target><Target id="@+id/seekbar_eq_bg" view="LinearLayout"><Expressions/><location startLine="421" startOffset="12" endLine="672" endOffset="26"/></Target><Target id="@+id/seekbar_eq_60" view="SeekBar"><Expressions/><location startLine="441" startOffset="20" endLine="449" endOffset="25"/></Target><Target id="@+id/text_eq_60" view="TextView"><Expressions/><location startLine="451" startOffset="20" endLine="459" endOffset="25"/></Target><Target id="@+id/seekbar_eq_120" view="SeekBar"><Expressions/><location startLine="476" startOffset="20" endLine="484" endOffset="25"/></Target><Target id="@+id/text_eq_120" view="TextView"><Expressions/><location startLine="486" startOffset="20" endLine="494" endOffset="25"/></Target><Target id="@+id/seekbar_eq_250" view="SeekBar"><Expressions/><location startLine="511" startOffset="20" endLine="519" endOffset="25"/></Target><Target id="@+id/text_eq_250" view="TextView"><Expressions/><location startLine="521" startOffset="20" endLine="529" endOffset="25"/></Target><Target id="@+id/seekbar_eq_750" view="SeekBar"><Expressions/><location startLine="546" startOffset="20" endLine="554" endOffset="25"/></Target><Target id="@+id/text_eq_750" view="TextView"><Expressions/><location startLine="556" startOffset="20" endLine="564" endOffset="25"/></Target><Target id="@+id/seekbar_eq_2k" view="SeekBar"><Expressions/><location startLine="581" startOffset="20" endLine="589" endOffset="25"/></Target><Target id="@+id/text_eq_2k" view="TextView"><Expressions/><location startLine="591" startOffset="20" endLine="599" endOffset="25"/></Target><Target id="@+id/seekbar_eq_5k" view="SeekBar"><Expressions/><location startLine="616" startOffset="20" endLine="624" endOffset="25"/></Target><Target id="@+id/text_eq_5k" view="TextView"><Expressions/><location startLine="626" startOffset="20" endLine="634" endOffset="25"/></Target><Target id="@+id/seekbar_eq_15k" view="SeekBar"><Expressions/><location startLine="651" startOffset="20" endLine="659" endOffset="25"/></Target><Target id="@+id/text_eq_15k" view="TextView"><Expressions/><location startLine="661" startOffset="20" endLine="669" endOffset="25"/></Target><Target id="@+id/start_ota_btn" view="Button"><Expressions/><location startLine="674" startOffset="12" endLine="685" endOffset="17"/></Target><Target id="@+id/linear_multiPoint_bg" view="LinearLayout"><Expressions/><location startLine="686" startOffset="12" endLine="709" endOffset="26"/></Target><Target id="@+id/switchButton_multiPoint" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="700" startOffset="16" endLine="707" endOffset="42"/></Target><Target id="@+id/get_bt_state" view="Button"><Expressions/><location startLine="714" startOffset="16" endLine="724" endOffset="21"/></Target><Target id="@+id/bt_state_text" view="TextView"><Expressions/><location startLine="726" startOffset="16" endLine="734" endOffset="21"/></Target><Target id="@+id/get_spp_state" view="Button"><Expressions/><location startLine="741" startOffset="16" endLine="751" endOffset="21"/></Target><Target id="@+id/spp_state_text" view="TextView"><Expressions/><location startLine="753" startOffset="16" endLine="761" endOffset="21"/></Target><Target id="@+id/check_mic_state" view="Button"><Expressions/><location startLine="768" startOffset="16" endLine="778" endOffset="21"/></Target><Target id="@+id/mic_state_text" view="TextView"><Expressions/><location startLine="780" startOffset="16" endLine="788" endOffset="21"/></Target><Target id="@+id/check_left_speaker" view="Button"><Expressions/><location startLine="796" startOffset="16" endLine="807" endOffset="21"/></Target><Target id="@+id/check_right_speaker" view="Button"><Expressions/><location startLine="808" startOffset="16" endLine="819" endOffset="21"/></Target><Target id="@+id/line_wear_detect" view="View"><Expressions/><location startLine="823" startOffset="12" endLine="829" endOffset="53"/></Target><Target id="@+id/wear_bg1" view="LinearLayout"><Expressions/><location startLine="831" startOffset="12" endLine="927" endOffset="26"/></Target><Target id="@+id/switchButton_in_ear_detection_all" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="846" startOffset="16" endLine="852" endOffset="50"/></Target><Target id="@+id/text_tws_connect_state" view="TextView"><Expressions/><location startLine="869" startOffset="24" endLine="875" endOffset="52"/></Target><Target id="@+id/text_master_ear" view="TextView"><Expressions/><location startLine="892" startOffset="24" endLine="898" endOffset="52"/></Target><Target id="@+id/text_slave_ear" view="TextView"><Expressions/><location startLine="915" startOffset="24" endLine="921" endOffset="52"/></Target><Target id="@+id/wear_bg2" view="LinearLayout"><Expressions/><location startLine="929" startOffset="12" endLine="994" endOffset="26"/></Target><Target id="@+id/switchButton_in_ear_detection_left" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="969" startOffset="24" endLine="975" endOffset="58"/></Target><Target id="@+id/switchButton_in_ear_detection_right" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="984" startOffset="24" endLine="990" endOffset="58"/></Target><Target id="@+id/wear_bg3" view="LinearLayout"><Expressions/><location startLine="996" startOffset="12" endLine="1019" endOffset="26"/></Target><Target id="@+id/text_in_ear_detection_left" view="TextView"><Expressions/><location startLine="1002" startOffset="16" endLine="1009" endOffset="51"/></Target><Target id="@+id/text_in_ear_detection_right" view="TextView"><Expressions/><location startLine="1011" startOffset="16" endLine="1018" endOffset="51"/></Target><Target id="@+id/wear_bg4" view="LinearLayout"><Expressions/><location startLine="1021" startOffset="12" endLine="1042" endOffset="26"/></Target><Target id="@+id/switchButton_wd_prompt" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="1027" startOffset="16" endLine="1033" endOffset="50"/></Target><Target id="@+id/button_setting_bg" view="LinearLayout"><Expressions/><location startLine="1044" startOffset="12" endLine="1601" endOffset="26"/></Target><Target id="@+id/linear_disable_swipe" view="LinearLayout"><Expressions/><location startLine="1056" startOffset="16" endLine="1097" endOffset="30"/></Target><Target id="@+id/switchButton_disable_swipe_left" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="1073" startOffset="20" endLine="1079" endOffset="54"/></Target><Target id="@+id/switchButton_disable_swipe_right" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="1089" startOffset="20" endLine="1095" endOffset="54"/></Target><Target id="@+id/earbuds_click_left" view="Button"><Expressions/><location startLine="1105" startOffset="20" endLine="1117" endOffset="28"/></Target><Target id="@+id/earbuds_click_right" view="Button"><Expressions/><location startLine="1119" startOffset="20" endLine="1131" endOffset="28"/></Target><Target id="@+id/button_state_1" view="TextView"><Expressions/><location startLine="1141" startOffset="20" endLine="1150" endOffset="25"/></Target><Target id="@+id/button_state_2" view="TextView"><Expressions/><location startLine="1152" startOffset="20" endLine="1161" endOffset="25"/></Target><Target id="@+id/earbuds_double_click_left" view="Button"><Expressions/><location startLine="1170" startOffset="20" endLine="1180" endOffset="56"/></Target><Target id="@+id/earbuds_double_click_right" view="Button"><Expressions/><location startLine="1182" startOffset="20" endLine="1194" endOffset="28"/></Target><Target id="@+id/button_state_3" view="TextView"><Expressions/><location startLine="1202" startOffset="20" endLine="1211" endOffset="25"/></Target><Target id="@+id/button_state_4" view="TextView"><Expressions/><location startLine="1213" startOffset="20" endLine="1222" endOffset="25"/></Target><Target id="@+id/earbuds_triple_click_left" view="Button"><Expressions/><location startLine="1230" startOffset="20" endLine="1242" endOffset="28"/></Target><Target id="@+id/earbuds_triple_click_right" view="Button"><Expressions/><location startLine="1244" startOffset="20" endLine="1256" endOffset="28"/></Target><Target id="@+id/button_state_5" view="TextView"><Expressions/><location startLine="1264" startOffset="20" endLine="1273" endOffset="25"/></Target><Target id="@+id/button_state_6" view="TextView"><Expressions/><location startLine="1275" startOffset="20" endLine="1284" endOffset="25"/></Target><Target id="@+id/earbuds_long_press_left" view="Button"><Expressions/><location startLine="1292" startOffset="20" endLine="1304" endOffset="28"/></Target><Target id="@+id/earbuds_long_press_right" view="Button"><Expressions/><location startLine="1306" startOffset="20" endLine="1318" endOffset="28"/></Target><Target id="@+id/button_state_7" view="TextView"><Expressions/><location startLine="1326" startOffset="20" endLine="1335" endOffset="25"/></Target><Target id="@+id/button_state_8" view="TextView"><Expressions/><location startLine="1337" startOffset="20" endLine="1346" endOffset="25"/></Target><Target id="@+id/earbuds_click_func_0" view="RadioGroup"><Expressions/><location startLine="1349" startOffset="16" endLine="1422" endOffset="28"/></Target><Target id="@+id/earbuds_click_func_last_music" view="RadioButton"><Expressions/><location startLine="1366" startOffset="20" endLine="1371" endOffset="25"/></Target><Target id="@+id/earbuds_click_func_next_music" view="RadioButton"><Expressions/><location startLine="1384" startOffset="20" endLine="1388" endOffset="25"/></Target><Target id="@+id/earbuds_click_func_ambient_music" view="RadioButton"><Expressions/><location startLine="1401" startOffset="20" endLine="1405" endOffset="25"/></Target><Target id="@+id/earbuds_click_func_call_back" view="RadioButton"><Expressions/><location startLine="1417" startOffset="20" endLine="1421" endOffset="25"/></Target><Target id="@+id/earbuds_click_func_1" view="RadioGroup"><Expressions/><location startLine="1424" startOffset="16" endLine="1494" endOffset="28"/></Target><Target id="@+id/earbuds_click_func_volume_add" view="RadioButton"><Expressions/><location startLine="1439" startOffset="20" endLine="1443" endOffset="25"/></Target><Target id="@+id/earbuds_click_func_volume_lose" view="RadioButton"><Expressions/><location startLine="1455" startOffset="20" endLine="1459" endOffset="25"/></Target><Target id="@+id/earbuds_click_func_play_music" view="RadioButton"><Expressions/><location startLine="1472" startOffset="20" endLine="1476" endOffset="25"/></Target><Target id="@+id/earbuds_click_func_stop_music" view="RadioButton"><Expressions/><location startLine="1489" startOffset="20" endLine="1493" endOffset="25"/></Target><Target id="@+id/earbuds_click_func_2" view="RadioGroup"><Expressions/><location startLine="1496" startOffset="16" endLine="1531" endOffset="28"/></Target><Target id="@+id/earbuds_click_func_assistant" view="RadioButton"><Expressions/><location startLine="1512" startOffset="20" endLine="1516" endOffset="25"/></Target><Target id="@+id/earbuds_click_func_play_pause_music" view="RadioButton"><Expressions/><location startLine="1526" startOffset="20" endLine="1530" endOffset="25"/></Target><Target id="@+id/earbuds_click_func_3" view="RadioGroup"><Expressions/><location startLine="1533" startOffset="16" endLine="1599" endOffset="28"/></Target><Target id="@+id/earbuds_click_func_game_mode" view="RadioButton"><Expressions/><location startLine="1549" startOffset="20" endLine="1553" endOffset="25"/></Target><Target id="@+id/earbuds_click_func_algo" view="RadioButton"><Expressions/><location startLine="1563" startOffset="20" endLine="1567" endOffset="25"/></Target><Target id="@+id/earbuds_click_func_speakthru" view="RadioButton"><Expressions/><location startLine="1577" startOffset="20" endLine="1581" endOffset="25"/></Target><Target id="@+id/tv_click_func_disable" view="TextView"><Expressions/><location startLine="1582" startOffset="20" endLine="1591" endOffset="25"/></Target><Target id="@+id/earbuds_click_func_disable" view="RadioButton"><Expressions/><location startLine="1593" startOffset="20" endLine="1598" endOffset="25"/></Target><Target id="@+id/factory_reset_cmd_set" view="Button"><Expressions/><location startLine="1603" startOffset="12" endLine="1614" endOffset="20"/></Target><Target id="@+id/linear_function_btn" view="LinearLayout"><Expressions/><location startLine="1616" startOffset="12" endLine="1694" endOffset="26"/></Target><Target id="@+id/play" view="Button"><Expressions/><location startLine="1628" startOffset="20" endLine="1640" endOffset="28"/></Target><Target id="@+id/pause" view="Button"><Expressions/><location startLine="1642" startOffset="20" endLine="1654" endOffset="28"/></Target><Target id="@+id/next" view="Button"><Expressions/><location startLine="1664" startOffset="20" endLine="1676" endOffset="28"/></Target><Target id="@+id/prev" view="Button"><Expressions/><location startLine="1678" startOffset="20" endLine="1690" endOffset="28"/></Target><Target id="@+id/button_get_left_battery" view="Button"><Expressions/><location startLine="1701" startOffset="16" endLine="1712" endOffset="24"/></Target><Target id="@+id/text_left_battery" view="TextView"><Expressions/><location startLine="1714" startOffset="16" endLine="1724" endOffset="26"/></Target><Target id="@+id/button_get_right_battery" view="Button"><Expressions/><location startLine="1733" startOffset="16" endLine="1744" endOffset="24"/></Target><Target id="@+id/text_right_battery" view="TextView"><Expressions/><location startLine="1746" startOffset="16" endLine="1756" endOffset="26"/></Target><Target id="@+id/button_get_box_battery" view="Button"><Expressions/><location startLine="1765" startOffset="16" endLine="1776" endOffset="24"/></Target><Target id="@+id/text_box_battery" view="TextView"><Expressions/><location startLine="1778" startOffset="16" endLine="1788" endOffset="26"/></Target><Target id="@+id/linear_get_codec_type" view="LinearLayout"><Expressions/><location startLine="1792" startOffset="12" endLine="1821" endOffset="26"/></Target><Target id="@+id/button_get_codec_type" view="Button"><Expressions/><location startLine="1798" startOffset="16" endLine="1809" endOffset="24"/></Target><Target id="@+id/text_codec_type" view="TextView"><Expressions/><location startLine="1811" startOffset="16" endLine="1820" endOffset="21"/></Target><Target id="@+id/eq_bgview" view="LinearLayout"><Expressions/><location startLine="1823" startOffset="12" endLine="2148" endOffset="26"/></Target><Target id="@+id/eq_test" view="Button"><Expressions/><location startLine="1833" startOffset="20" endLine="1844" endOffset="28"/></Target><Target id="@+id/eq_text" view="EditText"><Expressions/><location startLine="1846" startOffset="20" endLine="1858" endOffset="30"/></Target><Target id="@+id/eq_basetype" view="RadioGroup"><Expressions/><location startLine="1862" startOffset="16" endLine="1957" endOffset="28"/></Target><Target id="@+id/eq_basetype_pop" view="RadioButton"><Expressions/><location startLine="1883" startOffset="20" endLine="1887" endOffset="25"/></Target><Target id="@+id/eq_basetype_rock" view="RadioButton"><Expressions/><location startLine="1900" startOffset="20" endLine="1904" endOffset="25"/></Target><Target id="@+id/eq_basetype_jazz" view="RadioButton"><Expressions/><location startLine="1917" startOffset="20" endLine="1921" endOffset="25"/></Target><Target id="@+id/eq_basetype_classic" view="RadioButton"><Expressions/><location startLine="1934" startOffset="20" endLine="1938" endOffset="25"/></Target><Target id="@+id/eq_basetype_country" view="RadioButton"><Expressions/><location startLine="1951" startOffset="20" endLine="1955" endOffset="25"/></Target><Target id="@+id/eq_basetype2" view="RadioGroup"><Expressions/><location startLine="1959" startOffset="16" endLine="2090" endOffset="28"/></Target><Target id="@+id/eq_basetype2_bass_boost" view="RadioButton"><Expressions/><location startLine="1981" startOffset="20" endLine="1985" endOffset="25"/></Target><Target id="@+id/eq_basetype2_classic" view="RadioButton"><Expressions/><location startLine="1998" startOffset="20" endLine="2002" endOffset="25"/></Target><Target id="@+id/eq_basetype2_hip_hop" view="RadioButton"><Expressions/><location startLine="2015" startOffset="20" endLine="2019" endOffset="25"/></Target><Target id="@+id/eq_basetype2_jazz" view="RadioButton"><Expressions/><location startLine="2032" startOffset="20" endLine="2036" endOffset="25"/></Target><Target id="@+id/eq_basetype2_podcast" view="RadioButton"><Expressions/><location startLine="2049" startOffset="20" endLine="2053" endOffset="25"/></Target><Target id="@+id/eq_basetype2_rock" view="RadioButton"><Expressions/><location startLine="2066" startOffset="20" endLine="2070" endOffset="25"/></Target><Target id="@+id/eq_basetype2_pop" view="RadioButton"><Expressions/><location startLine="2083" startOffset="20" endLine="2087" endOffset="25"/></Target><Target id="@+id/eq_switch_type" view="RadioGroup"><Expressions/><location startLine="2107" startOffset="16" endLine="2146" endOffset="28"/></Target><Target id="@+id/eq_switch_open" view="RadioButton"><Expressions/><location startLine="2123" startOffset="20" endLine="2128" endOffset="25"/></Target><Target id="@+id/eq_switch_off" view="RadioButton"><Expressions/><location startLine="2140" startOffset="20" endLine="2144" endOffset="25"/></Target><Target id="@+id/linear_anc_state" view="LinearLayout"><Expressions/><location startLine="2150" startOffset="8" endLine="2268" endOffset="22"/></Target><Target id="@+id/regulate_anc_type" view="RadioGroup"><Expressions/><location startLine="2172" startOffset="12" endLine="2265" endOffset="24"/></Target><Target id="@+id/regulate_anc_anc" view="RadioButton"><Expressions/><location startLine="2188" startOffset="16" endLine="2193" endOffset="21"/></Target><Target id="@+id/regulate_anc_ambient" view="RadioButton"><Expressions/><location startLine="2205" startOffset="16" endLine="2209" endOffset="21"/></Target><Target id="@+id/text_anc_speakthru" view="TextView"><Expressions/><location startLine="2211" startOffset="16" endLine="2221" endOffset="21"/></Target><Target id="@+id/regulate_anc_speakthru" view="RadioButton"><Expressions/><location startLine="2223" startOffset="16" endLine="2228" endOffset="21"/></Target><Target id="@+id/text_anc_default" view="TextView"><Expressions/><location startLine="2230" startOffset="16" endLine="2239" endOffset="21"/></Target><Target id="@+id/regulate_anc_default" view="RadioButton"><Expressions/><location startLine="2241" startOffset="16" endLine="2245" endOffset="21"/></Target><Target id="@+id/text_anc_off" view="TextView"><Expressions/><location startLine="2247" startOffset="16" endLine="2257" endOffset="21"/></Target><Target id="@+id/regulate_anc_off" view="RadioButton"><Expressions/><location startLine="2259" startOffset="16" endLine="2264" endOffset="21"/></Target><Target id="@+id/linear_anc_new_state" view="LinearLayout"><Expressions/><location startLine="2270" startOffset="12" endLine="2349" endOffset="26"/></Target><Target id="@+id/anc_new_type" view="RadioGroup"><Expressions/><location startLine="2276" startOffset="16" endLine="2347" endOffset="28"/></Target><Target id="@+id/anc_new_type_adapt" view="RadioButton"><Expressions/><location startLine="2291" startOffset="20" endLine="2296" endOffset="25"/></Target><Target id="@+id/anc_new_type_low" view="RadioButton"><Expressions/><location startLine="2306" startOffset="20" endLine="2311" endOffset="25"/></Target><Target id="@+id/anc_new_type_mid" view="RadioButton"><Expressions/><location startLine="2323" startOffset="20" endLine="2328" endOffset="25"/></Target><Target id="@+id/anc_new_type_high" view="RadioButton"><Expressions/><location startLine="2340" startOffset="20" endLine="2345" endOffset="25"/></Target><Target id="@+id/linear_dolby_state" view="LinearLayout"><Expressions/><location startLine="2351" startOffset="12" endLine="2455" endOffset="26"/></Target><Target id="@+id/dolby_switch_title" view="TextView"><Expressions/><location startLine="2365" startOffset="16" endLine="2372" endOffset="44"/></Target><Target id="@+id/dolby_turn_off" view="Button"><Expressions/><location startLine="2414" startOffset="16" endLine="2424" endOffset="21"/></Target><Target id="@+id/dolby_type_natual" view="Button"><Expressions/><location startLine="2431" startOffset="20" endLine="2441" endOffset="25"/></Target><Target id="@+id/dolby_type_movie" view="Button"><Expressions/><location startLine="2443" startOffset="20" endLine="2453" endOffset="25"/></Target><Target id="@+id/linear_bes_spatial_state" view="LinearLayout"><Expressions/><location startLine="2457" startOffset="12" endLine="2518" endOffset="26"/></Target><Target id="@+id/bes_spatial_switch_type" view="RadioGroup"><Expressions/><location startLine="2478" startOffset="16" endLine="2517" endOffset="28"/></Target><Target id="@+id/bes_spatial_switch_open" view="RadioButton"><Expressions/><location startLine="2494" startOffset="20" endLine="2499" endOffset="25"/></Target><Target id="@+id/bes_spatial_switch_off" view="RadioButton"><Expressions/><location startLine="2511" startOffset="20" endLine="2515" endOffset="25"/></Target><Target id="@+id/linear_mimi_state" view="LinearLayout"><Expressions/><location startLine="2520" startOffset="12" endLine="2651" endOffset="26"/></Target><Target id="@+id/mimi_switch_type" view="RadioGroup"><Expressions/><location startLine="2542" startOffset="16" endLine="2581" endOffset="28"/></Target><Target id="@+id/mimi_switch_open" view="RadioButton"><Expressions/><location startLine="2558" startOffset="20" endLine="2563" endOffset="25"/></Target><Target id="@+id/mimi_switch_off" view="RadioButton"><Expressions/><location startLine="2575" startOffset="20" endLine="2579" endOffset="25"/></Target><Target id="@+id/text_mimi_switch_preset" view="TextView"><Expressions/><location startLine="2583" startOffset="16" endLine="2590" endOffset="44"/></Target><Target id="@+id/seekbar_mimi_switch_preset" view="SeekBar"><Expressions/><location startLine="2591" startOffset="16" endLine="2601" endOffset="21"/></Target><Target id="@+id/text_mimi_switch_intensity" view="TextView"><Expressions/><location startLine="2603" startOffset="16" endLine="2610" endOffset="44"/></Target><Target id="@+id/seekbar_mimi_switch_intensity" view="SeekBar"><Expressions/><location startLine="2612" startOffset="16" endLine="2622" endOffset="21"/></Target><Target id="@+id/get_tech_level" view="Button"><Expressions/><location startLine="2628" startOffset="20" endLine="2638" endOffset="25"/></Target><Target id="@+id/tech_level_text" view="TextView"><Expressions/><location startLine="2640" startOffset="20" endLine="2648" endOffset="25"/></Target><Target id="@+id/linear_head_tracking_pro" view="LinearLayout"><Expressions/><location startLine="2653" startOffset="12" endLine="2683" endOffset="26"/></Target><Target id="@+id/head_tracking_off_pro" view="Button"><Expressions/><location startLine="2659" startOffset="16" endLine="2669" endOffset="21"/></Target><Target id="@+id/head_tracking_on_pro" view="Button"><Expressions/><location startLine="2671" startOffset="16" endLine="2681" endOffset="21"/></Target><Target id="@+id/linear_ceva_state" view="LinearLayout"><Expressions/><location startLine="2685" startOffset="12" endLine="2897" endOffset="26"/></Target><Target id="@+id/ceva_switch_type" view="RadioGroup"><Expressions/><location startLine="2707" startOffset="16" endLine="2746" endOffset="28"/></Target><Target id="@+id/ceva_switch_open" view="RadioButton"><Expressions/><location startLine="2723" startOffset="20" endLine="2728" endOffset="25"/></Target><Target id="@+id/ceva_switch_off" view="RadioButton"><Expressions/><location startLine="2740" startOffset="20" endLine="2744" endOffset="25"/></Target><Target id="@+id/head_tracking_off" view="Button"><Expressions/><location startLine="2752" startOffset="20" endLine="2762" endOffset="25"/></Target><Target id="@+id/head_tracking_on" view="Button"><Expressions/><location startLine="2764" startOffset="20" endLine="2774" endOffset="25"/></Target><Target id="@+id/ceva_recentre" view="Button"><Expressions/><location startLine="2782" startOffset="20" endLine="2792" endOffset="25"/></Target><Target id="@+id/ceva_fast_recentre" view="Button"><Expressions/><location startLine="2794" startOffset="20" endLine="2804" endOffset="25"/></Target><Target id="@+id/ceva_slow_recentre" view="Button"><Expressions/><location startLine="2811" startOffset="20" endLine="2821" endOffset="25"/></Target><Target id="@+id/btn_get_head_tracking" view="Button"><Expressions/><location startLine="2828" startOffset="20" endLine="2838" endOffset="25"/></Target><Target id="@+id/tv_get_head_tracking_result" view="TextView"><Expressions/><location startLine="2840" startOffset="20" endLine="2845" endOffset="60"/></Target><Target id="@+id/btn_get_auto_center_mode" view="Button"><Expressions/><location startLine="2852" startOffset="20" endLine="2862" endOffset="25"/></Target><Target id="@+id/tv_get_auto_center_mode_result" view="TextView"><Expressions/><location startLine="2864" startOffset="20" endLine="2869" endOffset="63"/></Target><Target id="@+id/btn_get_imu_orientation" view="Button"><Expressions/><location startLine="2876" startOffset="20" endLine="2886" endOffset="25"/></Target><Target id="@+id/tv_get_imu_orientation_result" view="TextView"><Expressions/><location startLine="2888" startOffset="20" endLine="2894" endOffset="62"/></Target><Target id="@+id/linear_led_onoff_state" view="LinearLayout"><Expressions/><location startLine="2899" startOffset="12" endLine="3051" endOffset="26"/></Target><Target id="@+id/led_switch_type" view="RadioGroup"><Expressions/><location startLine="2920" startOffset="16" endLine="2975" endOffset="28"/></Target><Target id="@+id/led_switch_open" view="RadioButton"><Expressions/><location startLine="2936" startOffset="20" endLine="2941" endOffset="25"/></Target><Target id="@+id/led_switch_off" view="RadioButton"><Expressions/><location startLine="2953" startOffset="20" endLine="2957" endOffset="25"/></Target><Target id="@+id/led_switch_flash" view="RadioButton"><Expressions/><location startLine="2969" startOffset="20" endLine="2973" endOffset="25"/></Target><Target id="@+id/led_switch_type_2" view="RadioGroup"><Expressions/><location startLine="2977" startOffset="16" endLine="3049" endOffset="28"/></Target><Target id="@+id/led_switch_2_open_0" view="RadioButton"><Expressions/><location startLine="2994" startOffset="20" endLine="2999" endOffset="25"/></Target><Target id="@+id/led_switch_2_open_1" view="RadioButton"><Expressions/><location startLine="3011" startOffset="20" endLine="3015" endOffset="25"/></Target><Target id="@+id/led_switch_2_off" view="RadioButton"><Expressions/><location startLine="3027" startOffset="20" endLine="3031" endOffset="25"/></Target><Target id="@+id/led_switch_2_flash" view="RadioButton"><Expressions/><location startLine="3043" startOffset="20" endLine="3047" endOffset="25"/></Target><Target id="@+id/linear_spatial_audio_state" view="LinearLayout"><Expressions/><location startLine="3054" startOffset="12" endLine="3174" endOffset="26"/></Target><Target id="@+id/spatial_audio_switch_type" view="RadioGroup"><Expressions/><location startLine="3075" startOffset="16" endLine="3115" endOffset="28"/></Target><Target id="@+id/spatial_audio_switch_open" view="RadioButton"><Expressions/><location startLine="3092" startOffset="20" endLine="3097" endOffset="25"/></Target><Target id="@+id/spatial_audio_switch_off" view="RadioButton"><Expressions/><location startLine="3109" startOffset="20" endLine="3113" endOffset="25"/></Target><Target id="@+id/spatial_audio_switch_type_2" view="RadioGroup"><Expressions/><location startLine="3117" startOffset="16" endLine="3173" endOffset="28"/></Target><Target id="@+id/spatial_audio_switch_spa" view="RadioButton"><Expressions/><location startLine="3134" startOffset="20" endLine="3139" endOffset="25"/></Target><Target id="@+id/spatial_audio_switch_360" view="RadioButton"><Expressions/><location startLine="3151" startOffset="20" endLine="3155" endOffset="25"/></Target><Target id="@+id/spatial_audio_switch_off_2" view="RadioButton"><Expressions/><location startLine="3167" startOffset="20" endLine="3171" endOffset="25"/></Target><Target id="@+id/linear_share_mode_onoff" view="LinearLayout"><Expressions/><location startLine="3176" startOffset="12" endLine="3237" endOffset="26"/></Target><Target id="@+id/share_mode_switch_type" view="RadioGroup"><Expressions/><location startLine="3196" startOffset="16" endLine="3235" endOffset="28"/></Target><Target id="@+id/share_mode_switch_open" view="RadioButton"><Expressions/><location startLine="3212" startOffset="20" endLine="3217" endOffset="25"/></Target><Target id="@+id/share_mode_switch_off" view="RadioButton"><Expressions/><location startLine="3229" startOffset="20" endLine="3233" endOffset="25"/></Target><Target id="@+id/linear_game_mode" view="LinearLayout"><Expressions/><location startLine="3239" startOffset="12" endLine="3301" endOffset="26"/></Target><Target id="@+id/game_mode_type" view="RadioGroup"><Expressions/><location startLine="3261" startOffset="16" endLine="3299" endOffset="28"/></Target><Target id="@+id/game_mode_open" view="RadioButton"><Expressions/><location startLine="3277" startOffset="20" endLine="3282" endOffset="25"/></Target><Target id="@+id/game_mode_close" view="RadioButton"><Expressions/><location startLine="3294" startOffset="20" endLine="3298" endOffset="25"/></Target><Target id="@+id/linear_volume" view="LinearLayout"><Expressions/><location startLine="3303" startOffset="12" endLine="3346" endOffset="26"/></Target><Target id="@+id/btn_volume" view="Button"><Expressions/><location startLine="3311" startOffset="16" endLine="3321" endOffset="21"/></Target><Target id="@+id/tv_volume" view="TextView"><Expressions/><location startLine="3323" startOffset="16" endLine="3328" endOffset="45"/></Target><Target id="@+id/seekbar_volume" view="SeekBar"><Expressions/><location startLine="3329" startOffset="16" endLine="3337" endOffset="21"/></Target><Target id="@+id/tv_volume_cur" view="TextView"><Expressions/><location startLine="3338" startOffset="16" endLine="3344" endOffset="45"/></Target><Target id="@+id/linear_touch_onoff" view="LinearLayout"><Expressions/><location startLine="3348" startOffset="12" endLine="3373" endOffset="26"/></Target><Target id="@+id/switchButton_touch_onoff" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="3365" startOffset="16" endLine="3371" endOffset="50"/></Target><Target id="@+id/disconnect" view="Button"><Expressions/><location startLine="3382" startOffset="12" endLine="3394" endOffset="20"/></Target></Targets></Layout>