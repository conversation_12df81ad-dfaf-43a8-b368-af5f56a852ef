<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_functionchoose" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_functionchoose.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_functionchoose_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="106" endOffset="51"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="7" startOffset="4" endLine="104" endOffset="18"/></Target><Target id="@+id/tool" tag="binding_1" include="toolbar"><Expressions/><location startLine="12" startOffset="8" endLine="15" endOffset="13"/></Target><Target id="@+id/Func_ota" view="Button"><Expressions/><location startLine="23" startOffset="8" endLine="34" endOffset="33"/></Target><Target id="@+id/Func_tools" view="Button"><Expressions/><location startLine="43" startOffset="8" endLine="55" endOffset="41"/></Target><Target id="@+id/watch_tools" view="Button"><Expressions/><location startLine="64" startOffset="8" endLine="76" endOffset="41"/></Target><Target id="@+id/go_to_google_assistant" view="Button"><Expressions/><location startLine="83" startOffset="8" endLine="95" endOffset="50"/></Target></Targets></Layout>