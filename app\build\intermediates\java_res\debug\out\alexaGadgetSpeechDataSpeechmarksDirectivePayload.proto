// Copyright 2018 Amazon.com, Inc. or its affiliates. All Rights Reserved.
//
// Licensed under the Amazon Software License (the "License"). You may not use this file except in
// compliance with the License. A copy of the License is located at
//
//    http://aws.amazon.com/asl/
//
// or in the "license" file accompanying this file. This file is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, express or implied. See the License for the specific
// language governing permissions and limitations under the License.

syntax = "proto3";

package alexaGadgetSpeechData;

option java_package = "com.amazon.proto.avs.v20160207.alexaGadgetSpeechData";

option java_outer_classname = "SpeechmarksDirectivePayload";

message SpeechmarksDirectivePayloadProto {
	repeated SpeechmarksData speechmarksData = 2;
	message SpeechmarksData {
		int32 startOffsetInMilliSeconds = 3;
		string type = 2;
		string value = 1;
	}
	int32 playerOffsetInMilliSeconds = 1;
}
