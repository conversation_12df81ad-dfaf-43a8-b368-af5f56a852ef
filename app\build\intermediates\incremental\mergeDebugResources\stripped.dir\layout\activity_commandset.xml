<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6">

    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <include
        android:id="@+id/loginfo"
        layout="@layout/logview"
        android:visibility="gone"/>

    <ScrollView
        android:layout_marginTop="15dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="50dp"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <EditText
                android:id="@+id/command_set_receive_data"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:textSize="14sp"
                android:textColor="@color/black"
                android:background="@null"
                />

            <TextView
                android:id="@+id/command_set_current_version"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:layout_marginLeft="20dp"
                android:textSize="14sp"
                android:textColor="@color/black"
                android:text="CRC:  \nVersion:  \nBuild Data:  "
                />
            <TextView
                android:id="@+id/command_set_current_product_model"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:textSize="18sp"
                android:textColor="@color/black"
                android:text="Current Product Model"
                />

            <LinearLayout
                android:id="@+id/seekbar_eq_2_bg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:orientation="vertical"
                android:visibility="gone">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="32Hz"
                        />

                    <SeekBar
                        android:id="@+id/seekbar_eq_2_32"
                        android:layout_width="match_parent"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="50dp"
                        android:layout_height="20dp"
                        android:max="24"
                        android:progress="12"
                        />

                    <TextView
                        android:id="@+id/text_eq_2_32"
                        android:layout_width="40dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="-50dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="0.0"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="64Hz"
                        />

                    <SeekBar
                        android:id="@+id/seekbar_eq_2_64"
                        android:layout_width="match_parent"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="50dp"
                        android:layout_height="20dp"
                        android:max="24"
                        android:progress="12"
                        />

                    <TextView
                        android:id="@+id/text_eq_2_64"
                        android:layout_width="40dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="-50dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="0.0"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="125Hz"
                        />

                    <SeekBar
                        android:id="@+id/seekbar_eq_2_125"
                        android:layout_width="match_parent"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="50dp"
                        android:layout_height="20dp"
                        android:max="24"
                        android:progress="12"
                        />

                    <TextView
                        android:id="@+id/text_eq_2_125"
                        android:layout_width="40dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="-50dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="0.0"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="250Hz"
                        />

                    <SeekBar
                        android:id="@+id/seekbar_eq_2_250"
                        android:layout_width="match_parent"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="50dp"
                        android:layout_height="20dp"
                        android:max="24"
                        android:progress="12"
                        />

                    <TextView
                        android:id="@+id/text_eq_2_250"
                        android:layout_width="40dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="-50dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="0.0"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="500Hz"
                        />

                    <SeekBar
                        android:id="@+id/seekbar_eq_2_500"
                        android:layout_width="match_parent"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="50dp"
                        android:layout_height="20dp"
                        android:max="24"
                        android:progress="12"
                        />

                    <TextView
                        android:id="@+id/text_eq_2_500"
                        android:layout_width="40dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="-50dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="0.0"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="1kHz"
                        />

                    <SeekBar
                        android:id="@+id/seekbar_eq_2_1k"
                        android:layout_width="match_parent"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="50dp"
                        android:layout_height="20dp"
                        android:max="24"
                        android:progress="12"
                        />

                    <TextView
                        android:id="@+id/text_eq_2_1k"
                        android:layout_width="40dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="-50dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="0.0"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="2kHz"
                        />

                    <SeekBar
                        android:id="@+id/seekbar_eq_2_2k"
                        android:layout_width="match_parent"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="50dp"
                        android:layout_height="20dp"
                        android:max="24"
                        android:progress="12"
                        />

                    <TextView
                        android:id="@+id/text_eq_2_2k"
                        android:layout_width="40dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="-50dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="0.0"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="4kHz"
                        />

                    <SeekBar
                        android:id="@+id/seekbar_eq_2_4k"
                        android:layout_width="match_parent"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="50dp"
                        android:layout_height="20dp"
                        android:max="24"
                        android:progress="12"
                        />

                    <TextView
                        android:id="@+id/text_eq_2_4k"
                        android:layout_width="40dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="-50dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="0.0"
                        />
                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="8kHz"
                        />

                    <SeekBar
                        android:id="@+id/seekbar_eq_2_8k"
                        android:layout_width="match_parent"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="50dp"
                        android:layout_height="20dp"
                        android:max="24"
                        android:progress="12"
                        />

                    <TextView
                        android:id="@+id/text_eq_2_8k"
                        android:layout_width="40dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="-50dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="0.0"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="16kHz"
                        />

                    <SeekBar
                        android:id="@+id/seekbar_eq_2_16k"
                        android:layout_width="match_parent"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="50dp"
                        android:layout_height="20dp"
                        android:max="24"
                        android:progress="12"
                        />

                    <TextView
                        android:id="@+id/text_eq_2_16k"
                        android:layout_width="40dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="-50dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="0.0"
                        />
                </LinearLayout>


            </LinearLayout>


            <LinearLayout
                android:id="@+id/seekbar_eq_bg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="60Hz"
                        />

                    <SeekBar
                        android:id="@+id/seekbar_eq_60"
                        android:layout_width="match_parent"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="50dp"
                        android:layout_height="20dp"
                        android:max="10"
                        android:progress="5"
                        />

                    <TextView
                        android:id="@+id/text_eq_60"
                        android:layout_width="30dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="-50dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="0.0"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="120Hz"
                        />

                    <SeekBar
                        android:id="@+id/seekbar_eq_120"
                        android:layout_width="match_parent"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="50dp"
                        android:layout_height="20dp"
                        android:max="10"
                        android:progress="5"
                        />

                    <TextView
                        android:id="@+id/text_eq_120"
                        android:layout_width="30dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="-50dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="0.0"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="250Hz"
                        />

                    <SeekBar
                        android:id="@+id/seekbar_eq_250"
                        android:layout_width="match_parent"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="50dp"
                        android:layout_height="20dp"
                        android:max="10"
                        android:progress="5"
                        />

                    <TextView
                        android:id="@+id/text_eq_250"
                        android:layout_width="30dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="-50dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="0.0"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="750Hz"
                        />

                    <SeekBar
                        android:id="@+id/seekbar_eq_750"
                        android:layout_width="match_parent"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="50dp"
                        android:layout_height="20dp"
                        android:max="10"
                        android:progress="5"
                        />

                    <TextView
                        android:id="@+id/text_eq_750"
                        android:layout_width="30dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="-50dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="0.0"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="2kHz"
                        />

                    <SeekBar
                        android:id="@+id/seekbar_eq_2k"
                        android:layout_width="match_parent"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="50dp"
                        android:layout_height="20dp"
                        android:max="10"
                        android:progress="5"
                        />

                    <TextView
                        android:id="@+id/text_eq_2k"
                        android:layout_width="30dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="-50dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="0.0"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="5kHz"
                        />

                    <SeekBar
                        android:id="@+id/seekbar_eq_5k"
                        android:layout_width="match_parent"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="50dp"
                        android:layout_height="20dp"
                        android:max="10"
                        android:progress="5"
                        />

                    <TextView
                        android:id="@+id/text_eq_5k"
                        android:layout_width="30dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="-50dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="0.0"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="15kHz"
                        />

                    <SeekBar
                        android:id="@+id/seekbar_eq_15k"
                        android:layout_width="match_parent"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="50dp"
                        android:layout_height="20dp"
                        android:max="10"
                        android:progress="5"
                        />

                    <TextView
                        android:id="@+id/text_eq_15k"
                        android:layout_width="30dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="-50dp"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:text="0.0"
                        />
                </LinearLayout>

            </LinearLayout>

            <Button
                android:id="@+id/start_ota_btn"
                android:layout_marginTop="40dp"
                android:layout_width="214dp"
                android:layout_height="68dp"
                android:layout_gravity="center"
                android:background="@drawable/ota_click"
                android:text="Start OTA"
                android:textColor="@color/white"
                android:textAllCaps="false"
                android:textSize="14sp"
                />
            <LinearLayout
                android:id="@+id/linear_multiPoint_bg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="Allow MultiPoint"
                    android:layout_marginLeft="20dp"
                    android:textColor="@color/ff666666"
                    android:textSize="16sp"/>

                <com.suke.widget.SwitchButton
                    android:id="@+id/switchButton_multiPoint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15dp"
                    android:layout_gravity="center_vertical"
                    app:sb_show_indicator="false"
                    app:sb_checked="true"/>

            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/get_bt_state"
                    android:layout_width="214dp"
                    android:layout_height="68dp"
                    android:layout_gravity="left"
                    android:background="@drawable/ota_click"
                    android:text="Get Bt State"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    />

                <TextView
                    android:id="@+id/bt_state_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:textAlignment="center"
                    android:hint="bt state"
                    android:textColor="@color/black"
                    />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/get_spp_state"
                    android:layout_width="214dp"
                    android:layout_height="68dp"
                    android:layout_gravity="left"
                    android:background="@drawable/ota_click"
                    android:text="Get SPP State"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    />

                <TextView
                    android:id="@+id/spp_state_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:textAlignment="center"
                    android:hint="spp state"
                    android:textColor="@color/black"
                    />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/check_mic_state"
                    android:layout_width="214dp"
                    android:layout_height="68dp"
                    android:layout_gravity="left"
                    android:background="@drawable/ota_click"
                    android:text="Check MIC State"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    />

                <TextView
                    android:id="@+id/mic_state_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:textAlignment="center"
                    android:hint="mic state"
                    android:textColor="@color/black"
                    />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="1">
                <Button
                    android:id="@+id/check_left_speaker"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_height="68dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="Check Left Speaker"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    />
                <Button
                    android:id="@+id/check_right_speaker"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_height="68dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="Check Right Speaker"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    />

            </LinearLayout>

            <View
                android:id="@+id/line_wear_detect"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:background="@color/lineview"/>

            <LinearLayout
                android:id="@+id/wear_bg1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                >

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:textColor="@color/ff666666"
                    android:text="Wear Detect"
                    android:textSize="16sp"/>

                <com.suke.widget.SwitchButton
                    android:id="@+id/switchButton_in_ear_detection_all"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15dp"
                    android:layout_gravity="center_vertical"
                    app:sb_show_indicator="false"/>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/ff666666"
                            android:text="TWS state:"
                            android:textSize="13sp"/>

                        <TextView
                            android:id="@+id/text_tws_connect_state"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/fff8921f"
                            android:text="disconnect"
                            android:textSize="13sp"/>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:orientation="horizontal">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="5dp"
                            android:textColor="@color/ff666666"
                            android:text="Master:"
                            android:textSize="13sp"/>

                        <TextView
                            android:id="@+id/text_master_ear"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/fff8921f"
                            android:text="Left"
                            android:textSize="13sp"/>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:orientation="horizontal">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="5dp"
                            android:textColor="@color/ff666666"
                            android:text="Slave: "
                            android:textSize="13sp"/>

                        <TextView
                            android:id="@+id/text_slave_ear"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/fff8921f"
                            android:text="Right"
                            android:textSize="13sp"/>

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/wear_bg2"
                android:layout_marginTop="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="1">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.5"
                        android:text="Left Earphone"
                        android:textAlignment="center"/>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.5"
                        android:text="Right Earphone"
                        android:textAlignment="center"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_weight="1"
                    >
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        >
                        <com.suke.widget.SwitchButton
                            android:id="@+id/switchButton_in_ear_detection_left"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="15dp"
                            android:layout_gravity="center_vertical"
                            app:sb_show_indicator="false"/>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        >
                        <com.suke.widget.SwitchButton
                            android:id="@+id/switchButton_in_ear_detection_right"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="15dp"
                            android:layout_gravity="center_vertical"
                            app:sb_show_indicator="false"/>
                    </LinearLayout>
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/wear_bg3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="1">
                <TextView
                    android:id="@+id/text_in_ear_detection_left"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5"
                    android:text="In-ear testing is turned off"
                    android:layout_gravity="center"
                    android:textAlignment="center"/>

                <TextView
                    android:id="@+id/text_in_ear_detection_right"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5"
                    android:text="In-ear testing is turned off"
                    android:layout_gravity="center"
                    android:textAlignment="center"/>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/wear_bg4"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:orientation="horizontal">
                <com.suke.widget.SwitchButton
                    android:id="@+id/switchButton_wd_prompt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15dp"
                    android:layout_gravity="center_vertical"
                    app:sb_show_indicator="false"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:text="WD prompt"
                    android:layout_gravity="center"
                    android:textAlignment="center"/>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/button_setting_bg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="5dp"
                    android:background="@color/lineview"/>

                <LinearLayout
                    android:id="@+id/linear_disable_swipe"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginLeft="20dp"
                    android:gravity="center|left"
                    android:orientation="horizontal"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Disable Swipe Left"
                        android:textColor="@color/ff666666"
                        android:textSize="12sp"/>

                    <com.suke.widget.SwitchButton
                        android:id="@+id/switchButton_disable_swipe_left"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:layout_gravity="center_vertical"
                        app:sb_show_indicator="false"/>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:text="Disable Swipe Right"
                        android:textColor="@color/ff666666"
                        android:textSize="12sp"/>

                    <com.suke.widget.SwitchButton
                        android:id="@+id/switchButton_disable_swipe_right"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:layout_gravity="center_vertical"
                        app:sb_show_indicator="false"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal"
                    android:weightSum="1">
                    <Button
                        android:id="@+id/earbuds_click_left"
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ota_click"
                        android:text="Click Left Earbud"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="13sp"
                        >
                    </Button>

                    <Button
                        android:id="@+id/earbuds_click_right"
                        android:layout_weight="0.5"
                        android:layout_width="0dp"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ota_click"
                        android:text="Click Right Earbud"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="13sp"
                        >
                    </Button>


                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="1">
                    <TextView
                        android:id="@+id/button_state_1"
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="wrap_content"
                        android:textAlignment="center"
                        android:textColor="@color/black"
                        android:textSize="13sp"
                        android:text="current functon"
                        />

                    <TextView
                        android:id="@+id/button_state_2"
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="wrap_content"
                        android:textAlignment="center"
                        android:textColor="@color/black"
                        android:textSize="13sp"
                        android:text="current functon"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="1">

                    <Button
                        android:id="@+id/earbuds_double_click_left"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:layout_weight="0.5"
                        android:background="@drawable/ota_click"
                        android:text="Double Click Left Earbud"
                        android:textAllCaps="false"
                        android:textColor="@color/white"
                        android:textSize="13sp"></Button>

                    <Button
                        android:id="@+id/earbuds_double_click_right"
                        android:layout_weight="0.5"
                        android:layout_width="0dp"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ota_click"
                        android:text="Double Click Right Earbud"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="13sp"
                        >
                    </Button>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="1">
                    <TextView
                        android:id="@+id/button_state_3"
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="wrap_content"
                        android:textAlignment="center"
                        android:textColor="@color/black"
                        android:textSize="13sp"
                        android:text="current functon"
                        />

                    <TextView
                        android:id="@+id/button_state_4"
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="wrap_content"
                        android:textAlignment="center"
                        android:textColor="@color/black"
                        android:textSize="13sp"
                        android:text="current functon"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="1">
                    <Button
                        android:id="@+id/earbuds_triple_click_left"
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ota_click"
                        android:text="Triple Click Left Earbud"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="13sp"
                        >
                    </Button>

                    <Button
                        android:id="@+id/earbuds_triple_click_right"
                        android:layout_weight="0.5"
                        android:layout_width="0dp"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ota_click"
                        android:text="Triple Click Right Earbud"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="13sp"
                        >
                    </Button>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="1">
                    <TextView
                        android:id="@+id/button_state_5"
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="wrap_content"
                        android:textAlignment="center"
                        android:textColor="@color/black"
                        android:textSize="13sp"
                        android:text="current functon"
                        />

                    <TextView
                        android:id="@+id/button_state_6"
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="wrap_content"
                        android:textAlignment="center"
                        android:textColor="@color/black"
                        android:textSize="13sp"
                        android:text="current functon"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="1">
                    <Button
                        android:id="@+id/earbuds_long_press_left"
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ota_click"
                        android:text="Long Press Left Earbud"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="13sp"
                        >
                    </Button>

                    <Button
                        android:id="@+id/earbuds_long_press_right"
                        android:layout_weight="0.5"
                        android:layout_width="0dp"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ota_click"
                        android:text="Long Press Right Earbud"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="13sp"
                        >
                    </Button>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="1">
                    <TextView
                        android:id="@+id/button_state_7"
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="wrap_content"
                        android:textAlignment="center"
                        android:textColor="@color/black"
                        android:textSize="13sp"
                        android:text="current functon"
                        />

                    <TextView
                        android:id="@+id/button_state_8"
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="wrap_content"
                        android:textAlignment="center"
                        android:textColor="@color/black"
                        android:textSize="13sp"
                        android:text="current functon"
                        />
                </LinearLayout>

                <RadioGroup
                    android:id="@+id/earbuds_click_func_0"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="5dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="Last Music"
                        android:textColor="@color/title_color"
                        android:textSize="14sp" />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_last_music"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="14sp"
                        android:text="Next Music"
                        />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_next_music"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="12sp"
                        android:text="ANC"
                        />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_ambient_music"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:fontFamily="@font/sanshan_normal"
                        android:gravity="right"
                        android:text="Call back"
                        android:textColor="@color/title_color"
                        android:textSize="14sp" />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_call_back"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />
                </RadioGroup>

                <RadioGroup
                    android:id="@+id/earbuds_click_func_1"
                    android:layout_marginLeft="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="Volume+"
                        android:textColor="@color/title_color"
                        android:textSize="14sp" />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_volume_add"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:fontFamily="@font/sanshan_normal"
                        android:gravity="right"
                        android:text="Volume-"
                        android:textColor="@color/title_color"
                        android:textSize="14sp" />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_volume_lose"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="12sp"
                        android:text="Play music"
                        />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_play_music"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="14sp"
                        android:text="Stop music"
                        />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_stop_music"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />
                </RadioGroup>

                <RadioGroup
                    android:id="@+id/earbuds_click_func_2"
                    android:layout_marginLeft="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="left"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="14sp"
                        android:text="Wake up voice assistant"
                        />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_assistant"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="14sp"
                        android:text="play pause music"
                        />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_play_pause_music"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />
                </RadioGroup>

                <RadioGroup
                    android:id="@+id/earbuds_click_func_3"
                    android:layout_marginLeft="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="left"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="14sp"
                        android:text="Game mode"
                        />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_game_mode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="14sp"
                        android:text="ALGO"
                        />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_algo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="14sp"
                        android:text="Speakthru"
                        />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_speakthru"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />
                    <TextView
                        android:id="@+id/tv_click_func_disable"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="14sp"
                        android:text="Disable"
                        android:visibility="gone"
                        />

                    <RadioButton
                        android:id="@+id/earbuds_click_func_disable"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        />
                </RadioGroup>

            </LinearLayout>

            <Button
                android:id="@+id/factory_reset_cmd_set"
                android:layout_width="214dp"
                android:layout_height="68dp"
                android:layout_gravity="center"
                android:background="@drawable/ota_click"
                android:text="Factory Reset"
                android:textColor="@color/white"
                android:textAllCaps="false"
                android:textSize="14sp"
                >
            </Button>

            <LinearLayout
                android:id="@+id/linear_function_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="1">

                    <Button
                        android:id="@+id/play"
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ota_click"
                        android:text="PLAY"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="13sp"
                        >
                    </Button>

                    <Button
                        android:id="@+id/pause"
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ota_click"
                        android:text="PAUSE"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="13sp"
                        >
                    </Button>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="1">

                    <Button
                        android:id="@+id/next"
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ota_click"
                        android:text="NEXT"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="13sp"
                        >
                    </Button>

                    <Button
                        android:id="@+id/prev"
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ota_click"
                        android:text="PREV"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="13sp"
                        >
                    </Button>

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/button_get_left_battery"
                    android:layout_width="214dp"
                    android:layout_height="68dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="GET Left Battery"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    >
                </Button>

                <TextView
                    android:id="@+id/text_left_battery"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:textAlignment="center"
                    android:hint="current left battery"
                    android:textColor="@color/black"
                    >

                </TextView>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/button_get_right_battery"
                    android:layout_width="214dp"
                    android:layout_height="68dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="GET Right Battery"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    >
                </Button>

                <TextView
                    android:id="@+id/text_right_battery"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:textAlignment="center"
                    android:hint="current right battery"
                    android:textColor="@color/black"
                    >

                </TextView>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/button_get_box_battery"
                    android:layout_width="214dp"
                    android:layout_height="68dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="GET Box Battery"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    >
                </Button>

                <TextView
                    android:id="@+id/text_box_battery"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:textAlignment="center"
                    android:hint="current box battery"
                    android:textColor="@color/black"
                    >

                </TextView>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linear_get_codec_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/button_get_codec_type"
                    android:layout_width="180dp"
                    android:layout_height="68dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="GET Codec Type"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    >
                </Button>

                <TextView
                    android:id="@+id/text_codec_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:textAlignment="center"
                    android:textSize="13sp"
                    android:hint="support codec type\ncurrent codec type"
                    android:textColor="@color/black"
                    />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/eq_bgview"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <Button
                        android:id="@+id/eq_test"
                        android:layout_width="214dp"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ota_click"
                        android:text="Change EQ"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="14sp"
                        >
                    </Button>

                    <EditText
                        android:visibility="gone"
                        android:id="@+id/eq_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:textAlignment="center"
                        android:hint="eq data (Hex)"
                        android:textColor="@color/black"
                        android:textSize="14sp"
                        >

                    </EditText>

                </LinearLayout>

                <RadioGroup
                    android:layout_marginLeft="20dp"
                    android:id="@+id/eq_basetype"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="14sp"
                        android:text="Pop"
                        />

                    <RadioButton
                        android:id="@+id/eq_basetype_pop"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="12sp"
                        android:text="Rock"
                        />

                    <RadioButton
                        android:id="@+id/eq_basetype_rock"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="14sp"
                        android:text="Jazz"
                        />

                    <RadioButton
                        android:id="@+id/eq_basetype_jazz"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="8sp"
                        android:text="Classic"
                        />

                    <RadioButton
                        android:id="@+id/eq_basetype_classic"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="8sp"
                        android:text="Country"
                        />

                    <RadioButton
                        android:id="@+id/eq_basetype_country"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                </RadioGroup>

                <RadioGroup
                    android:layout_marginLeft="20dp"
                    android:id="@+id/eq_basetype2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="8sp"
                        android:text="Bass Boost"
                        />

                    <RadioButton
                        android:id="@+id/eq_basetype2_bass_boost"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="8sp"
                        android:text="Classic"
                        />

                    <RadioButton
                        android:id="@+id/eq_basetype2_classic"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="8sp"
                        android:text="Hip Hop"
                        />

                    <RadioButton
                        android:id="@+id/eq_basetype2_hip_hop"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="8sp"
                        android:text="Jazz"
                        />

                    <RadioButton
                        android:id="@+id/eq_basetype2_jazz"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="8sp"
                        android:text="PodCast"
                        />

                    <RadioButton
                        android:id="@+id/eq_basetype2_podcast"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="8sp"
                        android:text="Rock"
                        />

                    <RadioButton
                        android:id="@+id/eq_basetype2_rock"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="8sp"
                        android:text="Pop"
                        />

                    <RadioButton
                        android:id="@+id/eq_basetype2_pop"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />


                </RadioGroup>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:background="@color/lineview"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:textColor="@color/ff666666"
                    android:text="EQ State"
                    android:textSize="16sp"/>

                <RadioGroup
                    android:layout_marginLeft="20dp"
                    android:id="@+id/eq_switch_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="On"
                        android:textColor="@color/title_color"
                        android:textSize="16sp" />

                    <RadioButton
                        android:id="@+id/eq_switch_open"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="30dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="Off"
                        />

                    <RadioButton
                        android:id="@+id/eq_switch_off"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                </RadioGroup>

            </LinearLayout>

        <LinearLayout
            android:id="@+id/linear_anc_state"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:background="@color/lineview"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:textColor="@color/ff666666"
                android:text="ANC State"
                android:textSize="16sp"/>

            <RadioGroup
                android:layout_marginLeft="20dp"
                android:id="@+id/regulate_anc_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/sanshan_normal"
                    android:text="ANC"
                    android:textColor="@color/title_color"
                    android:textSize="13sp" />

                <RadioButton
                    android:id="@+id/regulate_anc_anc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:fontFamily="@font/sanshan_normal"
                    android:textColor="@color/title_color"
                    android:textSize="13sp"
                    android:text="Ambient"
                    />

                <RadioButton
                    android:id="@+id/regulate_anc_ambient"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />

                <TextView
                    android:id="@+id/text_anc_speakthru"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:fontFamily="@font/sanshan_normal"
                    android:textColor="@color/title_color"
                    android:textSize="13sp"
                    android:text="Speakthru"
                    android:visibility="gone"
                    />

                <RadioButton
                    android:id="@+id/regulate_anc_speakthru"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    />

                <TextView
                    android:id="@+id/text_anc_default"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:fontFamily="@font/sanshan_normal"
                    android:textColor="@color/title_color"
                    android:textSize="13sp"
                    android:text="Default"
                    />

                <RadioButton
                    android:id="@+id/regulate_anc_default"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />

                <TextView
                    android:id="@+id/text_anc_off"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:fontFamily="@font/sanshan_normal"
                    android:textColor="@color/title_color"
                    android:textSize="13sp"
                    android:text="OFF"
                    android:visibility="gone"
                    />

                <RadioButton
                    android:id="@+id/regulate_anc_off"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    />
            </RadioGroup>


        </LinearLayout>

            <LinearLayout
                android:id="@+id/linear_anc_new_state"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="gone">
                <RadioGroup
                    android:id="@+id/anc_new_type"
                    android:layout_marginLeft="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="Adapt"
                        android:textColor="@color/title_color"
                        android:textSize="13sp" />

                    <RadioButton
                        android:id="@+id/anc_new_type_adapt"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="Low"
                        android:textColor="@color/title_color"
                        android:textSize="13sp" />

                    <RadioButton
                        android:id="@+id/anc_new_type_low"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="13sp"
                        android:text="Mid"
                        />

                    <RadioButton
                        android:id="@+id/anc_new_type_mid"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="13sp"
                        android:text="High"
                        />

                    <RadioButton
                        android:id="@+id/anc_new_type_high"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        />

                </RadioGroup>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linear_dolby_state"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                >
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:background="@color/lineview"/>

                <TextView
                    android:id="@+id/dolby_switch_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:textColor="@color/ff666666"
                    android:text="Dolby State"
                    android:textSize="16sp"/>

<!--                <RadioGroup-->
<!--                    android:layout_marginLeft="20dp"-->
<!--                    android:id="@+id/dolby_switch_type"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginTop="5dp"-->
<!--                    android:orientation="horizontal">-->

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:fontFamily="@font/sanshan_normal"-->
<!--                        android:text="On"-->
<!--                        android:textColor="@color/title_color"-->
<!--                        android:textSize="16sp" />-->

<!--                    <RadioButton-->
<!--                        android:id="@+id/dolby_switch_open"-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:checked="true"-->
<!--                        />-->

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:layout_marginLeft="30dp"-->
<!--                        android:fontFamily="@font/sanshan_normal"-->
<!--                        android:textColor="@color/title_color"-->
<!--                        android:textSize="16sp"-->
<!--                        android:text="Off"-->
<!--                        />-->

<!--                    <RadioButton-->
<!--                        android:id="@+id/dolby_switch_off"-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        />-->

<!--                </RadioGroup>-->
                <Button
                    android:id="@+id/dolby_turn_off"
                    android:layout_width="200dp"
                    android:layout_height="68dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="Turn off"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <Button
                        android:id="@+id/dolby_type_natual"
                        android:layout_width="200dp"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ota_button_bg_press"
                        android:text="Natual"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="14sp"
                        />

                    <Button
                        android:id="@+id/dolby_type_movie"
                        android:layout_width="200dp"
                        android:layout_height="68dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ota_button_bg_press"
                        android:text="Movie"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="14sp"
                        />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/linear_bes_spatial_state"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:background="@color/lineview"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:textColor="@color/ff666666"
                    android:text="BES spatial State"
                    android:textSize="16sp"/>

                <RadioGroup
                    android:layout_marginLeft="20dp"
                    android:id="@+id/bes_spatial_switch_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="On"
                        android:textColor="@color/title_color"
                        android:textSize="16sp" />

                    <RadioButton
                        android:id="@+id/bes_spatial_switch_open"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="30dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="Off"
                        />

                    <RadioButton
                        android:id="@+id/bes_spatial_switch_off"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                </RadioGroup>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/linear_mimi_state"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                >
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:background="@color/lineview"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:textColor="@color/ff666666"
                    android:text="mimi State"
                    android:textSize="16sp"/>

                <RadioGroup
                    android:layout_marginLeft="20dp"
                    android:id="@+id/mimi_switch_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="On"
                        android:textColor="@color/title_color"
                        android:textSize="16sp" />

                    <RadioButton
                        android:id="@+id/mimi_switch_open"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="30dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="Off"
                        />

                    <RadioButton
                        android:id="@+id/mimi_switch_off"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                </RadioGroup>

                <TextView
                    android:id="@+id/text_mimi_switch_preset"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:textColor="@color/ff666666"
                    android:text="preset: 18"
                    android:textSize="16sp"/>
                <SeekBar
                    android:id="@+id/seekbar_mimi_switch_preset"
                    android:layout_width="match_parent"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:layout_height="20dp"
                    android:max="2"
                    android:progress="0"
                    />

                <TextView
                    android:id="@+id/text_mimi_switch_intensity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:textColor="@color/ff666666"
                    android:text="intensity: 0.0"
                    android:textSize="16sp"/>

                <SeekBar
                    android:id="@+id/seekbar_mimi_switch_intensity"
                    android:layout_width="match_parent"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:layout_height="20dp"
                    android:max="100"
                    android:progress="0"
                    />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <Button
                        android:id="@+id/get_tech_level"
                        android:layout_width="214dp"
                        android:layout_height="68dp"
                        android:layout_gravity="left"
                        android:background="@drawable/ota_click"
                        android:text="Get tech level"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="14sp"
                        />

                    <TextView
                        android:id="@+id/tech_level_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:textAlignment="center"
                        android:hint="0"
                        android:textColor="@color/black"
                        />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linear_head_tracking_pro"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="gone">
                <Button
                    android:id="@+id/head_tracking_off_pro"
                    android:layout_width="200dp"
                    android:layout_height="68dp"
                    android:layout_gravity="left"
                    android:background="@drawable/ota_click"
                    android:text="Head Tracking OFF(20)"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    />

                <Button
                    android:id="@+id/head_tracking_on_pro"
                    android:layout_width="200dp"
                    android:layout_height="68dp"
                    android:layout_gravity="right"
                    android:background="@drawable/ota_click"
                    android:text="Head Tracking ON(21)"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linear_ceva_state"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:background="@color/lineview"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:textColor="@color/ff666666"
                    android:text="ceva State"
                    android:textSize="16sp"/>

                <RadioGroup
                    android:layout_marginLeft="20dp"
                    android:id="@+id/ceva_switch_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="On"
                        android:textColor="@color/title_color"
                        android:textSize="16sp" />

                    <RadioButton
                        android:id="@+id/ceva_switch_open"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="30dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="Off"
                        />

                    <RadioButton
                        android:id="@+id/ceva_switch_off"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                </RadioGroup>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <Button
                        android:id="@+id/head_tracking_off"
                        android:layout_width="200dp"
                        android:layout_height="68dp"
                        android:layout_gravity="left"
                        android:background="@drawable/ota_click"
                        android:text="Head Tracking OFF(20)"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="14sp"
                        />

                    <Button
                        android:id="@+id/head_tracking_on"
                        android:layout_width="200dp"
                        android:layout_height="68dp"
                        android:layout_gravity="right"
                        android:background="@drawable/ota_click"
                        android:text="Head Tracking ON(21)"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="14sp"
                        />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <Button
                        android:id="@+id/ceva_recentre"
                        android:layout_width="200dp"
                        android:layout_height="68dp"
                        android:layout_gravity="left"
                        android:background="@drawable/ota_click"
                        android:text="Recentre(26)"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="14sp"
                        />

                    <Button
                        android:id="@+id/ceva_fast_recentre"
                        android:layout_width="200dp"
                        android:layout_height="68dp"
                        android:layout_gravity="right"
                        android:background="@drawable/ota_click"
                        android:text="Fast Recentre(27)"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="14sp"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <Button
                        android:id="@+id/ceva_slow_recentre"
                        android:layout_width="200dp"
                        android:layout_height="68dp"
                        android:layout_gravity="left"
                        android:background="@drawable/ota_click"
                        android:text="Slow Recentre(28)"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="14sp"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <Button
                        android:id="@+id/btn_get_head_tracking"
                        android:layout_width="200dp"
                        android:layout_height="68dp"
                        android:layout_gravity="left"
                        android:background="@drawable/ota_click"
                        android:text="Get Head Tracking"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="14sp"
                        />

                    <TextView
                        android:id="@+id/tv_get_head_tracking_result"
                        android:layout_width="200dp"
                        android:layout_height="68dp"
                        android:gravity="center"
                        android:hint="head tracking result"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <Button
                        android:id="@+id/btn_get_auto_center_mode"
                        android:layout_width="200dp"
                        android:layout_height="68dp"
                        android:layout_gravity="left"
                        android:background="@drawable/ota_click"
                        android:text="Get Auto-Center Mode"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="14sp"
                        />

                    <TextView
                        android:id="@+id/tv_get_auto_center_mode_result"
                        android:layout_width="200dp"
                        android:layout_height="68dp"
                        android:gravity="center"
                        android:hint="Auto-Center mode result"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <Button
                        android:id="@+id/btn_get_imu_orientation"
                        android:layout_width="150dp"
                        android:layout_height="68dp"
                        android:layout_gravity="left"
                        android:background="@drawable/ota_click"
                        android:text="Get IMU Orientation"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:textSize="12sp"
                        />

                    <TextView
                        android:id="@+id/tv_get_imu_orientation_result"
                        android:layout_width="match_parent"
                        android:layout_marginLeft="10dp"
                        android:layout_height="68dp"
                        android:gravity="center"
                        android:hint="IMU Orientation result"/>
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linear_led_onoff_state"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:background="@color/lineview"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:textColor="@color/ff666666"
                    android:text="LED State"
                    android:textSize="16sp"/>

                <RadioGroup
                    android:layout_marginLeft="20dp"
                    android:id="@+id/led_switch_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="ON"
                        android:textColor="@color/title_color"
                        android:textSize="16sp" />

                    <RadioButton
                        android:id="@+id/led_switch_open"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="30dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="OFF"
                        />

                    <RadioButton
                        android:id="@+id/led_switch_off"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="30dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="FLASH"
                        />

                    <RadioButton
                        android:id="@+id/led_switch_flash"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                </RadioGroup>

                <RadioGroup
                    android:layout_marginLeft="20dp"
                    android:id="@+id/led_switch_type_2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="Mode1"
                        android:textColor="@color/title_color"
                        android:textSize="16sp" />

                    <RadioButton
                        android:id="@+id/led_switch_2_open_0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="Mode2"
                        />

                    <RadioButton
                        android:id="@+id/led_switch_2_open_1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="OFF"
                        />

                    <RadioButton
                        android:id="@+id/led_switch_2_off"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="FLASH"
                        />

                    <RadioButton
                        android:id="@+id/led_switch_2_flash"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                </RadioGroup>

            </LinearLayout>


            <LinearLayout
                android:id="@+id/linear_spatial_audio_state"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:background="@color/lineview"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:textColor="@color/ff666666"
                    android:text="Spatial Audio"
                    android:textSize="16sp"/>

                <RadioGroup
                    android:layout_marginLeft="20dp"
                    android:id="@+id/spatial_audio_switch_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="On"
                        android:textColor="@color/title_color"
                        android:textSize="16sp" />

                    <RadioButton
                        android:id="@+id/spatial_audio_switch_open"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="30dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="Off"
                        />

                    <RadioButton
                        android:id="@+id/spatial_audio_switch_off"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                </RadioGroup>

                <RadioGroup
                    android:layout_marginLeft="20dp"
                    android:id="@+id/spatial_audio_switch_type_2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="spa"
                        android:textColor="@color/title_color"
                        android:textSize="16sp" />

                    <RadioButton
                        android:id="@+id/spatial_audio_switch_spa"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="30dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="360"
                        />

                    <RadioButton
                        android:id="@+id/spatial_audio_switch_360"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="30dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="Off"
                        />

                    <RadioButton
                        android:id="@+id/spatial_audio_switch_off_2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                </RadioGroup>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/linear_share_mode_onoff"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginBottom="5dp"
                    android:background="@color/lineview"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:textColor="@color/ff666666"
                    android:text="Broadcast"
                    android:textSize="16sp"/>

                <RadioGroup
                    android:layout_marginLeft="20dp"
                    android:id="@+id/share_mode_switch_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="On"
                        android:textColor="@color/title_color"
                        android:textSize="16sp" />

                    <RadioButton
                        android:id="@+id/share_mode_switch_open"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="30dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="16sp"
                        android:text="Off"
                        />

                    <RadioButton
                        android:id="@+id/share_mode_switch_off"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                </RadioGroup>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linear_game_mode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:background="@color/lineview"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:textColor="@color/ff666666"
                    android:text="Game Mode"
                    android:textSize="16sp"/>

                <RadioGroup
                    android:layout_marginLeft="20dp"
                    android:id="@+id/game_mode_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanshan_normal"
                        android:text="ON"
                        android:textColor="@color/title_color"
                        android:textSize="13sp" />

                    <RadioButton
                        android:id="@+id/game_mode_open"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color"
                        android:textSize="13sp"
                        android:text="OFF"
                        />

                    <RadioButton
                        android:id="@+id/game_mode_close"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />
                </RadioGroup>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linear_volume"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="gone"
                android:gravity="center">

                <Button
                    android:id="@+id/btn_volume"
                    android:layout_width="100dp"
                    android:layout_height="68dp"
                    android:layout_marginLeft="20dp"
                    android:background="@drawable/ota_click"
                    android:text="Volume"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="14sp"
                    />

                <TextView
                    android:id="@+id/tv_volume"
                    android:layout_width="30dp"
                    android:layout_height="68dp"
                    android:text="A:00\nB:00"
                    android:gravity="center"/>
                <SeekBar
                    android:id="@+id/seekbar_volume"
                    android:layout_width="match_parent"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="30dp"
                    android:layout_height="20dp"
                    android:max="16"
                    android:progress="0"
                    />
                <TextView
                    android:id="@+id/tv_volume_cur"
                    android:layout_width="30dp"
                    android:layout_height="68dp"
                    android:layout_marginLeft="-30dp"
                    android:text="00"
                    android:gravity="center"/>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linear_touch_onoff"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:orientation="horizontal"
                android:gravity="center|left"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:textColor="@color/ff666666"
                    android:text="Touch"
                    android:textSize="16sp"/>

                <com.suke.widget.SwitchButton
                    android:id="@+id/switchButton_touch_onoff"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15dp"
                    android:layout_gravity="center_vertical"
                    app:sb_show_indicator="false"/>

            </LinearLayout>

            <!--////////////////////////////////////////-->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:background="@color/lineview"/>
            <Button
                android:id="@+id/disconnect"
                android:layout_width="214dp"
                android:layout_height="68dp"
                android:layout_marginTop="20dp"
                android:layout_gravity="center"
                android:background="@drawable/ota_click"
                android:text="Disconnect"
                android:textColor="@color/white"
                android:textAllCaps="false"
                android:textSize="14sp"
                >
            </Button>
        </LinearLayout>

    </ScrollView>

</LinearLayout>



