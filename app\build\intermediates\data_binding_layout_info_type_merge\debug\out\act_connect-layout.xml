<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="act_connect" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\act_connect.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/act_connect_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="155" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/act_connect_0" include="toolbar"><Expressions/><location startLine="10" startOffset="4" endLine="13" endOffset="9"/></Target><Target id="@+id/loginfo" tag="layout/act_connect_0" include="logview"><Expressions/><location startLine="15" startOffset="4" endLine="18" endOffset="34"/></Target><Target id="@+id/device_name" view="TextView"><Expressions/><location startLine="38" startOffset="4" endLine="46" endOffset="33"/></Target><Target id="@+id/device_address" view="TextView"><Expressions/><location startLine="48" startOffset="4" endLine="57" endOffset="9"/></Target><Target id="@+id/loading" view="ImageView"><Expressions/><location startLine="59" startOffset="4" endLine="67" endOffset="9"/></Target><Target id="@+id/customer_uuid_tips" view="TextView"><Expressions/><location startLine="76" startOffset="4" endLine="85" endOffset="9"/></Target><Target id="@+id/customer_uuid" view="EditText"><Expressions/><location startLine="87" startOffset="4" endLine="99" endOffset="39"/></Target><Target id="@+id/edit_cutomercmd" view="Button"><Expressions/><location startLine="101" startOffset="4" endLine="113" endOffset="12"/></Target><Target id="@+id/pick_device_ble" view="Button"><Expressions/><location startLine="115" startOffset="4" endLine="126" endOffset="12"/></Target><Target id="@+id/pick_device" view="Button"><Expressions/><location startLine="128" startOffset="4" endLine="139" endOffset="12"/></Target><Target id="@+id/connect_device" view="Button"><Expressions/><location startLine="141" startOffset="4" endLine="152" endOffset="12"/></Target></Targets></Layout>