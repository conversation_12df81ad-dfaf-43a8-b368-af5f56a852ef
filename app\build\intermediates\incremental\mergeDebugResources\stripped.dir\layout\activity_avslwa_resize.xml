<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6">
    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <include
        android:id="@+id/loginfo"
        layout="@layout/logview"
        android:visibility="gone"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="20dp"
        android:orientation="vertical"
        android:padding="20dp"
        >
       <LinearLayout
           android:layout_width="match_parent"
           android:layout_height="wrap_content"
           android:orientation="vertical">

           <TextView
               android:id="@+id/testtest"
               android:layout_width="wrap_content"
               android:layout_height="20dp"
               android:layout_marginTop="20dp"
               android:layout_marginLeft="20dp"
               android:text="Lighting"
               android:textSize="16sp" />
           <LinearLayout
               android:layout_marginTop="5dp"
               android:layout_width="match_parent"
               android:layout_height="50dp"
               android:orientation="horizontal"
               android:background="@drawable/shape_background"
               android:gravity="center_vertical"
               >

               <ImageView
                   android:layout_marginLeft="20dp"
                   android:background="@drawable/light_low"
                   android:layout_width="20dp"
                   android:layout_height="20dp"/>

               <SeekBar
                   android:id="@+id/seekbar_lighting"
                   android:layout_marginLeft="10dp"
                   android:layout_marginRight="50dp"
                   android:layout_width="match_parent"
                   android:layout_height="20dp"
                   />
               <ImageView
                   android:layout_marginLeft="-40dp"
                   android:background="@drawable/light_high"
                   android:layout_width="20dp"
                   android:layout_height="20dp"/>
           </LinearLayout>

           <TextView
               android:layout_width="wrap_content"
               android:layout_height="20dp"
               android:layout_marginTop="30dp"
               android:layout_marginLeft="20dp"
               android:text="Speaker Volume"
               android:textSize="16sp" />
           <LinearLayout
               android:layout_marginTop="5dp"
               android:layout_width="match_parent"
               android:layout_height="50dp"
               android:orientation="horizontal"
               android:background="@drawable/shape_background"
               android:gravity="center_vertical"
               >

               <ImageView
                   android:layout_marginLeft="20dp"
                   android:background="@drawable/sound_low"
                   android:layout_width="20dp"
                   android:layout_height="20dp"/>

               <SeekBar
                   android:id="@+id/seekbar_speaker_volume"
                   android:layout_marginLeft="10dp"
                   android:layout_marginRight="50dp"
                   android:layout_width="match_parent"
                   android:layout_height="20dp"
                   />
               <ImageView
                   android:layout_marginLeft="-40dp"
                   android:background="@drawable/sound_high"
                   android:layout_width="20dp"
                   android:layout_height="20dp"/>
           </LinearLayout>

           <TextView
               android:layout_width="wrap_content"
               android:layout_height="20dp"
               android:layout_marginTop="30dp"
               android:layout_marginLeft="20dp"
               android:text="Alert Volume"
               android:textSize="16sp" />
           <LinearLayout
               android:layout_marginTop="5dp"
               android:layout_width="match_parent"
               android:layout_height="50dp"
               android:orientation="horizontal"
               android:background="@drawable/shape_background"
               android:gravity="center_vertical"
               >

               <ImageView
                   android:layout_marginLeft="20dp"
                   android:background="@drawable/sound_low"
                   android:layout_width="20dp"
                   android:layout_height="20dp"/>

               <SeekBar
                   android:id="@+id/seekbar_alert_volume"
                   android:layout_marginLeft="10dp"
                   android:layout_marginRight="50dp"
                   android:layout_width="match_parent"
                   android:layout_height="20dp"
                   />
               <ImageView
                   android:layout_marginLeft="-40dp"
                   android:background="@drawable/sound_high"
                   android:layout_width="20dp"
                   android:layout_height="20dp"/>
           </LinearLayout>

           <TextView
               android:layout_width="wrap_content"
               android:layout_height="20dp"
               android:layout_marginTop="30dp"
               android:layout_marginLeft="20dp"
               android:text="UX LED"
               android:textSize="16sp" />
           <LinearLayout
               android:layout_marginTop="5dp"
               android:layout_width="match_parent"
               android:layout_height="50dp"
               android:orientation="horizontal"
               android:background="@drawable/shape_background"
               android:gravity="center_vertical"
               >

               <ImageView
                   android:layout_marginLeft="20dp"
                   android:background="@drawable/light_low"
                   android:layout_width="20dp"
                   android:layout_height="20dp"/>

               <SeekBar
                   android:id="@+id/seekbar_ux_led"
                   android:layout_marginLeft="10dp"
                   android:layout_marginRight="50dp"
                   android:layout_width="match_parent"
                   android:layout_height="20dp"
                   />
               <ImageView
                   android:layout_marginLeft="-40dp"
                   android:background="@drawable/light_high"
                   android:layout_width="20dp"
                   android:layout_height="20dp"/>
           </LinearLayout>

           <TextView
               android:layout_width="wrap_content"
               android:layout_height="20dp"
               android:layout_marginTop="30dp"
               android:layout_marginLeft="20dp"
               android:text="Request Sound"
               android:textSize="16sp" />
        <LinearLayout
            android:layout_marginTop="5dp"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:orientation="vertical"
            android:background="@drawable/shape_background"
            >
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="21dp"
                    android:layout_marginLeft="20dp"
                    android:text="Start Lisening"
                    android:textSize="18sp"
                    android:textColor="@color/black"/>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginRight="50dp"/>

                <com.suke.widget.SwitchButton
                    android:id="@+id/switchButton_start_lisening"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="-75dp"
                    app:sb_show_indicator="false"/>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:background="@color/lineview"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="21dp"
                    android:layout_marginLeft="20dp"
                    android:text="End Lisening"
                    android:textSize="18sp"
                    android:textColor="@color/black"/>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginRight="50dp"/>

                <com.suke.widget.SwitchButton
                    android:id="@+id/switchButton_end_lisening"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="-75dp"
                    app:sb_show_indicator="false"/>

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_marginTop="30dp"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="@drawable/shape_background"
            >
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="21dp"
                android:layout_marginLeft="20dp"
                android:text="Do Not Disturb"
                android:textSize="18sp"
                android:textColor="@color/black"/>

            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginRight="50dp"/>

            <com.suke.widget.SwitchButton
                android:id="@+id/switchButton_do_not_disturb"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="-75dp"
                app:sb_show_indicator="false"/>

        </LinearLayout>

        <LinearLayout
               android:layout_marginTop="30dp"
               android:layout_width="match_parent"
               android:layout_height="50dp"
               android:orientation="horizontal"
               android:gravity="center_vertical"
               android:background="@drawable/shape_background"
               >
               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="21dp"
                   android:layout_marginLeft="20dp"
                   android:text="Language Select"
                   android:textSize="18sp"
                   android:textColor="@color/black"/>

               <View
                   android:layout_width="match_parent"
                   android:layout_height="match_parent"
                   android:layout_marginRight="200dp"/>

               <Button
                   android:id="@+id/language_select_btn"
                   android:layout_marginLeft="-200dp"
                   android:layout_width="180dp"
                   android:layout_height="match_parent"
                   android:textSize="16sp"
                   android:textColor="@color/title_color_light"
                   android:text="English"
                   android:gravity="center_vertical"
                   android:textAlignment="viewEnd"
                   tools:ignore="RtlCompat"
                   android:drawableRight="@drawable/home_icon_arrow"
                   android:background="@drawable/rectangle_longbtn"
                   android:textAllCaps="false"
                   />

        </LinearLayout>







       </LinearLayout>
    </ScrollView>
</LinearLayout>