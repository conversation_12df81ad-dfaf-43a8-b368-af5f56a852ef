<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_watchavs" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_watchavs.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_watchavs_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="72" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/activity_watchavs_0" include="toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="12" endOffset="9"/></Target><Target id="@+id/loginfo" tag="layout/activity_watchavs_0" include="logview"><Expressions/><location startLine="14" startOffset="4" endLine="17" endOffset="34"/></Target><Target id="@+id/watch_avs_login_text" view="TextView"><Expressions/><location startLine="19" startOffset="4" endLine="27" endOffset="49"/></Target><Target id="@+id/watch_avs_login_btn" view="Button"><Expressions/><location startLine="29" startOffset="4" endLine="38" endOffset="9"/></Target><Target id="@+id/watch_avs_device_state_text" view="TextView"><Expressions/><location startLine="40" startOffset="4" endLine="47" endOffset="24"/></Target><Target id="@+id/watch_avs_connect_device_btn" view="Button"><Expressions/><location startLine="49" startOffset="4" endLine="58" endOffset="9"/></Target><Target id="@+id/watch_avs_record_btn" view="Button"><Expressions/><location startLine="59" startOffset="4" endLine="69" endOffset="9"/></Target></Targets></Layout>