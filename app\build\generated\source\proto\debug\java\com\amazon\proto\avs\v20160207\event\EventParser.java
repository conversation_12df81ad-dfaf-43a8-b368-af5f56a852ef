// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: eventParser.proto

package com.amazon.proto.avs.v20160207.event;

public final class EventParser {
  private EventParser() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface EventParserProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:event.EventParserProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.event.EventParserProto.Event event = 1;</code>
     * @return Whether the event field is set.
     */
    boolean hasEvent();
    /**
     * <code>.event.EventParserProto.Event event = 1;</code>
     * @return The event.
     */
    com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event getEvent();
    /**
     * <code>.event.EventParserProto.Event event = 1;</code>
     */
    com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.EventOrBuilder getEventOrBuilder();
  }
  /**
   * Protobuf type {@code event.EventParserProto}
   */
  public static final class EventParserProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:event.EventParserProto)
      EventParserProtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use EventParserProto.newBuilder() to construct.
    private EventParserProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private EventParserProto() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new EventParserProto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.proto.avs.v20160207.event.EventParser.internal_static_event_EventParserProto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.proto.avs.v20160207.event.EventParser.internal_static_event_EventParserProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.class, com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Builder.class);
    }

    public interface EventOrBuilder extends
        // @@protoc_insertion_point(interface_extends:event.EventParserProto.Event)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>bytes payload = 2;</code>
       * @return The payload.
       */
      com.google.protobuf.ByteString getPayload();

      /**
       * <code>.header.EventHeaderProto header = 1;</code>
       * @return Whether the header field is set.
       */
      boolean hasHeader();
      /**
       * <code>.header.EventHeaderProto header = 1;</code>
       * @return The header.
       */
      com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProto getHeader();
      /**
       * <code>.header.EventHeaderProto header = 1;</code>
       */
      com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProtoOrBuilder getHeaderOrBuilder();
    }
    /**
     * Protobuf type {@code event.EventParserProto.Event}
     */
    public static final class Event extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:event.EventParserProto.Event)
        EventOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use Event.newBuilder() to construct.
      private Event(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private Event() {
        payload_ = com.google.protobuf.ByteString.EMPTY;
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new Event();
      }

      @java.lang.Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.event.EventParser.internal_static_event_EventParserProto_Event_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.event.EventParser.internal_static_event_EventParserProto_Event_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event.class, com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event.Builder.class);
      }

      public static final int PAYLOAD_FIELD_NUMBER = 2;
      private com.google.protobuf.ByteString payload_;
      /**
       * <code>bytes payload = 2;</code>
       * @return The payload.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getPayload() {
        return payload_;
      }

      public static final int HEADER_FIELD_NUMBER = 1;
      private com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProto header_;
      /**
       * <code>.header.EventHeaderProto header = 1;</code>
       * @return Whether the header field is set.
       */
      @java.lang.Override
      public boolean hasHeader() {
        return header_ != null;
      }
      /**
       * <code>.header.EventHeaderProto header = 1;</code>
       * @return The header.
       */
      @java.lang.Override
      public com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProto getHeader() {
        return header_ == null ? com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProto.getDefaultInstance() : header_;
      }
      /**
       * <code>.header.EventHeaderProto header = 1;</code>
       */
      @java.lang.Override
      public com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProtoOrBuilder getHeaderOrBuilder() {
        return getHeader();
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (header_ != null) {
          output.writeMessage(1, getHeader());
        }
        if (!payload_.isEmpty()) {
          output.writeBytes(2, payload_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (header_ != null) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, getHeader());
        }
        if (!payload_.isEmpty()) {
          size += com.google.protobuf.CodedOutputStream
            .computeBytesSize(2, payload_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event)) {
          return super.equals(obj);
        }
        com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event other = (com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event) obj;

        if (!getPayload()
            .equals(other.getPayload())) return false;
        if (hasHeader() != other.hasHeader()) return false;
        if (hasHeader()) {
          if (!getHeader()
              .equals(other.getHeader())) return false;
        }
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + PAYLOAD_FIELD_NUMBER;
        hash = (53 * hash) + getPayload().hashCode();
        if (hasHeader()) {
          hash = (37 * hash) + HEADER_FIELD_NUMBER;
          hash = (53 * hash) + getHeader().hashCode();
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code event.EventParserProto.Event}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:event.EventParserProto.Event)
          com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.EventOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.amazon.proto.avs.v20160207.event.EventParser.internal_static_event_EventParserProto_Event_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.amazon.proto.avs.v20160207.event.EventParser.internal_static_event_EventParserProto_Event_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event.class, com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event.Builder.class);
        }

        // Construct using com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          payload_ = com.google.protobuf.ByteString.EMPTY;

          if (headerBuilder_ == null) {
            header_ = null;
          } else {
            header_ = null;
            headerBuilder_ = null;
          }
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.amazon.proto.avs.v20160207.event.EventParser.internal_static_event_EventParserProto_Event_descriptor;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event getDefaultInstanceForType() {
          return com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event.getDefaultInstance();
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event build() {
          com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event buildPartial() {
          com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event result = new com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event(this);
          result.payload_ = payload_;
          if (headerBuilder_ == null) {
            result.header_ = header_;
          } else {
            result.header_ = headerBuilder_.build();
          }
          onBuilt();
          return result;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event) {
            return mergeFrom((com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event other) {
          if (other == com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event.getDefaultInstance()) return this;
          if (other.getPayload() != com.google.protobuf.ByteString.EMPTY) {
            setPayload(other.getPayload());
          }
          if (other.hasHeader()) {
            mergeHeader(other.getHeader());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 10: {
                  input.readMessage(
                      getHeaderFieldBuilder().getBuilder(),
                      extensionRegistry);

                  break;
                } // case 10
                case 18: {
                  payload_ = input.readBytes();

                  break;
                } // case 18
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }

        private com.google.protobuf.ByteString payload_ = com.google.protobuf.ByteString.EMPTY;
        /**
         * <code>bytes payload = 2;</code>
         * @return The payload.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString getPayload() {
          return payload_;
        }
        /**
         * <code>bytes payload = 2;</code>
         * @param value The payload to set.
         * @return This builder for chaining.
         */
        public Builder setPayload(com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  
          payload_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>bytes payload = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearPayload() {
          
          payload_ = getDefaultInstance().getPayload();
          onChanged();
          return this;
        }

        private com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProto header_;
        private com.google.protobuf.SingleFieldBuilderV3<
            com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProto, com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProto.Builder, com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProtoOrBuilder> headerBuilder_;
        /**
         * <code>.header.EventHeaderProto header = 1;</code>
         * @return Whether the header field is set.
         */
        public boolean hasHeader() {
          return headerBuilder_ != null || header_ != null;
        }
        /**
         * <code>.header.EventHeaderProto header = 1;</code>
         * @return The header.
         */
        public com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProto getHeader() {
          if (headerBuilder_ == null) {
            return header_ == null ? com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProto.getDefaultInstance() : header_;
          } else {
            return headerBuilder_.getMessage();
          }
        }
        /**
         * <code>.header.EventHeaderProto header = 1;</code>
         */
        public Builder setHeader(com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProto value) {
          if (headerBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            header_ = value;
            onChanged();
          } else {
            headerBuilder_.setMessage(value);
          }

          return this;
        }
        /**
         * <code>.header.EventHeaderProto header = 1;</code>
         */
        public Builder setHeader(
            com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProto.Builder builderForValue) {
          if (headerBuilder_ == null) {
            header_ = builderForValue.build();
            onChanged();
          } else {
            headerBuilder_.setMessage(builderForValue.build());
          }

          return this;
        }
        /**
         * <code>.header.EventHeaderProto header = 1;</code>
         */
        public Builder mergeHeader(com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProto value) {
          if (headerBuilder_ == null) {
            if (header_ != null) {
              header_ =
                com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProto.newBuilder(header_).mergeFrom(value).buildPartial();
            } else {
              header_ = value;
            }
            onChanged();
          } else {
            headerBuilder_.mergeFrom(value);
          }

          return this;
        }
        /**
         * <code>.header.EventHeaderProto header = 1;</code>
         */
        public Builder clearHeader() {
          if (headerBuilder_ == null) {
            header_ = null;
            onChanged();
          } else {
            header_ = null;
            headerBuilder_ = null;
          }

          return this;
        }
        /**
         * <code>.header.EventHeaderProto header = 1;</code>
         */
        public com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProto.Builder getHeaderBuilder() {
          
          onChanged();
          return getHeaderFieldBuilder().getBuilder();
        }
        /**
         * <code>.header.EventHeaderProto header = 1;</code>
         */
        public com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProtoOrBuilder getHeaderOrBuilder() {
          if (headerBuilder_ != null) {
            return headerBuilder_.getMessageOrBuilder();
          } else {
            return header_ == null ?
                com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProto.getDefaultInstance() : header_;
          }
        }
        /**
         * <code>.header.EventHeaderProto header = 1;</code>
         */
        private com.google.protobuf.SingleFieldBuilderV3<
            com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProto, com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProto.Builder, com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProtoOrBuilder> 
            getHeaderFieldBuilder() {
          if (headerBuilder_ == null) {
            headerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
                com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProto, com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProto.Builder, com.amazon.proto.avs.v20160207.header.EventHeader.EventHeaderProtoOrBuilder>(
                    getHeader(),
                    getParentForChildren(),
                    isClean());
            header_ = null;
          }
          return headerBuilder_;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:event.EventParserProto.Event)
      }

      // @@protoc_insertion_point(class_scope:event.EventParserProto.Event)
      private static final com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event();
      }

      public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Event>
          PARSER = new com.google.protobuf.AbstractParser<Event>() {
        @java.lang.Override
        public Event parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Event> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Event> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public static final int EVENT_FIELD_NUMBER = 1;
    private com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event event_;
    /**
     * <code>.event.EventParserProto.Event event = 1;</code>
     * @return Whether the event field is set.
     */
    @java.lang.Override
    public boolean hasEvent() {
      return event_ != null;
    }
    /**
     * <code>.event.EventParserProto.Event event = 1;</code>
     * @return The event.
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event getEvent() {
      return event_ == null ? com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event.getDefaultInstance() : event_;
    }
    /**
     * <code>.event.EventParserProto.Event event = 1;</code>
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.EventOrBuilder getEventOrBuilder() {
      return getEvent();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (event_ != null) {
        output.writeMessage(1, getEvent());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (event_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getEvent());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto)) {
        return super.equals(obj);
      }
      com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto other = (com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto) obj;

      if (hasEvent() != other.hasEvent()) return false;
      if (hasEvent()) {
        if (!getEvent()
            .equals(other.getEvent())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasEvent()) {
        hash = (37 * hash) + EVENT_FIELD_NUMBER;
        hash = (53 * hash) + getEvent().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code event.EventParserProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:event.EventParserProto)
        com.amazon.proto.avs.v20160207.event.EventParser.EventParserProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.event.EventParser.internal_static_event_EventParserProto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.event.EventParser.internal_static_event_EventParserProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.class, com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Builder.class);
      }

      // Construct using com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (eventBuilder_ == null) {
          event_ = null;
        } else {
          event_ = null;
          eventBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.proto.avs.v20160207.event.EventParser.internal_static_event_EventParserProto_descriptor;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto getDefaultInstanceForType() {
        return com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto build() {
        com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto buildPartial() {
        com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto result = new com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto(this);
        if (eventBuilder_ == null) {
          result.event_ = event_;
        } else {
          result.event_ = eventBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto) {
          return mergeFrom((com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto other) {
        if (other == com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.getDefaultInstance()) return this;
        if (other.hasEvent()) {
          mergeEvent(other.getEvent());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getEventFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event event_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event, com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event.Builder, com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.EventOrBuilder> eventBuilder_;
      /**
       * <code>.event.EventParserProto.Event event = 1;</code>
       * @return Whether the event field is set.
       */
      public boolean hasEvent() {
        return eventBuilder_ != null || event_ != null;
      }
      /**
       * <code>.event.EventParserProto.Event event = 1;</code>
       * @return The event.
       */
      public com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event getEvent() {
        if (eventBuilder_ == null) {
          return event_ == null ? com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event.getDefaultInstance() : event_;
        } else {
          return eventBuilder_.getMessage();
        }
      }
      /**
       * <code>.event.EventParserProto.Event event = 1;</code>
       */
      public Builder setEvent(com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event value) {
        if (eventBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          event_ = value;
          onChanged();
        } else {
          eventBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.event.EventParserProto.Event event = 1;</code>
       */
      public Builder setEvent(
          com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event.Builder builderForValue) {
        if (eventBuilder_ == null) {
          event_ = builderForValue.build();
          onChanged();
        } else {
          eventBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.event.EventParserProto.Event event = 1;</code>
       */
      public Builder mergeEvent(com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event value) {
        if (eventBuilder_ == null) {
          if (event_ != null) {
            event_ =
              com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event.newBuilder(event_).mergeFrom(value).buildPartial();
          } else {
            event_ = value;
          }
          onChanged();
        } else {
          eventBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.event.EventParserProto.Event event = 1;</code>
       */
      public Builder clearEvent() {
        if (eventBuilder_ == null) {
          event_ = null;
          onChanged();
        } else {
          event_ = null;
          eventBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.event.EventParserProto.Event event = 1;</code>
       */
      public com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event.Builder getEventBuilder() {
        
        onChanged();
        return getEventFieldBuilder().getBuilder();
      }
      /**
       * <code>.event.EventParserProto.Event event = 1;</code>
       */
      public com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.EventOrBuilder getEventOrBuilder() {
        if (eventBuilder_ != null) {
          return eventBuilder_.getMessageOrBuilder();
        } else {
          return event_ == null ?
              com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event.getDefaultInstance() : event_;
        }
      }
      /**
       * <code>.event.EventParserProto.Event event = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event, com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event.Builder, com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.EventOrBuilder> 
          getEventFieldBuilder() {
        if (eventBuilder_ == null) {
          eventBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event, com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.Event.Builder, com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto.EventOrBuilder>(
                  getEvent(),
                  getParentForChildren(),
                  isClean());
          event_ = null;
        }
        return eventBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:event.EventParserProto)
    }

    // @@protoc_insertion_point(class_scope:event.EventParserProto)
    private static final com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto();
    }

    public static com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<EventParserProto>
        PARSER = new com.google.protobuf.AbstractParser<EventParserProto>() {
      @java.lang.Override
      public EventParserProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<EventParserProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<EventParserProto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.proto.avs.v20160207.event.EventParser.EventParserProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_event_EventParserProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_event_EventParserProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_event_EventParserProto_Event_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_event_EventParserProto_Event_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\021eventParser.proto\022\005event\032\021eventHeader." +
      "proto\"\204\001\n\020EventParserProto\022,\n\005event\030\001 \001(" +
      "\0132\035.event.EventParserProto.Event\032B\n\005Even" +
      "t\022\017\n\007payload\030\002 \001(\014\022(\n\006header\030\001 \001(\0132\030.hea" +
      "der.EventHeaderProtoB3\n$com.amazon.proto" +
      ".avs.v20160207.eventB\013EventParserb\006proto" +
      "3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.amazon.proto.avs.v20160207.header.EventHeader.getDescriptor(),
        });
    internal_static_event_EventParserProto_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_event_EventParserProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_event_EventParserProto_descriptor,
        new java.lang.String[] { "Event", });
    internal_static_event_EventParserProto_Event_descriptor =
      internal_static_event_EventParserProto_descriptor.getNestedTypes().get(0);
    internal_static_event_EventParserProto_Event_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_event_EventParserProto_Event_descriptor,
        new java.lang.String[] { "Payload", "Header", });
    com.amazon.proto.avs.v20160207.header.EventHeader.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
