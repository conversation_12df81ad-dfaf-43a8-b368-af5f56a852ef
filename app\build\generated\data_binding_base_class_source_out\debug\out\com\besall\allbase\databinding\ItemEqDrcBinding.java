// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemEqDrcBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final SeekBar seekbar;

  @NonNull
  public final TextView seekbarMax;

  @NonNull
  public final TextView seekbarMin;

  @NonNull
  public final RadioButton seekbarRadio;

  @NonNull
  public final View seekbarRadioview;

  @NonNull
  public final TextView seekbarReal;

  @NonNull
  public final TextView seekbarTitle;

  private ItemEqDrcBinding(@NonNull LinearLayout rootView, @NonNull SeekBar seekbar,
      @NonNull TextView seekbarMax, @NonNull TextView seekbarMin, @NonNull RadioButton seekbarRadio,
      @NonNull View seekbarRadioview, @NonNull TextView seekbarReal,
      @NonNull TextView seekbarTitle) {
    this.rootView = rootView;
    this.seekbar = seekbar;
    this.seekbarMax = seekbarMax;
    this.seekbarMin = seekbarMin;
    this.seekbarRadio = seekbarRadio;
    this.seekbarRadioview = seekbarRadioview;
    this.seekbarReal = seekbarReal;
    this.seekbarTitle = seekbarTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemEqDrcBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemEqDrcBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_eq_drc, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemEqDrcBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.seekbar;
      SeekBar seekbar = rootView.findViewById(id);
      if (seekbar == null) {
        break missingId;
      }

      id = R.id.seekbar_max;
      TextView seekbarMax = rootView.findViewById(id);
      if (seekbarMax == null) {
        break missingId;
      }

      id = R.id.seekbar_min;
      TextView seekbarMin = rootView.findViewById(id);
      if (seekbarMin == null) {
        break missingId;
      }

      id = R.id.seekbar_radio;
      RadioButton seekbarRadio = rootView.findViewById(id);
      if (seekbarRadio == null) {
        break missingId;
      }

      id = R.id.seekbar_radioview;
      View seekbarRadioview = rootView.findViewById(id);
      if (seekbarRadioview == null) {
        break missingId;
      }

      id = R.id.seekbar_real;
      TextView seekbarReal = rootView.findViewById(id);
      if (seekbarReal == null) {
        break missingId;
      }

      id = R.id.seekbar_title;
      TextView seekbarTitle = rootView.findViewById(id);
      if (seekbarTitle == null) {
        break missingId;
      }

      return new ItemEqDrcBinding((LinearLayout) rootView, seekbar, seekbarMax, seekbarMin,
          seekbarRadio, seekbarRadioview, seekbarReal, seekbarTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
