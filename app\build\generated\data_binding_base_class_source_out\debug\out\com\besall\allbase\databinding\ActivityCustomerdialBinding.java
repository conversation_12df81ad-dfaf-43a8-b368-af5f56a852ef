// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import com.suke.widget.SwitchButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityCustomerdialBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final EditText addressText;

  @NonNull
  public final Button chooseDevice;

  @NonNull
  public final Button chooseDfuFile;

  @NonNull
  public final Button chooseDial;

  @NonNull
  public final Button chooseOldFile;

  @NonNull
  public final TextView currentDevice;

  @NonNull
  public final LogviewBinding loginfo;

  @NonNull
  public final RadioButton radioButtonFileTypeFont;

  @NonNull
  public final RadioButton radioButtonFileTypeHeartRate;

  @NonNull
  public final RadioButton radioButtonFileTypeLanguage;

  @NonNull
  public final RadioButton radioButtonFileTypeOnline;

  @NonNull
  public final RadioButton radioButtonFileTypeOtaBoot;

  @NonNull
  public final RadioButton radioButtonFileTypePicture;

  @NonNull
  public final RadioButton radioButtonFileTypeTp;

  @NonNull
  public final RadioGroup radioGroupFileType;

  @NonNull
  public final TextView showDialDfuPath;

  @NonNull
  public final ImageView showDialPhoto;

  @NonNull
  public final TextView showOldFilePath;

  @NonNull
  public final Button startTransfer;

  @NonNull
  public final SwitchButton switchButtonUseDiffUpgrade;

  @NonNull
  public final ToolbarBinding tool;

  @NonNull
  public final TextView transferPercent;

  @NonNull
  public final ProgressBar transferProgress;

  private ActivityCustomerdialBinding(@NonNull LinearLayout rootView, @NonNull EditText addressText,
      @NonNull Button chooseDevice, @NonNull Button chooseDfuFile, @NonNull Button chooseDial,
      @NonNull Button chooseOldFile, @NonNull TextView currentDevice,
      @NonNull LogviewBinding loginfo, @NonNull RadioButton radioButtonFileTypeFont,
      @NonNull RadioButton radioButtonFileTypeHeartRate,
      @NonNull RadioButton radioButtonFileTypeLanguage,
      @NonNull RadioButton radioButtonFileTypeOnline,
      @NonNull RadioButton radioButtonFileTypeOtaBoot,
      @NonNull RadioButton radioButtonFileTypePicture, @NonNull RadioButton radioButtonFileTypeTp,
      @NonNull RadioGroup radioGroupFileType, @NonNull TextView showDialDfuPath,
      @NonNull ImageView showDialPhoto, @NonNull TextView showOldFilePath,
      @NonNull Button startTransfer, @NonNull SwitchButton switchButtonUseDiffUpgrade,
      @NonNull ToolbarBinding tool, @NonNull TextView transferPercent,
      @NonNull ProgressBar transferProgress) {
    this.rootView = rootView;
    this.addressText = addressText;
    this.chooseDevice = chooseDevice;
    this.chooseDfuFile = chooseDfuFile;
    this.chooseDial = chooseDial;
    this.chooseOldFile = chooseOldFile;
    this.currentDevice = currentDevice;
    this.loginfo = loginfo;
    this.radioButtonFileTypeFont = radioButtonFileTypeFont;
    this.radioButtonFileTypeHeartRate = radioButtonFileTypeHeartRate;
    this.radioButtonFileTypeLanguage = radioButtonFileTypeLanguage;
    this.radioButtonFileTypeOnline = radioButtonFileTypeOnline;
    this.radioButtonFileTypeOtaBoot = radioButtonFileTypeOtaBoot;
    this.radioButtonFileTypePicture = radioButtonFileTypePicture;
    this.radioButtonFileTypeTp = radioButtonFileTypeTp;
    this.radioGroupFileType = radioGroupFileType;
    this.showDialDfuPath = showDialDfuPath;
    this.showDialPhoto = showDialPhoto;
    this.showOldFilePath = showOldFilePath;
    this.startTransfer = startTransfer;
    this.switchButtonUseDiffUpgrade = switchButtonUseDiffUpgrade;
    this.tool = tool;
    this.transferPercent = transferPercent;
    this.transferProgress = transferProgress;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCustomerdialBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCustomerdialBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_customerdial, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCustomerdialBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.address_text;
      EditText addressText = rootView.findViewById(id);
      if (addressText == null) {
        break missingId;
      }

      id = R.id.choose_device;
      Button chooseDevice = rootView.findViewById(id);
      if (chooseDevice == null) {
        break missingId;
      }

      id = R.id.choose_dfu_file;
      Button chooseDfuFile = rootView.findViewById(id);
      if (chooseDfuFile == null) {
        break missingId;
      }

      id = R.id.choose_dial;
      Button chooseDial = rootView.findViewById(id);
      if (chooseDial == null) {
        break missingId;
      }

      id = R.id.choose_old_file;
      Button chooseOldFile = rootView.findViewById(id);
      if (chooseOldFile == null) {
        break missingId;
      }

      id = R.id.current_device;
      TextView currentDevice = rootView.findViewById(id);
      if (currentDevice == null) {
        break missingId;
      }

      id = R.id.loginfo;
      View loginfo = rootView.findViewById(id);
      if (loginfo == null) {
        break missingId;
      }
      LogviewBinding binding_loginfo = LogviewBinding.bind(loginfo);

      id = R.id.radio_button_file_type_font;
      RadioButton radioButtonFileTypeFont = rootView.findViewById(id);
      if (radioButtonFileTypeFont == null) {
        break missingId;
      }

      id = R.id.radio_button_file_type_heart_rate;
      RadioButton radioButtonFileTypeHeartRate = rootView.findViewById(id);
      if (radioButtonFileTypeHeartRate == null) {
        break missingId;
      }

      id = R.id.radio_button_file_type_language;
      RadioButton radioButtonFileTypeLanguage = rootView.findViewById(id);
      if (radioButtonFileTypeLanguage == null) {
        break missingId;
      }

      id = R.id.radio_button_file_type_online;
      RadioButton radioButtonFileTypeOnline = rootView.findViewById(id);
      if (radioButtonFileTypeOnline == null) {
        break missingId;
      }

      id = R.id.radio_button_file_type_ota_boot;
      RadioButton radioButtonFileTypeOtaBoot = rootView.findViewById(id);
      if (radioButtonFileTypeOtaBoot == null) {
        break missingId;
      }

      id = R.id.radio_button_file_type_picture;
      RadioButton radioButtonFileTypePicture = rootView.findViewById(id);
      if (radioButtonFileTypePicture == null) {
        break missingId;
      }

      id = R.id.radio_button_file_type_tp;
      RadioButton radioButtonFileTypeTp = rootView.findViewById(id);
      if (radioButtonFileTypeTp == null) {
        break missingId;
      }

      id = R.id.radio_group_file_type;
      RadioGroup radioGroupFileType = rootView.findViewById(id);
      if (radioGroupFileType == null) {
        break missingId;
      }

      id = R.id.show_dial_dfu_path;
      TextView showDialDfuPath = rootView.findViewById(id);
      if (showDialDfuPath == null) {
        break missingId;
      }

      id = R.id.show_dial_photo;
      ImageView showDialPhoto = rootView.findViewById(id);
      if (showDialPhoto == null) {
        break missingId;
      }

      id = R.id.show_old_file_path;
      TextView showOldFilePath = rootView.findViewById(id);
      if (showOldFilePath == null) {
        break missingId;
      }

      id = R.id.start_transfer;
      Button startTransfer = rootView.findViewById(id);
      if (startTransfer == null) {
        break missingId;
      }

      id = R.id.switchButton_use_diff_upgrade;
      SwitchButton switchButtonUseDiffUpgrade = rootView.findViewById(id);
      if (switchButtonUseDiffUpgrade == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      id = R.id.transfer_percent;
      TextView transferPercent = rootView.findViewById(id);
      if (transferPercent == null) {
        break missingId;
      }

      id = R.id.transfer_progress;
      ProgressBar transferProgress = rootView.findViewById(id);
      if (transferProgress == null) {
        break missingId;
      }

      return new ActivityCustomerdialBinding((LinearLayout) rootView, addressText, chooseDevice,
          chooseDfuFile, chooseDial, chooseOldFile, currentDevice, binding_loginfo,
          radioButtonFileTypeFont, radioButtonFileTypeHeartRate, radioButtonFileTypeLanguage,
          radioButtonFileTypeOnline, radioButtonFileTypeOtaBoot, radioButtonFileTypePicture,
          radioButtonFileTypeTp, radioGroupFileType, showDialDfuPath, showDialPhoto,
          showOldFilePath, startTransfer, switchButtonUseDiffUpgrade, binding_tool, transferPercent,
          transferProgress);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
