package com.besall.allbase.bluetooth.service.capsensor;

import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOR_HAS_EXTRAS_PARAM_KEY;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOR_HAS_EXTRAS_PARAM_VALUE;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOR_ISBTH_KEY;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOR_ISBTH_VALUE;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOR_SHOW_TOUCH_EVENT;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOR_SHOW_TOUCH_EVENT_VALUE;

import android.content.Context;
import android.text.format.DateFormat;
import android.util.Log;

import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.besall.allbase.common.utils.FileUtils;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class CapSensorCMD {

    private static int repNum = 4;//rep numbers
    private static long interval = 0;
    private static long lastTimeMillis = 0;
    private static String channelData = "";
    private static String capsensorData = "";
    private static String capsensorAllData = "";
    private static String extraParam = "";

    private static List<String> capsensorDataList = new ArrayList();
    private static List<String> channelDataList = new ArrayList();
    private static byte[] CAPSENSOR_DEBUG = ArrayUtil.toBytes(ArrayUtil.str2HexStr("CAPSENSOR_DEBUG"));
    private static byte[] CAPSENSOR = ArrayUtil.toBytes(ArrayUtil.str2HexStr("CAPSENSOR"));

    public static byte[] getNormalStartOrStopCMD(boolean isStart) {
        if (isStart) {
            interval = 0;
            channelData = "";
            capsensorData = "";
            capsensorAllData = "";
            extraParam = "";
            capsensorDataList = new ArrayList();
            channelDataList = new ArrayList();
        }
        return ArrayUtil.byteMerger(CAPSENSOR_DEBUG, new byte[]{isStart ? (byte) 0x01 : (byte) 0x00});
    }

    public static String[] receiveData(Context context, byte[] data, boolean isTota) {
        Log.i("CapSensorCMD", "receiveData: ----" + ArrayUtil.toHex(data));
        boolean isBtn = (boolean) SPHelper.getPreference(context, BES_CAPSENSOR_ISBTH_KEY, BES_CAPSENSOR_ISBTH_VALUE);
        boolean isHasExtraParam = (boolean) SPHelper.getPreference(context, BES_CAPSENSOR_HAS_EXTRAS_PARAM_KEY, BES_CAPSENSOR_HAS_EXTRAS_PARAM_VALUE);
        boolean shwoTouchEvent = (boolean) SPHelper.getPreference(context, BES_CAPSENSOR_SHOW_TOUCH_EVENT, BES_CAPSENSOR_SHOW_TOUCH_EVENT_VALUE);
        if (isTota ? (data[0] == (byte)0x21 && data[1] == (byte)0x63) : ArrayUtil.startsWith(data, CAPSENSOR)) {
            if (isBtn == false) {
                int headLength = isTota ? 2 : CAPSENSOR.length;
                byte[] lB = new byte[4];
                System.arraycopy(data, headLength, lB, 0, 2);
                int l = ArrayUtil.bytesToIntLittle(lB);

                capsensorData = "";
                channelData = "";

                for (int i = 0; i < l / 4; i ++) {
                    int adcin = 0;
                    int chn = 0;
                    byte[] curCNData = new byte[4];
                    System.arraycopy(data, i * 4 + headLength + 2, curCNData, 0, 4);
//                    Log.i("TAG", "curCNData: --------" + ArrayUtil.toHex(curCNData));
                    for (int j = 0; j < 2; j ++) {
                        byte[] cnData = new byte[2];
                        System.arraycopy(curCNData, 2 * j, cnData, 0, 2);
//                        Log.i("TAG", "cnData: --------" + ArrayUtil.toHex(cnData));
                        short cnShort = ArrayUtil.getShort(cnData[0], cnData[1]);
                        adcin += cnShort & 0xfff;
                        chn = (cnShort & 0x7000) >> 12;
                    }
                    capsensorData = capsensorData + (capsensorData.length() > 0 ? "\n" : "") + adcin;
                    channelData = channelData + (channelData.length() > 0 ? "\n" : "") + chn;
//                    short cnShort = ArrayUtil.getShort(data[i * 2 + headLength + 2], data[i * 2 + 5]);
//                    int adcin = cnShort & 0xfff;
//                    capsensorData = capsensorData + (capsensorData.length() > 0 ? "\n" : "") + adcin;
//                    int chn = (cnShort & 0x7000) >> 12;
//                    channelData = channelData + (channelData.length() > 0 ? "\n" : "") + chn;
                }
//                Log.i("TAG", "capsensorData: ------" + capsensorData);
//                Log.i("TAG", "channelData: ------" + channelData);

                long curTimeMillis = System.currentTimeMillis();
                StringBuilder stringBuilder = new StringBuilder();
                CharSequence time1 = DateFormat.format("HH:mm:ss:", curTimeMillis);
                String time2 = (curTimeMillis + "").substring(10, 13);
                extraParam = stringBuilder.append(time1).append(time2).toString() + "\n" + capsensorData;
//                LOGAAA(time1 + time2 + "-----" + data.length);
                return new String[]{extraParam, capsensorData};
            } else {
                int startLen = isTota ? 4 : (CAPSENSOR.length + 2);
                byte[] lB = new byte[4];
                System.arraycopy(data, startLen - 2, lB, 0, 2);
                int l = ArrayUtil.bytesToIntLittle(lB);
                capsensorDataList = new ArrayList();
                channelDataList = new ArrayList();
                capsensorData = "";
                channelData = "";
//                for (int j = 0; j < data.length / (startLen + l); j ++) {

                int extraParamL = 20 + (shwoTouchEvent ? 4 : 0);
                int dataL = l - (isHasExtraParam ? extraParamL : 0);
                byte[] mData = new byte[dataL];
                System.arraycopy(data, startLen, mData, 0, dataL);
                for (int i = 0; i < mData.length / 8; i ++) {
                    byte[] cnData = new byte[4];
                    System.arraycopy(mData, (i + 1) * 8 - 8, cnData, 0, 4);
//                    Log.i("TAG", "cnData: ---" + ArrayUtil.toHex(cnData));
                    int cn = ArrayUtil.bytesToIntLittle(cnData);
//                    Log.i("TAG", "receiveData: --------" + cn);
                    if (cn > 8) {
                        return new String[0];
                    }
                    byte[] adcinData = new byte[4];
                    System.arraycopy(mData, (i + 1) * 8 - 4, adcinData, 0, 4);
                    int adcin = ArrayUtil.bytesToIntLittle(adcinData);

                    capsensorData = capsensorData + (capsensorData.length() > 0 ? "\n" : "") + adcin;
                    capsensorDataList.add(capsensorData);
                    channelData = channelData + (channelData.length() > 0 ? "\n" : "") + cn;
                    channelDataList.add(channelData);
                }

                long curTimeMillis = System.currentTimeMillis();
                StringBuilder stringBuilder = new StringBuilder();
//                extraParam = stringBuilder.append(DateFormat.format("HH:mm:ss:", curTimeMillis)).append((curTimeMillis + "").substring(10, 13)).toString() + "\n" + capsensorData;
                CharSequence time1 = DateFormat.format("HH:mm:ss:", curTimeMillis);
                String time2 = (curTimeMillis + "").substring(10, 13);
                extraParam = stringBuilder.append(time1).append(time2).toString() + "\n" + capsensorData;
//                LOGAAA(time1 + time2 + "-----" + data.length);

                if (isHasExtraParam) {
                    byte[] extraData = new byte[extraParamL];
                    System.arraycopy(data, data.length - extraParamL, extraData, 0, extraParamL);
                    for (int i = 0; i < extraData.length / 4; i ++) {
                        byte[] curExtraData = new byte[4];
                        System.arraycopy(extraData, i * 4, curExtraData, 0, 4);
                        int curExtra = ArrayUtil.bytesToIntLittle(curExtraData);
                        extraParam = extraParam + (extraParam.length() > 0 ? "\n" : "") + curExtra;
                    }
                }
//                Log.i("TAG", "extraParam: -----------" + extraParam);
                return new String[]{extraParam, capsensorData};
            }
//            }
//            Log.i("TAG", "receiveData capsensorData: -------" + capsensorData);
//            Log.i("TAG", "receiveData channelData: -------" + channelData);
//            if (channelDataList.size() > 1) {
//                return CapSensorConstants.CAPSENSOR_DATA_MERGE;
//            }
//            return CapSensorConstants.CAPSENSOR_DATA;
        }
//        if (data[0] == (byte)0x21 && data[1] == (byte)0x63) {
//            byte[] lB = new byte[4];
//            System.arraycopy(data, 2, lB, 0, 2);
//            int l = ArrayUtil.bytesToIntLittle(lB);
//            byte[] shortData = new byte[l];
//            System.arraycopy(data, 4, shortData, 0, l);
//            float[] floatData = new float[l / 2];
//            for (int i = 0; i < floatData.length; i ++) {
//                floatData[i] = ArrayUtil.getShort(shortData[i * 2], shortData[i * 2 + 1]) & 0xfff;
//            }
//            float[] channelData = new float[floatData.length / repNum];
//            for (int i = 0; i < channelData.length; i ++) {
//                channelData[i] = floatData[i * repNum] + floatData[i * repNum + 1] + floatData[i * repNum + 2] + floatData[i * repNum + 3];
//            }
//            capsensorData = "";
//            for (int i = 0; i < channelData.length; i ++) {
//                capsensorData = capsensorData + (capsensorData.length() > 0 ? "\n" : "") + channelData[i];
//            }

//            byte[] lB = new byte[4];
//            System.arraycopy(data, 2, lB, 0, 2);
//            int l = ArrayUtil.bytesToIntLittle(lB);
//            capsensorData = "";
//            channelData = "";
//            for (int i = 0; i < l / 2; i ++) {
//                short cnShort = ArrayUtil.getShort(data[i * 2 + 4], data[i * 2 + 5]);
//                int adcin = cnShort & 0xfff;
//                capsensorData = capsensorData + (capsensorData.length() > 0 ? "\n" : "") + adcin;
//                int chn = (cnShort & 0x7000) >> 12;
//                channelData = channelData + (channelData.length() > 0 ? "\n" : "") + chn;
//            }
//
//            return CapSensorConstants.CAPSENSOR_DATA;
//        }

//        if (data[0] == (byte)0x00 && data[1] == (byte)0x67) {
//            byte[] lB = new byte[4];
//            System.arraycopy(data, 2, lB, 0, 2);
//            int l = ArrayUtil.bytesToIntLittle(lB);
//            byte[] shortData = new byte[l];
//            System.arraycopy(data, 4, shortData, 0, l);
//            float[] floatData = new float[l / 2];
//            for (int i = 0; i < floatData.length; i ++) {
//                floatData[i] = ArrayUtil.getShort(shortData[i * 2], shortData[i * 2 + 1]) & 0xfff;
//            }
//            float[] channelData = new float[floatData.length / repNum];
//            for (int i = 0; i < channelData.length; i ++) {
//                channelData[i] = floatData[i * repNum] + floatData[i * repNum + 1] + floatData[i * repNum + 2] + floatData[i * repNum + 3];
//            }
//            capsensorData = "";
//            for (int i = 0; i < channelData.length; i ++) {
//                capsensorData = capsensorData + (capsensorData.length() > 0 ? "\n" : "") + channelData[i];
//            }
//            return CapSensorConstants.CAPSENSOR_DATA;
//        }
//        else if (data[0] == (byte)0x03 && data[1] == (byte)0x70) {
//
//            long curTimeMillis = Calendar.getInstance().getTimeInMillis();
//            Log.i("TAG", "lastTimeMillis: -------" + lastTimeMillis);
//            Log.i("TAG", "curTimeMillis: -------" + curTimeMillis);
//            if (lastTimeMillis > 0) {
//                interval = lastTimeMillis - curTimeMillis;
//            }
//            Log.i("TAG", "interval: -------" + interval);
//
//            lastTimeMillis = curTimeMillis;
//            return CapSensorConstants.SPEED_TEST_SEND_DATA;
//        }
        return new String[0];
    }

    public static String getChannelData() {
        return channelData;
    }

    public static String getCapsensorData() {
        return capsensorData;
    }

    public static String getExtraParamData() {
        return extraParam;
    }

    public static String getSSSpeedTestIntervel() {
        return interval + "";
    }

    private static void LOGAAA(String str) {
        FileUtils.writeTOfileAndActiveClear("Capsensor", str);
    }

}
