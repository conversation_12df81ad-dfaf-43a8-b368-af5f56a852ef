<?xml version="1.0" encoding="utf-8"?>
<com.besall.allbase.common.utils.SlideLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="62dp"
    android:clickable="true">

    <LinearLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="62dp"
        android:gravity="center">

        <ImageView
            android:id="@+id/item_imageview"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginLeft="20dp"
            android:scaleType="fitXY"
            android:src="@drawable/documents" />

        <TextView
            android:id="@+id/file_name"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_marginLeft="17dp"
            android:gravity="center"
            android:text="-"
            android:textSize="14sp"
            android:textAlignment="viewStart"/>
    </LinearLayout>

                <TextView
                    android:id="@+id/delete_button"
                    android:layout_width="88dp"
                    android:layout_height="62dp"
                    android:gravity="center"
                    android:textColor="#ffffffff"
                    android:background="#ffff5735"
                    android:text="Delete"
                    android:textSize="16sp"/>

</com.besall.allbase.common.utils.SlideLayout>

