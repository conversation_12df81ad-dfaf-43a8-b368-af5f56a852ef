// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActionSheetBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button actionSheet0;

  @NonNull
  public final Button actionSheet1;

  @NonNull
  public final Button actionSheetCancel;

  private ActionSheetBinding(@NonNull LinearLayout rootView, @NonNull Button actionSheet0,
      @NonNull Button actionSheet1, @NonNull Button actionSheetCancel) {
    this.rootView = rootView;
    this.actionSheet0 = actionSheet0;
    this.actionSheet1 = actionSheet1;
    this.actionSheetCancel = actionSheetCancel;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActionSheetBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActionSheetBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.action_sheet, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActionSheetBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.action_sheet_0;
      Button actionSheet0 = rootView.findViewById(id);
      if (actionSheet0 == null) {
        break missingId;
      }

      id = R.id.action_sheet_1;
      Button actionSheet1 = rootView.findViewById(id);
      if (actionSheet1 == null) {
        break missingId;
      }

      id = R.id.action_sheet_cancel;
      Button actionSheetCancel = rootView.findViewById(id);
      if (actionSheetCancel == null) {
        break missingId;
      }

      return new ActionSheetBinding((LinearLayout) rootView, actionSheet0, actionSheet1,
          actionSheetCancel);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
