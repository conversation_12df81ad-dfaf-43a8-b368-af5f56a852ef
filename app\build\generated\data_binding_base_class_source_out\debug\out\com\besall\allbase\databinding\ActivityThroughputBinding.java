// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityThroughputBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final FrameLayout frame;

  @NonNull
  public final ToolbarBinding tool;

  @NonNull
  public final Button typeBle;

  @NonNull
  public final Button typeSpp;

  private ActivityThroughputBinding(@NonNull ConstraintLayout rootView, @NonNull FrameLayout frame,
      @NonNull ToolbarBinding tool, @NonNull Button typeBle, @NonNull Button typeSpp) {
    this.rootView = rootView;
    this.frame = frame;
    this.tool = tool;
    this.typeBle = typeBle;
    this.typeSpp = typeSpp;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityThroughputBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityThroughputBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_throughput, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityThroughputBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.frame;
      FrameLayout frame = rootView.findViewById(id);
      if (frame == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      id = R.id.type_ble;
      Button typeBle = rootView.findViewById(id);
      if (typeBle == null) {
        break missingId;
      }

      id = R.id.type_spp;
      Button typeSpp = rootView.findViewById(id);
      if (typeSpp == null) {
        break missingId;
      }

      return new ActivityThroughputBinding((ConstraintLayout) rootView, frame, binding_tool,
          typeBle, typeSpp);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
