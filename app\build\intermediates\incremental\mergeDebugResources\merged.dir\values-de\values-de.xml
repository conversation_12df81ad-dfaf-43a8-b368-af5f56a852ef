<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="urn:oasis:names:tc:xliff:document:1.2">
    <plurals name="mtrl_badge_content_description">
    <item quantity="one">%d neue Benachrichtigung</item>
    <item quantity="other">%d neue Benachrichtigungen</item>
  </plurals>
    <string msgid="5976598919945601918" name="abc_action_bar_home_description">"Zur Startseite"</string>
    <string msgid="8388173803310557296" name="abc_action_bar_up_description">"Nach oben"</string>
    <string msgid="3937310113216875497" name="abc_action_menu_overflow_description">"Weitere Optionen"</string>
    <string msgid="4692188335987374352" name="abc_action_mode_done">"Fertig"</string>
    <string msgid="1189761859438369441" name="abc_activity_chooser_view_see_all">"Alle anzeigen"</string>
    <string msgid="2165779757652331008" name="abc_activitychooserview_choose_application">"App auswählen"</string>
    <string msgid="4215997306490295099" name="abc_capital_off">"AUS"</string>
    <string msgid="884982626291842264" name="abc_capital_on">"AN"</string>
    <string msgid="8833365367933412986" name="abc_menu_alt_shortcut_label">"Alt +"</string>
    <string msgid="2223301931652355242" name="abc_menu_ctrl_shortcut_label">"Strg +"</string>
    <string msgid="838001238306846836" name="abc_menu_delete_shortcut_label">"Löschen"</string>
    <string msgid="7986526966204849475" name="abc_menu_enter_shortcut_label">"Eingabetaste"</string>
    <string msgid="375214403600139847" name="abc_menu_function_shortcut_label">"Funktionstaste +"</string>
    <string msgid="4192209724446364286" name="abc_menu_meta_shortcut_label">"Meta-Taste +"</string>
    <string msgid="4741552369836443843" name="abc_menu_shift_shortcut_label">"Umschalttaste +"</string>
    <string msgid="5473865519181928982" name="abc_menu_space_shortcut_label">"Leertaste"</string>
    <string msgid="6180552449598693998" name="abc_menu_sym_shortcut_label">"Sym-Taste +"</string>
    <string msgid="5520303668377388990" name="abc_prepend_shortcut_label">"Menütaste +"</string>
    <string msgid="7208076849092622260" name="abc_search_hint">"Suchen…"</string>
    <string msgid="3741173234950517107" name="abc_searchview_description_clear">"Suchanfrage löschen"</string>
    <string msgid="693312494995508443" name="abc_searchview_description_query">"Suchanfrage"</string>
    <string msgid="3417662926640357176" name="abc_searchview_description_search">"Suche"</string>
    <string msgid="1486535517437947103" name="abc_searchview_description_submit">"Anfrage senden"</string>
    <string msgid="2293578557972875415" name="abc_searchview_description_voice">"Sprachsuche"</string>
    <string msgid="8875138169939072951" name="abc_shareactionprovider_share_with">"Teilen mit"</string>
    <string msgid="9055268688411532828" name="abc_shareactionprovider_share_with_application">"Mit <ns1:g id="APPLICATION_NAME">%s</ns1:g> teilen"</string>
    <string msgid="1656852541809559762" name="abc_toolbar_collapse_description">"Minimieren"</string>
    <string name="bottomsheet_action_expand_halfway">Zur Hälfte maximieren</string>
    <string name="character_counter_content_description">Eingegebene Zeichen: %1$d von %2$d</string>
    <string name="character_counter_overflowed_content_description">Zeichenbeschränkung überschritten: %1$d/%2$d</string>
    <string name="clear_text_end_icon_content_description">Text löschen</string>
    <string name="error_icon_content_description">Fehler</string>
    <string name="exposed_dropdown_menu_content_description">Drop-down-Menü anzeigen</string>
    <string name="icon_content_description">Dialogfeldsymbol</string>
    <string name="item_view_role_description">Tab</string>
    <string name="leak_canary_analysis_failed">Leak Analyse fehlgeschlagen</string>
    <string name="leak_canary_class_has_leaked">%1$s hat geleaked</string>
    <string name="leak_canary_class_has_leaked_retaining">%1$s hat %2$s geleaked</string>
    <string name="leak_canary_class_no_leak">%1$s was never GCed but no leak found</string>
    <string name="leak_canary_could_not_save_text">LeakCanary konnte das Ergebnis der Analyse nicht speichern.</string>
    <string name="leak_canary_delete">Löschen</string>
    <string name="leak_canary_delete_all">Alle löschen</string>
    <string name="leak_canary_delete_all_leaks_title">Bist du dir sicher, dass du alle Leaks löschen möchtest?</string>
    <string name="leak_canary_display_activity_label">Leaks</string>
    <string name="leak_canary_download_dump">You can download the heap dump via \"Menu > Share Heap Dump\" or \"adb pull %1$s\"</string>
    <string name="leak_canary_excluded_row">[Ausgeschlossen] %s</string>
    <string name="leak_canary_failure_report">"Bitte sende diesen Fehler an http://github.com/square/leakcanary\n"</string>
    <string name="leak_canary_help_detail"><![CDATA[Ein Memory-Leak ist ein Programmierfehler der
    dafür sorgt, dass deine Anwendung eine Referenz auf ein Objekt hält, das nicht länger benötigt
    wird. Daraus folgt, dass der Speicher der dem Objekt zugewiesen wurde nicht wieder freigegeben
    wird, was eventuell zu einem OutOfMemoryError-Absturz führt.<br>
  <br>Eine Instanz einer Android-Activity wird nicht mehr benötigt, nachdem ihre <i>onDestroy()</i>
  Methode aufgerufen wurde. Würde man eine Referenz auf diese Activity in einem statischen Feld
  speichern, dann wird der durch die Activity genutzte Speicher nicht mehr durch den Garbage
  Collector freigegeben.<br>
  <br>
  LeakCanary identifiziert ein Objekt, dass nicht länger benötigt wird und findet die Kette an
  <font color=\'#9976a8\'>Referenzen</font> die es davon abhält seinen Speicher freizugeben.<br>
  <br>
  Um ein Memory-Leak zu beheben, musst du anhand der Kette herausfinden, welche Referenz für das
  Leak verantwortlich ist, d.h. welche Referenz beim Auftreten des Leaks bereinigt sein soll.
  LeakCanary hebt <b><u><font color=\'#9976a8\'>Referenzen</font></u></b> die mögliche Ursachen
  für das Memory-Leak sein könnten mit einer roten Wellenlinie hervor.<br>
  <br>
  Tippe auf eine Zeile mit einer Referenz um mehr Details anzuzeigen, tippe noch einmal um die
  Details zu schließen.
]]></string>
    <string name="leak_canary_help_title">Tippe hier, um mehr zu erfahren</string>
    <string name="leak_canary_leak_excluded">[Ausgeschlossen] %1$s hat geleaked</string>
    <string name="leak_canary_leak_excluded_retaining">[Ausgeschlossen] %1$s hat %2$s geleaked</string>
    <string name="leak_canary_leak_list_title">Leaks in %s</string>
    <string name="leak_canary_no_leak_details">LeakCanary could not find a valid path to GC roots. Download the heap dump and investigate with MAT or YourKit.</string>
    <string name="leak_canary_notification_analysing">Analyisere Heap Dump</string>
    <string name="leak_canary_notification_channel">LeakCanary</string>
    <string name="leak_canary_notification_dumping">Sichere Heap</string>
    <string name="leak_canary_notification_foreground_text">LeakCanary ist beschäftigt.</string>
    <string name="leak_canary_notification_message">Für mehr Details, hier klicken</string>
    <string name="leak_canary_notification_reporting">Sichere LeakCanary Ergebnis</string>
    <string name="leak_canary_permission_not_granted">Bitte gewähre die Storage Berechtigung, andernfalls werden Memory Leaks nicht erkannt.</string>
    <string name="leak_canary_permission_notification_text">Hier klicken, um Storage Berechtigung für %s zu aktivieren.</string>
    <string name="leak_canary_permission_notification_title">Leak erkannt, benötige Berechtigung</string>
    <string name="leak_canary_result_failure_no_disk_space">Das Analyseergebnis konnte nicht gespeichert werden</string>
    <string name="leak_canary_result_failure_no_file">Das Analyseergebnis konnte nicht aus dem Speicher geladen werden</string>
    <string name="leak_canary_result_failure_title">Analysis result failure</string>
    <string name="leak_canary_share_heap_dump">Heap Dump teilen</string>
    <string name="leak_canary_share_leak">Info teilen</string>
    <string name="leak_canary_share_with">Teilen mit…</string>
    <string name="leak_canary_storage_permission_activity_label">Storage Berechtigung</string>
    <string name="leak_canary_toast_heap_dump">Sichere den Speicher, die App wird einfrieren. Brrr.</string>
    <string name="material_clock_toggle_content_description">Vormittags oder Nachmittags auswählen</string>
    <string name="material_hour_selection">Stunde auswählen</string>
    <string name="material_hour_suffix">%1$s Uhr</string>
    <string name="material_minute_selection">Minuten auswählen</string>
    <string name="material_minute_suffix">%1$s Minuten</string>
    <string name="material_timepicker_am">AM</string>
    <string name="material_timepicker_clock_mode_description">In den Uhrzeitmodus wechseln, um die Uhrzeit einzugeben.</string>
    <string name="material_timepicker_hour">Stunde</string>
    <string name="material_timepicker_minute">Minute</string>
    <string name="material_timepicker_pm">PM</string>
    <string name="material_timepicker_select_time">Uhrzeit auswählen</string>
    <string name="material_timepicker_text_input_mode_description">In den Texteingabemodus wechseln, um die Uhrzeit einzugeben.</string>
    <string name="mtrl_badge_numberless_content_description">Neue Benachrichtigung</string>
    <string name="mtrl_chip_close_icon_content_description">%1$s entfernen</string>
    <string name="mtrl_exceed_max_badge_number_content_description">Mehr als %1$d neue Benachrichtigungen</string>
    <string name="mtrl_picker_a11y_next_month">Zum nächsten Monat wechseln</string>
    <string name="mtrl_picker_a11y_prev_month">Zum vorherigen Monat wechseln</string>
    <string name="mtrl_picker_announce_current_selection">Aktuelle Auswahl: %1$s</string>
    <string name="mtrl_picker_confirm">@android:string/ok</string>
    <string name="mtrl_picker_date_header_selected">%1$s</string>
    <string name="mtrl_picker_date_header_title">Datum auswählen</string>
    <string name="mtrl_picker_date_header_unselected">Ausgewähltes Datum</string>
    <string name="mtrl_picker_day_of_week_column_header">Spalte \"Wochentag\": %1$s</string>
    <string name="mtrl_picker_invalid_format">Ungültiges Format.</string>
    <string name="mtrl_picker_invalid_format_example">Beispiel: %1$s</string>
    <string name="mtrl_picker_invalid_format_use">Format: %1$s</string>
    <string name="mtrl_picker_invalid_range">Ungültiger Bereich.</string>
    <string name="mtrl_picker_navigate_to_year_description">Zum Jahr %1$s wechseln</string>
    <string name="mtrl_picker_out_of_range">Außerhalb des Bereichs: %1$s</string>
    <string name="mtrl_picker_range_header_only_end_selected">Startdatum – %1$s</string>
    <string name="mtrl_picker_range_header_only_start_selected">%1$s – Enddatum</string>
    <string name="mtrl_picker_range_header_selected">%1$s – %2$s</string>
    <string name="mtrl_picker_range_header_title">Zeitraum auswählen</string>
    <string name="mtrl_picker_range_header_unselected">Startdatum – Enddatum</string>
    <string name="mtrl_picker_save">Speichern</string>
    <string name="mtrl_picker_text_input_date_hint">Datum</string>
    <string name="mtrl_picker_text_input_date_range_end_hint">Enddatum</string>
    <string name="mtrl_picker_text_input_date_range_start_hint">Startdatum</string>
    <string name="mtrl_picker_text_input_day_abbr">T</string>
    <string name="mtrl_picker_text_input_month_abbr">M</string>
    <string name="mtrl_picker_text_input_year_abbr">J</string>
    <string name="mtrl_picker_toggle_to_calendar_input_mode">In den Kalendereingabemodus wechseln</string>
    <string name="mtrl_picker_toggle_to_day_selection">Tippen, um zur Tagesauswahl zu wechseln</string>
    <string name="mtrl_picker_toggle_to_text_input_mode">In den Texteingabemodus wechseln</string>
    <string name="mtrl_picker_toggle_to_year_selection">Tippen, um zur Jahresauswahl zu wechseln</string>
    <string msgid="6301633601645100427" name="nav_app_bar_navigate_up_description">"Nach oben"</string>
    <string msgid="7456070600745802113" name="nav_app_bar_open_drawer_description">"Navigationsleiste öffnen"</string>
    <string name="password_toggle_content_description">Passwort anzeigen</string>
    <string msgid="6264217191555673260" name="search_menu_title">"Suche"</string>
    <string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string>
    <string name="zxing_app_name">Barcode Scanner</string>
    <string name="zxing_button_ok">OK</string>
    <string name="zxing_msg_camera_framework_bug">Mit der Android-Kamera ist leider ein Fehler aufgetreten. Es könnte sein, dass Sie das Gerät neu starten müssen.</string>
    <string name="zxing_msg_default_status">Positionieren Sie den Barcode innerhalb des Rechteckes.</string>
</resources>