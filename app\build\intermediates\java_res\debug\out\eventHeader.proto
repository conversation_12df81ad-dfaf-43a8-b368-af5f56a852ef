// Copyright 2018 Amazon.com, Inc. or its affiliates. All Rights Reserved.
//
// Licensed under the Amazon Software License (the "License"). You may not use this file except in
// compliance with the License. A copy of the License is located at
//
//    http://aws.amazon.com/asl/
//
// or in the "license" file accompanying this file. This file is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, express or implied. See the License for the specific
// language governing permissions and limitations under the License.

syntax = "proto3";

package header;

option java_package = "com.amazon.proto.avs.v20160207.header";

option java_outer_classname = "EventHeader";

message EventHeaderProto {
	string namespace = 1;
	string name = 2;
	string messageId = 3;
}
