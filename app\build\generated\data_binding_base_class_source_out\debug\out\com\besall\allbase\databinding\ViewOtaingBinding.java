// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import com.besall.allbase.common.utils.CircleProgressView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ViewOtaingBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final LinearLayout baseView0;

  @NonNull
  public final LinearLayout baseView1;

  @NonNull
  public final LinearLayout baseView2;

  @NonNull
  public final LinearLayout bgview0;

  @NonNull
  public final LinearLayout bgview1;

  @NonNull
  public final CircleProgressView tasksView;

  @NonNull
  public final TextView textName0;

  @NonNull
  public final TextView textName01;

  @NonNull
  public final TextView textName1;

  @NonNull
  public final TextView textName11;

  @NonNull
  public final TextView textName2;

  @NonNull
  public final TextView textName3;

  @NonNull
  public final TextView textName4;

  @NonNull
  public final TextView textName5;

  @NonNull
  public final TextView textName6;

  @NonNull
  public final TextView textName7;

  @NonNull
  public final TextView textName8;

  @NonNull
  public final TextView textName9;

  @NonNull
  public final TextView textPercent0;

  @NonNull
  public final TextView textPercent01;

  @NonNull
  public final TextView textPercent1;

  @NonNull
  public final TextView textPercent11;

  @NonNull
  public final TextView textPercent2;

  @NonNull
  public final TextView textPercent3;

  @NonNull
  public final TextView textPercent4;

  @NonNull
  public final TextView textPercent5;

  @NonNull
  public final TextView textPercent6;

  @NonNull
  public final TextView textPercent7;

  @NonNull
  public final TextView textPercent8;

  @NonNull
  public final TextView textPercent9;

  @NonNull
  public final LinearLayout textView0;

  @NonNull
  public final LinearLayout textView1;

  @NonNull
  public final LinearLayout textView2;

  @NonNull
  public final LinearLayout textView3;

  @NonNull
  public final LinearLayout textView4;

  @NonNull
  public final LinearLayout textView5;

  @NonNull
  public final LinearLayout textView6;

  @NonNull
  public final LinearLayout textView7;

  @NonNull
  public final LinearLayout textView8;

  @NonNull
  public final LinearLayout textView9;

  @NonNull
  public final LinearLayout viewOtaingBg;

  @NonNull
  public final LinearLayout viewOtaoverBg;

  @NonNull
  public final ImageView viewOtaoverImage;

  @NonNull
  public final TextView viewOtaoverText;

  private ViewOtaingBinding(@NonNull ConstraintLayout rootView, @NonNull LinearLayout baseView0,
      @NonNull LinearLayout baseView1, @NonNull LinearLayout baseView2,
      @NonNull LinearLayout bgview0, @NonNull LinearLayout bgview1,
      @NonNull CircleProgressView tasksView, @NonNull TextView textName0,
      @NonNull TextView textName01, @NonNull TextView textName1, @NonNull TextView textName11,
      @NonNull TextView textName2, @NonNull TextView textName3, @NonNull TextView textName4,
      @NonNull TextView textName5, @NonNull TextView textName6, @NonNull TextView textName7,
      @NonNull TextView textName8, @NonNull TextView textName9, @NonNull TextView textPercent0,
      @NonNull TextView textPercent01, @NonNull TextView textPercent1,
      @NonNull TextView textPercent11, @NonNull TextView textPercent2,
      @NonNull TextView textPercent3, @NonNull TextView textPercent4,
      @NonNull TextView textPercent5, @NonNull TextView textPercent6,
      @NonNull TextView textPercent7, @NonNull TextView textPercent8,
      @NonNull TextView textPercent9, @NonNull LinearLayout textView0,
      @NonNull LinearLayout textView1, @NonNull LinearLayout textView2,
      @NonNull LinearLayout textView3, @NonNull LinearLayout textView4,
      @NonNull LinearLayout textView5, @NonNull LinearLayout textView6,
      @NonNull LinearLayout textView7, @NonNull LinearLayout textView8,
      @NonNull LinearLayout textView9, @NonNull LinearLayout viewOtaingBg,
      @NonNull LinearLayout viewOtaoverBg, @NonNull ImageView viewOtaoverImage,
      @NonNull TextView viewOtaoverText) {
    this.rootView = rootView;
    this.baseView0 = baseView0;
    this.baseView1 = baseView1;
    this.baseView2 = baseView2;
    this.bgview0 = bgview0;
    this.bgview1 = bgview1;
    this.tasksView = tasksView;
    this.textName0 = textName0;
    this.textName01 = textName01;
    this.textName1 = textName1;
    this.textName11 = textName11;
    this.textName2 = textName2;
    this.textName3 = textName3;
    this.textName4 = textName4;
    this.textName5 = textName5;
    this.textName6 = textName6;
    this.textName7 = textName7;
    this.textName8 = textName8;
    this.textName9 = textName9;
    this.textPercent0 = textPercent0;
    this.textPercent01 = textPercent01;
    this.textPercent1 = textPercent1;
    this.textPercent11 = textPercent11;
    this.textPercent2 = textPercent2;
    this.textPercent3 = textPercent3;
    this.textPercent4 = textPercent4;
    this.textPercent5 = textPercent5;
    this.textPercent6 = textPercent6;
    this.textPercent7 = textPercent7;
    this.textPercent8 = textPercent8;
    this.textPercent9 = textPercent9;
    this.textView0 = textView0;
    this.textView1 = textView1;
    this.textView2 = textView2;
    this.textView3 = textView3;
    this.textView4 = textView4;
    this.textView5 = textView5;
    this.textView6 = textView6;
    this.textView7 = textView7;
    this.textView8 = textView8;
    this.textView9 = textView9;
    this.viewOtaingBg = viewOtaingBg;
    this.viewOtaoverBg = viewOtaoverBg;
    this.viewOtaoverImage = viewOtaoverImage;
    this.viewOtaoverText = viewOtaoverText;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ViewOtaingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ViewOtaingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.view_otaing, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ViewOtaingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.base_view0;
      LinearLayout baseView0 = rootView.findViewById(id);
      if (baseView0 == null) {
        break missingId;
      }

      id = R.id.base_view1;
      LinearLayout baseView1 = rootView.findViewById(id);
      if (baseView1 == null) {
        break missingId;
      }

      id = R.id.base_view2;
      LinearLayout baseView2 = rootView.findViewById(id);
      if (baseView2 == null) {
        break missingId;
      }

      id = R.id.bgview0;
      LinearLayout bgview0 = rootView.findViewById(id);
      if (bgview0 == null) {
        break missingId;
      }

      id = R.id.bgview1;
      LinearLayout bgview1 = rootView.findViewById(id);
      if (bgview1 == null) {
        break missingId;
      }

      id = R.id.tasks_view;
      CircleProgressView tasksView = rootView.findViewById(id);
      if (tasksView == null) {
        break missingId;
      }

      id = R.id.text_name0;
      TextView textName0 = rootView.findViewById(id);
      if (textName0 == null) {
        break missingId;
      }

      id = R.id.text_name_0;
      TextView textName01 = rootView.findViewById(id);
      if (textName01 == null) {
        break missingId;
      }

      id = R.id.text_name1;
      TextView textName1 = rootView.findViewById(id);
      if (textName1 == null) {
        break missingId;
      }

      id = R.id.text_name_1;
      TextView textName11 = rootView.findViewById(id);
      if (textName11 == null) {
        break missingId;
      }

      id = R.id.text_name_2;
      TextView textName2 = rootView.findViewById(id);
      if (textName2 == null) {
        break missingId;
      }

      id = R.id.text_name_3;
      TextView textName3 = rootView.findViewById(id);
      if (textName3 == null) {
        break missingId;
      }

      id = R.id.text_name_4;
      TextView textName4 = rootView.findViewById(id);
      if (textName4 == null) {
        break missingId;
      }

      id = R.id.text_name_5;
      TextView textName5 = rootView.findViewById(id);
      if (textName5 == null) {
        break missingId;
      }

      id = R.id.text_name_6;
      TextView textName6 = rootView.findViewById(id);
      if (textName6 == null) {
        break missingId;
      }

      id = R.id.text_name_7;
      TextView textName7 = rootView.findViewById(id);
      if (textName7 == null) {
        break missingId;
      }

      id = R.id.text_name_8;
      TextView textName8 = rootView.findViewById(id);
      if (textName8 == null) {
        break missingId;
      }

      id = R.id.text_name_9;
      TextView textName9 = rootView.findViewById(id);
      if (textName9 == null) {
        break missingId;
      }

      id = R.id.text_percent0;
      TextView textPercent0 = rootView.findViewById(id);
      if (textPercent0 == null) {
        break missingId;
      }

      id = R.id.text_percent_0;
      TextView textPercent01 = rootView.findViewById(id);
      if (textPercent01 == null) {
        break missingId;
      }

      id = R.id.text_percent1;
      TextView textPercent1 = rootView.findViewById(id);
      if (textPercent1 == null) {
        break missingId;
      }

      id = R.id.text_percent_1;
      TextView textPercent11 = rootView.findViewById(id);
      if (textPercent11 == null) {
        break missingId;
      }

      id = R.id.text_percent_2;
      TextView textPercent2 = rootView.findViewById(id);
      if (textPercent2 == null) {
        break missingId;
      }

      id = R.id.text_percent_3;
      TextView textPercent3 = rootView.findViewById(id);
      if (textPercent3 == null) {
        break missingId;
      }

      id = R.id.text_percent_4;
      TextView textPercent4 = rootView.findViewById(id);
      if (textPercent4 == null) {
        break missingId;
      }

      id = R.id.text_percent_5;
      TextView textPercent5 = rootView.findViewById(id);
      if (textPercent5 == null) {
        break missingId;
      }

      id = R.id.text_percent_6;
      TextView textPercent6 = rootView.findViewById(id);
      if (textPercent6 == null) {
        break missingId;
      }

      id = R.id.text_percent_7;
      TextView textPercent7 = rootView.findViewById(id);
      if (textPercent7 == null) {
        break missingId;
      }

      id = R.id.text_percent_8;
      TextView textPercent8 = rootView.findViewById(id);
      if (textPercent8 == null) {
        break missingId;
      }

      id = R.id.text_percent_9;
      TextView textPercent9 = rootView.findViewById(id);
      if (textPercent9 == null) {
        break missingId;
      }

      id = R.id.text_view_0;
      LinearLayout textView0 = rootView.findViewById(id);
      if (textView0 == null) {
        break missingId;
      }

      id = R.id.text_view_1;
      LinearLayout textView1 = rootView.findViewById(id);
      if (textView1 == null) {
        break missingId;
      }

      id = R.id.text_view_2;
      LinearLayout textView2 = rootView.findViewById(id);
      if (textView2 == null) {
        break missingId;
      }

      id = R.id.text_view_3;
      LinearLayout textView3 = rootView.findViewById(id);
      if (textView3 == null) {
        break missingId;
      }

      id = R.id.text_view_4;
      LinearLayout textView4 = rootView.findViewById(id);
      if (textView4 == null) {
        break missingId;
      }

      id = R.id.text_view_5;
      LinearLayout textView5 = rootView.findViewById(id);
      if (textView5 == null) {
        break missingId;
      }

      id = R.id.text_view_6;
      LinearLayout textView6 = rootView.findViewById(id);
      if (textView6 == null) {
        break missingId;
      }

      id = R.id.text_view_7;
      LinearLayout textView7 = rootView.findViewById(id);
      if (textView7 == null) {
        break missingId;
      }

      id = R.id.text_view_8;
      LinearLayout textView8 = rootView.findViewById(id);
      if (textView8 == null) {
        break missingId;
      }

      id = R.id.text_view_9;
      LinearLayout textView9 = rootView.findViewById(id);
      if (textView9 == null) {
        break missingId;
      }

      id = R.id.view_otaing_bg;
      LinearLayout viewOtaingBg = rootView.findViewById(id);
      if (viewOtaingBg == null) {
        break missingId;
      }

      id = R.id.view_otaover_bg;
      LinearLayout viewOtaoverBg = rootView.findViewById(id);
      if (viewOtaoverBg == null) {
        break missingId;
      }

      id = R.id.view_otaover_image;
      ImageView viewOtaoverImage = rootView.findViewById(id);
      if (viewOtaoverImage == null) {
        break missingId;
      }

      id = R.id.view_otaover_text;
      TextView viewOtaoverText = rootView.findViewById(id);
      if (viewOtaoverText == null) {
        break missingId;
      }

      return new ViewOtaingBinding((ConstraintLayout) rootView, baseView0, baseView1, baseView2,
          bgview0, bgview1, tasksView, textName0, textName01, textName1, textName11, textName2,
          textName3, textName4, textName5, textName6, textName7, textName8, textName9, textPercent0,
          textPercent01, textPercent1, textPercent11, textPercent2, textPercent3, textPercent4,
          textPercent5, textPercent6, textPercent7, textPercent8, textPercent9, textView0,
          textView1, textView2, textView3, textView4, textView5, textView6, textView7, textView8,
          textView9, viewOtaingBg, viewOtaoverBg, viewOtaoverImage, viewOtaoverText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
