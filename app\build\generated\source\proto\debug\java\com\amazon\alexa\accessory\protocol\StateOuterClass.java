// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: state.proto

package com.amazon.alexa.accessory.protocol;

public final class StateOuterClass {
  private StateOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface StateOrBuilder extends
      // @@protoc_insertion_point(interface_extends:State)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 feature = 1;</code>
     * @return The feature.
     */
    int getFeature();

    /**
     * <code>bool boolean = 2;</code>
     * @return Whether the boolean field is set.
     */
    boolean hasBoolean();
    /**
     * <code>bool boolean = 2;</code>
     * @return The boolean.
     */
    boolean getBoolean();

    /**
     * <code>uint32 integer = 3;</code>
     * @return Whether the integer field is set.
     */
    boolean hasInteger();
    /**
     * <code>uint32 integer = 3;</code>
     * @return The integer.
     */
    int getInteger();

    public com.amazon.alexa.accessory.protocol.StateOuterClass.State.ValueCase getValueCase();
  }
  /**
   * Protobuf type {@code State}
   */
  public static final class State extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:State)
      StateOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use State.newBuilder() to construct.
    private State(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private State() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new State();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.StateOuterClass.internal_static_State_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.StateOuterClass.internal_static_State_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.StateOuterClass.State.class, com.amazon.alexa.accessory.protocol.StateOuterClass.State.Builder.class);
    }

    private int valueCase_ = 0;
    private java.lang.Object value_;
    public enum ValueCase
        implements com.google.protobuf.Internal.EnumLite,
            com.google.protobuf.AbstractMessage.InternalOneOfEnum {
      BOOLEAN(2),
      INTEGER(3),
      VALUE_NOT_SET(0);
      private final int value;
      private ValueCase(int value) {
        this.value = value;
      }
      /**
       * @param value The number of the enum to look for.
       * @return The enum associated with the given number.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static ValueCase valueOf(int value) {
        return forNumber(value);
      }

      public static ValueCase forNumber(int value) {
        switch (value) {
          case 2: return BOOLEAN;
          case 3: return INTEGER;
          case 0: return VALUE_NOT_SET;
          default: return null;
        }
      }
      public int getNumber() {
        return this.value;
      }
    };

    public ValueCase
    getValueCase() {
      return ValueCase.forNumber(
          valueCase_);
    }

    public static final int FEATURE_FIELD_NUMBER = 1;
    private int feature_;
    /**
     * <code>uint32 feature = 1;</code>
     * @return The feature.
     */
    @java.lang.Override
    public int getFeature() {
      return feature_;
    }

    public static final int BOOLEAN_FIELD_NUMBER = 2;
    /**
     * <code>bool boolean = 2;</code>
     * @return Whether the boolean field is set.
     */
    @java.lang.Override
    public boolean hasBoolean() {
      return valueCase_ == 2;
    }
    /**
     * <code>bool boolean = 2;</code>
     * @return The boolean.
     */
    @java.lang.Override
    public boolean getBoolean() {
      if (valueCase_ == 2) {
        return (java.lang.Boolean) value_;
      }
      return false;
    }

    public static final int INTEGER_FIELD_NUMBER = 3;
    /**
     * <code>uint32 integer = 3;</code>
     * @return Whether the integer field is set.
     */
    @java.lang.Override
    public boolean hasInteger() {
      return valueCase_ == 3;
    }
    /**
     * <code>uint32 integer = 3;</code>
     * @return The integer.
     */
    @java.lang.Override
    public int getInteger() {
      if (valueCase_ == 3) {
        return (java.lang.Integer) value_;
      }
      return 0;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (feature_ != 0) {
        output.writeUInt32(1, feature_);
      }
      if (valueCase_ == 2) {
        output.writeBool(
            2, (boolean)((java.lang.Boolean) value_));
      }
      if (valueCase_ == 3) {
        output.writeUInt32(
            3, (int)((java.lang.Integer) value_));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (feature_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, feature_);
      }
      if (valueCase_ == 2) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(
              2, (boolean)((java.lang.Boolean) value_));
      }
      if (valueCase_ == 3) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(
              3, (int)((java.lang.Integer) value_));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.StateOuterClass.State)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.StateOuterClass.State other = (com.amazon.alexa.accessory.protocol.StateOuterClass.State) obj;

      if (getFeature()
          != other.getFeature()) return false;
      if (!getValueCase().equals(other.getValueCase())) return false;
      switch (valueCase_) {
        case 2:
          if (getBoolean()
              != other.getBoolean()) return false;
          break;
        case 3:
          if (getInteger()
              != other.getInteger()) return false;
          break;
        case 0:
        default:
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + FEATURE_FIELD_NUMBER;
      hash = (53 * hash) + getFeature();
      switch (valueCase_) {
        case 2:
          hash = (37 * hash) + BOOLEAN_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
              getBoolean());
          break;
        case 3:
          hash = (37 * hash) + INTEGER_FIELD_NUMBER;
          hash = (53 * hash) + getInteger();
          break;
        case 0:
        default:
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.StateOuterClass.State parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.State parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.State parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.State parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.State parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.State parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.State parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.State parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.State parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.State parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.State parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.State parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.StateOuterClass.State prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code State}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:State)
        com.amazon.alexa.accessory.protocol.StateOuterClass.StateOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.StateOuterClass.internal_static_State_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.StateOuterClass.internal_static_State_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.StateOuterClass.State.class, com.amazon.alexa.accessory.protocol.StateOuterClass.State.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.StateOuterClass.State.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        feature_ = 0;

        valueCase_ = 0;
        value_ = null;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.StateOuterClass.internal_static_State_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.StateOuterClass.State getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.StateOuterClass.State.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.StateOuterClass.State build() {
        com.amazon.alexa.accessory.protocol.StateOuterClass.State result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.StateOuterClass.State buildPartial() {
        com.amazon.alexa.accessory.protocol.StateOuterClass.State result = new com.amazon.alexa.accessory.protocol.StateOuterClass.State(this);
        result.feature_ = feature_;
        if (valueCase_ == 2) {
          result.value_ = value_;
        }
        if (valueCase_ == 3) {
          result.value_ = value_;
        }
        result.valueCase_ = valueCase_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.StateOuterClass.State) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.StateOuterClass.State)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.StateOuterClass.State other) {
        if (other == com.amazon.alexa.accessory.protocol.StateOuterClass.State.getDefaultInstance()) return this;
        if (other.getFeature() != 0) {
          setFeature(other.getFeature());
        }
        switch (other.getValueCase()) {
          case BOOLEAN: {
            setBoolean(other.getBoolean());
            break;
          }
          case INTEGER: {
            setInteger(other.getInteger());
            break;
          }
          case VALUE_NOT_SET: {
            break;
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                feature_ = input.readUInt32();

                break;
              } // case 8
              case 16: {
                value_ = input.readBool();
                valueCase_ = 2;
                break;
              } // case 16
              case 24: {
                value_ = input.readUInt32();
                valueCase_ = 3;
                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int valueCase_ = 0;
      private java.lang.Object value_;
      public ValueCase
          getValueCase() {
        return ValueCase.forNumber(
            valueCase_);
      }

      public Builder clearValue() {
        valueCase_ = 0;
        value_ = null;
        onChanged();
        return this;
      }


      private int feature_ ;
      /**
       * <code>uint32 feature = 1;</code>
       * @return The feature.
       */
      @java.lang.Override
      public int getFeature() {
        return feature_;
      }
      /**
       * <code>uint32 feature = 1;</code>
       * @param value The feature to set.
       * @return This builder for chaining.
       */
      public Builder setFeature(int value) {
        
        feature_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 feature = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearFeature() {
        
        feature_ = 0;
        onChanged();
        return this;
      }

      /**
       * <code>bool boolean = 2;</code>
       * @return Whether the boolean field is set.
       */
      public boolean hasBoolean() {
        return valueCase_ == 2;
      }
      /**
       * <code>bool boolean = 2;</code>
       * @return The boolean.
       */
      public boolean getBoolean() {
        if (valueCase_ == 2) {
          return (java.lang.Boolean) value_;
        }
        return false;
      }
      /**
       * <code>bool boolean = 2;</code>
       * @param value The boolean to set.
       * @return This builder for chaining.
       */
      public Builder setBoolean(boolean value) {
        valueCase_ = 2;
        value_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>bool boolean = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearBoolean() {
        if (valueCase_ == 2) {
          valueCase_ = 0;
          value_ = null;
          onChanged();
        }
        return this;
      }

      /**
       * <code>uint32 integer = 3;</code>
       * @return Whether the integer field is set.
       */
      public boolean hasInteger() {
        return valueCase_ == 3;
      }
      /**
       * <code>uint32 integer = 3;</code>
       * @return The integer.
       */
      public int getInteger() {
        if (valueCase_ == 3) {
          return (java.lang.Integer) value_;
        }
        return 0;
      }
      /**
       * <code>uint32 integer = 3;</code>
       * @param value The integer to set.
       * @return This builder for chaining.
       */
      public Builder setInteger(int value) {
        valueCase_ = 3;
        value_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 integer = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearInteger() {
        if (valueCase_ == 3) {
          valueCase_ = 0;
          value_ = null;
          onChanged();
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:State)
    }

    // @@protoc_insertion_point(class_scope:State)
    private static final com.amazon.alexa.accessory.protocol.StateOuterClass.State DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.StateOuterClass.State();
    }

    public static com.amazon.alexa.accessory.protocol.StateOuterClass.State getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<State>
        PARSER = new com.google.protobuf.AbstractParser<State>() {
      @java.lang.Override
      public State parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<State> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<State> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.StateOuterClass.State getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetStateOrBuilder extends
      // @@protoc_insertion_point(interface_extends:GetState)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 feature = 1;</code>
     * @return The feature.
     */
    int getFeature();
  }
  /**
   * Protobuf type {@code GetState}
   */
  public static final class GetState extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:GetState)
      GetStateOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetState.newBuilder() to construct.
    private GetState(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetState() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetState();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.StateOuterClass.internal_static_GetState_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.StateOuterClass.internal_static_GetState_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.StateOuterClass.GetState.class, com.amazon.alexa.accessory.protocol.StateOuterClass.GetState.Builder.class);
    }

    public static final int FEATURE_FIELD_NUMBER = 1;
    private int feature_;
    /**
     * <code>uint32 feature = 1;</code>
     * @return The feature.
     */
    @java.lang.Override
    public int getFeature() {
      return feature_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (feature_ != 0) {
        output.writeUInt32(1, feature_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (feature_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, feature_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.StateOuterClass.GetState)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.StateOuterClass.GetState other = (com.amazon.alexa.accessory.protocol.StateOuterClass.GetState) obj;

      if (getFeature()
          != other.getFeature()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + FEATURE_FIELD_NUMBER;
      hash = (53 * hash) + getFeature();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.StateOuterClass.GetState parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.GetState parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.GetState parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.GetState parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.GetState parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.GetState parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.GetState parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.GetState parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.GetState parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.GetState parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.GetState parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.GetState parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.StateOuterClass.GetState prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code GetState}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:GetState)
        com.amazon.alexa.accessory.protocol.StateOuterClass.GetStateOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.StateOuterClass.internal_static_GetState_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.StateOuterClass.internal_static_GetState_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.StateOuterClass.GetState.class, com.amazon.alexa.accessory.protocol.StateOuterClass.GetState.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.StateOuterClass.GetState.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        feature_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.StateOuterClass.internal_static_GetState_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.StateOuterClass.GetState getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.StateOuterClass.GetState.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.StateOuterClass.GetState build() {
        com.amazon.alexa.accessory.protocol.StateOuterClass.GetState result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.StateOuterClass.GetState buildPartial() {
        com.amazon.alexa.accessory.protocol.StateOuterClass.GetState result = new com.amazon.alexa.accessory.protocol.StateOuterClass.GetState(this);
        result.feature_ = feature_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.StateOuterClass.GetState) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.StateOuterClass.GetState)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.StateOuterClass.GetState other) {
        if (other == com.amazon.alexa.accessory.protocol.StateOuterClass.GetState.getDefaultInstance()) return this;
        if (other.getFeature() != 0) {
          setFeature(other.getFeature());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                feature_ = input.readUInt32();

                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private int feature_ ;
      /**
       * <code>uint32 feature = 1;</code>
       * @return The feature.
       */
      @java.lang.Override
      public int getFeature() {
        return feature_;
      }
      /**
       * <code>uint32 feature = 1;</code>
       * @param value The feature to set.
       * @return This builder for chaining.
       */
      public Builder setFeature(int value) {
        
        feature_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 feature = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearFeature() {
        
        feature_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:GetState)
    }

    // @@protoc_insertion_point(class_scope:GetState)
    private static final com.amazon.alexa.accessory.protocol.StateOuterClass.GetState DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.StateOuterClass.GetState();
    }

    public static com.amazon.alexa.accessory.protocol.StateOuterClass.GetState getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GetState>
        PARSER = new com.google.protobuf.AbstractParser<GetState>() {
      @java.lang.Override
      public GetState parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<GetState> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetState> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.StateOuterClass.GetState getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SetStateOrBuilder extends
      // @@protoc_insertion_point(interface_extends:SetState)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.State state = 1;</code>
     * @return Whether the state field is set.
     */
    boolean hasState();
    /**
     * <code>.State state = 1;</code>
     * @return The state.
     */
    com.amazon.alexa.accessory.protocol.StateOuterClass.State getState();
    /**
     * <code>.State state = 1;</code>
     */
    com.amazon.alexa.accessory.protocol.StateOuterClass.StateOrBuilder getStateOrBuilder();
  }
  /**
   * Protobuf type {@code SetState}
   */
  public static final class SetState extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:SetState)
      SetStateOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SetState.newBuilder() to construct.
    private SetState(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SetState() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SetState();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.StateOuterClass.internal_static_SetState_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.StateOuterClass.internal_static_SetState_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.StateOuterClass.SetState.class, com.amazon.alexa.accessory.protocol.StateOuterClass.SetState.Builder.class);
    }

    public static final int STATE_FIELD_NUMBER = 1;
    private com.amazon.alexa.accessory.protocol.StateOuterClass.State state_;
    /**
     * <code>.State state = 1;</code>
     * @return Whether the state field is set.
     */
    @java.lang.Override
    public boolean hasState() {
      return state_ != null;
    }
    /**
     * <code>.State state = 1;</code>
     * @return The state.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.StateOuterClass.State getState() {
      return state_ == null ? com.amazon.alexa.accessory.protocol.StateOuterClass.State.getDefaultInstance() : state_;
    }
    /**
     * <code>.State state = 1;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.StateOuterClass.StateOrBuilder getStateOrBuilder() {
      return getState();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (state_ != null) {
        output.writeMessage(1, getState());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (state_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getState());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.StateOuterClass.SetState)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.StateOuterClass.SetState other = (com.amazon.alexa.accessory.protocol.StateOuterClass.SetState) obj;

      if (hasState() != other.hasState()) return false;
      if (hasState()) {
        if (!getState()
            .equals(other.getState())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasState()) {
        hash = (37 * hash) + STATE_FIELD_NUMBER;
        hash = (53 * hash) + getState().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SetState parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SetState parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SetState parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SetState parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SetState parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SetState parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SetState parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SetState parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SetState parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SetState parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SetState parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SetState parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.StateOuterClass.SetState prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code SetState}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:SetState)
        com.amazon.alexa.accessory.protocol.StateOuterClass.SetStateOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.StateOuterClass.internal_static_SetState_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.StateOuterClass.internal_static_SetState_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.StateOuterClass.SetState.class, com.amazon.alexa.accessory.protocol.StateOuterClass.SetState.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.StateOuterClass.SetState.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (stateBuilder_ == null) {
          state_ = null;
        } else {
          state_ = null;
          stateBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.StateOuterClass.internal_static_SetState_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.StateOuterClass.SetState getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.StateOuterClass.SetState.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.StateOuterClass.SetState build() {
        com.amazon.alexa.accessory.protocol.StateOuterClass.SetState result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.StateOuterClass.SetState buildPartial() {
        com.amazon.alexa.accessory.protocol.StateOuterClass.SetState result = new com.amazon.alexa.accessory.protocol.StateOuterClass.SetState(this);
        if (stateBuilder_ == null) {
          result.state_ = state_;
        } else {
          result.state_ = stateBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.StateOuterClass.SetState) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.StateOuterClass.SetState)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.StateOuterClass.SetState other) {
        if (other == com.amazon.alexa.accessory.protocol.StateOuterClass.SetState.getDefaultInstance()) return this;
        if (other.hasState()) {
          mergeState(other.getState());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getStateFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private com.amazon.alexa.accessory.protocol.StateOuterClass.State state_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.StateOuterClass.State, com.amazon.alexa.accessory.protocol.StateOuterClass.State.Builder, com.amazon.alexa.accessory.protocol.StateOuterClass.StateOrBuilder> stateBuilder_;
      /**
       * <code>.State state = 1;</code>
       * @return Whether the state field is set.
       */
      public boolean hasState() {
        return stateBuilder_ != null || state_ != null;
      }
      /**
       * <code>.State state = 1;</code>
       * @return The state.
       */
      public com.amazon.alexa.accessory.protocol.StateOuterClass.State getState() {
        if (stateBuilder_ == null) {
          return state_ == null ? com.amazon.alexa.accessory.protocol.StateOuterClass.State.getDefaultInstance() : state_;
        } else {
          return stateBuilder_.getMessage();
        }
      }
      /**
       * <code>.State state = 1;</code>
       */
      public Builder setState(com.amazon.alexa.accessory.protocol.StateOuterClass.State value) {
        if (stateBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          state_ = value;
          onChanged();
        } else {
          stateBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.State state = 1;</code>
       */
      public Builder setState(
          com.amazon.alexa.accessory.protocol.StateOuterClass.State.Builder builderForValue) {
        if (stateBuilder_ == null) {
          state_ = builderForValue.build();
          onChanged();
        } else {
          stateBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.State state = 1;</code>
       */
      public Builder mergeState(com.amazon.alexa.accessory.protocol.StateOuterClass.State value) {
        if (stateBuilder_ == null) {
          if (state_ != null) {
            state_ =
              com.amazon.alexa.accessory.protocol.StateOuterClass.State.newBuilder(state_).mergeFrom(value).buildPartial();
          } else {
            state_ = value;
          }
          onChanged();
        } else {
          stateBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.State state = 1;</code>
       */
      public Builder clearState() {
        if (stateBuilder_ == null) {
          state_ = null;
          onChanged();
        } else {
          state_ = null;
          stateBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.State state = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.StateOuterClass.State.Builder getStateBuilder() {
        
        onChanged();
        return getStateFieldBuilder().getBuilder();
      }
      /**
       * <code>.State state = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.StateOuterClass.StateOrBuilder getStateOrBuilder() {
        if (stateBuilder_ != null) {
          return stateBuilder_.getMessageOrBuilder();
        } else {
          return state_ == null ?
              com.amazon.alexa.accessory.protocol.StateOuterClass.State.getDefaultInstance() : state_;
        }
      }
      /**
       * <code>.State state = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.StateOuterClass.State, com.amazon.alexa.accessory.protocol.StateOuterClass.State.Builder, com.amazon.alexa.accessory.protocol.StateOuterClass.StateOrBuilder> 
          getStateFieldBuilder() {
        if (stateBuilder_ == null) {
          stateBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.StateOuterClass.State, com.amazon.alexa.accessory.protocol.StateOuterClass.State.Builder, com.amazon.alexa.accessory.protocol.StateOuterClass.StateOrBuilder>(
                  getState(),
                  getParentForChildren(),
                  isClean());
          state_ = null;
        }
        return stateBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:SetState)
    }

    // @@protoc_insertion_point(class_scope:SetState)
    private static final com.amazon.alexa.accessory.protocol.StateOuterClass.SetState DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.StateOuterClass.SetState();
    }

    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SetState getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SetState>
        PARSER = new com.google.protobuf.AbstractParser<SetState>() {
      @java.lang.Override
      public SetState parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SetState> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SetState> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.StateOuterClass.SetState getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SynchronizeStateOrBuilder extends
      // @@protoc_insertion_point(interface_extends:SynchronizeState)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.State state = 1;</code>
     * @return Whether the state field is set.
     */
    boolean hasState();
    /**
     * <code>.State state = 1;</code>
     * @return The state.
     */
    com.amazon.alexa.accessory.protocol.StateOuterClass.State getState();
    /**
     * <code>.State state = 1;</code>
     */
    com.amazon.alexa.accessory.protocol.StateOuterClass.StateOrBuilder getStateOrBuilder();
  }
  /**
   * Protobuf type {@code SynchronizeState}
   */
  public static final class SynchronizeState extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:SynchronizeState)
      SynchronizeStateOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SynchronizeState.newBuilder() to construct.
    private SynchronizeState(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SynchronizeState() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SynchronizeState();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.StateOuterClass.internal_static_SynchronizeState_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.StateOuterClass.internal_static_SynchronizeState_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState.class, com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState.Builder.class);
    }

    public static final int STATE_FIELD_NUMBER = 1;
    private com.amazon.alexa.accessory.protocol.StateOuterClass.State state_;
    /**
     * <code>.State state = 1;</code>
     * @return Whether the state field is set.
     */
    @java.lang.Override
    public boolean hasState() {
      return state_ != null;
    }
    /**
     * <code>.State state = 1;</code>
     * @return The state.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.StateOuterClass.State getState() {
      return state_ == null ? com.amazon.alexa.accessory.protocol.StateOuterClass.State.getDefaultInstance() : state_;
    }
    /**
     * <code>.State state = 1;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.StateOuterClass.StateOrBuilder getStateOrBuilder() {
      return getState();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (state_ != null) {
        output.writeMessage(1, getState());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (state_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getState());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState other = (com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState) obj;

      if (hasState() != other.hasState()) return false;
      if (hasState()) {
        if (!getState()
            .equals(other.getState())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasState()) {
        hash = (37 * hash) + STATE_FIELD_NUMBER;
        hash = (53 * hash) + getState().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code SynchronizeState}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:SynchronizeState)
        com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeStateOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.StateOuterClass.internal_static_SynchronizeState_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.StateOuterClass.internal_static_SynchronizeState_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState.class, com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (stateBuilder_ == null) {
          state_ = null;
        } else {
          state_ = null;
          stateBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.StateOuterClass.internal_static_SynchronizeState_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState build() {
        com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState buildPartial() {
        com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState result = new com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState(this);
        if (stateBuilder_ == null) {
          result.state_ = state_;
        } else {
          result.state_ = stateBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState other) {
        if (other == com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState.getDefaultInstance()) return this;
        if (other.hasState()) {
          mergeState(other.getState());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getStateFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private com.amazon.alexa.accessory.protocol.StateOuterClass.State state_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.StateOuterClass.State, com.amazon.alexa.accessory.protocol.StateOuterClass.State.Builder, com.amazon.alexa.accessory.protocol.StateOuterClass.StateOrBuilder> stateBuilder_;
      /**
       * <code>.State state = 1;</code>
       * @return Whether the state field is set.
       */
      public boolean hasState() {
        return stateBuilder_ != null || state_ != null;
      }
      /**
       * <code>.State state = 1;</code>
       * @return The state.
       */
      public com.amazon.alexa.accessory.protocol.StateOuterClass.State getState() {
        if (stateBuilder_ == null) {
          return state_ == null ? com.amazon.alexa.accessory.protocol.StateOuterClass.State.getDefaultInstance() : state_;
        } else {
          return stateBuilder_.getMessage();
        }
      }
      /**
       * <code>.State state = 1;</code>
       */
      public Builder setState(com.amazon.alexa.accessory.protocol.StateOuterClass.State value) {
        if (stateBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          state_ = value;
          onChanged();
        } else {
          stateBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.State state = 1;</code>
       */
      public Builder setState(
          com.amazon.alexa.accessory.protocol.StateOuterClass.State.Builder builderForValue) {
        if (stateBuilder_ == null) {
          state_ = builderForValue.build();
          onChanged();
        } else {
          stateBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.State state = 1;</code>
       */
      public Builder mergeState(com.amazon.alexa.accessory.protocol.StateOuterClass.State value) {
        if (stateBuilder_ == null) {
          if (state_ != null) {
            state_ =
              com.amazon.alexa.accessory.protocol.StateOuterClass.State.newBuilder(state_).mergeFrom(value).buildPartial();
          } else {
            state_ = value;
          }
          onChanged();
        } else {
          stateBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.State state = 1;</code>
       */
      public Builder clearState() {
        if (stateBuilder_ == null) {
          state_ = null;
          onChanged();
        } else {
          state_ = null;
          stateBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.State state = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.StateOuterClass.State.Builder getStateBuilder() {
        
        onChanged();
        return getStateFieldBuilder().getBuilder();
      }
      /**
       * <code>.State state = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.StateOuterClass.StateOrBuilder getStateOrBuilder() {
        if (stateBuilder_ != null) {
          return stateBuilder_.getMessageOrBuilder();
        } else {
          return state_ == null ?
              com.amazon.alexa.accessory.protocol.StateOuterClass.State.getDefaultInstance() : state_;
        }
      }
      /**
       * <code>.State state = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.StateOuterClass.State, com.amazon.alexa.accessory.protocol.StateOuterClass.State.Builder, com.amazon.alexa.accessory.protocol.StateOuterClass.StateOrBuilder> 
          getStateFieldBuilder() {
        if (stateBuilder_ == null) {
          stateBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.StateOuterClass.State, com.amazon.alexa.accessory.protocol.StateOuterClass.State.Builder, com.amazon.alexa.accessory.protocol.StateOuterClass.StateOrBuilder>(
                  getState(),
                  getParentForChildren(),
                  isClean());
          state_ = null;
        }
        return stateBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:SynchronizeState)
    }

    // @@protoc_insertion_point(class_scope:SynchronizeState)
    private static final com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState();
    }

    public static com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SynchronizeState>
        PARSER = new com.google.protobuf.AbstractParser<SynchronizeState>() {
      @java.lang.Override
      public SynchronizeState parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SynchronizeState> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SynchronizeState> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_State_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_State_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_GetState_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_GetState_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_SetState_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_SetState_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_SynchronizeState_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_SynchronizeState_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\013state.proto\"G\n\005State\022\017\n\007feature\030\001 \001(\r\022" +
      "\021\n\007boolean\030\002 \001(\010H\000\022\021\n\007integer\030\003 \001(\rH\000B\007\n" +
      "\005value\"\033\n\010GetState\022\017\n\007feature\030\001 \001(\r\"!\n\010S" +
      "etState\022\025\n\005state\030\001 \001(\0132\006.State\")\n\020Synchr" +
      "onizeState\022\025\n\005state\030\001 \001(\0132\006.StateB0\n#com" +
      ".amazon.alexa.accessory.protocolH\003\240\001\001\242\002\003" +
      "AACb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_State_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_State_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_State_descriptor,
        new java.lang.String[] { "Feature", "Boolean", "Integer", "Value", });
    internal_static_GetState_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_GetState_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_GetState_descriptor,
        new java.lang.String[] { "Feature", });
    internal_static_SetState_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_SetState_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_SetState_descriptor,
        new java.lang.String[] { "State", });
    internal_static_SynchronizeState_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_SynchronizeState_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_SynchronizeState_descriptor,
        new java.lang.String[] { "State", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
