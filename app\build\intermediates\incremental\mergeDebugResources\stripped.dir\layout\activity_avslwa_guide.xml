<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:tc="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6"
    android:weightSum="1"
    >
    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <include
        android:id="@+id/loginfo"
        layout="@layout/logview"
        android:visibility="gone"/>

    <ImageView
        android:layout_width="288dp"
        android:layout_height="136dp"
        android:layout_marginTop="30dp"
        android:layout_gravity="center"
        android:background="@drawable/amazon_log_guide"/>
    
    <TextView
        android:layout_width="260dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:textSize="15sp"
        android:layout_gravity="center"
        android:gravity="center"
        android:text="@string/amazon_guide"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="150dp">

    </View>

    <Button
        android:id="@+id/avs_guide_next"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="40dp"
        android:layout_marginTop="-150dp"
        android:layout_gravity="center"
        android:background="@drawable/shap_btn"
        android:text="Next"
        android:textAllCaps="false"
        android:textColor="@color/white"
        >
    </Button>


    <TextView
        android:id="@+id/jump_alexa_app_textview"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:layout_marginTop="20dp"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="50dp"
        android:text=""
        android:gravity="center"
        android:lineSpacingExtra="5dp"
        />


</LinearLayout>
