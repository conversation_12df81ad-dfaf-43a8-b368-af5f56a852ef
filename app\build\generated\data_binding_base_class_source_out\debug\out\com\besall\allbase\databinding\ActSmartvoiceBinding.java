// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActSmartvoiceBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView bleName;

  @NonNull
  public final Button connectDevice;

  @NonNull
  public final Button pickDevice;

  @NonNull
  public final Button startRecord;

  @NonNull
  public final Button stopRecord;

  @NonNull
  public final ToolbarBinding tool;

  private ActSmartvoiceBinding(@NonNull LinearLayout rootView, @NonNull TextView bleName,
      @NonNull Button connectDevice, @NonNull Button pickDevice, @NonNull Button startRecord,
      @NonNull Button stopRecord, @NonNull ToolbarBinding tool) {
    this.rootView = rootView;
    this.bleName = bleName;
    this.connectDevice = connectDevice;
    this.pickDevice = pickDevice;
    this.startRecord = startRecord;
    this.stopRecord = stopRecord;
    this.tool = tool;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActSmartvoiceBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActSmartvoiceBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.act_smartvoice, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActSmartvoiceBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ble_name;
      TextView bleName = rootView.findViewById(id);
      if (bleName == null) {
        break missingId;
      }

      id = R.id.connect_device;
      Button connectDevice = rootView.findViewById(id);
      if (connectDevice == null) {
        break missingId;
      }

      id = R.id.pick_device;
      Button pickDevice = rootView.findViewById(id);
      if (pickDevice == null) {
        break missingId;
      }

      id = R.id.start_record;
      Button startRecord = rootView.findViewById(id);
      if (startRecord == null) {
        break missingId;
      }

      id = R.id.stop_record;
      Button stopRecord = rootView.findViewById(id);
      if (stopRecord == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      return new ActSmartvoiceBinding((LinearLayout) rootView, bleName, connectDevice, pickDevice,
          startRecord, stopRecord, binding_tool);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
