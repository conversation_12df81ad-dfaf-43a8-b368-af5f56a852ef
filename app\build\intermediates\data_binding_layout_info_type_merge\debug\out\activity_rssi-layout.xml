<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_rssi" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_rssi.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_rssi_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="770" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/activity_rssi_0" include="toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="12" endOffset="9"/></Target><Target id="@+id/loginfo" tag="layout/activity_rssi_0" include="logview"><Expressions/><location startLine="14" startOffset="4" endLine="17" endOffset="34"/></Target><Target id="@+id/rssiChart" view="com.github.mikephil.charting.charts.LineChart"><Expressions/><location startLine="36" startOffset="8" endLine="40" endOffset="42"/></Target><Target id="@+id/connect_spp" view="Button"><Expressions/><location startLine="42" startOffset="8" endLine="55" endOffset="13"/></Target><Target id="@+id/interval" view="EditText"><Expressions/><location startLine="89" startOffset="16" endLine="100" endOffset="45"/></Target><Target id="@+id/rssi_read_start" view="Button"><Expressions/><location startLine="102" startOffset="16" endLine="112" endOffset="21"/></Target><Target id="@+id/left_call_agc" view="TextView"><Expressions/><location startLine="162" startOffset="16" endLine="171" endOffset="45"/></Target><Target id="@+id/left_call_rssi" view="TextView"><Expressions/><location startLine="173" startOffset="16" endLine="182" endOffset="45"/></Target><Target id="@+id/left_call_min_rssi" view="TextView"><Expressions/><location startLine="190" startOffset="16" endLine="199" endOffset="45"/></Target><Target id="@+id/left_call_max_rssi" view="TextView"><Expressions/><location startLine="201" startOffset="16" endLine="210" endOffset="45"/></Target><Target id="@+id/left_tws_agc" view="TextView"><Expressions/><location startLine="228" startOffset="20" endLine="237" endOffset="48"/></Target><Target id="@+id/left_tws_rssi" view="TextView"><Expressions/><location startLine="239" startOffset="20" endLine="248" endOffset="49"/></Target><Target id="@+id/left_tws_min_rssi" view="TextView"><Expressions/><location startLine="256" startOffset="20" endLine="265" endOffset="49"/></Target><Target id="@+id/left_tws_max_rssi" view="TextView"><Expressions/><location startLine="267" startOffset="20" endLine="276" endOffset="49"/></Target><Target id="@+id/right_call_agc" view="TextView"><Expressions/><location startLine="310" startOffset="20" endLine="319" endOffset="25"/></Target><Target id="@+id/right_call_rssi" view="TextView"><Expressions/><location startLine="321" startOffset="20" endLine="329" endOffset="49"/></Target><Target id="@+id/right_call_min_rssi" view="TextView"><Expressions/><location startLine="337" startOffset="20" endLine="345" endOffset="49"/></Target><Target id="@+id/right_call_max_rssi" view="TextView"><Expressions/><location startLine="347" startOffset="20" endLine="355" endOffset="49"/></Target><Target id="@+id/right_tws_agc" view="TextView"><Expressions/><location startLine="373" startOffset="20" endLine="381" endOffset="48"/></Target><Target id="@+id/right_tws_rssi" view="TextView"><Expressions/><location startLine="383" startOffset="20" endLine="391" endOffset="49"/></Target><Target id="@+id/right_tws_min_rssi" view="TextView"><Expressions/><location startLine="399" startOffset="20" endLine="407" endOffset="49"/></Target><Target id="@+id/right_tws_max_rssi" view="TextView"><Expressions/><location startLine="409" startOffset="20" endLine="417" endOffset="49"/></Target><Target id="@+id/left_packet_loss_rate" view="TextView"><Expressions/><location startLine="430" startOffset="12" endLine="439" endOffset="40"/></Target><Target id="@+id/right_packet_loss_rate" view="TextView"><Expressions/><location startLine="441" startOffset="12" endLine="449" endOffset="41"/></Target><Target id="@+id/left_FA_IDX" view="TextView"><Expressions/><location startLine="458" startOffset="12" endLine="467" endOffset="41"/></Target><Target id="@+id/right_FA_IDX" view="TextView"><Expressions/><location startLine="469" startOffset="12" endLine="477" endOffset="40"/></Target><Target id="@+id/left_call_agc_mirror" view="TextView"><Expressions/><location startLine="525" startOffset="20" endLine="534" endOffset="49"/></Target><Target id="@+id/left_call_rssi_mirror" view="TextView"><Expressions/><location startLine="536" startOffset="20" endLine="545" endOffset="49"/></Target><Target id="@+id/left_call_min_rssi_mirror" view="TextView"><Expressions/><location startLine="553" startOffset="20" endLine="562" endOffset="49"/></Target><Target id="@+id/left_call_max_rssi_mirror" view="TextView"><Expressions/><location startLine="564" startOffset="20" endLine="573" endOffset="48"/></Target><Target id="@+id/right_call_agc_mirror" view="TextView"><Expressions/><location startLine="607" startOffset="20" endLine="615" endOffset="49"/></Target><Target id="@+id/right_call_rssi_mirror" view="TextView"><Expressions/><location startLine="617" startOffset="20" endLine="625" endOffset="48"/></Target><Target id="@+id/right_call_min_rssi_mirror" view="TextView"><Expressions/><location startLine="633" startOffset="20" endLine="641" endOffset="49"/></Target><Target id="@+id/right_call_max_rssi_mirror" view="TextView"><Expressions/><location startLine="643" startOffset="20" endLine="651" endOffset="49"/></Target><Target id="@+id/rssi_read_stop" view="Button"><Expressions/><location startLine="669" startOffset="8" endLine="681" endOffset="13"/></Target><Target id="@+id/spp_stop" view="Button"><Expressions/><location startLine="689" startOffset="8" endLine="701" endOffset="13"/></Target></Targets></Layout>