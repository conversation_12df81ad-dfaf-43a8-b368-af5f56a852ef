<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    xmlns:tc="http://schemas.android.com/apk/res-auto"
    android:layout_height="match_parent"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="30dp"
            tools:ignore="MissingConstraints"
            android:orientation="horizontal"
            >

        </LinearLayout>

        <LinearLayout
            android:id="@+id/view_otaover_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:visibility="gone">
            <ImageView
                android:id="@+id/view_otaover_image"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="38dp"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/ota_icon_success">
            </ImageView>

            <TextView
                android:id="@+id/view_otaover_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="13dp"
                android:text="@string/OTASuccess"
                android:textSize="18sp"
                android:fontFamily="@font/sanshan_bold"
                android:textColor="@color/ff28c2b0"
                >

            </TextView>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/view_otaing_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            >
            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="0dp"
                tools:ignore="MissingConstraints">
                <ImageView
                    android:layout_width="209dp"
                    android:layout_height="209dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_product_big_jpg" />

                <com.besall.allbase.common.utils.CircleProgressView
                    android:layout_width="214dp"
                    android:layout_height="214dp"
                    android:id="@+id/tasks_view"
                    tc:circleColor="#00000000"
                    tc:radius="100dp"
                    tc:ringBgColor="@color/color_transparent"
                    tc:ringColor="@color/circlering"
                    tc:strokeWidth="5dip"
                    android:layout_centerInParent="true"
                    tools:ignore="MissingClass" />

            </FrameLayout>

            <LinearLayout
                android:id="@+id/base_view0"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_horizontal"
                android:layout_marginTop="5dp"
                android:weightSum="1">
                <LinearLayout
                    android:id="@+id/bgview0"
                    android:layout_weight="0.5"
                    android:layout_width="0dp"
                    android:gravity="center_horizontal"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_horizontal"
                        android:orientation="vertical"
                        >
                        <TextView
                            android:id="@+id/text_name0"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textSize="8sp"
                            android:textAlignment="center"
                            android:textColor="@color/ff087ec2"
                            android:text="--">
                        </TextView>
                        <TextView
                            android:id="@+id/text_percent0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="50sp"
                            android:fontFamily="@font/sanshan_bold"
                            android:textColor="@color/ff087ec2"
                            android:text="0.00">

                        </TextView>
                    </LinearLayout>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="28dp"
                        android:background="@drawable/percent">

                    </ImageView>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/bgview1"
                    android:visibility="gone"
                    android:layout_weight="0.5"
                    android:layout_width="0dp"
                    android:gravity="center_horizontal"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_horizontal"
                        android:orientation="vertical"
                        >
                        <TextView
                            android:id="@+id/text_name1"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textSize="8sp"
                            android:textAlignment="center"
                            android:textColor="@color/ff087ec2"
                            android:text="--">
                        </TextView>
                        <TextView
                            android:id="@+id/text_percent1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="50sp"
                            android:fontFamily="@font/sanshan_bold"
                            android:textColor="@color/ff087ec2"
                            android:text="0.00">

                        </TextView>
                    </LinearLayout>

                    <ImageView
                        android:layout_marginTop="28dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/percent">

                    </ImageView>
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/base_view1"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="10dp"
                android:gravity="center"
                android:weightSum="1">

                <LinearLayout
                    android:id="@+id/text_view_0"
                    android:visibility="gone"
                    android:layout_weight="0.2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_gravity="center_horizontal"
                    >
                    <TextView
                        android:id="@+id/text_name_0"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="8sp"
                        android:textAlignment="center"
                        android:textColor="@color/ff087ec2"
                        android:text="text_name_0">
                    </TextView>
                    <TextView
                        android:id="@+id/text_percent_0"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="18sp"
                        android:fontFamily="@font/sanshan_bold"
                        android:textColor="@color/ff087ec2"
                        android:textAlignment="center"
                        android:text="0.00%">
                    </TextView>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/text_view_1"
                    android:visibility="gone"
                    android:layout_weight="0.2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_gravity="center_horizontal"
                    >
                    <TextView
                        android:id="@+id/text_name_1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="8sp"
                        android:textAlignment="center"
                        android:textColor="@color/ff087ec2"
                        android:text="--">
                    </TextView>
                    <TextView
                        android:id="@+id/text_percent_1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="18sp"
                        android:fontFamily="@font/sanshan_bold"
                        android:textColor="@color/ff087ec2"
                        android:textAlignment="center"
                        android:text="0.00%">
                    </TextView>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/text_view_2"
                    android:visibility="gone"
                    android:layout_weight="0.2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_gravity="center_horizontal"
                    >
                    <TextView
                        android:id="@+id/text_name_2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="8sp"
                        android:textAlignment="center"
                        android:textColor="@color/ff087ec2"
                        android:text="--">
                    </TextView>
                    <TextView
                        android:id="@+id/text_percent_2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="18sp"
                        android:fontFamily="@font/sanshan_bold"
                        android:textColor="@color/ff087ec2"
                        android:textAlignment="center"
                        android:text="0.00%">
                    </TextView>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/text_view_3"
                    android:visibility="gone"
                    android:layout_weight="0.2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_gravity="center_horizontal"
                    >
                    <TextView
                        android:id="@+id/text_name_3"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="8sp"
                        android:textAlignment="center"
                        android:textColor="@color/ff087ec2"
                        android:text="--">
                    </TextView>
                    <TextView
                        android:id="@+id/text_percent_3"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="18sp"
                        android:fontFamily="@font/sanshan_bold"
                        android:textColor="@color/ff087ec2"
                        android:textAlignment="center"
                        android:text="0.00%">
                    </TextView>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/text_view_4"
                    android:visibility="gone"
                    android:layout_weight="0.2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_gravity="center_horizontal"
                    >
                    <TextView
                        android:id="@+id/text_name_4"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="8sp"
                        android:textAlignment="center"
                        android:textColor="@color/ff087ec2"
                        android:text="--">
                    </TextView>
                    <TextView
                        android:id="@+id/text_percent_4"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="18sp"
                        android:fontFamily="@font/sanshan_bold"
                        android:textColor="@color/ff087ec2"
                        android:textAlignment="center"
                        android:text="0.00%">
                    </TextView>
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/base_view2"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="10dp"
                android:gravity="center"
                android:weightSum="1">

                <LinearLayout
                    android:id="@+id/text_view_5"
                    android:visibility="gone"
                    android:layout_weight="0.2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_gravity="center_horizontal"
                    >
                    <TextView
                        android:id="@+id/text_name_5"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="8sp"
                        android:textAlignment="center"
                        android:textColor="@color/ff087ec2"
                        android:text="--">
                    </TextView>
                    <TextView
                        android:id="@+id/text_percent_5"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="18sp"
                        android:fontFamily="@font/sanshan_bold"
                        android:textColor="@color/ff087ec2"
                        android:textAlignment="center"
                        android:text="0.00%">
                    </TextView>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/text_view_6"
                    android:visibility="gone"
                    android:layout_weight="0.2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_gravity="center_horizontal"
                    >
                    <TextView
                        android:id="@+id/text_name_6"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="8sp"
                        android:textAlignment="center"
                        android:textColor="@color/ff087ec2"
                        android:text="--">
                    </TextView>
                    <TextView
                        android:id="@+id/text_percent_6"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="18sp"
                        android:fontFamily="@font/sanshan_bold"
                        android:textColor="@color/ff087ec2"
                        android:textAlignment="center"
                        android:text="0.00%">
                    </TextView>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/text_view_7"
                    android:visibility="gone"
                    android:layout_weight="0.2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_gravity="center_horizontal"
                    >
                    <TextView
                        android:id="@+id/text_name_7"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="8sp"
                        android:textAlignment="center"
                        android:textColor="@color/ff087ec2"
                        android:text="--">
                    </TextView>
                    <TextView
                        android:id="@+id/text_percent_7"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="18sp"
                        android:fontFamily="@font/sanshan_bold"
                        android:textColor="@color/ff087ec2"
                        android:textAlignment="center"
                        android:text="0.00%">
                    </TextView>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/text_view_8"
                    android:visibility="gone"
                    android:layout_weight="0.2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_gravity="center_horizontal"
                    >
                    <TextView
                        android:id="@+id/text_name_8"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="8sp"
                        android:textAlignment="center"
                        android:textColor="@color/ff087ec2"
                        android:text="--">
                    </TextView>
                    <TextView
                        android:id="@+id/text_percent_8"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="18sp"
                        android:fontFamily="@font/sanshan_bold"
                        android:textColor="@color/ff087ec2"
                        android:textAlignment="center"
                        android:text="0.00%">
                    </TextView>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/text_view_9"
                    android:visibility="gone"
                    android:layout_weight="0.2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_gravity="center_horizontal"
                    >
                    <TextView
                        android:id="@+id/text_name_9"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="8sp"
                        android:textAlignment="center"
                        android:textColor="@color/ff087ec2"
                        android:text="--">
                    </TextView>
                    <TextView
                        android:id="@+id/text_percent_9"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="18sp"
                        android:fontFamily="@font/sanshan_bold"
                        android:textColor="@color/ff087ec2"
                        android:textAlignment="center"
                        android:text="0.00%">
                    </TextView>
                </LinearLayout>

            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="18dp"
                android:layout_marginTop="10dp"
                android:layout_gravity="center_horizontal"
                android:fontFamily="@font/sanshan_normal"
                android:orientation="horizontal"
                android:text="@string/upgradeUnderWay"
                android:textColor="@color/ff087dc0"
                android:textSize="16sp">

            </TextView>

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>