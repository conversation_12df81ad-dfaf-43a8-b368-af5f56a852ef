<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="toolbar" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\toolbar.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/toolbar_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="35" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="4" endLine="29" endOffset="39"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="20" startOffset="8" endLine="27" endOffset="51"/></Target></Targets></Layout>