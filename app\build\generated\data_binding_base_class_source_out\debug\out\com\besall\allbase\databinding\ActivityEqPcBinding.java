// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityEqPcBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button audition;

  @NonNull
  public final RadioButton drc;

  @NonNull
  public final Button eqFunctionBtn;

  @NonNull
  public final Spinner eqModelSpinner;

  @NonNull
  public final Button eqOpenBtn;

  @NonNull
  public final Button eqWriteflashBtn;

  @NonNull
  public final EditText gainLeftEt;

  @NonNull
  public final EditText gainRightEt;

  @NonNull
  public final RadioButton iirEq;

  @NonNull
  public final LinearLayout iirEqViewTitle;

  @NonNull
  public final Spinner iirTypeSpinner;

  @NonNull
  public final RadioButton limiter;

  @NonNull
  public final Button load;

  @NonNull
  public final TextView msgview;

  @NonNull
  public final Button preview;

  @NonNull
  public final RadioGroup radiogroupFunction;

  @NonNull
  public final RecyclerView recyclerviewContent;

  @NonNull
  public final Button saveAs;

  @NonNull
  public final ToolbarBinding tool;

  private ActivityEqPcBinding(@NonNull LinearLayout rootView, @NonNull Button audition,
      @NonNull RadioButton drc, @NonNull Button eqFunctionBtn, @NonNull Spinner eqModelSpinner,
      @NonNull Button eqOpenBtn, @NonNull Button eqWriteflashBtn, @NonNull EditText gainLeftEt,
      @NonNull EditText gainRightEt, @NonNull RadioButton iirEq,
      @NonNull LinearLayout iirEqViewTitle, @NonNull Spinner iirTypeSpinner,
      @NonNull RadioButton limiter, @NonNull Button load, @NonNull TextView msgview,
      @NonNull Button preview, @NonNull RadioGroup radiogroupFunction,
      @NonNull RecyclerView recyclerviewContent, @NonNull Button saveAs,
      @NonNull ToolbarBinding tool) {
    this.rootView = rootView;
    this.audition = audition;
    this.drc = drc;
    this.eqFunctionBtn = eqFunctionBtn;
    this.eqModelSpinner = eqModelSpinner;
    this.eqOpenBtn = eqOpenBtn;
    this.eqWriteflashBtn = eqWriteflashBtn;
    this.gainLeftEt = gainLeftEt;
    this.gainRightEt = gainRightEt;
    this.iirEq = iirEq;
    this.iirEqViewTitle = iirEqViewTitle;
    this.iirTypeSpinner = iirTypeSpinner;
    this.limiter = limiter;
    this.load = load;
    this.msgview = msgview;
    this.preview = preview;
    this.radiogroupFunction = radiogroupFunction;
    this.recyclerviewContent = recyclerviewContent;
    this.saveAs = saveAs;
    this.tool = tool;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityEqPcBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityEqPcBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_eq_pc, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityEqPcBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.audition;
      Button audition = rootView.findViewById(id);
      if (audition == null) {
        break missingId;
      }

      id = R.id.drc;
      RadioButton drc = rootView.findViewById(id);
      if (drc == null) {
        break missingId;
      }

      id = R.id.eq_function_btn;
      Button eqFunctionBtn = rootView.findViewById(id);
      if (eqFunctionBtn == null) {
        break missingId;
      }

      id = R.id.eq_model_spinner;
      Spinner eqModelSpinner = rootView.findViewById(id);
      if (eqModelSpinner == null) {
        break missingId;
      }

      id = R.id.eq_open_btn;
      Button eqOpenBtn = rootView.findViewById(id);
      if (eqOpenBtn == null) {
        break missingId;
      }

      id = R.id.eq_writeflash_btn;
      Button eqWriteflashBtn = rootView.findViewById(id);
      if (eqWriteflashBtn == null) {
        break missingId;
      }

      id = R.id.gain_left_et;
      EditText gainLeftEt = rootView.findViewById(id);
      if (gainLeftEt == null) {
        break missingId;
      }

      id = R.id.gain_right_et;
      EditText gainRightEt = rootView.findViewById(id);
      if (gainRightEt == null) {
        break missingId;
      }

      id = R.id.iir_eq;
      RadioButton iirEq = rootView.findViewById(id);
      if (iirEq == null) {
        break missingId;
      }

      id = R.id.iir_eq_view_title;
      LinearLayout iirEqViewTitle = rootView.findViewById(id);
      if (iirEqViewTitle == null) {
        break missingId;
      }

      id = R.id.iir_type_spinner;
      Spinner iirTypeSpinner = rootView.findViewById(id);
      if (iirTypeSpinner == null) {
        break missingId;
      }

      id = R.id.limiter;
      RadioButton limiter = rootView.findViewById(id);
      if (limiter == null) {
        break missingId;
      }

      id = R.id.load;
      Button load = rootView.findViewById(id);
      if (load == null) {
        break missingId;
      }

      id = R.id.msgview;
      TextView msgview = rootView.findViewById(id);
      if (msgview == null) {
        break missingId;
      }

      id = R.id.preview;
      Button preview = rootView.findViewById(id);
      if (preview == null) {
        break missingId;
      }

      id = R.id.radiogroup_function;
      RadioGroup radiogroupFunction = rootView.findViewById(id);
      if (radiogroupFunction == null) {
        break missingId;
      }

      id = R.id.recyclerview_content;
      RecyclerView recyclerviewContent = rootView.findViewById(id);
      if (recyclerviewContent == null) {
        break missingId;
      }

      id = R.id.save_as;
      Button saveAs = rootView.findViewById(id);
      if (saveAs == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      return new ActivityEqPcBinding((LinearLayout) rootView, audition, drc, eqFunctionBtn,
          eqModelSpinner, eqOpenBtn, eqWriteflashBtn, gainLeftEt, gainRightEt, iirEq,
          iirEqViewTitle, iirTypeSpinner, limiter, load, msgview, preview, radiogroupFunction,
          recyclerviewContent, saveAs, binding_tool);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
