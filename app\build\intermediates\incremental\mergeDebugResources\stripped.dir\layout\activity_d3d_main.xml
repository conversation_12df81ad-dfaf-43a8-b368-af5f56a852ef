<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:orientation="vertical">

        <Button
            android:id="@+id/classBt_btn"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_alignParentBottom="true"
            android:textSize="20sp" />


        <Button
            android:id="@+id/clear"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_alignParentBottom="true"
            android:layout_marginTop="10dp"
            android:text="SPP DISCONNECT"
            android:textSize="20sp" />

        <Button
            android:id="@+id/sensor_start"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_alignParentBottom="true"
            android:layout_marginTop="10dp"
            android:text="SENSOR START"
            android:textSize="20sp" />

        <Button
            android:id="@+id/sensor_stop"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_alignParentBottom="true"
            android:layout_marginTop="10dp"
            android:text="SENSOR STOP"
            android:textSize="20sp" />

        <EditText
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/period_num"
            android:hint="default 50ms">

        </EditText>

        <Button
            android:id="@+id/sensor_config"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_alignParentBottom="true"
            android:layout_marginTop="10dp"
            android:text="SENSOR CONFIG"
            android:textSize="20sp" />

        <TextView
            android:id="@+id/play_file_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="--" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <Button
                android:id="@+id/play_file"
                android:layout_width="wrap_content"
                android:layout_height="73dp"
                android:background="@android:drawable/ic_media_play"
                android:onClick="@string/audio_playing" />

            <Button
                android:id="@+id/stop_file"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:background="@android:drawable/ic_media_pause"
                android:onClick="@string/audio_paused" />

            <Button
                android:id="@+id/select_file"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="choose record file" />


        </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="sensor data:">
        </TextView>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/sensor_info"
            android:text="--">

        </TextView>

    </LinearLayout>


    </LinearLayout>
</ScrollView>



