// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common.proto

package com.amazon.alexa.accessory.protocol;

public final class Common {
  private Common() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code Transport}
   */
  public enum Transport
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>BLUETOOTH_LOW_ENERGY = 0;</code>
     */
    BLUETOOTH_LOW_ENERGY(0),
    /**
     * <code>BLUETOOTH_RFCOMM = 1;</code>
     */
    BLUETOOTH_RFCOMM(1),
    /**
     * <code>BLUETOOTH_IAP = 2;</code>
     */
    BLUETOOTH_IAP(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>BLUETOOTH_LOW_ENERGY = 0;</code>
     */
    public static final int BLUETOOTH_LOW_ENERGY_VALUE = 0;
    /**
     * <code>BLUETOOTH_RFCOMM = 1;</code>
     */
    public static final int BLUETOOTH_RFCOMM_VALUE = 1;
    /**
     * <code>BLUETOOTH_IAP = 2;</code>
     */
    public static final int BLUETOOTH_IAP_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static Transport valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static Transport forNumber(int value) {
      switch (value) {
        case 0: return BLUETOOTH_LOW_ENERGY;
        case 1: return BLUETOOTH_RFCOMM;
        case 2: return BLUETOOTH_IAP;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<Transport>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        Transport> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<Transport>() {
            public Transport findValueByNumber(int number) {
              return Transport.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Common.getDescriptor().getEnumTypes().get(0);
    }

    private static final Transport[] VALUES = values();

    public static Transport valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private Transport(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:Transport)
  }

  /**
   * Protobuf enum {@code ErrorCode}
   */
  public enum ErrorCode
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>SUCCESS = 0;</code>
     */
    SUCCESS(0),
    /**
     * <code>UNKNOWN = 1;</code>
     */
    UNKNOWN(1),
    /**
     * <code>INTERNAL = 2;</code>
     */
    INTERNAL(2),
    /**
     * <code>UNSUPPORTED = 3;</code>
     */
    UNSUPPORTED(3),
    /**
     * <code>USER_CANCELLED = 4;</code>
     */
    USER_CANCELLED(4),
    /**
     * <code>NOT_FOUND = 5;</code>
     */
    NOT_FOUND(5),
    /**
     * <code>INVALID = 6;</code>
     */
    INVALID(6),
    /**
     * <code>BUSY = 7;</code>
     */
    BUSY(7),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>SUCCESS = 0;</code>
     */
    public static final int SUCCESS_VALUE = 0;
    /**
     * <code>UNKNOWN = 1;</code>
     */
    public static final int UNKNOWN_VALUE = 1;
    /**
     * <code>INTERNAL = 2;</code>
     */
    public static final int INTERNAL_VALUE = 2;
    /**
     * <code>UNSUPPORTED = 3;</code>
     */
    public static final int UNSUPPORTED_VALUE = 3;
    /**
     * <code>USER_CANCELLED = 4;</code>
     */
    public static final int USER_CANCELLED_VALUE = 4;
    /**
     * <code>NOT_FOUND = 5;</code>
     */
    public static final int NOT_FOUND_VALUE = 5;
    /**
     * <code>INVALID = 6;</code>
     */
    public static final int INVALID_VALUE = 6;
    /**
     * <code>BUSY = 7;</code>
     */
    public static final int BUSY_VALUE = 7;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static ErrorCode valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static ErrorCode forNumber(int value) {
      switch (value) {
        case 0: return SUCCESS;
        case 1: return UNKNOWN;
        case 2: return INTERNAL;
        case 3: return UNSUPPORTED;
        case 4: return USER_CANCELLED;
        case 5: return NOT_FOUND;
        case 6: return INVALID;
        case 7: return BUSY;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<ErrorCode>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        ErrorCode> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<ErrorCode>() {
            public ErrorCode findValueByNumber(int number) {
              return ErrorCode.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Common.getDescriptor().getEnumTypes().get(1);
    }

    private static final ErrorCode[] VALUES = values();

    public static ErrorCode valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private ErrorCode(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:ErrorCode)
  }

  /**
   * Protobuf enum {@code SpeechInitiationType}
   */
  public enum SpeechInitiationType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>PRESS_AND_HOLD = 0;</code>
     */
    PRESS_AND_HOLD(0),
    /**
     * <code>TAP = 1;</code>
     */
    TAP(1),
    /**
     * <code>WAKEWORD = 2;</code>
     */
    WAKEWORD(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>PRESS_AND_HOLD = 0;</code>
     */
    public static final int PRESS_AND_HOLD_VALUE = 0;
    /**
     * <code>TAP = 1;</code>
     */
    public static final int TAP_VALUE = 1;
    /**
     * <code>WAKEWORD = 2;</code>
     */
    public static final int WAKEWORD_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static SpeechInitiationType valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static SpeechInitiationType forNumber(int value) {
      switch (value) {
        case 0: return PRESS_AND_HOLD;
        case 1: return TAP;
        case 2: return WAKEWORD;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<SpeechInitiationType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        SpeechInitiationType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<SpeechInitiationType>() {
            public SpeechInitiationType findValueByNumber(int number) {
              return SpeechInitiationType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Common.getDescriptor().getEnumTypes().get(2);
    }

    private static final SpeechInitiationType[] VALUES = values();

    public static SpeechInitiationType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private SpeechInitiationType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:SpeechInitiationType)
  }


  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014common.proto*N\n\tTransport\022\030\n\024BLUETOOTH" +
      "_LOW_ENERGY\020\000\022\024\n\020BLUETOOTH_RFCOMM\020\001\022\021\n\rB" +
      "LUETOOTH_IAP\020\002*~\n\tErrorCode\022\013\n\007SUCCESS\020\000" +
      "\022\013\n\007UNKNOWN\020\001\022\014\n\010INTERNAL\020\002\022\017\n\013UNSUPPORT" +
      "ED\020\003\022\022\n\016USER_CANCELLED\020\004\022\r\n\tNOT_FOUND\020\005\022" +
      "\013\n\007INVALID\020\006\022\010\n\004BUSY\020\007*A\n\024SpeechInitiati" +
      "onType\022\022\n\016PRESS_AND_HOLD\020\000\022\007\n\003TAP\020\001\022\014\n\010W" +
      "AKEWORD\020\002B0\n#com.amazon.alexa.accessory." +
      "protocolH\003\240\001\001\242\002\003AACb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
