<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_rssiextend" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_rssiextend.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_rssiextend_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="551" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/activity_rssiextend_0" include="toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="12" endOffset="9"/></Target><Target id="@+id/loginfo" tag="layout/activity_rssiextend_0" include="logview"><Expressions/><location startLine="14" startOffset="4" endLine="17" endOffset="34"/></Target><Target id="@+id/connect_spp" view="Button"><Expressions/><location startLine="36" startOffset="8" endLine="49" endOffset="13"/></Target><Target id="@+id/interval" view="EditText"><Expressions/><location startLine="83" startOffset="16" endLine="94" endOffset="45"/></Target><Target id="@+id/rssi_read_start" view="Button"><Expressions/><location startLine="96" startOffset="16" endLine="105" endOffset="68"/></Target><Target id="@+id/left_call_agc" view="TextView"><Expressions/><location startLine="156" startOffset="20" endLine="165" endOffset="49"/></Target><Target id="@+id/left_call_rssi" view="TextView"><Expressions/><location startLine="167" startOffset="20" endLine="176" endOffset="49"/></Target><Target id="@+id/left_call_min_rssi" view="TextView"><Expressions/><location startLine="184" startOffset="20" endLine="193" endOffset="49"/></Target><Target id="@+id/left_call_max_rssi" view="TextView"><Expressions/><location startLine="195" startOffset="20" endLine="204" endOffset="49"/></Target><Target id="@+id/left_tws_agc" view="TextView"><Expressions/><location startLine="222" startOffset="20" endLine="231" endOffset="48"/></Target><Target id="@+id/left_tws_rssi" view="TextView"><Expressions/><location startLine="233" startOffset="20" endLine="242" endOffset="49"/></Target><Target id="@+id/left_tws_min_rssi" view="TextView"><Expressions/><location startLine="250" startOffset="20" endLine="259" endOffset="49"/></Target><Target id="@+id/left_tws_max_rssi" view="TextView"><Expressions/><location startLine="261" startOffset="20" endLine="270" endOffset="49"/></Target><Target id="@+id/right_call_agc" view="TextView"><Expressions/><location startLine="304" startOffset="20" endLine="313" endOffset="25"/></Target><Target id="@+id/right_call_rssi" view="TextView"><Expressions/><location startLine="315" startOffset="20" endLine="323" endOffset="49"/></Target><Target id="@+id/right_call_min_rssi" view="TextView"><Expressions/><location startLine="331" startOffset="20" endLine="339" endOffset="49"/></Target><Target id="@+id/right_call_max_rssi" view="TextView"><Expressions/><location startLine="341" startOffset="20" endLine="349" endOffset="49"/></Target><Target id="@+id/right_tws_agc" view="TextView"><Expressions/><location startLine="367" startOffset="20" endLine="375" endOffset="48"/></Target><Target id="@+id/right_tws_rssi" view="TextView"><Expressions/><location startLine="377" startOffset="20" endLine="385" endOffset="49"/></Target><Target id="@+id/right_tws_min_rssi" view="TextView"><Expressions/><location startLine="393" startOffset="20" endLine="401" endOffset="49"/></Target><Target id="@+id/right_tws_max_rssi" view="TextView"><Expressions/><location startLine="403" startOffset="20" endLine="411" endOffset="49"/></Target><Target id="@+id/left_packet_loss_rate" view="TextView"><Expressions/><location startLine="424" startOffset="12" endLine="433" endOffset="40"/></Target><Target id="@+id/right_packet_loss_rate" view="TextView"><Expressions/><location startLine="435" startOffset="12" endLine="443" endOffset="41"/></Target><Target id="@+id/rssi_extra_info" view="TextView"><Expressions/><location startLine="446" startOffset="8" endLine="453" endOffset="37"/></Target><Target id="@+id/rssi_read_stop" view="Button"><Expressions/><location startLine="502" startOffset="8" endLine="514" endOffset="13"/></Target><Target id="@+id/spp_stop" view="Button"><Expressions/><location startLine="522" startOffset="8" endLine="534" endOffset="13"/></Target></Targets></Layout>