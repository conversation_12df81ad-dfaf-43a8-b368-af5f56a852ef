<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="act_sports" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\act_sports.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/act_sports_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="99" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/act_sports_0" include="toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="12" endOffset="9"/></Target><Target id="@+id/loginfo" tag="layout/act_sports_0" include="logview"><Expressions/><location startLine="14" startOffset="4" endLine="17" endOffset="34"/></Target><Target id="@+id/data_pattern1" view="RadioGroup"><Expressions/><location startLine="26" startOffset="8" endLine="73" endOffset="20"/></Target><Target id="@+id/pattern_00" view="RadioButton"><Expressions/><location startLine="33" startOffset="12" endLine="46" endOffset="49"/></Target><Target id="@+id/pattern_01" view="RadioButton"><Expressions/><location startLine="48" startOffset="12" endLine="58" endOffset="49"/></Target><Target id="@+id/pattern_02" view="RadioButton"><Expressions/><location startLine="60" startOffset="12" endLine="71" endOffset="49"/></Target><Target id="@+id/sports_read_start" view="Button"><Expressions/><location startLine="75" startOffset="8" endLine="86" endOffset="13"/></Target><Target id="@+id/sportsChart" view="com.github.mikephil.charting.charts.BarChart"><Expressions/><location startLine="88" startOffset="8" endLine="92" endOffset="42"/></Target></Targets></Layout>