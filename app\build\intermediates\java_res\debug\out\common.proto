// ***************************
// *** AMAZON CONFIDENTIAL ***
// ***************************
//
// Copyright 2017 - 2018 Amazon.com, Inc. or its affiliates.  All Rights Reserved.
//
// You may not use this file except in compliance with the terms and conditions set forth in the accompanying LICENSE file.
//
// THESE MATERIALS ARE PROVIDED ON AN "AS IS" BASIS.  AMAZON SPECIFICALLY DISCLAIMS, WITH RESPECT TO THESE MATERIALS,
// ALL WARRANTIES, EXPRESS, IMPLIED, OR STATUTORY, INCLUDING THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
// PARTICULAR PURPOSE, AND NON-INFRINGEMENT.

syntax = "proto3";

option java_package = "com.amazon.alexa.accessory.protocol";
option java_generate_equals_and_hash = true;
option objc_class_prefix = "AAC"; // Amazon Alexa Accessory
option optimize_for = LITE_RUNTIME;

enum Transport {
    BLUETOOTH_LOW_ENERGY = 0;
    BLUETOOTH_RFCOMM = 1;
    BLUETOOTH_IAP = 2;
}

enum ErrorCode {
    SUCCESS = 0;
    UNKNOWN = 1;
    INTERNAL = 2;
    UNSUPPORTED = 3;
    USER_CANCELLED = 4;
    NOT_FOUND = 5;
    INVALID = 6;
    BUSY = 7;
}

enum SpeechInitiationType {
    PRESS_AND_HOLD = 0;
    TAP = 1;
    WAKEWORD = 2;
}
