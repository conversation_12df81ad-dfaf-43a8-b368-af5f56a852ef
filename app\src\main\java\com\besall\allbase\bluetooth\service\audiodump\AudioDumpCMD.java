package com.besall.allbase.bluetooth.service.audiodump;

import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.AUDIODUMP_CUSTOM_TYPE_KEY;
import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.AUDIODUMP_SAVE_FILE_TYPE_KEY;
import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.AUDIODUMP_SAVE_FILE_TYPE_VALUE_CUSTOM;
import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.AUDIODUMP_SAVE_FILE_TYPE_VALUE_PCM;
import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.BES_INSERT_BYTE_LOST;
import static com.besall.allbase.bluetooth.service.audiodump.AudioDumpConstants.BES_INSERT_BYTE_ZERO;

import android.content.Context;
import android.util.Log;

import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.CmdInfo;
import com.bes.bessdk.utils.SPHelper;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.service.log_dump.LogDumpConstants;
import com.besall.allbase.common.utils.FileUtils;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

import io.reactivex.internal.util.BlockingIgnoringReceiver;

public class AudioDumpCMD {
    static String TAG = "AudioDumpCMD";

    static String startTime = "";

    static byte insertData = BES_INSERT_BYTE_LOST;
    static int lastIndex = 0;

    public static void setInsertData(byte singleByte) {
        insertData = singleByte;
    }

    public static byte[] audioDumpStart() {
        startTime = "";
        lastIndex = 0;
        byte flashType = (byte) BluetoothConstants.Connect.CMD_CONFIRM;
        CmdInfo flashreadcmdInfo = new CmdInfo(AudioDumpConstants.OP_TOTA_AUDIO_DUMP_START, new byte[]{flashType});
        return flashreadcmdInfo.toBytes();
    }

    public static byte[] audioDumpStop() {
        byte stopType = (byte) BluetoothConstants.Connect.CMD_CONFIRM;
        CmdInfo stopcmdInfo = new CmdInfo(AudioDumpConstants.OP_TOTA_AUDIO_DUMP_STOP, new byte[]{stopType});
        return stopcmdInfo.toBytes();
    }

    public static String receiveData(byte[] data, Context context) {
        Log.i(TAG, "receiveData: ---- " + ArrayUtil.toHex(data));
        Log.i(TAG, "receiveData: length" + data.length);
        if (data[0] == 0x00 && (data[1]&0xff) == 0x64) {
            int datalen = data.length;
            int k = 0;
            while (datalen > k) {
                byte[] pkglen = ArrayUtil.extractBytes(data, k + 2, 2);
                int curpl = ArrayUtil.byte2int(pkglen);
                int onepkg = curpl + 4;
                Log.i(TAG, "xxx: pkglen" + curpl);
                byte[] dataret = ArrayUtil.extractBytes(ArrayUtil.extractBytes(data, k + 4 , curpl), 0, curpl);
                Log.i(TAG, "xxx: ---" + ArrayUtil.toHex(dataret));
                k = onepkg + k;
                saveData(dataret, context);
            }
        } else if (data[0] == (byte) 0x48 && data[1] == (byte) 0x45 && data[8] == 0x00 && (data[9]&0xff) == 0x64) {
            int remainder = data.length % 664;
            boolean isLastData = (remainder != 0);
            int count = data.length / 664 + (isLastData ? 1 : 0);
            Log.i(TAG, "remainder: ----" + remainder);
            Log.i(TAG, "isLastData: ----" + isLastData);
            Log.i(TAG, "count: ----" + count);
            for (int i = 0; i < count; i ++) {
                byte[] mData;
                int curDataLen;
                if (isLastData && i == count - 1) {
                    curDataLen = remainder;
                } else {
                    curDataLen = 664;
                }
                Log.i(TAG, "curDataLen: ----" + curDataLen);
                mData = new byte[curDataLen];;
                System.arraycopy(data, i * 664, mData, 0, curDataLen);
                byte[] pkglen = ArrayUtil.extractBytes(mData, 4, 2);
                int curpl = ArrayUtil.byte2int(pkglen) - 2 - 4;
                byte[] lastData = new byte[curpl];
                System.arraycopy(mData, 8 + 2 + 4, lastData, 0, curpl);
                ///////////////////////////////////////////////
                //insert data
                byte[] indexData = new byte[4];
                System.arraycopy(mData, 8 + 2, indexData, 0, 4);
                int curIndex = ArrayUtil.bytesToIntLittle(indexData);
                Log.i(TAG, "indexData: -----" + curIndex);
                if (curIndex - lastIndex > 1 && insertData != BES_INSERT_BYTE_LOST && startTime.length() > 0) {
                    byte[] insertDatas = new byte[curpl];
                    int m = 0;
                    while (m < curpl) {
                        insertDatas[m] = insertData;
                        m ++;
                    }
                    for (int n = 0; n < curIndex - lastIndex; n ++) {
                        saveBytesToFile(insertDatas, context);
                    }
                }
                lastIndex = curIndex;
                //////////////////////////////////////////////

                saveBytesToFile(lastData, context);
            }
            return "";
        }

//        if (data[2] == (byte) 0x03) {
//            Log.i(TAG, "onReceive: +++03extract" + data[2] + data[3] + data[4]);
//
//        } else if (data.length % 666 == 0) {
//            Log.i(TAG, "datalength==="+ data.length);
//            int count = data.length/666;
//            for (int i = 0; i < count; i ++) {
//                byte[] datanew = ArrayUtil.extractBytes(ArrayUtil.extractBytes(data, 666 * i, 666),5,666-5);
//                if (data[2]==0x03 ) {
//                    continue;
//                }
//                Log.i(TAG, "datalength: =======" + datanew.length);
//                Log.i(TAG, "onReceive: ------->"+ArrayUtil.toHex(datanew));
//                saveData(datanew, context);
//            }
//            Log.i(TAG, "datalength==="+ data.length);
//            byte[] datanew = ArrayUtil.extractBytes(data,5,data.length-5);
//            Log.i(TAG, "datalength: =======" + datanew.length);
//            Log.i(TAG, "onReceive: ------->"+ArrayUtil.toHex(datanew));
//            PropertyObservable.getInstance().fireEvent(EventID.UPDATA_RECE_RSSI_INFO, null, null,datanew);
//        }
        return "null";
    }

    public static void saveData(byte[] data, Context context) {
//        Log.i(TAG, "saveData: " + ArrayUtil.toHex(data));
//        FileUtils fileUtils = new FileUtils(context);
        if (startTime.length() == 0) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
            Date date = new Date(System.currentTimeMillis());
            startTime = simpleDateFormat.format(date);
        }

//        fileUtils.saveBytesToFile(startTime+"_audiodata", AudioDumpConstants.AUDIODUMP_SAVE_FOLDER,data);
        String type = (String) SPHelper.getPreference(context, AUDIODUMP_SAVE_FILE_TYPE_KEY, AUDIODUMP_SAVE_FILE_TYPE_VALUE_PCM);
        if (type.equals(AUDIODUMP_SAVE_FILE_TYPE_VALUE_CUSTOM)) {
            type = (String) SPHelper.getPreference(context, AUDIODUMP_CUSTOM_TYPE_KEY, "");
        }
        FileUtils.writeTOFile(data, AudioDumpConstants.AUDIODUMP_SAVE_FOLDER, startTime + "_audiorecord", type);
    }


    public static void saveBytesToFile(byte[] data, Context context) {
        Log.i(TAG, "saveBytesToFile: ---------" + data.length);
        if (startTime.length() == 0) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
            Date date = new Date(System.currentTimeMillis());
            startTime = simpleDateFormat.format(date);
        }
        String type = (String) SPHelper.getPreference(context, AUDIODUMP_SAVE_FILE_TYPE_KEY, AUDIODUMP_SAVE_FILE_TYPE_VALUE_PCM);
        if (type.equals(AUDIODUMP_SAVE_FILE_TYPE_VALUE_CUSTOM)) {
            type = (String) SPHelper.getPreference(context, AUDIODUMP_CUSTOM_TYPE_KEY, "");
        }
        File file = new File("/storage/emulated/0/Android/data/com.bes.besall/files/AudioDump/" + startTime + "." + type);
        if (!file.exists()) {
            try {
                boolean isCreate = file.createNewFile();
                Log.i(TAG, "saveBytesToFile: ---------" + isCreate);
                Log.i(TAG, "saveBytesToFile: ---------" + file.length());

            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        BufferedOutputStream outStream = null;
        try {
            outStream = new BufferedOutputStream(new FileOutputStream(file, true));
            outStream.write(data);
            outStream.flush();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != outStream) {
                try {
                    outStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }


}