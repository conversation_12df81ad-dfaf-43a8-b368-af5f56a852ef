// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAvslwaSettingBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button deregisterTheDevice;

  @NonNull
  public final LogviewBinding loginfo;

  @NonNull
  public final TextView settingDeviceSn;

  @NonNull
  public final TextView settingMacAddress;

  @NonNull
  public final TextView settingSoftwareVersion;

  @NonNull
  public final ToolbarBinding tool;

  private ActivityAvslwaSettingBinding(@NonNull LinearLayout rootView,
      @NonNull Button deregisterTheDevice, @NonNull LogviewBinding loginfo,
      @NonNull TextView settingDeviceSn, @NonNull TextView settingMacAddress,
      @NonNull TextView settingSoftwareVersion, @NonNull ToolbarBinding tool) {
    this.rootView = rootView;
    this.deregisterTheDevice = deregisterTheDevice;
    this.loginfo = loginfo;
    this.settingDeviceSn = settingDeviceSn;
    this.settingMacAddress = settingMacAddress;
    this.settingSoftwareVersion = settingSoftwareVersion;
    this.tool = tool;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAvslwaSettingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAvslwaSettingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_avslwa_setting, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAvslwaSettingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.deregister_the_device;
      Button deregisterTheDevice = rootView.findViewById(id);
      if (deregisterTheDevice == null) {
        break missingId;
      }

      id = R.id.loginfo;
      View loginfo = rootView.findViewById(id);
      if (loginfo == null) {
        break missingId;
      }
      LogviewBinding binding_loginfo = LogviewBinding.bind(loginfo);

      id = R.id.setting_device_sn;
      TextView settingDeviceSn = rootView.findViewById(id);
      if (settingDeviceSn == null) {
        break missingId;
      }

      id = R.id.setting_mac_address;
      TextView settingMacAddress = rootView.findViewById(id);
      if (settingMacAddress == null) {
        break missingId;
      }

      id = R.id.setting_software_version;
      TextView settingSoftwareVersion = rootView.findViewById(id);
      if (settingSoftwareVersion == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      return new ActivityAvslwaSettingBinding((LinearLayout) rootView, deregisterTheDevice,
          binding_loginfo, settingDeviceSn, settingMacAddress, settingSoftwareVersion,
          binding_tool);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
