<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white">



    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="2dp">
        <include
            android:id="@+id/tool"
            layout="@layout/toolbar"
            />
        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="vertical">
            <TextView
                android:id="@+id/usertext"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="left"
                android:text="OTA USER"
                android:textColor="#000000"

                android:textSize="20sp" />

            <RadioGroup
                android:id = "@+id/userchoose"
                android:orientation="horizontal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <RadioButton
                    android:id="@+id/user_fw"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="true"
                    android:gravity="center"
                    android:textColor="#000000"
                    android:text="firmware" />

                <RadioButton
                    android:id="@+id/user_lg"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="false"
                    android:textColor="#000000"
                    android:text="warning tone" />

                />

                <RadioButton
                    android:id="@+id/user_combo"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="false"
                    android:textColor="#000000"
                    android:text="combo" />
                />
            </RadioGroup>

            <TextView
                android:id="@+id/transferacktext"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="left"
                android:text="ACK"
                android:textColor="#000000"

                android:textSize="20sp" />
            <RadioGroup
                android:id = "@+id/transferack"
                android:orientation="horizontal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <RadioButton
                    android:id="@+id/withoutack"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="true"
                    android:gravity="center"
                    android:textColor="#000000"
                    android:text="without" />

                <RadioButton
                    android:id="@+id/withack"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="false"
                    android:textColor="#000000"
                    android:text="with" />

                />
            </RadioGroup>

            <TextView
                android:id="@+id/upgradetext"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="left"
                android:text="@string/ota_upgrade_type"
                android:textColor="#000000"

                android:textSize="20sp" />

            <RadioGroup
                android:id = "@+id/upgradechoose"
                android:orientation="horizontal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <RadioButton
                    android:id="@+id/slow_mod"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="true"
                    android:gravity="center"
                    android:textColor="#000000"
                    android:text="normal way" />

                <RadioButton
                    android:id="@+id/fast_mod"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="false"
                    android:textColor="#000000"
                    android:text="fast way" />

                />
            </RadioGroup>

            <RadioGroup
                android:id = "@+id/oldotaupgradetype"
                android:orientation="vertical"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <RadioButton
                    android:id="@+id/onlyleftearbud"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="true"
                    android:gravity="center"
                    android:textColor="#000000"
                    android:text="@string/left_earbud_only" />

                <RadioButton
                    android:id="@+id/onlyrightearbud"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="false"
                    android:textColor="#000000"
                    android:text="@string/right_earbud_only" />

                />
                <RadioButton
                    android:id="@+id/bothearbudsamebin"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="false"
                    android:textColor="#000000"
                    android:text="@string/both_earbuds_in_one_bin" />
                />
                <RadioButton
                    android:id="@+id/bothearbuddiffbin"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="false"
                    android:textColor="#000000"
                    android:text="@string/both_earbuds_in_two_bins" />

                />

            </RadioGroup>


        </LinearLayout>


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/current_device"
            android:textSize="20sp"
            android:layout_marginTop="15dp"
            android:textColor="#000000"
            />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp">

            <TextView
                android:id="@+id/device_address"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#000000"
                android:text="--" />

            <TextView
                android:id="@+id/device_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:textColor="#000000"
                tools:text="OTA" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/pick_device"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="left"
                android:background="@drawable/ota_button_bg"
                android:minWidth="140dp"
                android:minHeight="50dp"
                android:text="@string/change_device" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <Button
                android:id="@+id/connect_device"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right"
                android:layout_marginTop="10dp"
                android:layout_marginRight="5dp"
                android:background="@drawable/ota_button_bg"
                android:minWidth="140dp"
                android:minHeight="50dp"
                android:text="@string/connect_device" />


        </LinearLayout>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="@string/current_version"
            android:textColor="#000000"
            android:textSize="20sp" />


        <TextView
            android:id="@+id/current_version_details"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:textColor="#000000"
            android:text="--"
            android:textSize="16dp" />

        <TextView
            android:id="@+id/current_ota_file_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:textColor="#000000"
            android:text="@string/current_ota_file"
            android:textSize="20sp" />

        <TextView
            android:id="@+id/ota_file"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:textColor="#000000"
            android:text="--" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="left"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/pick_ota_file"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="left"
                android:layout_marginLeft="5dp"
                android:background="@drawable/ota_button_bg"
                android:minWidth="140dp"
                android:minHeight="50dp"
                android:text="@string/pick_ota_file" />
        </LinearLayout>

        <TextView
            android:id="@+id/ota_file_right"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:textColor="#000000"
            android:text="--" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="left"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/pick_ota_file_right"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="left"
                android:layout_marginLeft="5dp"
                android:background="@drawable/ota_button_bg"
                android:minWidth="140dp"
                android:minHeight="50dp"
                android:text="@string/pick_ota_file_right" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:textColor="#000000"
                android:text="@string/current_ota_progress"
                android:textSize="20sp" />


            <Button
                android:id="@+id/start_ota"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="5dp"
                android:background="@drawable/ota_button_bg"
                android:minWidth="140dp"
                android:minHeight="50dp"
                android:text="@string/start_ota" />
        </LinearLayout>

    <TextView
        android:id="@+id/ota_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="5dp"
        android:textColor="#000000"
        android:text="--" />


    <ProgressBar
        android:id="@+id/ota_progress"
        style="@android:style/Widget.ProgressBar.Horizontal"
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:layout_marginTop="10dp"
        android:max="100"
        android:progressDrawable="@drawable/progress_bar" />

        <TextView
            android:id="@+id/ota_status"
            android:layout_width="70dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="5dp"
            android:text="0%"
            android:textColor="#000000" />


    <TextView
        android:id="@+id/ota_info_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#000000"
        android:layout_marginLeft="10dp">
    </TextView>
</LinearLayout>
</ScrollView>