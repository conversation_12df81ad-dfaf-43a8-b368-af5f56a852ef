<?xml version="1.0" encoding="utf-8"?>
<com.besall.allbase.common.utils.SlideLayout
xmlns:android="http://schemas.android.com/apk/res/android"
android:layout_width="match_parent"
android:layout_height="match_parent"
android:clickable="true">

<LinearLayout
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center">


    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:layout_marginLeft="20dp"
        android:gravity="center"
        android:text="1111"
        android:textColor="@color/title_color_light"
        android:textSize="15sp"
        />
</LinearLayout>

<TextView
    android:id="@+id/delete_button"
    android:layout_width="88dp"
    android:layout_height="match_parent"
    android:gravity="center"
    android:textColor="#ffffffff"
    android:background="#ffff5735"
    android:text="Delete"
    android:textSize="16sp"/>

</com.besall.allbase.common.utils.SlideLayout>