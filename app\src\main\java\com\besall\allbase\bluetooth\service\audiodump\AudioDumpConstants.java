package com.besall.allbase.bluetooth.service.audiodump;

public class AudioDumpConstants {

    public static final byte                          BES_INSERT_BYTE_MAX = (byte) 0x7F;
    public static final byte                         BES_INSERT_BYTE_ZERO = (byte) 0x00;
    public static final byte                         BES_INSERT_BYTE_LOST = (byte) 0xAA;

    public static final String               AUDIODUMP_SAVE_FILE_TYPE_KEY = "AUDIODUMP_SAVE_FILE_TYPE_KEY";
    public static final String         AUDIODUMP_SAVE_FILE_TYPE_VALUE_PCM = "pcm";
    public static final String         AUDIODUMP_SAVE_FILE_TYPE_VALUE_WAV = "wav";
    public static final String        AUDIODUMP_SAVE_FILE_TYPE_VALUE_FLAV = "flac";
    public static final String      AUDIODUMP_SAVE_FILE_TYPE_VALUE_CUSTOM = "custom";

    public static final String                  AUDIODUMP_CUSTOM_TYPE_KEY = "AUDIODUMP_CUSTOM_TYPE_KEY";


    public static final String            AUDIODUMP_STREAM_START_TEXT_KEY = "AUDIODUMP_STREAM_START_TEXT_KEY";
    public static final String          AUDIODUMP_STREAM_START_TEXT_VALUE = "00000003";

    //cmd
    public static final short                    OP_TOTA_AUDIO_DUMP_START = (short)0x6400;
    public static final short                     OP_TOTA_AUDIO_DUMP_STOP = (short)0x6401;
    public static final short                  OP_TOTA_AUDIO_DUMP_CONTROL = (short)0x6402;

    //msg id
    public static final int                              AUDIO_DUMP_START = 0x00003000;



    public static final String                      AUDIODUMP_SAVE_FOLDER = "AudioDump";


}
