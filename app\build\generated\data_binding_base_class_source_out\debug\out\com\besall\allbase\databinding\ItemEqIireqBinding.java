// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemEqIireqBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Spinner enableFilterType;

  @NonNull
  public final EditText freq;

  @NonNull
  public final EditText gainEt;

  @NonNull
  public final EditText q;

  @NonNull
  public final RadioButton radioBtn;

  private ItemEqIireqBinding(@NonNull LinearLayout rootView, @NonNull Spinner enableFilterType,
      @NonNull EditText freq, @NonNull EditText gainEt, @NonNull EditText q,
      @NonNull RadioButton radioBtn) {
    this.rootView = rootView;
    this.enableFilterType = enableFilterType;
    this.freq = freq;
    this.gainEt = gainEt;
    this.q = q;
    this.radioBtn = radioBtn;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemEqIireqBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemEqIireqBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_eq_iireq, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemEqIireqBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.enableFilterType;
      Spinner enableFilterType = rootView.findViewById(id);
      if (enableFilterType == null) {
        break missingId;
      }

      id = R.id.freq;
      EditText freq = rootView.findViewById(id);
      if (freq == null) {
        break missingId;
      }

      id = R.id.gain_et;
      EditText gainEt = rootView.findViewById(id);
      if (gainEt == null) {
        break missingId;
      }

      id = R.id.q;
      EditText q = rootView.findViewById(id);
      if (q == null) {
        break missingId;
      }

      id = R.id.radioBtn;
      RadioButton radioBtn = rootView.findViewById(id);
      if (radioBtn == null) {
        break missingId;
      }

      return new ItemEqIireqBinding((LinearLayout) rootView, enableFilterType, freq, gainEt, q,
          radioBtn);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
