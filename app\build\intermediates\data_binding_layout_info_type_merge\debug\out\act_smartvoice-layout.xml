<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="act_smartvoice" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\act_smartvoice.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/act_smartvoice_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="97" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/act_smartvoice_0" include="toolbar"><Expressions/><location startLine="7" startOffset="4" endLine="10" endOffset="9"/></Target><Target id="@+id/ble_name" view="TextView"><Expressions/><location startLine="24" startOffset="4" endLine="30" endOffset="27"/></Target><Target id="@+id/pick_device" view="Button"><Expressions/><location startLine="38" startOffset="8" endLine="47" endOffset="13"/></Target><Target id="@+id/connect_device" view="Button"><Expressions/><location startLine="49" startOffset="8" endLine="58" endOffset="13"/></Target><Target id="@+id/start_record" view="Button"><Expressions/><location startLine="68" startOffset="8" endLine="77" endOffset="13"/></Target><Target id="@+id/stop_record" view="Button"><Expressions/><location startLine="79" startOffset="8" endLine="88" endOffset="13"/></Target></Targets></Layout>