<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android" android:layout_width="match_parent"
    android:layout_height="wrap_content" android:background="@color/view_bg">
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <LinearLayout
                android:id="@+id/view_earphone"
                android:layout_width="match_parent"
                android:layout_height="90dp"
                android:orientation="vertical">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:layout_marginLeft="20dp"
                    android:text="Earphone"
                    android:textSize="16sp"
                    android:textColor="@color/title_color"
                    android:fontFamily="@font/sanshan_bold">
                </TextView>
                <LinearLayout
                    android:layout_marginTop="10dp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:weightSum="1">
                    <RelativeLayout
                        android:layout_marginLeft="0dp"
                        android:layout_width="0dp"
                        android:layout_height="39dp"
                        android:layout_weight="0.47"
                        >

                        <Button
                            android:id="@+id/earphone_btn_0"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/btn_shape"
                            android:gravity="center_vertical"
                            android:stateListAnimator="@null"
                            android:text="Two earphone"
                            android:textAllCaps="false"
                            android:textColor="@color/title_color_light"
                            android:textSize="14sp"></Button>
                        <ImageView
                            android:id="@+id/earphone_image_0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="8dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            >
                        </ImageView>
                    </RelativeLayout>
                    <View
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.06"
                        >
                    </View>
                    <RelativeLayout
                        android:layout_marginLeft="0dp"
                        android:layout_width="0dp"
                        android:layout_height="39dp"
                        android:layout_weight="0.47"
                        >
                        <Button
                            android:id="@+id/earphone_btn_1"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:stateListAnimator="@null"
                            android:background="@drawable/btn_shape"
                            android:gravity="center_vertical"
                            android:text="Single earphone"
                            android:textSize="14sp"
                            android:textColor="@color/title_color_light"
                            android:textAllCaps="false"
                            >
                        </Button>
                        <ImageView
                            android:id="@+id/earphone_image_1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="8dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            >
                        </ImageView>
                    </RelativeLayout>
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/view_ack"
                android:layout_width="match_parent"
                android:layout_height="90dp"
                android:orientation="vertical">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:layout_marginLeft="20dp"
                    android:text="ACK"
                    android:textSize="16sp"
                    android:textColor="@color/title_color"
                    android:fontFamily="@font/sanshan_bold">
                </TextView>
                <LinearLayout
                    android:layout_marginTop="10dp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:weightSum="1">
                    <RelativeLayout
                        android:layout_marginLeft="0dp"
                        android:layout_width="0dp"
                        android:layout_height="39dp"
                        android:layout_weight="0.47"
                        >

                        <Button
                            android:id="@+id/ack_btn_0"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/btn_shape"
                            android:gravity="center_vertical"
                            android:stateListAnimator="@null"
                            android:text="@string/ack_exist_no"
                            android:textAllCaps="false"
                            android:textColor="@color/title_color_light"
                            android:textSize="14sp"></Button>
                        <ImageView
                            android:id="@+id/ack_image_0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="8dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            >
                        </ImageView>
                    </RelativeLayout>
                    <View
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.06"
                        >
                    </View>
                    <RelativeLayout
                        android:layout_marginLeft="0dp"
                        android:layout_width="0dp"
                        android:layout_height="39dp"
                        android:layout_weight="0.47"
                        >
                        <Button
                            android:id="@+id/ack_btn_1"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:stateListAnimator="@null"
                            android:background="@drawable/btn_shape"
                            android:gravity="center_vertical"
                            android:text="@string/ack_exist_yes"
                            android:textSize="14sp"
                            android:textColor="@color/title_color_light"
                            android:textAllCaps="false"
                            >
                        </Button>
                        <ImageView
                            android:id="@+id/ack_image_1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="8dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            >
                        </ImageView>
                    </RelativeLayout>
                </LinearLayout>
            </LinearLayout>


            <LinearLayout
                android:id="@+id/view_user"
                android:layout_width="match_parent"
                android:layout_height="180dp"
                android:orientation="vertical">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:layout_marginLeft="20dp"
                    android:text="OTA USER"
                    android:textSize="16sp"
                    android:textColor="@color/title_color"
                    android:fontFamily="@font/sanshan_bold">
                </TextView>
                <LinearLayout
                    android:layout_marginTop="10dp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:weightSum="1">
                    <RelativeLayout
                        android:layout_marginLeft="0dp"
                        android:layout_width="0dp"
                        android:layout_height="39dp"
                        android:layout_weight="0.3"
                        >
                        <Button
                            android:id="@+id/user_btn_0"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:stateListAnimator="@null"
                            android:background="@drawable/btn_shape"
                            android:gravity="center_vertical"
                            android:text="firmware"
                            android:textAllCaps="false"
                            android:textSize="14sp"
                            android:textColor="@color/title_color_light"
                            >
                        </Button>
                        <ImageView
                            android:id="@+id/user_image_0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="8dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            >
                        </ImageView>
                    </RelativeLayout>
                    <View
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.05"
                        >
                    </View>
                    <RelativeLayout
                        android:layout_marginLeft="0dp"
                        android:layout_width="0dp"
                        android:layout_height="39dp"
                        android:layout_weight="0.3"
                        >
                        <Button
                            android:id="@+id/user_btn_1"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:stateListAnimator="@null"
                            android:background="@drawable/btn_shape"
                            android:gravity="center_vertical"
                            android:text="language"
                            android:textAllCaps="false"
                            android:textSize="14sp"
                            android:textColor="@color/title_color_light"
                            >
                        </Button>
                        <ImageView
                            android:id="@+id/user_image_1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="8dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            >
                        </ImageView>
                    </RelativeLayout>
                    <View
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.05"
                        >
                    </View>
                    <RelativeLayout
                        android:layout_marginLeft="0dp"
                        android:layout_width="0dp"
                        android:layout_height="39dp"
                        android:layout_weight="0.3"
                        >
                        <Button
                            android:id="@+id/user_btn_2"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:stateListAnimator="@null"
                            android:background="@drawable/btn_shape"
                            android:gravity="center_vertical"
                            android:text="combo"
                            android:textAllCaps="false"
                            android:textSize="14sp"
                            android:textColor="@color/title_color_light"
                            >
                        </Button>
                        <ImageView
                            android:id="@+id/user_image_2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="8dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            >
                        </ImageView>
                    </RelativeLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_marginTop="10dp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:weightSum="1">
                    <RelativeLayout
                        android:layout_marginLeft="0dp"
                        android:layout_width="0dp"
                        android:layout_height="39dp"
                        android:layout_weight="0.3"
                        >

                        <Button
                            android:id="@+id/user_btn_3"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:stateListAnimator="@null"
                            android:background="@drawable/btn_shape"
                            android:gravity="center_vertical"
                            android:text="trustzone"
                            android:textAllCaps="false"
                            android:textSize="14sp"
                            android:textColor="@color/title_color_light"
                            />

                        <ImageView
                            android:id="@+id/user_image_3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="8dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            >
                        </ImageView>
                    </RelativeLayout>
                    <View
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.05"
                        >
                    </View>
                    <RelativeLayout
                        android:layout_marginLeft="0dp"
                        android:layout_width="0dp"
                        android:layout_height="39dp"
                        android:layout_weight="0.3"
                        >

                        <Button
                            android:id="@+id/user_btn_4"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/btn_shape"
                            android:gravity="center_vertical"
                            android:stateListAnimator="@null"
                            android:text="multiple"
                            android:textAllCaps="false"
                            android:textColor="@color/title_color_light"
                            android:textSize="14sp"></Button>

                        <ImageView
                            android:id="@+id/user_image_4"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="8dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"></ImageView>
                    </RelativeLayout>
                    <View
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.05"
                        >
                    </View>
                    <RelativeLayout
                        android:layout_marginLeft="0dp"
                        android:layout_width="0dp"
                        android:layout_height="39dp"
                        android:layout_weight="0.3"
                        >

                        <Button
                            android:id="@+id/user_btn_5"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/btn_shape"
                            android:gravity="center_vertical"
                            android:stateListAnimator="@null"
                            android:text="bth"
                            android:textAllCaps="false"
                            android:textColor="@color/title_color_light"
                            android:textSize="14sp"></Button>
                        <ImageView
                            android:id="@+id/user_image_5"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="8dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            >
                        </ImageView>
                    </RelativeLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_marginTop="10dp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:weightSum="1">
                    <RelativeLayout
                        android:layout_marginLeft="0dp"
                        android:layout_width="0dp"
                        android:layout_height="39dp"
                        android:layout_weight="0.3"
                        >
                        <Button
                            android:id="@+id/user_btn_6"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:stateListAnimator="@null"
                            android:background="@drawable/btn_shape"
                            android:gravity="center_vertical"
                            android:text="diff file"
                            android:textAllCaps="false"
                            android:textSize="14sp"
                            android:textColor="@color/title_color_light"
                            >
                        </Button>
                        <ImageView
                            android:id="@+id/user_image_6"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="8dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            >
                        </ImageView>
                    </RelativeLayout>
                    <View
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.05"
                        >
                    </View>
                    <RelativeLayout
                        android:layout_marginLeft="0dp"
                        android:layout_width="0dp"
                        android:layout_height="39dp"
                        android:layout_weight="0.3"
                        >
                        <Button
                            android:id="@+id/user_btn_7"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:stateListAnimator="@null"
                            android:background="@drawable/btn_shape"
                            android:gravity="center_vertical"
                            android:text="zip files"
                            android:textAllCaps="false"
                            android:textSize="14sp"
                            android:textColor="@color/title_color_light"
                            >
                        </Button>
                        <ImageView
                            android:id="@+id/user_image_7"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="8dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            >
                        </ImageView>
                    </RelativeLayout>
                    <View
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.05"
                        >
                    </View>
                    <RelativeLayout
                        android:layout_marginLeft="0dp"
                        android:layout_width="0dp"
                        android:layout_height="39dp"
                        android:layout_weight="0.3"
                        android:visibility="invisible"
                        >
                        <Button
                            android:id="@+id/user_btn_8"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:stateListAnimator="@null"
                            android:background="@drawable/btn_shape"
                            android:gravity="center_vertical"
                            android:text="combo"
                            android:textAllCaps="false"
                            android:textSize="14sp"
                            android:textColor="@color/title_color_light"
                            >
                        </Button>
                        <ImageView
                            android:id="@+id/user_image_8"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="8dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            >
                        </ImageView>
                    </RelativeLayout>
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/view_response"
                android:layout_width="match_parent"
                android:layout_height="90dp"
                android:orientation="vertical">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:layout_marginLeft="20dp"
                    android:text="RESPONSE"
                    android:textSize="16sp"
                    android:textColor="@color/title_color"
                    android:fontFamily="@font/sanshan_bold">
                </TextView>
                <LinearLayout
                    android:layout_marginTop="10dp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:weightSum="1">
                    <RelativeLayout
                        android:layout_marginLeft="0dp"
                        android:layout_width="0dp"
                        android:layout_height="39dp"
                        android:layout_weight="0.47"
                        >
                        <Button
                            android:id="@+id/response_btn_0"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:stateListAnimator="@null"
                            android:background="@drawable/btn_shape"
                            android:gravity="center_vertical"
                            android:text="@string/ack_exist_no"
                            android:textSize="14sp"
                            android:textAllCaps="false"
                            android:textColor="@color/title_color_light"
                            >
                        </Button>
                        <ImageView
                            android:id="@+id/response_image_0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="8dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            >
                        </ImageView>
                    </RelativeLayout>
                    <View
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.06"
                        >
                    </View>
                    <RelativeLayout
                        android:layout_marginLeft="0dp"
                        android:layout_width="0dp"
                        android:layout_height="39dp"
                        android:layout_weight="0.47"
                        >
                        <Button
                            android:id="@+id/response_btn_1"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:stateListAnimator="@null"
                            android:background="@drawable/btn_shape"
                            android:gravity="center_vertical"
                            android:text="@string/ack_exist_yes"
                            android:textSize="14sp"
                            android:textAllCaps="false"
                            android:textColor="@color/title_color_light"
                            >
                        </Button>
                        <ImageView
                            android:id="@+id/response_image_1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="8dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            >
                        </ImageView>
                    </RelativeLayout>
                </LinearLayout>
            </LinearLayout>


            <LinearLayout
                android:id="@+id/view_upgradetype"
                android:layout_width="match_parent"
                android:layout_height="90dp"
                android:orientation="vertical">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="23dp"
                    android:layout_marginTop="3dp"
                    android:layout_marginLeft="20dp"
                    android:text="@string/ota_upgrade_type"
                    android:textSize="16sp"
                    android:textColor="@color/title_color"
                    android:fontFamily="@font/sanshan_bold">
                </TextView>
                <LinearLayout
                    android:layout_marginTop="10dp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:weightSum="1">
                    <RelativeLayout
                        android:layout_marginLeft="0dp"
                        android:layout_width="0dp"
                        android:layout_height="39dp"
                        android:layout_weight="0.47"
                        >
                        <Button
                            android:id="@+id/upgradetype_btn_0"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:stateListAnimator="@null"
                            android:background="@drawable/btn_shape"
                            android:gravity="center_vertical"
                            android:text="@string/ota_set_upgrade_type_slow"
                            android:textSize="14sp"
                            android:textAllCaps="false"
                            android:textColor="@color/title_color_light"
                            >
                        </Button>
                        <ImageView
                            android:id="@+id/upgradetype_image_0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="8dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            >
                        </ImageView>
                    </RelativeLayout>
                    <View
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.06"
                        >
                    </View>
                    <RelativeLayout
                        android:layout_marginLeft="0dp"
                        android:layout_width="0dp"
                        android:layout_height="39dp"
                        android:layout_weight="0.47"
                        >
                        <Button
                            android:id="@+id/upgradetype_btn_1"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:stateListAnimator="@null"
                            android:background="@drawable/btn_shape"
                            android:gravity="center_vertical"
                            android:text="@string/ota_set_upgrade_type_fast"
                            android:textSize="14sp"
                            android:textColor="@color/title_color_light"
                            android:textAllCaps="false"
                            >
                        </Button>
                        <ImageView
                            android:id="@+id/upgradetype_image_1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="8dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            >
                        </ImageView>
                    </RelativeLayout>
                </LinearLayout>
            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>