// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: alexaGadgetMusicDataTempoDirectivePayload.proto

package com.amazon.proto.avs.v20160207.alexaGadgetMusicData;

public final class TempoDirectivePayload {
  private TempoDirectivePayload() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface TempoDirectivePayloadProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:alexaGadgetMusicData.TempoDirectivePayloadProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 playerOffsetInMilliSeconds = 1;</code>
     * @return The playerOffsetInMilliSeconds.
     */
    int getPlayerOffsetInMilliSeconds();

    /**
     * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
     */
    java.util.List<com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData> 
        getTempoDataList();
    /**
     * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
     */
    com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData getTempoData(int index);
    /**
     * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
     */
    int getTempoDataCount();
    /**
     * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
     */
    java.util.List<? extends com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoDataOrBuilder> 
        getTempoDataOrBuilderList();
    /**
     * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
     */
    com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoDataOrBuilder getTempoDataOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code alexaGadgetMusicData.TempoDirectivePayloadProto}
   */
  public static final class TempoDirectivePayloadProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:alexaGadgetMusicData.TempoDirectivePayloadProto)
      TempoDirectivePayloadProtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TempoDirectivePayloadProto.newBuilder() to construct.
    private TempoDirectivePayloadProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TempoDirectivePayloadProto() {
      tempoData_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TempoDirectivePayloadProto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.class, com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.Builder.class);
    }

    public interface TempoDataOrBuilder extends
        // @@protoc_insertion_point(interface_extends:alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>int32 startOffsetInMilliSeconds = 2;</code>
       * @return The startOffsetInMilliSeconds.
       */
      int getStartOffsetInMilliSeconds();

      /**
       * <code>int32 value = 1;</code>
       * @return The value.
       */
      int getValue();
    }
    /**
     * Protobuf type {@code alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData}
     */
    public static final class TempoData extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData)
        TempoDataOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use TempoData.newBuilder() to construct.
      private TempoData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private TempoData() {
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new TempoData();
      }

      @java.lang.Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_TempoData_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_TempoData_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData.class, com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData.Builder.class);
      }

      public static final int STARTOFFSETINMILLISECONDS_FIELD_NUMBER = 2;
      private int startOffsetInMilliSeconds_;
      /**
       * <code>int32 startOffsetInMilliSeconds = 2;</code>
       * @return The startOffsetInMilliSeconds.
       */
      @java.lang.Override
      public int getStartOffsetInMilliSeconds() {
        return startOffsetInMilliSeconds_;
      }

      public static final int VALUE_FIELD_NUMBER = 1;
      private int value_;
      /**
       * <code>int32 value = 1;</code>
       * @return The value.
       */
      @java.lang.Override
      public int getValue() {
        return value_;
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (value_ != 0) {
          output.writeInt32(1, value_);
        }
        if (startOffsetInMilliSeconds_ != 0) {
          output.writeInt32(2, startOffsetInMilliSeconds_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (value_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(1, value_);
        }
        if (startOffsetInMilliSeconds_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(2, startOffsetInMilliSeconds_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData)) {
          return super.equals(obj);
        }
        com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData other = (com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData) obj;

        if (getStartOffsetInMilliSeconds()
            != other.getStartOffsetInMilliSeconds()) return false;
        if (getValue()
            != other.getValue()) return false;
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + STARTOFFSETINMILLISECONDS_FIELD_NUMBER;
        hash = (53 * hash) + getStartOffsetInMilliSeconds();
        hash = (37 * hash) + VALUE_FIELD_NUMBER;
        hash = (53 * hash) + getValue();
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData)
          com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoDataOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_TempoData_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_TempoData_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData.class, com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData.Builder.class);
        }

        // Construct using com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          startOffsetInMilliSeconds_ = 0;

          value_ = 0;

          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_TempoData_descriptor;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData getDefaultInstanceForType() {
          return com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData.getDefaultInstance();
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData build() {
          com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData buildPartial() {
          com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData result = new com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData(this);
          result.startOffsetInMilliSeconds_ = startOffsetInMilliSeconds_;
          result.value_ = value_;
          onBuilt();
          return result;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData) {
            return mergeFrom((com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData other) {
          if (other == com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData.getDefaultInstance()) return this;
          if (other.getStartOffsetInMilliSeconds() != 0) {
            setStartOffsetInMilliSeconds(other.getStartOffsetInMilliSeconds());
          }
          if (other.getValue() != 0) {
            setValue(other.getValue());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 8: {
                  value_ = input.readInt32();

                  break;
                } // case 8
                case 16: {
                  startOffsetInMilliSeconds_ = input.readInt32();

                  break;
                } // case 16
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }

        private int startOffsetInMilliSeconds_ ;
        /**
         * <code>int32 startOffsetInMilliSeconds = 2;</code>
         * @return The startOffsetInMilliSeconds.
         */
        @java.lang.Override
        public int getStartOffsetInMilliSeconds() {
          return startOffsetInMilliSeconds_;
        }
        /**
         * <code>int32 startOffsetInMilliSeconds = 2;</code>
         * @param value The startOffsetInMilliSeconds to set.
         * @return This builder for chaining.
         */
        public Builder setStartOffsetInMilliSeconds(int value) {
          
          startOffsetInMilliSeconds_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>int32 startOffsetInMilliSeconds = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearStartOffsetInMilliSeconds() {
          
          startOffsetInMilliSeconds_ = 0;
          onChanged();
          return this;
        }

        private int value_ ;
        /**
         * <code>int32 value = 1;</code>
         * @return The value.
         */
        @java.lang.Override
        public int getValue() {
          return value_;
        }
        /**
         * <code>int32 value = 1;</code>
         * @param value The value to set.
         * @return This builder for chaining.
         */
        public Builder setValue(int value) {
          
          value_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>int32 value = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearValue() {
          
          value_ = 0;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData)
      }

      // @@protoc_insertion_point(class_scope:alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData)
      private static final com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData();
      }

      public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<TempoData>
          PARSER = new com.google.protobuf.AbstractParser<TempoData>() {
        @java.lang.Override
        public TempoData parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<TempoData> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<TempoData> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public static final int PLAYEROFFSETINMILLISECONDS_FIELD_NUMBER = 1;
    private int playerOffsetInMilliSeconds_;
    /**
     * <code>int32 playerOffsetInMilliSeconds = 1;</code>
     * @return The playerOffsetInMilliSeconds.
     */
    @java.lang.Override
    public int getPlayerOffsetInMilliSeconds() {
      return playerOffsetInMilliSeconds_;
    }

    public static final int TEMPODATA_FIELD_NUMBER = 2;
    private java.util.List<com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData> tempoData_;
    /**
     * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData> getTempoDataList() {
      return tempoData_;
    }
    /**
     * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoDataOrBuilder> 
        getTempoDataOrBuilderList() {
      return tempoData_;
    }
    /**
     * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
     */
    @java.lang.Override
    public int getTempoDataCount() {
      return tempoData_.size();
    }
    /**
     * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData getTempoData(int index) {
      return tempoData_.get(index);
    }
    /**
     * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoDataOrBuilder getTempoDataOrBuilder(
        int index) {
      return tempoData_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (playerOffsetInMilliSeconds_ != 0) {
        output.writeInt32(1, playerOffsetInMilliSeconds_);
      }
      for (int i = 0; i < tempoData_.size(); i++) {
        output.writeMessage(2, tempoData_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (playerOffsetInMilliSeconds_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, playerOffsetInMilliSeconds_);
      }
      for (int i = 0; i < tempoData_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, tempoData_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto)) {
        return super.equals(obj);
      }
      com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto other = (com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto) obj;

      if (getPlayerOffsetInMilliSeconds()
          != other.getPlayerOffsetInMilliSeconds()) return false;
      if (!getTempoDataList()
          .equals(other.getTempoDataList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + PLAYEROFFSETINMILLISECONDS_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerOffsetInMilliSeconds();
      if (getTempoDataCount() > 0) {
        hash = (37 * hash) + TEMPODATA_FIELD_NUMBER;
        hash = (53 * hash) + getTempoDataList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code alexaGadgetMusicData.TempoDirectivePayloadProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:alexaGadgetMusicData.TempoDirectivePayloadProto)
        com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.class, com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.Builder.class);
      }

      // Construct using com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerOffsetInMilliSeconds_ = 0;

        if (tempoDataBuilder_ == null) {
          tempoData_ = java.util.Collections.emptyList();
        } else {
          tempoData_ = null;
          tempoDataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_descriptor;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto getDefaultInstanceForType() {
        return com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto build() {
        com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto buildPartial() {
        com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto result = new com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto(this);
        int from_bitField0_ = bitField0_;
        result.playerOffsetInMilliSeconds_ = playerOffsetInMilliSeconds_;
        if (tempoDataBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            tempoData_ = java.util.Collections.unmodifiableList(tempoData_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.tempoData_ = tempoData_;
        } else {
          result.tempoData_ = tempoDataBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto) {
          return mergeFrom((com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto other) {
        if (other == com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.getDefaultInstance()) return this;
        if (other.getPlayerOffsetInMilliSeconds() != 0) {
          setPlayerOffsetInMilliSeconds(other.getPlayerOffsetInMilliSeconds());
        }
        if (tempoDataBuilder_ == null) {
          if (!other.tempoData_.isEmpty()) {
            if (tempoData_.isEmpty()) {
              tempoData_ = other.tempoData_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureTempoDataIsMutable();
              tempoData_.addAll(other.tempoData_);
            }
            onChanged();
          }
        } else {
          if (!other.tempoData_.isEmpty()) {
            if (tempoDataBuilder_.isEmpty()) {
              tempoDataBuilder_.dispose();
              tempoDataBuilder_ = null;
              tempoData_ = other.tempoData_;
              bitField0_ = (bitField0_ & ~0x00000001);
              tempoDataBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getTempoDataFieldBuilder() : null;
            } else {
              tempoDataBuilder_.addAllMessages(other.tempoData_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                playerOffsetInMilliSeconds_ = input.readInt32();

                break;
              } // case 8
              case 18: {
                com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData m =
                    input.readMessage(
                        com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData.parser(),
                        extensionRegistry);
                if (tempoDataBuilder_ == null) {
                  ensureTempoDataIsMutable();
                  tempoData_.add(m);
                } else {
                  tempoDataBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int playerOffsetInMilliSeconds_ ;
      /**
       * <code>int32 playerOffsetInMilliSeconds = 1;</code>
       * @return The playerOffsetInMilliSeconds.
       */
      @java.lang.Override
      public int getPlayerOffsetInMilliSeconds() {
        return playerOffsetInMilliSeconds_;
      }
      /**
       * <code>int32 playerOffsetInMilliSeconds = 1;</code>
       * @param value The playerOffsetInMilliSeconds to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerOffsetInMilliSeconds(int value) {
        
        playerOffsetInMilliSeconds_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 playerOffsetInMilliSeconds = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerOffsetInMilliSeconds() {
        
        playerOffsetInMilliSeconds_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData> tempoData_ =
        java.util.Collections.emptyList();
      private void ensureTempoDataIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          tempoData_ = new java.util.ArrayList<com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData>(tempoData_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData, com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData.Builder, com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoDataOrBuilder> tempoDataBuilder_;

      /**
       * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
       */
      public java.util.List<com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData> getTempoDataList() {
        if (tempoDataBuilder_ == null) {
          return java.util.Collections.unmodifiableList(tempoData_);
        } else {
          return tempoDataBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
       */
      public int getTempoDataCount() {
        if (tempoDataBuilder_ == null) {
          return tempoData_.size();
        } else {
          return tempoDataBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData getTempoData(int index) {
        if (tempoDataBuilder_ == null) {
          return tempoData_.get(index);
        } else {
          return tempoDataBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
       */
      public Builder setTempoData(
          int index, com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData value) {
        if (tempoDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTempoDataIsMutable();
          tempoData_.set(index, value);
          onChanged();
        } else {
          tempoDataBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
       */
      public Builder setTempoData(
          int index, com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData.Builder builderForValue) {
        if (tempoDataBuilder_ == null) {
          ensureTempoDataIsMutable();
          tempoData_.set(index, builderForValue.build());
          onChanged();
        } else {
          tempoDataBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
       */
      public Builder addTempoData(com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData value) {
        if (tempoDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTempoDataIsMutable();
          tempoData_.add(value);
          onChanged();
        } else {
          tempoDataBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
       */
      public Builder addTempoData(
          int index, com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData value) {
        if (tempoDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTempoDataIsMutable();
          tempoData_.add(index, value);
          onChanged();
        } else {
          tempoDataBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
       */
      public Builder addTempoData(
          com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData.Builder builderForValue) {
        if (tempoDataBuilder_ == null) {
          ensureTempoDataIsMutable();
          tempoData_.add(builderForValue.build());
          onChanged();
        } else {
          tempoDataBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
       */
      public Builder addTempoData(
          int index, com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData.Builder builderForValue) {
        if (tempoDataBuilder_ == null) {
          ensureTempoDataIsMutable();
          tempoData_.add(index, builderForValue.build());
          onChanged();
        } else {
          tempoDataBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
       */
      public Builder addAllTempoData(
          java.lang.Iterable<? extends com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData> values) {
        if (tempoDataBuilder_ == null) {
          ensureTempoDataIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, tempoData_);
          onChanged();
        } else {
          tempoDataBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
       */
      public Builder clearTempoData() {
        if (tempoDataBuilder_ == null) {
          tempoData_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          tempoDataBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
       */
      public Builder removeTempoData(int index) {
        if (tempoDataBuilder_ == null) {
          ensureTempoDataIsMutable();
          tempoData_.remove(index);
          onChanged();
        } else {
          tempoDataBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData.Builder getTempoDataBuilder(
          int index) {
        return getTempoDataFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoDataOrBuilder getTempoDataOrBuilder(
          int index) {
        if (tempoDataBuilder_ == null) {
          return tempoData_.get(index);  } else {
          return tempoDataBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
       */
      public java.util.List<? extends com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoDataOrBuilder> 
           getTempoDataOrBuilderList() {
        if (tempoDataBuilder_ != null) {
          return tempoDataBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(tempoData_);
        }
      }
      /**
       * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData.Builder addTempoDataBuilder() {
        return getTempoDataFieldBuilder().addBuilder(
            com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData.getDefaultInstance());
      }
      /**
       * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData.Builder addTempoDataBuilder(
          int index) {
        return getTempoDataFieldBuilder().addBuilder(
            index, com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData.getDefaultInstance());
      }
      /**
       * <code>repeated .alexaGadgetMusicData.TempoDirectivePayloadProto.TempoData tempoData = 2;</code>
       */
      public java.util.List<com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData.Builder> 
           getTempoDataBuilderList() {
        return getTempoDataFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData, com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData.Builder, com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoDataOrBuilder> 
          getTempoDataFieldBuilder() {
        if (tempoDataBuilder_ == null) {
          tempoDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData, com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoData.Builder, com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto.TempoDataOrBuilder>(
                  tempoData_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          tempoData_ = null;
        }
        return tempoDataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:alexaGadgetMusicData.TempoDirectivePayloadProto)
    }

    // @@protoc_insertion_point(class_scope:alexaGadgetMusicData.TempoDirectivePayloadProto)
    private static final com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto();
    }

    public static com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TempoDirectivePayloadProto>
        PARSER = new com.google.protobuf.AbstractParser<TempoDirectivePayloadProto>() {
      @java.lang.Override
      public TempoDirectivePayloadProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<TempoDirectivePayloadProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TempoDirectivePayloadProto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alexaGadgetMusicData.TempoDirectivePayload.TempoDirectivePayloadProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_TempoData_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_TempoData_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n/alexaGadgetMusicDataTempoDirectivePayl" +
      "oad.proto\022\024alexaGadgetMusicData\"\316\001\n\032Temp" +
      "oDirectivePayloadProto\022\"\n\032playerOffsetIn" +
      "MilliSeconds\030\001 \001(\005\022M\n\ttempoData\030\002 \003(\0132:." +
      "alexaGadgetMusicData.TempoDirectivePaylo" +
      "adProto.TempoData\032=\n\tTempoData\022!\n\031startO" +
      "ffsetInMilliSeconds\030\002 \001(\005\022\r\n\005value\030\001 \001(\005" +
      "BL\n3com.amazon.proto.avs.v20160207.alexa" +
      "GadgetMusicDataB\025TempoDirectivePayloadb\006" +
      "proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_descriptor,
        new java.lang.String[] { "PlayerOffsetInMilliSeconds", "TempoData", });
    internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_TempoData_descriptor =
      internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_descriptor.getNestedTypes().get(0);
    internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_TempoData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_alexaGadgetMusicData_TempoDirectivePayloadProto_TempoData_descriptor,
        new java.lang.String[] { "StartOffsetInMilliSeconds", "Value", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
