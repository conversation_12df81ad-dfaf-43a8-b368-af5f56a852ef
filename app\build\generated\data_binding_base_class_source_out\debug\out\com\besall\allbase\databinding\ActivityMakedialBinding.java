// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMakedialBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView curDialSize;

  @NonNull
  public final Button dialButton;

  @NonNull
  public final ImageView dialTabBackgroundImage;

  @NonNull
  public final TextView dialTabBackgroundText;

  @NonNull
  public final ImageView dialTabColorImage;

  @NonNull
  public final TextView dialTabColorText;

  @NonNull
  public final ImageView dialTabStyleImage;

  @NonNull
  public final TextView dialTabStyleText;

  @NonNull
  public final ListView listView;

  @NonNull
  public final TextView photoResolution;

  @NonNull
  public final ToolbarBinding tool;

  private ActivityMakedialBinding(@NonNull LinearLayout rootView, @NonNull TextView curDialSize,
      @NonNull Button dialButton, @NonNull ImageView dialTabBackgroundImage,
      @NonNull TextView dialTabBackgroundText, @NonNull ImageView dialTabColorImage,
      @NonNull TextView dialTabColorText, @NonNull ImageView dialTabStyleImage,
      @NonNull TextView dialTabStyleText, @NonNull ListView listView,
      @NonNull TextView photoResolution, @NonNull ToolbarBinding tool) {
    this.rootView = rootView;
    this.curDialSize = curDialSize;
    this.dialButton = dialButton;
    this.dialTabBackgroundImage = dialTabBackgroundImage;
    this.dialTabBackgroundText = dialTabBackgroundText;
    this.dialTabColorImage = dialTabColorImage;
    this.dialTabColorText = dialTabColorText;
    this.dialTabStyleImage = dialTabStyleImage;
    this.dialTabStyleText = dialTabStyleText;
    this.listView = listView;
    this.photoResolution = photoResolution;
    this.tool = tool;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMakedialBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMakedialBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_makedial, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMakedialBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cur_dial_size;
      TextView curDialSize = rootView.findViewById(id);
      if (curDialSize == null) {
        break missingId;
      }

      id = R.id.dial_button;
      Button dialButton = rootView.findViewById(id);
      if (dialButton == null) {
        break missingId;
      }

      id = R.id.dial_tab_background_image;
      ImageView dialTabBackgroundImage = rootView.findViewById(id);
      if (dialTabBackgroundImage == null) {
        break missingId;
      }

      id = R.id.dial_tab_background_text;
      TextView dialTabBackgroundText = rootView.findViewById(id);
      if (dialTabBackgroundText == null) {
        break missingId;
      }

      id = R.id.dial_tab_color_image;
      ImageView dialTabColorImage = rootView.findViewById(id);
      if (dialTabColorImage == null) {
        break missingId;
      }

      id = R.id.dial_tab_color_text;
      TextView dialTabColorText = rootView.findViewById(id);
      if (dialTabColorText == null) {
        break missingId;
      }

      id = R.id.dial_tab_style_image;
      ImageView dialTabStyleImage = rootView.findViewById(id);
      if (dialTabStyleImage == null) {
        break missingId;
      }

      id = R.id.dial_tab_style_text;
      TextView dialTabStyleText = rootView.findViewById(id);
      if (dialTabStyleText == null) {
        break missingId;
      }

      id = R.id.list_view;
      ListView listView = rootView.findViewById(id);
      if (listView == null) {
        break missingId;
      }

      id = R.id.photo_resolution;
      TextView photoResolution = rootView.findViewById(id);
      if (photoResolution == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      return new ActivityMakedialBinding((LinearLayout) rootView, curDialSize, dialButton,
          dialTabBackgroundImage, dialTabBackgroundText, dialTabColorImage, dialTabColorText,
          dialTabStyleImage, dialTabStyleText, listView, photoResolution, binding_tool);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
