<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_eq_pc" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_eq_pc.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_eq_pc_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="346" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/activity_eq_pc_0" include="toolbar"><Expressions/><location startLine="12" startOffset="4" endLine="15" endOffset="9"/></Target><Target id="@+id/radiogroup_function" view="RadioGroup"><Expressions/><location startLine="16" startOffset="4" endLine="47" endOffset="16"/></Target><Target id="@+id/iir_eq" view="RadioButton"><Expressions/><location startLine="22" startOffset="8" endLine="30" endOffset="21"/></Target><Target id="@+id/drc" view="RadioButton"><Expressions/><location startLine="31" startOffset="8" endLine="38" endOffset="21"/></Target><Target id="@+id/limiter" view="RadioButton"><Expressions/><location startLine="39" startOffset="8" endLine="46" endOffset="21"/></Target><Target id="@+id/msgview" view="TextView"><Expressions/><location startLine="49" startOffset="4" endLine="59" endOffset="14"/></Target><Target id="@+id/iir_eq_view_title" view="LinearLayout"><Expressions/><location startLine="61" startOffset="4" endLine="100" endOffset="18"/></Target><Target id="@+id/recyclerview_content" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="102" startOffset="4" endLine="108" endOffset="9"/></Target><Target id="@+id/preview" view="Button"><Expressions/><location startLine="123" startOffset="16" endLine="130" endOffset="24"/></Target><Target id="@+id/eq_function_btn" view="Button"><Expressions/><location startLine="132" startOffset="16" endLine="138" endOffset="24"/></Target><Target id="@+id/audition" view="Button"><Expressions/><location startLine="140" startOffset="16" endLine="146" endOffset="24"/></Target><Target id="@+id/gain_left_et" view="EditText"><Expressions/><location startLine="171" startOffset="16" endLine="179" endOffset="26"/></Target><Target id="@+id/gain_right_et" view="EditText"><Expressions/><location startLine="204" startOffset="16" endLine="212" endOffset="26"/></Target><Target id="@+id/eq_open_btn" view="Button"><Expressions/><location startLine="238" startOffset="16" endLine="244" endOffset="24"/></Target><Target id="@+id/eq_writeflash_btn" view="Button"><Expressions/><location startLine="246" startOffset="16" endLine="252" endOffset="24"/></Target><Target id="@+id/eq_model_spinner" view="Spinner"><Expressions/><location startLine="278" startOffset="16" endLine="286" endOffset="25"/></Target><Target id="@+id/iir_type_spinner" view="Spinner"><Expressions/><location startLine="301" startOffset="16" endLine="309" endOffset="25"/></Target><Target id="@+id/save_as" view="Button"><Expressions/><location startLine="321" startOffset="16" endLine="327" endOffset="24"/></Target><Target id="@+id/load" view="Button"><Expressions/><location startLine="329" startOffset="16" endLine="335" endOffset="24"/></Target></Targets></Layout>