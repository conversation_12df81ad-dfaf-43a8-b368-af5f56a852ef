// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class OtaConfigBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button cancel;

  @NonNull
  public final RadioGroup clearUserData;

  @NonNull
  public final RadioButton clearUserDataNo;

  @NonNull
  public final RadioButton clearUserDataYes;

  @NonNull
  public final Button ok;

  @NonNull
  public final RadioGroup updateBleAddr;

  @NonNull
  public final EditText updateBleAddrInput;

  @NonNull
  public final RadioButton updateBleAddrNo;

  @NonNull
  public final RadioButton updateBleAddrYes;

  @NonNull
  public final RadioGroup updateBleName;

  @NonNull
  public final EditText updateBleNameInput;

  @NonNull
  public final RadioButton updateBleNameNo;

  @NonNull
  public final RadioButton updateBleNameYes;

  @NonNull
  public final RadioGroup updateBtAddr;

  @NonNull
  public final EditText updateBtAddrInput;

  @NonNull
  public final RadioButton updateBtAddrNo;

  @NonNull
  public final RadioButton updateBtAddrYes;

  @NonNull
  public final RadioGroup updateBtName;

  @NonNull
  public final EditText updateBtNameInput;

  @NonNull
  public final RadioButton updateBtNameNo;

  @NonNull
  public final RadioButton updateBtNameYes;

  private OtaConfigBinding(@NonNull ScrollView rootView, @NonNull Button cancel,
      @NonNull RadioGroup clearUserData, @NonNull RadioButton clearUserDataNo,
      @NonNull RadioButton clearUserDataYes, @NonNull Button ok, @NonNull RadioGroup updateBleAddr,
      @NonNull EditText updateBleAddrInput, @NonNull RadioButton updateBleAddrNo,
      @NonNull RadioButton updateBleAddrYes, @NonNull RadioGroup updateBleName,
      @NonNull EditText updateBleNameInput, @NonNull RadioButton updateBleNameNo,
      @NonNull RadioButton updateBleNameYes, @NonNull RadioGroup updateBtAddr,
      @NonNull EditText updateBtAddrInput, @NonNull RadioButton updateBtAddrNo,
      @NonNull RadioButton updateBtAddrYes, @NonNull RadioGroup updateBtName,
      @NonNull EditText updateBtNameInput, @NonNull RadioButton updateBtNameNo,
      @NonNull RadioButton updateBtNameYes) {
    this.rootView = rootView;
    this.cancel = cancel;
    this.clearUserData = clearUserData;
    this.clearUserDataNo = clearUserDataNo;
    this.clearUserDataYes = clearUserDataYes;
    this.ok = ok;
    this.updateBleAddr = updateBleAddr;
    this.updateBleAddrInput = updateBleAddrInput;
    this.updateBleAddrNo = updateBleAddrNo;
    this.updateBleAddrYes = updateBleAddrYes;
    this.updateBleName = updateBleName;
    this.updateBleNameInput = updateBleNameInput;
    this.updateBleNameNo = updateBleNameNo;
    this.updateBleNameYes = updateBleNameYes;
    this.updateBtAddr = updateBtAddr;
    this.updateBtAddrInput = updateBtAddrInput;
    this.updateBtAddrNo = updateBtAddrNo;
    this.updateBtAddrYes = updateBtAddrYes;
    this.updateBtName = updateBtName;
    this.updateBtNameInput = updateBtNameInput;
    this.updateBtNameNo = updateBtNameNo;
    this.updateBtNameYes = updateBtNameYes;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static OtaConfigBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static OtaConfigBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.ota_config, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static OtaConfigBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cancel;
      Button cancel = rootView.findViewById(id);
      if (cancel == null) {
        break missingId;
      }

      id = R.id.clear_user_data;
      RadioGroup clearUserData = rootView.findViewById(id);
      if (clearUserData == null) {
        break missingId;
      }

      id = R.id.clear_user_data_no;
      RadioButton clearUserDataNo = rootView.findViewById(id);
      if (clearUserDataNo == null) {
        break missingId;
      }

      id = R.id.clear_user_data_yes;
      RadioButton clearUserDataYes = rootView.findViewById(id);
      if (clearUserDataYes == null) {
        break missingId;
      }

      id = R.id.ok;
      Button ok = rootView.findViewById(id);
      if (ok == null) {
        break missingId;
      }

      id = R.id.update_ble_addr;
      RadioGroup updateBleAddr = rootView.findViewById(id);
      if (updateBleAddr == null) {
        break missingId;
      }

      id = R.id.update_ble_addr_input;
      EditText updateBleAddrInput = rootView.findViewById(id);
      if (updateBleAddrInput == null) {
        break missingId;
      }

      id = R.id.update_ble_addr_no;
      RadioButton updateBleAddrNo = rootView.findViewById(id);
      if (updateBleAddrNo == null) {
        break missingId;
      }

      id = R.id.update_ble_addr_yes;
      RadioButton updateBleAddrYes = rootView.findViewById(id);
      if (updateBleAddrYes == null) {
        break missingId;
      }

      id = R.id.update_ble_name;
      RadioGroup updateBleName = rootView.findViewById(id);
      if (updateBleName == null) {
        break missingId;
      }

      id = R.id.update_ble_name_input;
      EditText updateBleNameInput = rootView.findViewById(id);
      if (updateBleNameInput == null) {
        break missingId;
      }

      id = R.id.update_ble_name_no;
      RadioButton updateBleNameNo = rootView.findViewById(id);
      if (updateBleNameNo == null) {
        break missingId;
      }

      id = R.id.update_ble_name_yes;
      RadioButton updateBleNameYes = rootView.findViewById(id);
      if (updateBleNameYes == null) {
        break missingId;
      }

      id = R.id.update_bt_addr;
      RadioGroup updateBtAddr = rootView.findViewById(id);
      if (updateBtAddr == null) {
        break missingId;
      }

      id = R.id.update_bt_addr_input;
      EditText updateBtAddrInput = rootView.findViewById(id);
      if (updateBtAddrInput == null) {
        break missingId;
      }

      id = R.id.update_bt_addr_no;
      RadioButton updateBtAddrNo = rootView.findViewById(id);
      if (updateBtAddrNo == null) {
        break missingId;
      }

      id = R.id.update_bt_addr_yes;
      RadioButton updateBtAddrYes = rootView.findViewById(id);
      if (updateBtAddrYes == null) {
        break missingId;
      }

      id = R.id.update_bt_name;
      RadioGroup updateBtName = rootView.findViewById(id);
      if (updateBtName == null) {
        break missingId;
      }

      id = R.id.update_bt_name_input;
      EditText updateBtNameInput = rootView.findViewById(id);
      if (updateBtNameInput == null) {
        break missingId;
      }

      id = R.id.update_bt_name_no;
      RadioButton updateBtNameNo = rootView.findViewById(id);
      if (updateBtNameNo == null) {
        break missingId;
      }

      id = R.id.update_bt_name_yes;
      RadioButton updateBtNameYes = rootView.findViewById(id);
      if (updateBtNameYes == null) {
        break missingId;
      }

      return new OtaConfigBinding((ScrollView) rootView, cancel, clearUserData, clearUserDataNo,
          clearUserDataYes, ok, updateBleAddr, updateBleAddrInput, updateBleAddrNo,
          updateBleAddrYes, updateBleName, updateBleNameInput, updateBleNameNo, updateBleNameYes,
          updateBtAddr, updateBtAddrInput, updateBtAddrNo, updateBtAddrYes, updateBtName,
          updateBtNameInput, updateBtNameNo, updateBtNameYes);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
