<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_cuscmd" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_cuscmd.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_cuscmd_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="351" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/activity_cuscmd_0" include="toolbar"><Expressions/><location startLine="8" startOffset="4" endLine="11" endOffset="9"/></Target><Target id="@+id/loginfo" tag="layout/activity_cuscmd_0" include="logview"><Expressions/><location startLine="13" startOffset="4" endLine="16" endOffset="34"/></Target><Target id="@+id/cmd_header" view="RadioGroup"><Expressions/><location startLine="32" startOffset="12" endLine="73" endOffset="24"/></Target><Target id="@+id/use_cmd_header" view="RadioButton"><Expressions/><location startLine="49" startOffset="16" endLine="54" endOffset="21"/></Target><Target id="@+id/nouse_cmd_header" view="RadioButton"><Expressions/><location startLine="67" startOffset="16" endLine="71" endOffset="21"/></Target><Target id="@+id/cmd_need_efffective_length" view="RadioGroup"><Expressions/><location startLine="83" startOffset="12" endLine="123" endOffset="24"/></Target><Target id="@+id/cmd_need_efffective_length_no" view="RadioButton"><Expressions/><location startLine="100" startOffset="16" endLine="105" endOffset="21"/></Target><Target id="@+id/cmd_need_efffective_length_yes" view="RadioButton"><Expressions/><location startLine="117" startOffset="16" endLine="121" endOffset="21"/></Target><Target id="@+id/file_path" view="TextView"><Expressions/><location startLine="134" startOffset="8" endLine="141" endOffset="37"/></Target><Target id="@+id/cur_cmd_info" view="TextView"><Expressions/><location startLine="157" startOffset="12" endLine="164" endOffset="41"/></Target><Target id="@+id/cmd_typing" view="RelativeLayout"><Expressions/><location startLine="183" startOffset="16" endLine="236" endOffset="32"/></Target><Target id="@+id/account_login_detete" view="ImageView"><Expressions/><location startLine="209" startOffset="20" endLine="216" endOffset="51"/></Target><Target id="@+id/cmd_ed" view="EditText"><Expressions/><location startLine="219" startOffset="20" endLine="233" endOffset="49"/></Target><Target id="@+id/login_account_textview" view="TextView"><Expressions/><location startLine="238" startOffset="16" endLine="242" endOffset="22"/></Target><Target id="@+id/data_send" view="ImageButton"><Expressions/><location startLine="247" startOffset="12" endLine="252" endOffset="70"/></Target><Target id="@+id/receive_info" view="TextView"><Expressions/><location startLine="256" startOffset="8" endLine="268" endOffset="13"/></Target><Target id="@+id/ImportCmd" view="Button"><Expressions/><location startLine="278" startOffset="12" endLine="292" endOffset="17"/></Target><Target id="@+id/cmd_save" view="Button"><Expressions/><location startLine="300" startOffset="12" endLine="313" endOffset="44"/></Target><Target id="@+id/datamode" view="Spinner"><Expressions/><location startLine="317" startOffset="8" endLine="324" endOffset="13"/></Target><Target id="@+id/spp_stop" view="Button"><Expressions/><location startLine="335" startOffset="8" endLine="349" endOffset="16"/></Target></Targets></Layout>