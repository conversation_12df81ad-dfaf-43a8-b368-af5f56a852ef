// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import com.suke.widget.SwitchButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActSettingBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button ackBtn0;

  @NonNull
  public final ImageView ackImage0;

  @NonNull
  public final ImageView actOtauiBg;

  @NonNull
  public final Button agree;

  @NonNull
  public final TextView agreeTV;

  @NonNull
  public final LinearLayout agreeView;

  @NonNull
  public final EditText bleInterval;

  @NonNull
  public final Button disagree;

  @NonNull
  public final EditText edittextBleOtaAutoTestInterval;

  @NonNull
  public final ListView locolLogList;

  @NonNull
  public final Button privacyPolicy;

  @NonNull
  public final ScrollView scrPolicy;

  @NonNull
  public final LinearLayout sendDelay;

  @NonNull
  public final EditText sppInterval;

  @NonNull
  public final SwitchButton switchButton;

  @NonNull
  public final SwitchButton switchButtonBleOtaAutoTest;

  @NonNull
  public final SwitchButton switchButtonPhy2m;

  @NonNull
  public final SwitchButton switchButtonTota;

  @NonNull
  public final SwitchButton switchButtonTotav2;

  @NonNull
  public final SwitchButton switchButtonUseNormalConnect;

  @NonNull
  public final ToolbarBinding tool;

  @NonNull
  public final Button versionButton;

  @NonNull
  public final TextView versionText;

  @NonNull
  public final ScrollView wholeScr;

  private ActSettingBinding(@NonNull ConstraintLayout rootView, @NonNull Button ackBtn0,
      @NonNull ImageView ackImage0, @NonNull ImageView actOtauiBg, @NonNull Button agree,
      @NonNull TextView agreeTV, @NonNull LinearLayout agreeView, @NonNull EditText bleInterval,
      @NonNull Button disagree, @NonNull EditText edittextBleOtaAutoTestInterval,
      @NonNull ListView locolLogList, @NonNull Button privacyPolicy, @NonNull ScrollView scrPolicy,
      @NonNull LinearLayout sendDelay, @NonNull EditText sppInterval,
      @NonNull SwitchButton switchButton, @NonNull SwitchButton switchButtonBleOtaAutoTest,
      @NonNull SwitchButton switchButtonPhy2m, @NonNull SwitchButton switchButtonTota,
      @NonNull SwitchButton switchButtonTotav2, @NonNull SwitchButton switchButtonUseNormalConnect,
      @NonNull ToolbarBinding tool, @NonNull Button versionButton, @NonNull TextView versionText,
      @NonNull ScrollView wholeScr) {
    this.rootView = rootView;
    this.ackBtn0 = ackBtn0;
    this.ackImage0 = ackImage0;
    this.actOtauiBg = actOtauiBg;
    this.agree = agree;
    this.agreeTV = agreeTV;
    this.agreeView = agreeView;
    this.bleInterval = bleInterval;
    this.disagree = disagree;
    this.edittextBleOtaAutoTestInterval = edittextBleOtaAutoTestInterval;
    this.locolLogList = locolLogList;
    this.privacyPolicy = privacyPolicy;
    this.scrPolicy = scrPolicy;
    this.sendDelay = sendDelay;
    this.sppInterval = sppInterval;
    this.switchButton = switchButton;
    this.switchButtonBleOtaAutoTest = switchButtonBleOtaAutoTest;
    this.switchButtonPhy2m = switchButtonPhy2m;
    this.switchButtonTota = switchButtonTota;
    this.switchButtonTotav2 = switchButtonTotav2;
    this.switchButtonUseNormalConnect = switchButtonUseNormalConnect;
    this.tool = tool;
    this.versionButton = versionButton;
    this.versionText = versionText;
    this.wholeScr = wholeScr;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActSettingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActSettingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.act_setting, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActSettingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ack_btn_0;
      Button ackBtn0 = rootView.findViewById(id);
      if (ackBtn0 == null) {
        break missingId;
      }

      id = R.id.ack_image_0;
      ImageView ackImage0 = rootView.findViewById(id);
      if (ackImage0 == null) {
        break missingId;
      }

      id = R.id.act_otaui_bg;
      ImageView actOtauiBg = rootView.findViewById(id);
      if (actOtauiBg == null) {
        break missingId;
      }

      id = R.id.agree;
      Button agree = rootView.findViewById(id);
      if (agree == null) {
        break missingId;
      }

      id = R.id.agreeTV;
      TextView agreeTV = rootView.findViewById(id);
      if (agreeTV == null) {
        break missingId;
      }

      id = R.id.agree_view;
      LinearLayout agreeView = rootView.findViewById(id);
      if (agreeView == null) {
        break missingId;
      }

      id = R.id.ble_interval;
      EditText bleInterval = rootView.findViewById(id);
      if (bleInterval == null) {
        break missingId;
      }

      id = R.id.disagree;
      Button disagree = rootView.findViewById(id);
      if (disagree == null) {
        break missingId;
      }

      id = R.id.edittext_ble_ota_auto_test_interval;
      EditText edittextBleOtaAutoTestInterval = rootView.findViewById(id);
      if (edittextBleOtaAutoTestInterval == null) {
        break missingId;
      }

      id = R.id.locolLog_list;
      ListView locolLogList = rootView.findViewById(id);
      if (locolLogList == null) {
        break missingId;
      }

      id = R.id.privacy_policy;
      Button privacyPolicy = rootView.findViewById(id);
      if (privacyPolicy == null) {
        break missingId;
      }

      id = R.id.scr_policy;
      ScrollView scrPolicy = rootView.findViewById(id);
      if (scrPolicy == null) {
        break missingId;
      }

      id = R.id.send_delay;
      LinearLayout sendDelay = rootView.findViewById(id);
      if (sendDelay == null) {
        break missingId;
      }

      id = R.id.spp_interval;
      EditText sppInterval = rootView.findViewById(id);
      if (sppInterval == null) {
        break missingId;
      }

      id = R.id.switchButton;
      SwitchButton switchButton = rootView.findViewById(id);
      if (switchButton == null) {
        break missingId;
      }

      id = R.id.switchButton_ble_ota_auto_test;
      SwitchButton switchButtonBleOtaAutoTest = rootView.findViewById(id);
      if (switchButtonBleOtaAutoTest == null) {
        break missingId;
      }

      id = R.id.switchButton_phy2m;
      SwitchButton switchButtonPhy2m = rootView.findViewById(id);
      if (switchButtonPhy2m == null) {
        break missingId;
      }

      id = R.id.switchButton_tota;
      SwitchButton switchButtonTota = rootView.findViewById(id);
      if (switchButtonTota == null) {
        break missingId;
      }

      id = R.id.switchButton_totav2;
      SwitchButton switchButtonTotav2 = rootView.findViewById(id);
      if (switchButtonTotav2 == null) {
        break missingId;
      }

      id = R.id.switchButton_use_normal_connect;
      SwitchButton switchButtonUseNormalConnect = rootView.findViewById(id);
      if (switchButtonUseNormalConnect == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      id = R.id.version_button;
      Button versionButton = rootView.findViewById(id);
      if (versionButton == null) {
        break missingId;
      }

      id = R.id.version_text;
      TextView versionText = rootView.findViewById(id);
      if (versionText == null) {
        break missingId;
      }

      id = R.id.whole_scr;
      ScrollView wholeScr = rootView.findViewById(id);
      if (wholeScr == null) {
        break missingId;
      }

      return new ActSettingBinding((ConstraintLayout) rootView, ackBtn0, ackImage0, actOtauiBg,
          agree, agreeTV, agreeView, bleInterval, disagree, edittextBleOtaAutoTestInterval,
          locolLogList, privacyPolicy, scrPolicy, sendDelay, sppInterval, switchButton,
          switchButtonBleOtaAutoTest, switchButtonPhy2m, switchButtonTota, switchButtonTotav2,
          switchButtonUseNormalConnect, binding_tool, versionButton, versionText, wholeScr);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
