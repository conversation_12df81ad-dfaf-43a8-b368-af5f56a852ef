package com.besall.allbase.bluetooth.service.crashdump;

import android.content.Context;
import android.util.Log;

import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.CmdInfo;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.common.utils.FileUtils;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

import static com.besall.allbase.bluetooth.service.crashdump.CrashDumpConstants.CMD_6504_ERROR;
import static com.besall.allbase.bluetooth.service.crashdump.CrashDumpConstants.CMD_LENGTH_ERROR;
import static com.besall.allbase.bluetooth.service.crashdump.CrashDumpConstants.CMD_ROLE_ERROR;
import static com.besall.allbase.bluetooth.service.crashdump.CrashDumpConstants.CMD_TOTA_CRASH_DUMP_END;
import static com.besall.allbase.bluetooth.service.crashdump.CrashDumpConstants.CMD_TOTA_CRASH_DUMP_PARAM_REQ;
import static com.besall.allbase.bluetooth.service.crashdump.CrashDumpConstants.CMD_TOTA_CRASH_DUMP_RECEIVED_ACK;
import static com.besall.allbase.bluetooth.service.crashdump.CrashDumpConstants.CMD_TOTA_CRASH_DUMP_START;
import static com.besall.allbase.bluetooth.service.crashdump.CrashDumpConstants.NO_CRASHDATA;
import static com.besall.allbase.bluetooth.service.crashdump.CrashDumpConstants.OP_TOTA_CRASH_DUMP_END;
import static com.besall.allbase.bluetooth.service.crashdump.CrashDumpConstants.OP_TOTA_CRASH_DUMP_PARAM_REQ;
import static com.besall.allbase.bluetooth.service.crashdump.CrashDumpConstants.OP_TOTA_CRASH_DUMP_RECEIVED_ACK;
import static com.besall.allbase.bluetooth.service.crashdump.CrashDumpConstants.OP_TOTA_CRASH_DUMP_START_REQ;


public class CrashDumpCMD {
    static String TAG = "CrashDumpCMD";
    private static byte[][] cmdArray;
    private static byte[][] msgArray;
    private static int cmdSendNum = 0;
    private static int curFileType = 0;//0: crash 1: log
    private static int curIdLength = 0;
    private static int curIdSendLength = 0;
    private static String curTime;
    private static String zip_path = "";


    public static byte[] crashDumpStart() {
        byte flashType = (byte) BluetoothConstants.Connect.CMD_CONFIRM;
        CmdInfo cmdinforeq = new CmdInfo(CrashDumpConstants.OP_TOTA_CRASH_DUMP_REQ, new byte[]{flashType});
        return cmdinforeq.toBytes();
    }

    public static byte[] crashDumpCMD() {
        return cmdArray[cmdSendNum];
    }

    public static byte[] crashDumpStartDownload() {
        CmdInfo cmdinfo = new CmdInfo(OP_TOTA_CRASH_DUMP_START_REQ, new byte[]{});
        byte[] bytes = new byte[6];
        bytes[0] = cmdinfo.toBytes()[0];
        bytes[1] = cmdinfo.toBytes()[1];
        bytes[2] = 0x02;
        bytes[3] = 0x00;
        bytes[4] = cmdArray[cmdSendNum][4];//role
        bytes[5] = curFileType == 0 ? (byte)0x00 : 0x01;
        return bytes;
    }

    public static byte[] crashDumpAckCMD() {
        CmdInfo cmdinfoack = new CmdInfo(OP_TOTA_CRASH_DUMP_RECEIVED_ACK, new byte[]{});
        byte[] byteAck = new byte[6];
        byteAck[0] = cmdinfoack.toBytes()[0];
        byteAck[1] = cmdinfoack.toBytes()[1];
        byteAck[2] = 0x02;
        byteAck[3] = 0x00;
        byteAck[4] = cmdArray[cmdSendNum][4];//role
        byteAck[5] = 0x01;
        return byteAck;
    }

    public static byte[] crashDumpEnd() {
        CmdInfo cmdinfo = new CmdInfo(OP_TOTA_CRASH_DUMP_END, new byte[]{0x00, 0x00});
        return cmdinfo.toBytes();
    }


    public static int receiveData(byte[] data, Context context) {
        if (data.length > 4) {
            byte[] info = new byte[data.length - 4];
            for (int i = 0; i < (data.length - 4); i++) {
                info[i] = data[4 + i];
            }
            try {
               return handlerDviceResponeMsg(info);
            }
            catch (IOException ex)
            {
                Log.e("ex",ex.getMessage().toString());
            }
        }
        return 0;
    }

    private static int handlerDviceResponeMsg(byte[] data) throws FileNotFoundException {
        if (data == null || data.length == 0) {
            return 0;
        }
        if (data[0] == 0x00 && (data[1] & 0xff) == 0x65) {
            //50,80,00,00,0c,00, 00, 04,01,00,00,00, 01, 04,01,00,00,00,
            //                  role   lengtn   role   length  id  phone download
            //50,80,00,00,0c,00, 00,   00,       01,    04,    01,  00,   00,    00,
            int roleMaster = 6;
            byte[] bytesM = new byte[]{data[roleMaster + 1]};
            Log.i(TAG, "bytesM: -----" + ArrayUtil.toHex(bytesM));
            String byteStrM = ArrayUtil.toHex(bytesM);
            Log.i(TAG, "byteStrM: -----" + byteStrM);
            int masterL = Integer.parseInt(byteStrM.replace(",", ""), 16);
            Log.i(TAG, "masterL: -----" + masterL);

            int roleSlave = roleMaster + masterL + 2;
            byte[] bytesS = new byte[]{data[roleSlave + 1]};
            Log.i(TAG, "bytesS: -----" + ArrayUtil.toHex(bytesS));
            String byteStrS = ArrayUtil.toHex(bytesS);
            Log.i(TAG, "byteStrS: -----" + byteStrS);

            int slaveL = Integer.parseInt(byteStrS.replace(",", ""), 16);
            Log.i(TAG, "slaveL: -----" + slaveL);

            cmdArray = new byte[masterL / 4 + slaveL / 4][];
            msgArray = new byte[masterL / 4 + slaveL / 4][];
            cmdSendNum = 0;

            if (masterL % 4 > 0 || slaveL % 4 > 0) {
                return CMD_LENGTH_ERROR;
            }
            int cmdTimes = 0;
            CmdInfo cmdinfo = new CmdInfo(OP_TOTA_CRASH_DUMP_PARAM_REQ, new byte[]{});
            for (int i = 0; i < masterL / 4; i ++) {
                Log.i(TAG, "onCreate i: ++++" + i);
                byte updateByte = data[roleMaster + 4 + i * 4];
                if (updateByte == 0x00) {
                    byte idByte = data[roleMaster + 2 + i * 4];
                    byte mobileByte = data[roleMaster + 3 + i * 4];
                    byte[] msgBytes = new byte[3];
                    msgBytes[0] = idByte;
                    msgBytes[1] = mobileByte;
                    msgBytes[2] = updateByte;
                    Log.i(TAG, "onCreate msgBytes: ++++" + ArrayUtil.toHex(msgBytes));
                    msgArray[cmdTimes] = msgBytes;
                    cmdArray[cmdTimes] = new byte[]{cmdinfo.toBytes()[0], cmdinfo.toBytes()[1], 0x02, 0x00, 0x00, idByte};
                    Log.i(TAG, "onCreate cmdArrays: ++++" + ArrayUtil.toHex(cmdArray[cmdTimes]));
                    cmdTimes ++;
                }
            }

            for (int i = 0; i < slaveL / 4; i ++) {
                Log.i(TAG, "onCreate i: ++++" + i);
                byte updateByte = data[roleSlave + 4 + i * 4];
                if (updateByte == 0x00) {
                    byte idByte = data[roleSlave + 2 + i * 4];
                    byte mobileByte = data[roleSlave + 3 + i * 4];
                    byte[] msgBytes = new byte[3];
                    msgBytes[0] = idByte;
                    msgBytes[1] = mobileByte;
                    msgBytes[2] = updateByte;
                    Log.i(TAG, "onCreate msgBytes: ++++" + ArrayUtil.toHex(msgBytes));
                    msgArray[cmdTimes] = msgBytes;
                    cmdArray[cmdTimes] = new byte[]{cmdinfo.toBytes()[0], cmdinfo.toBytes()[1], 0x02, 0x00, 0x01, idByte};
                    Log.i(TAG, "onCreate cmdArrays: ++++" + ArrayUtil.toHex(cmdArray[cmdTimes]));
                    cmdTimes ++;
                }
            }
            if (cmdTimes == 0 ) {
                return NO_CRASHDATA;
            }
            return CMD_TOTA_CRASH_DUMP_PARAM_REQ;
        }

        else if (data[0] == 0x01 && (data[1] & 0xff) == 0x65) {
            if (data[6] == 0x01) {
                if (data[7] != cmdArray[cmdSendNum][4]) {
                    return CMD_ROLE_ERROR;
                }
                byte[] lengthBytes = new byte[]{data[8], data[9], data[10], data[11]};
//                String byteStr = ArrayUtil.toHex(lengthBytes);
//                curIdLength = Integer.parseInt(byteStr.replace(",", ""), 16);
                curIdLength = ArrayUtil.bytesToIntLittle(lengthBytes);
                curFileType = 0;
                return crashDumpParamReqId();
            } else {
                Log.i(TAG, "handlerDviceResponeMsg: ++++" + "reject");
                cmdSendNum ++;
                curFileType = 0;
                return CMD_TOTA_CRASH_DUMP_PARAM_REQ;
            }
        }

        else if (data[0] == 0x02 && (data[1] & 0xff) == 0x65 ) {
            byte[] saveData = new byte[data.length - 6];
            System.arraycopy(data, 6, saveData, 0,data.length - 6);
            curIdSendLength += saveData.length;
            writeFile(saveData);
            return CMD_TOTA_CRASH_DUMP_RECEIVED_ACK;
        }

        else if (data[0] == 0x04 && (data[1] & 0xff) == 0x65 ) {
            if (data[4] != 0x00) {
                return CMD_6504_ERROR;
            }
            if (curFileType == 0) {
                curFileType = 1;
            } else {
                curFileType = 0;
                cmdSendNum++;
            }
            return crashDumpParamReqId();
        }

        else if (data[0] == 0x06 && (data[1] & 0xff) == 0x65 ) {
            Log.i(TAG, "handlerDviceResponeMsg 0x56: ++++++");
        }
        return 0;
    }

    private static int crashDumpParamReqId() {
        if (cmdSendNum == cmdArray.length) {

//            try {
//                String folder = (String)BlePreferenceUtil.get(getApplicationContext(), Constants.KEY_FILE_FOLDER_NAME,"");
//                ZipUtil.zip(folder, folder +  ".zip");
//                String address = folder + ".zip";
//                int index = address.indexOf("BOID/");
//                String zip_name = address.substring(index+5,address.length()-1);
//                PropertyObservable.getInstance().fireEvent(EventID.UPDATA_RECE_DATA,
//                        null, null, "zip is ready & path is" + address);
//                BlePreferenceUtil.put(getApplicationContext(),Constants.KEY_ZIP_NAME, zip_name);
//                BlePreferenceUtil.put(getApplicationContext(),Constants.KEY_ZIP_PATH,address);
//            } catch (IOException ex) {
//                Log.i(TAG, "crashDumpParamReqId: -------" + ex);
//            }
            return CMD_TOTA_CRASH_DUMP_END;
        }
        curTime = getStrTime();
        curIdSendLength = 0;

        return CMD_TOTA_CRASH_DUMP_START;
    }


    public static String getStrTime() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
        Date curDate = new Date(System.currentTimeMillis());//获取当前时间
        String str  = formatter.format(curDate);
        return  str;
    }

    private static void writeFile(byte[] data) {
        String role = cmdArray[cmdSendNum][4] == 0x00 ? "master" : "slave";
        String idStr = ArrayUtil.toHex(new byte[]{cmdArray[cmdSendNum][5]}).replace(",", "");
        String fileType = curFileType == 0 ? "crash" : "log";
        String filename = curTime + "_" + role + "_" + idStr + "_" + fileType;
        FileUtils.writeTOFile(data, CrashDumpConstants.KEY_FILE_FOLDER_NAME, filename,"txt");
    }

    public static String crashDumpCurrentProgress() {
        double v1 = curIdSendLength * 100;
        if (v1 / 100 > curIdLength) {
            v1 = curIdLength;
        }
        return ArrayUtil.div(v1, curIdLength, 2) + "";
    }

    public static String crashDumpTotalProgress() {
        double v1 = cmdSendNum * 100;
        if (v1 / 100 > cmdArray.length) {
            v1 = cmdArray.length;
        }
        return ArrayUtil.div(v1, cmdArray.length, 2) + "";
    }
}
