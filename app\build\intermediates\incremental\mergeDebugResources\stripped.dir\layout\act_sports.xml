<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:tc="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6"
    >
    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <include
        android:id="@+id/loginfo"
        layout="@layout/logview"
        android:visibility="gone"/>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:layout_marginTop="20dp">

        <RadioGroup
            android:id="@+id/data_pattern1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="@color/color_transparent">

            <RadioButton

                android:id="@+id/pattern_00"
                android:layout_weight="0.5"
                android:layout_width="100dp"
                android:layout_height="42dp"
                android:layout_marginLeft="20dp"
                android:checked="true"
                android:padding="7dp"
                android:textSize="15dp"
                android:textColor="#ffafb8c3"
                android:text="0x01 NORMAL"
                android:background="@drawable/btn_shape2"
                style="@style/CustomRadioButton"/>

            <RadioButton
                android:id="@+id/pattern_01"
                android:layout_weight="0.5"
                android:layout_width="100dp"
                android:layout_height="42dp"
                android:padding="7dp"
                android:textSize="15dp"
                android:textColor="#ffafb8c3"
                android:text="0x02 RUNNING"
                android:background="@drawable/btn_shape2"
                style="@style/CustomRadioButton"/>

            <RadioButton
                android:id="@+id/pattern_02"
                android:layout_weight="0.5"
                android:layout_marginRight="20dp"
                android:layout_width="100dp"
                android:layout_height="42dp"
                android:textSize="15dp"
                android:padding="7dp"
                android:textColor="#ffafb8c3"
                android:text="0x03 WALKING"
                android:background="@drawable/btn_shape2"
                style="@style/CustomRadioButton"/>

        </RadioGroup>

        <Button
            android:id="@+id/sports_read_start"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:layout_gravity="center"
            android:text="OP_TOTA_RING_sports_INFO"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:background="@drawable/rectangle_button3"
            />

        <com.github.mikephil.charting.charts.BarChart
            android:id="@+id/sportsChart"
            android:layout_marginTop="50dp"
            android:layout_width="match_parent"
            android:layout_height="400dp"/>

    </LinearLayout>




</LinearLayout>