// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: calling.proto

package com.amazon.alexa.accessory.protocol;

public final class Calling {
  private Calling() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ForwardATCommandOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ForwardATCommand)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string command = 1;</code>
     * @return The command.
     */
    java.lang.String getCommand();
    /**
     * <code>string command = 1;</code>
     * @return The bytes for command.
     */
    com.google.protobuf.ByteString
        getCommandBytes();
  }
  /**
   * Protobuf type {@code ForwardATCommand}
   */
  public static final class ForwardATCommand extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ForwardATCommand)
      ForwardATCommandOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ForwardATCommand.newBuilder() to construct.
    private ForwardATCommand(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ForwardATCommand() {
      command_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ForwardATCommand();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Calling.internal_static_ForwardATCommand_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Calling.internal_static_ForwardATCommand_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand.class, com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand.Builder.class);
    }

    public static final int COMMAND_FIELD_NUMBER = 1;
    private volatile java.lang.Object command_;
    /**
     * <code>string command = 1;</code>
     * @return The command.
     */
    @java.lang.Override
    public java.lang.String getCommand() {
      java.lang.Object ref = command_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        command_ = s;
        return s;
      }
    }
    /**
     * <code>string command = 1;</code>
     * @return The bytes for command.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCommandBytes() {
      java.lang.Object ref = command_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        command_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(command_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, command_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(command_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, command_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand other = (com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand) obj;

      if (!getCommand()
          .equals(other.getCommand())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + COMMAND_FIELD_NUMBER;
      hash = (53 * hash) + getCommand().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code ForwardATCommand}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ForwardATCommand)
        com.amazon.alexa.accessory.protocol.Calling.ForwardATCommandOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Calling.internal_static_ForwardATCommand_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Calling.internal_static_ForwardATCommand_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand.class, com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        command_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Calling.internal_static_ForwardATCommand_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand build() {
        com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand buildPartial() {
        com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand result = new com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand(this);
        result.command_ = command_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand other) {
        if (other == com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand.getDefaultInstance()) return this;
        if (!other.getCommand().isEmpty()) {
          command_ = other.command_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                command_ = input.readStringRequireUtf8();

                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private java.lang.Object command_ = "";
      /**
       * <code>string command = 1;</code>
       * @return The command.
       */
      public java.lang.String getCommand() {
        java.lang.Object ref = command_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          command_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string command = 1;</code>
       * @return The bytes for command.
       */
      public com.google.protobuf.ByteString
          getCommandBytes() {
        java.lang.Object ref = command_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          command_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string command = 1;</code>
       * @param value The command to set.
       * @return This builder for chaining.
       */
      public Builder setCommand(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        command_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string command = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommand() {
        
        command_ = getDefaultInstance().getCommand();
        onChanged();
        return this;
      }
      /**
       * <code>string command = 1;</code>
       * @param value The bytes for command to set.
       * @return This builder for chaining.
       */
      public Builder setCommandBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        command_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ForwardATCommand)
    }

    // @@protoc_insertion_point(class_scope:ForwardATCommand)
    private static final com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand();
    }

    public static com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ForwardATCommand>
        PARSER = new com.google.protobuf.AbstractParser<ForwardATCommand>() {
      @java.lang.Override
      public ForwardATCommand parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ForwardATCommand> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ForwardATCommand> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface IncomingCallOrBuilder extends
      // @@protoc_insertion_point(interface_extends:IncomingCall)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string caller_number = 1;</code>
     * @return The callerNumber.
     */
    java.lang.String getCallerNumber();
    /**
     * <code>string caller_number = 1;</code>
     * @return The bytes for callerNumber.
     */
    com.google.protobuf.ByteString
        getCallerNumberBytes();

    /**
     * <code>string caller_name = 2;</code>
     * @return The callerName.
     */
    java.lang.String getCallerName();
    /**
     * <code>string caller_name = 2;</code>
     * @return The bytes for callerName.
     */
    com.google.protobuf.ByteString
        getCallerNameBytes();
  }
  /**
   * Protobuf type {@code IncomingCall}
   */
  public static final class IncomingCall extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:IncomingCall)
      IncomingCallOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use IncomingCall.newBuilder() to construct.
    private IncomingCall(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private IncomingCall() {
      callerNumber_ = "";
      callerName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new IncomingCall();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Calling.internal_static_IncomingCall_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Calling.internal_static_IncomingCall_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Calling.IncomingCall.class, com.amazon.alexa.accessory.protocol.Calling.IncomingCall.Builder.class);
    }

    public static final int CALLER_NUMBER_FIELD_NUMBER = 1;
    private volatile java.lang.Object callerNumber_;
    /**
     * <code>string caller_number = 1;</code>
     * @return The callerNumber.
     */
    @java.lang.Override
    public java.lang.String getCallerNumber() {
      java.lang.Object ref = callerNumber_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        callerNumber_ = s;
        return s;
      }
    }
    /**
     * <code>string caller_number = 1;</code>
     * @return The bytes for callerNumber.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCallerNumberBytes() {
      java.lang.Object ref = callerNumber_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        callerNumber_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CALLER_NAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object callerName_;
    /**
     * <code>string caller_name = 2;</code>
     * @return The callerName.
     */
    @java.lang.Override
    public java.lang.String getCallerName() {
      java.lang.Object ref = callerName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        callerName_ = s;
        return s;
      }
    }
    /**
     * <code>string caller_name = 2;</code>
     * @return The bytes for callerName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCallerNameBytes() {
      java.lang.Object ref = callerName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        callerName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(callerNumber_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, callerNumber_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(callerName_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, callerName_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(callerNumber_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, callerNumber_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(callerName_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, callerName_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Calling.IncomingCall)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Calling.IncomingCall other = (com.amazon.alexa.accessory.protocol.Calling.IncomingCall) obj;

      if (!getCallerNumber()
          .equals(other.getCallerNumber())) return false;
      if (!getCallerName()
          .equals(other.getCallerName())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CALLER_NUMBER_FIELD_NUMBER;
      hash = (53 * hash) + getCallerNumber().hashCode();
      hash = (37 * hash) + CALLER_NAME_FIELD_NUMBER;
      hash = (53 * hash) + getCallerName().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Calling.IncomingCall parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.IncomingCall parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.IncomingCall parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.IncomingCall parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.IncomingCall parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.IncomingCall parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.IncomingCall parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.IncomingCall parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.IncomingCall parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.IncomingCall parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.IncomingCall parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Calling.IncomingCall parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Calling.IncomingCall prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code IncomingCall}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:IncomingCall)
        com.amazon.alexa.accessory.protocol.Calling.IncomingCallOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Calling.internal_static_IncomingCall_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Calling.internal_static_IncomingCall_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Calling.IncomingCall.class, com.amazon.alexa.accessory.protocol.Calling.IncomingCall.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Calling.IncomingCall.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        callerNumber_ = "";

        callerName_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Calling.internal_static_IncomingCall_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Calling.IncomingCall getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Calling.IncomingCall.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Calling.IncomingCall build() {
        com.amazon.alexa.accessory.protocol.Calling.IncomingCall result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Calling.IncomingCall buildPartial() {
        com.amazon.alexa.accessory.protocol.Calling.IncomingCall result = new com.amazon.alexa.accessory.protocol.Calling.IncomingCall(this);
        result.callerNumber_ = callerNumber_;
        result.callerName_ = callerName_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Calling.IncomingCall) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Calling.IncomingCall)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Calling.IncomingCall other) {
        if (other == com.amazon.alexa.accessory.protocol.Calling.IncomingCall.getDefaultInstance()) return this;
        if (!other.getCallerNumber().isEmpty()) {
          callerNumber_ = other.callerNumber_;
          onChanged();
        }
        if (!other.getCallerName().isEmpty()) {
          callerName_ = other.callerName_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                callerNumber_ = input.readStringRequireUtf8();

                break;
              } // case 10
              case 18: {
                callerName_ = input.readStringRequireUtf8();

                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private java.lang.Object callerNumber_ = "";
      /**
       * <code>string caller_number = 1;</code>
       * @return The callerNumber.
       */
      public java.lang.String getCallerNumber() {
        java.lang.Object ref = callerNumber_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          callerNumber_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string caller_number = 1;</code>
       * @return The bytes for callerNumber.
       */
      public com.google.protobuf.ByteString
          getCallerNumberBytes() {
        java.lang.Object ref = callerNumber_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          callerNumber_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string caller_number = 1;</code>
       * @param value The callerNumber to set.
       * @return This builder for chaining.
       */
      public Builder setCallerNumber(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        callerNumber_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string caller_number = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCallerNumber() {
        
        callerNumber_ = getDefaultInstance().getCallerNumber();
        onChanged();
        return this;
      }
      /**
       * <code>string caller_number = 1;</code>
       * @param value The bytes for callerNumber to set.
       * @return This builder for chaining.
       */
      public Builder setCallerNumberBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        callerNumber_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object callerName_ = "";
      /**
       * <code>string caller_name = 2;</code>
       * @return The callerName.
       */
      public java.lang.String getCallerName() {
        java.lang.Object ref = callerName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          callerName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string caller_name = 2;</code>
       * @return The bytes for callerName.
       */
      public com.google.protobuf.ByteString
          getCallerNameBytes() {
        java.lang.Object ref = callerName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          callerName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string caller_name = 2;</code>
       * @param value The callerName to set.
       * @return This builder for chaining.
       */
      public Builder setCallerName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        callerName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string caller_name = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCallerName() {
        
        callerName_ = getDefaultInstance().getCallerName();
        onChanged();
        return this;
      }
      /**
       * <code>string caller_name = 2;</code>
       * @param value The bytes for callerName to set.
       * @return This builder for chaining.
       */
      public Builder setCallerNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        callerName_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:IncomingCall)
    }

    // @@protoc_insertion_point(class_scope:IncomingCall)
    private static final com.amazon.alexa.accessory.protocol.Calling.IncomingCall DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Calling.IncomingCall();
    }

    public static com.amazon.alexa.accessory.protocol.Calling.IncomingCall getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<IncomingCall>
        PARSER = new com.google.protobuf.AbstractParser<IncomingCall>() {
      @java.lang.Override
      public IncomingCall parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<IncomingCall> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IncomingCall> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Calling.IncomingCall getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ForwardATCommand_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ForwardATCommand_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_IncomingCall_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_IncomingCall_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rcalling.proto\"#\n\020ForwardATCommand\022\017\n\007c" +
      "ommand\030\001 \001(\t\":\n\014IncomingCall\022\025\n\rcaller_n" +
      "umber\030\001 \001(\t\022\023\n\013caller_name\030\002 \001(\tB0\n#com." +
      "amazon.alexa.accessory.protocolH\003\240\001\001\242\002\003A" +
      "ACb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_ForwardATCommand_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_ForwardATCommand_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ForwardATCommand_descriptor,
        new java.lang.String[] { "Command", });
    internal_static_IncomingCall_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_IncomingCall_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_IncomingCall_descriptor,
        new java.lang.String[] { "CallerNumber", "CallerName", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
