// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DeviceItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView address;

  @NonNull
  public final Button disconnectBtn;

  @NonNull
  public final TextView name;

  @NonNull
  public final ImageView rssiIcon;

  @NonNull
  public final TextView rssiValue;

  @NonNull
  public final ImageView selectImage;

  private DeviceItemBinding(@NonNull LinearLayout rootView, @NonNull TextView address,
      @NonNull Button disconnectBtn, @NonNull TextView name, @NonNull ImageView rssiIcon,
      @NonNull TextView rssiValue, @NonNull ImageView selectImage) {
    this.rootView = rootView;
    this.address = address;
    this.disconnectBtn = disconnectBtn;
    this.name = name;
    this.rssiIcon = rssiIcon;
    this.rssiValue = rssiValue;
    this.selectImage = selectImage;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DeviceItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DeviceItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.device_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DeviceItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.address;
      TextView address = rootView.findViewById(id);
      if (address == null) {
        break missingId;
      }

      id = R.id.disconnect_btn;
      Button disconnectBtn = rootView.findViewById(id);
      if (disconnectBtn == null) {
        break missingId;
      }

      id = R.id.name;
      TextView name = rootView.findViewById(id);
      if (name == null) {
        break missingId;
      }

      id = R.id.rssi_icon;
      ImageView rssiIcon = rootView.findViewById(id);
      if (rssiIcon == null) {
        break missingId;
      }

      id = R.id.rssi_value;
      TextView rssiValue = rootView.findViewById(id);
      if (rssiValue == null) {
        break missingId;
      }

      id = R.id.select_image;
      ImageView selectImage = rootView.findViewById(id);
      if (selectImage == null) {
        break missingId;
      }

      return new DeviceItemBinding((LinearLayout) rootView, address, disconnectBtn, name, rssiIcon,
          rssiValue, selectImage);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
