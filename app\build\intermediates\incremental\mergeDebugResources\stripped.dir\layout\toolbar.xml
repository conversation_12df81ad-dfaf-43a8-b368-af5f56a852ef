<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="54dp"
    android:layout_marginTop="43dp"
    android:background="#00000000"
    android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="54dp"
        android:background="#00000000"
        android:theme="@style/titlebartheme.ToolBar"
        android:layout_gravity="center"
        app:navigationIcon="@drawable/nav_bar_back"
        >

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/sanshan_bold"
            android:layout_gravity="center"
            android:textSize="16sp"
            android:textColor="@color/title_color"/>

    </androidx.appcompat.widget.Toolbar>




    <!-- 此处内容设置的colorPrimary和toolbar继承的父属性相同-->
</LinearLayout>