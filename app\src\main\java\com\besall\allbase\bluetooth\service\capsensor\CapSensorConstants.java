package com.besall.allbase.bluetooth.service.capsensor;

import android.graphics.Color;
import android.nfc.tech.Ndef;

import java.util.UUID;

public class CapSensorConstants {

    public static final UUID                           BES_CAPSENSOER_SPP_UUID = UUID.fromString("*************-9999-9999-************");

    public static final short                      OP_TOTA_SENSOE_HUB_DUMP_SET = (short)0x6320;
//    public static final short                          OP_TOTA_SPEED_TEST_STOP = (short)0x6701;
//    public static final short                     OP_TOTA_SPEED_TEST_SEND_DATA = (short)0x7003;

    //msg id
    public static final int                                     CAPSENSOR_DATA = 0x00001000;
    public static final int                               SPEED_TEST_SEND_DATA = 0x00001001;
    public static final int                                 CAPSENSOR_DATA_LOG = 0x00001002;
    public static final int                               CAPSENSOR_DATA_MERGE = 0x00001003;

    public static final String            CAPSENSOR_CHART_COEFFICIENTS_DATA_KEY = "CAPSENOR_CHART_COEFFICIENTS_DATA_KEY";
    public static final String                 CAPSENSOR_CHART_MAXIMUM_DATA_KEY = "CAPSENOR_CHART_MAXIMUM_DATA_KEY";
    public static final String              CAPSENSOR_CHART_LINE_WIDTH_DATA_KEY = "CAPSENOR_CHART_LINE_NUMBER_DATA_KEY";
    public static final String                   CAPSENSOR_CHART_PIECE_DATA_KEY = "CAPSENSOR_CHART_PIECE_DATA_KEY";
    public static final String              CAPSENSOR_CHART_USE_CUSTOM_DATA_KEY = "CAPSENOR_CHART_USE_CUSTOM_DATA_KEY";
    public static final int                   CAPSENSOR_CHART_COEFFICIENTS_DATA = 500;
    public static final int                        CAPSENSOR_CHART_MAXIMUM_DATA = 14000;
    public static final int                     CAPSENSOR_CHART_LINE_WIDTH_DATA = 60;
    public static final int                          CAPSENSOR_CHART_PIECE_DATA = 20;
    public static final boolean                 CAPSENSOR_CHART_USE_CUSTOM_DATA = false;
    public static final int                             CAPSENSOR_MAX_CN_NUMBER = 8;

    public final static String                          BES_CAPSENSOR_ISBTH_KEY = "BES_CAPSENSOR_ISBTH_KEY";
    public final static boolean                       BES_CAPSENSOR_ISBTH_VALUE = true;

    public final static String              BES_CAPSENSOR_HAS_EXTRAS_PARAM_KEY = "BES_CAPSENSOR_HAS_EXTRAS_PARAM_KEY";
    public final static boolean           BES_CAPSENSOR_HAS_EXTRAS_PARAM_VALUE = true;

    public final static String                   BES_CAPSENSOR_SHOW_TOUCH_EVENT = "BES_CAPSENSOR_SHOW_TOUCH_EVENT";
    public final static boolean            BES_CAPSENSOR_SHOW_TOUCH_EVENT_VALUE = true;

    public final static String                  BES_CAPSENSOR_CUSTOMER_UUID_KEY = "BES_CAPSENSOR_CUSTOMER_UUID_KEY";
    public final static String                BES_CAPSENSOR_CUSTOMER_UUID_VALUE = "99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,";

    public final static String                       BES_CAPSENSOR_SEND_CMD_KEY = "BES_CAPSENSOR_SEND_CMD_KEY";
    public final static String                     BES_CAPSENSOR_SEND_CMD_VALUE = "";

    public final static String                      BES_CAPSENSOR_DATA_MULTIPLE = "BES_CAPSENSOR_DATA_MULTIPLE";
    public final static int                   BES_CAPSENSOR_DATA_MULTIPLE_VALUE = 1;


    public static final int[]                                  CAPSENSOR_COLORS = new int[]{Color.RED, Color.BLUE, Color.BLACK, Color.GREEN, Color.rgb(128, 0, 128), Color.YELLOW, Color.rgb(187, 255, 255), Color.rgb(255, 20, 147)};

    public enum CAP_KEY_EVENT {
        CAP_KEY_EVENT_NONE     (0, ""),
        CAP_KEY_EVENT_DOWN     (1, ""),
        CAP_KEY_EVENT_UP     (4, ""),
        CAP_KEY_EVENT_LONGPRESS     (6, "Long Press"),
        CAP_KEY_EVENT_LONGLONGPRESS     (7, "Long Long Press"),
        CAP_KEY_EVENT_CLICK     (8, "Click"),
        CAP_KEY_EVENT_DOUBLECLICK     (9, "Double Click"),
        CAP_KEY_EVENT_TRIPLECLICK     (10, "Triple Click"),
        CAP_KEY_EVENT_ULTRACLICK     (11, "Ultra Click"),
        CAP_KEY_EVENT_RAMPAGECLICK     (12, "Rampage Click"),
        CAP_KEY_EVENT_UPSLIDE     (21, "Up Slide"),
        CAP_KEY_EVENT_DOWNSLIDE     (22, "Down Slide"),
        CAP_KEY_EVENT_ON_EAR     (23, "On Ear"),
        CAP_KEY_EVENT_OFF_EAR     (24, "Off Ear"),
        CAP_KEY_EVENT_LONGLONGLONGPRESS     (25, "Long Long Long Press"),
        CAP_KEY_EVENT_LONGLONGLONGLONGPRESS     (26, "Long Long Long Long Press"),
        CAP_KEY_EVENT_EXTEND     (40, "Extend"),
        CAP_KEY_EVENT_SIXCLICK     (41, "Six Click"),
        CAP_KEY_EVENT_SEVENCLICK     (42, "Seven Click"),
        CAP_KEY_EVENT_CLICK_AND_LONGPRESS     (43, "Click And LongPress"),
        CAP_KEY_EVENT_CLICK_AND_LONGLONGPRESS     (44, "Click And LongLongPress"),
        CAP_KEY_EVENT_DOUBLECLICK_AND_LONGLONGPRESS     (45, "DoubleClick And LongLongPress"),
        CAP_KEY_EVENT_TRIPLECLICK_AND_LONGLONGPRESS     (46, "TripleClick And LongLongPress");

        private int mValue;
        private String mName;

        CAP_KEY_EVENT(int value, String name) {
            mValue = value;
            mName = name;
        }

        public int getValue() {
            return mValue;
        }

        public String getName() {
            return mName;
        }
    }

}
