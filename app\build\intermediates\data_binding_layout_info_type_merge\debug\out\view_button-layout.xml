<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="view_button" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\view_button.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/view_button_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="41" endOffset="51"/></Target><Target id="@+id/choose_device" view="Button"><Expressions/><location startLine="13" startOffset="8" endLine="24" endOffset="16"/></Target><Target id="@+id/connect_device" view="Button"><Expressions/><location startLine="26" startOffset="8" endLine="36" endOffset="16"/></Target></Targets></Layout>