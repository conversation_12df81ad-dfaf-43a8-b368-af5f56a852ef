<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:windowSoftInputMode="stateAlwaysHidden|adjustResize"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView
        android:id="@+id/act_otaui_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bes_bg_white"

        >
    </ImageView>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <include
            android:id="@+id/tool"
            layout="@layout/toolbar"
            />
        <View
            android:layout_width="match_parent"
            android:layout_height="29dp"
            >
        </View>
        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/ffe1e6eb"
            android:alpha="0.8">
        </View>
        <ScrollView
            android:id="@+id/whole_scr"
            android:focusableInTouchMode="false"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="40dp">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="58dp"
                    android:orientation="horizontal"
                    >
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="19dp"
                        android:layout_marginLeft="20dp"
                        android:layout_gravity="center_vertical"
                        android:textSize="16sp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color_dark"
                        android:text="@string/totaDataEncryption">
                    </TextView>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                    </LinearLayout>

                    <com.suke.widget.SwitchButton
                        android:id="@+id/switchButton_tota"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="-75dp"
                        app:sb_show_indicator="false"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="58dp"
                    android:orientation="horizontal"
                    >
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="19dp"
                        android:layout_marginLeft="20dp"
                        android:layout_gravity="center_vertical"
                        android:textSize="16sp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color_dark"
                        android:text="@string/use_totav2">
                    </TextView>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                    </LinearLayout>

                    <com.suke.widget.SwitchButton
                        android:id="@+id/switchButton_totav2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="-75dp"
                        app:sb_show_indicator="false"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="58dp"
                    android:orientation="horizontal"
                    >
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="19dp"
                        android:layout_marginLeft="20dp"
                        android:layout_gravity="center_vertical"
                        android:textSize="16sp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color_dark"
                        android:text="@string/use_phy2m">
                    </TextView>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                    </LinearLayout>

                    <com.suke.widget.SwitchButton
                        android:id="@+id/switchButton_phy2m"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="-75dp"
                        app:sb_show_indicator="false"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="58dp"
                    android:orientation="horizontal"
                    >
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="19dp"
                        android:layout_marginLeft="20dp"
                        android:layout_gravity="center_vertical"
                        android:textSize="16sp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color_dark"
                        android:text="@string/use_normal_connect">
                    </TextView>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                    </LinearLayout>

                    <com.suke.widget.SwitchButton
                        android:id="@+id/switchButton_use_normal_connect"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="-75dp"
                        app:sb_show_indicator="false"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="58dp"
                    android:orientation="horizontal"
                    >
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="19dp"
                        android:layout_marginLeft="20dp"
                        android:layout_gravity="center_vertical"
                        android:textSize="16sp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color_dark"
                        android:text="ble ota auto test">
                    </TextView>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                    </LinearLayout>

                    <EditText
                        android:id="@+id/edittext_ble_ota_auto_test_interval"
                        android:layout_width="66dp"
                        android:layout_height="36dp"
                        android:layout_marginLeft="-150dp"
                        android:layout_gravity="center"
                        android:background="@drawable/edittext"
                        android:inputType="number"
                        android:hint="7min"
                        android:maxLength="5" />

                    <com.suke.widget.SwitchButton
                        android:id="@+id/switchButton_ble_ota_auto_test"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="10dp"
                        app:sb_show_indicator="false"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="58dp"
                    android:orientation="horizontal"
                    >
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="19dp"
                        android:layout_marginLeft="20dp"
                        android:layout_gravity="center_vertical"
                        android:textSize="16sp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color_dark"
                        android:text="@string/saveLog">
                    </TextView>

                   <LinearLayout
                       android:layout_width="match_parent"
                       android:layout_height="match_parent">

                   </LinearLayout>

                    <com.suke.widget.SwitchButton
                        android:id="@+id/switchButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="-75dp"
                        app:sb_show_indicator="false"/>
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_marginLeft="20dp"
                    android:layout_height="0.5dp"
                    android:background="@color/ffe1e6eb"
                    android:alpha="0.8">
                </View>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="19dp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="21dp"
                    android:layout_marginBottom="21dp"
                    android:textSize="16sp"
                    android:fontFamily="@font/sanshan_normal"
                    android:textColor="@color/title_color_dark"
                    android:text="@string/theLocalLog">
                </TextView>

                <View
                    android:layout_width="match_parent"
                    android:layout_marginLeft="20dp"
                    android:layout_height="0.5dp"
                    android:background="@color/ffe1e6eb"
                    android:alpha="0.8">
                </View>

                <ListView
                    android:id="@+id/locolLog_list"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="20dp"
                    android:divider="@null" >

                </ListView>

                <LinearLayout
                    android:id="@+id/send_delay"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal"
                        android:layout_gravity="center_horizontal">


                        <TextView
                            android:layout_width="64dp"
                            android:layout_height="16dp"
                            android:layout_marginLeft="20dp"
                            android:text="@string/send_interval"
                            android:layout_weight="1"
                            android:textColor="#ff2c4662"
                            android:textSize="16sp"
                            />
                        <ImageView
                            android:id="@+id/ack_image_0"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_marginRight="-30dp"
                            >
                        </ImageView>
                        <Button
                            android:id="@+id/ack_btn_0"
                            android:layout_width="wrap_content"
                            android:layout_height="20dp"
                            android:layout_marginRight="20dp"
                            android:stateListAnimator="@null"
                            android:background="@drawable/btn_shape1"
                            android:gravity="center_vertical"
                            android:text="@string/default_interval"
                            android:textColor="#ff2c4662"
                            android:textSize="14sp"
                            android:textAllCaps="false"
                            android:clickable="true"
                            >
                        </Button>


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="42dp"
                        android:layout_marginTop="15dp"
                        android:orientation="horizontal"
                        android:focusable="true"
                        android:focusableInTouchMode="true">

                        <EditText
                            android:id="@+id/ble_interval"
                            android:layout_width="66dp"
                            android:layout_height="36dp"
                            android:layout_marginLeft="20dp"
                            android:background="@drawable/edittext"
                            android:inputType="number"
                            android:maxLength="5"
                            android:layout_weight="0.5" />

                        <EditText
                            android:id="@+id/spp_interval"
                            android:layout_width="66dp"
                            android:layout_height="36dp"
                            android:layout_marginLeft="-35dp"
                            android:inputType="number"
                            android:maxLength="5"
                            android:background="@drawable/edittext"
                            android:layout_weight="1"/>

                        <!--                    <EditText-->
                        <!--                        android:id="@+id/gatt_interval"-->
                        <!--                        android:layout_width="66dp"-->
                        <!--                        android:layout_height="36dp"-->
                        <!--                        android:layout_marginLeft="-35dp"-->
                        <!--                        android:inputType="number"-->
                        <!--                        android:background="@drawable/edittext"-->
                        <!--                        android:layout_weight="0.5" />-->

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="ms"
                            android:layout_marginLeft="-120dp"
                            android:layout_weight="1"/>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="42dp"
                        android:orientation="horizontal">
                        <View
                            android:layout_width="25dp"
                            android:layout_height="0dp">

                        </View>
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="16dp"
                            android:text="ble"
                            android:textColor="#ff718498"
                            android:textSize="11sp"
                            android:layout_marginLeft="20dp"

                            />
                        <View
                            android:layout_width="40dp"
                            android:layout_height="0dp">

                        </View>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="16dp"
                            android:text="spp"
                            android:textColor="#ff718498"
                            android:textSize="11sp"

                            android:layout_marginLeft="50dp"
                            />

                        <!--                    <TextView-->
                        <!--                        android:layout_width="wrap_content"-->
                        <!--                        android:layout_height="16dp"-->
                        <!--                        android:text="gatt发送间隔"-->
                        <!--                        android:textColor="#ff718498"-->
                        <!--                        android:textSize="11sp"-->
                        <!--                        android:layout_marginLeft="25dp"-->
                        <!--                        android:layout_marginRight="100dp"-->

                        <!--                        />-->

                    </LinearLayout>

                </LinearLayout>

<!--                <View-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_marginTop="-15dp"-->
<!--                    android:layout_marginLeft="20dp"-->
<!--                    android:layout_height="0.5dp"-->
<!--                    android:background="@color/ffe1e6eb"-->
<!--                    android:alpha="0.8">-->
<!--                </View>-->

<!--                <LinearLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginTop="15dp"-->
<!--                    android:layout_marginLeft="20dp"-->
<!--                    android:orientation="vertical">-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:orientation="horizontal">-->

<!--                        <TextView-->
<!--                            android:layout_width="86dp"-->
<!--                            android:layout_height="16dp"-->
<!--                            android:text="设置service"-->
<!--                            android:textColor="#ff2c4662"-->
<!--                            android:textSize="16sp"-->
<!--                            />-->


<!--                    </LinearLayout>-->


<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:layout_marginTop="12dp">-->

<!--                        <TextView-->
<!--                            android:layout_width="40dp"-->
<!--                            android:layout_height="17dp"-->
<!--                            android:text="service"-->
<!--                            android:textColor="#ff718498"-->
<!--                            android:textSize="12sp"-->
<!--                            android:layout_weight="1"-->
<!--                            />-->
<!--                        <ImageView-->
<!--                            android:id="@+id/ack_image_1"-->
<!--                            android:layout_width="20dp"-->
<!--                            android:layout_height="20dp"-->
<!--                            android:layout_marginRight="-30dp"-->
<!--                            />-->
<!--                        <Button-->
<!--                            android:id="@+id/ack_btn_1"-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="20dp"-->
<!--                            android:layout_marginRight="20dp"-->
<!--                            android:stateListAnimator="@null"-->
<!--                            android:background="@drawable/btn_shape1"-->
<!--                            android:gravity="center_vertical"-->
<!--                            android:text="使用默认UUID"-->
<!--                            android:textColor="#ff2c4662"-->
<!--                            android:textSize="12sp"-->
<!--                            android:textAllCaps="false"-->
<!--                            />-->
<!--                    </LinearLayout>-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:layout_marginTop="8dp"-->
<!--                        android:focusable="true"-->
<!--                        android:focusableInTouchMode="true"-->
<!--                        >-->

<!--                        <EditText-->
<!--                            android:id="@+id/service_uuid"-->
<!--                            android:layout_width="350dp"-->
<!--                            android:layout_height="38dp"-->
<!--                            android:layout_marginRight="20dp"-->
<!--                            android:layout_weight="0.98"-->
<!--                            android:hint="*************-6666-6666-************"-->
<!--                            android:digits="0123456789abcdefghijklmnopqrstuvwxyz-"-->
<!--                            android:background="@drawable/edittext2"-->
<!--                            android:textSize="14sp"-->
<!--                            />-->
<!--                    </LinearLayout>-->

<!--                    <LinearLayout-->
<!--                        android:id="@+id/more_uuids"-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:orientation="vertical"-->
<!--                        android:visibility="gone"-->
<!--                        >-->
<!--                        <LinearLayout-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:orientation="horizontal"-->
<!--                            android:layout_marginTop="12dp">-->

<!--                            <TextView-->
<!--                                android:layout_width="40dp"-->
<!--                                android:layout_height="17dp"-->
<!--                                android:text="characteristicTx"-->
<!--                                android:textColor="#ff718498"-->
<!--                                android:textSize="12sp"-->
<!--                                android:layout_weight="1"-->
<!--                                />-->
<!--                            <ImageView-->
<!--                                android:id="@+id/ack_image_2"-->
<!--                                android:layout_width="20dp"-->
<!--                                android:layout_height="20dp"-->
<!--                                android:layout_marginRight="-30dp"/>-->
<!--                            <Button-->
<!--                                android:id="@+id/ack_btn_2"-->
<!--                                android:layout_width="wrap_content"-->
<!--                                android:layout_height="20dp"-->
<!--                                android:layout_marginRight="20dp"-->
<!--                                android:stateListAnimator="@null"-->
<!--                                android:background="@drawable/btn_shape1"-->
<!--                                android:gravity="center_vertical"-->
<!--                                android:text="使用默认UUID"-->
<!--                                android:textColor="#ff2c4662"-->
<!--                                android:textSize="12sp"-->
<!--                                android:textAllCaps="false"-->
<!--                                >-->
<!--                            </Button>-->


<!--                        </LinearLayout>-->

<!--                        <LinearLayout-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_marginTop="8dp"-->
<!--                            android:focusable="true"-->
<!--                            android:focusableInTouchMode="true"-->
<!--                            >-->

<!--                            <EditText-->
<!--                                android:id="@+id/characteristicTx_uuid"-->
<!--                                android:layout_width="350dp"-->
<!--                                android:layout_height="38dp"-->
<!--                                android:layout_weight="0.98"-->
<!--                                android:layout_marginRight="20dp"-->
<!--                                android:hint="00001101-0000-1000-8000-00805F9B34FB"-->
<!--                                android:digits="0123456789abcdefghijklmnopqrstuvwxyz-"-->
<!--                                android:background="@drawable/edittext2"-->
<!--                                android:textSize="14sp"-->
<!--                                />-->
<!--                        </LinearLayout>-->

<!--                        <LinearLayout-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:orientation="horizontal"-->
<!--                            android:layout_marginTop="12dp">-->

<!--                            <TextView-->
<!--                                android:layout_width="40dp"-->
<!--                                android:layout_height="17dp"-->
<!--                                android:text="characteristicRx"-->
<!--                                android:textColor="#ff718498"-->
<!--                                android:textSize="12sp"-->
<!--                                android:layout_weight="1"-->
<!--                                />-->

<!--                            <ImageView-->
<!--                                android:id="@+id/ack_image_3"-->
<!--                                android:layout_width="20dp"-->
<!--                                android:layout_height="20dp"-->
<!--                                android:layout_marginRight="-30dp"/>-->
<!--                            <Button-->
<!--                                android:id="@+id/ack_btn_3"-->
<!--                                android:layout_width="wrap_content"-->
<!--                                android:layout_height="20dp"-->
<!--                                android:layout_marginRight="20dp"-->
<!--                                android:stateListAnimator="@null"-->
<!--                                android:background="@drawable/btn_shape1"-->
<!--                                android:gravity="center_vertical"-->
<!--                                android:text="使用默认UUID"-->
<!--                                android:textColor="#ff2c4662"-->
<!--                                android:textSize="12sp"-->
<!--                                android:textAllCaps="false"-->
<!--                                >-->
<!--                            </Button>-->
<!--                        </LinearLayout>-->

<!--                        <LinearLayout-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_marginTop="8dp"-->
<!--                            android:focusable="true"-->
<!--                            android:focusableInTouchMode="true"-->
<!--                            >-->

<!--                            <EditText-->
<!--                                android:id="@+id/characteristicRx_uuid"-->
<!--                                android:layout_width="350dp"-->
<!--                                android:layout_height="38dp"-->
<!--                                android:layout_weight="0.98"-->
<!--                                android:layout_marginRight="20dp"-->
<!--                                android:hint="00001101-0000-1000-8000-00805F9B34FB"-->
<!--                                android:digits="0123456789abcdefghijklmnopqrstuvwxyz-"-->
<!--                                android:background="@drawable/edittext2"-->
<!--                                android:textSize="14sp"-->
<!--                                />-->
<!--                        </LinearLayout>-->

<!--                        <LinearLayout-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:orientation="horizontal"-->
<!--                            android:layout_marginTop="12dp">-->

<!--                            <TextView-->
<!--                                android:layout_width="40dp"-->
<!--                                android:layout_height="17dp"-->
<!--                                android:text="descriptor"-->
<!--                                android:textColor="#ff718498"-->
<!--                                android:textSize="12sp"-->
<!--                                android:layout_weight="1"-->
<!--                                />-->

<!--                            <ImageView-->
<!--                                android:id="@+id/ack_image_4"-->
<!--                                android:layout_width="20dp"-->
<!--                                android:layout_height="20dp"-->
<!--                                android:layout_marginRight="-30dp"/>-->
<!--                            <Button-->
<!--                                android:id="@+id/ack_btn_4"-->
<!--                                android:layout_width="wrap_content"-->
<!--                                android:layout_height="20dp"-->
<!--                                android:layout_marginRight="20dp"-->
<!--                                android:stateListAnimator="@null"-->
<!--                                android:background="@drawable/btn_shape1"-->
<!--                                android:gravity="center_vertical"-->
<!--                                android:text="使用默认UUID"-->
<!--                                android:textColor="#ff2c4662"-->
<!--                                android:textSize="12sp"-->
<!--                                android:textAllCaps="false"-->
<!--                                >-->
<!--                            </Button>-->

<!--                        </LinearLayout>-->

<!--                        <LinearLayout-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_marginTop="8dp"-->
<!--                            android:focusable="true"-->
<!--                            android:focusableInTouchMode="true"-->
<!--                            >-->

<!--                            <EditText-->
<!--                                android:id="@+id/descriptor_uuid"-->
<!--                                android:layout_width="330dp"-->
<!--                                android:layout_height="38dp"-->
<!--                                android:layout_weight="0.98"-->
<!--                                android:layout_marginRight="20dp"-->
<!--                                android:hint="00001101-0000-1000-8000-00805F9B34FB"-->
<!--                                android:digits="0123456789abcdefghijklmnopqrstuvwxyz-"-->
<!--                                android:background="@drawable/edittext2"-->
<!--                                android:textSize="14sp"-->
<!--                                />-->
<!--                        </LinearLayout>-->

<!--                    </LinearLayout>-->

<!--                    <LinearLayout-->
<!--                        android:id="@+id/showemore"-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_marginTop="13dp"-->
<!--                        android:gravity="center"-->
<!--                        android:layout_height="20dp"-->
<!--                        android:clickable="true">-->
<!--                        <ImageView-->
<!--                            android:id="@+id/more"-->
<!--                            android:layout_width="20dp"-->
<!--                            android:layout_height="20dp"-->
<!--                            android:background="@drawable/icon_hide"/>-->
<!--                    </LinearLayout>-->

<!--                </LinearLayout>-->

                <View
                    android:layout_width="match_parent"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="14dp"
                    android:layout_height="0.5dp"
                    android:background="@color/ffe1e6eb"
                    android:alpha="0.8">
                </View>

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <Button
                        android:id="@+id/version_button"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:alpha="0">

                    </Button>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="58dp"
                        android:orientation="horizontal">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="19dp"
                            android:layout_marginLeft="20dp"
                            android:layout_gravity="center_vertical"
                            android:textSize="16sp"
                            android:fontFamily="@font/sanshan_normal"
                            android:textColor="@color/title_color_dark"
                            android:text="@string/appVersion">
                        </TextView>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginRight="100dp">
                        </View>

                        <TextView
                            android:id="@+id/version_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="-90dp"
                            android:layout_gravity="center_vertical"
                            android:textSize="12sp"
                            android:fontFamily="@font/sanshan_normal"
                            android:textColor="@color/title_color_light"
                            >
                        </TextView>

                    </LinearLayout>

                </FrameLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_marginLeft="20dp"
                    android:layout_height="0.5dp"
                    android:background="@color/ffe1e6eb"
                    android:alpha="0.8">
                </View>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="58dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:layout_weight="1"
                        android:layout_gravity="center_vertical"
                        android:textSize="16sp"
                        android:fontFamily="@font/sanshan_normal"
                        android:textColor="@color/title_color_dark"
                        android:text="@string/privacy_guide"/>
                    <Button
                        android:id="@+id/privacy_policy"
                        android:layout_marginLeft="50dp"
                        android:layout_marginRight="20dp"
                        android:layout_weight="1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:text="@string/privacy_policy"
                        android:textColor="@color/ff087ec2"
                        android:background="@null"
                        android:textAllCaps="false">

                    </Button>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/agree_view"
                    android:layout_width="match_parent"
                    android:layout_height="500dp"
                    android:layout_marginLeft="50dp"
                    android:layout_marginRight="50dp"
                    android:layout_marginTop="-500dp"
                    android:background="@color/white"
                    android:orientation="vertical"
                    android:visibility="gone"
                    >

                    <ScrollView
                        android:id="@+id/scr_policy"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:paddingBottom="60dp"
                        android:background="@color/lineview"
                        >

                        <TextView
                            android:id="@+id/agreeTV"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:padding="10dp">

                        </TextView>

                    </ScrollView>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="-60dp"
                        android:orientation="horizontal"
                        android:gravity="center">
                        <Button
                            android:id="@+id/disagree"
                            android:layout_width="80dp"
                            android:layout_height="40dp"
                            android:padding="10dp"
                            android:text="@string/disagree"
                            android:background="@color/white"
                            android:textAllCaps="false">

                        </Button>

                        <View
                            android:layout_width="20dp"
                            android:layout_height="40dp">

                        </View>

                        <Button
                            android:id="@+id/agree"
                            android:layout_width="80dp"
                            android:layout_height="40dp"
                            android:padding="10dp"
                            android:text="@string/agree"
                            android:textColor="@color/ff087ec2"
                            android:background="@color/white"
                            android:textAllCaps="false">

                        </Button>

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>


        </ScrollView>


    </LinearLayout>



</androidx.constraintlayout.widget.ConstraintLayout>