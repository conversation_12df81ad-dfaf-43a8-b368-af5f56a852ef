// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: device.proto

package com.amazon.alexa.accessory.protocol;

public final class Device {
  private Device() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code ConnectionStatus}
   */
  public enum ConnectionStatus
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>CONNECTION_STATUS_UNKNOWN = 0;</code>
     */
    CONNECTION_STATUS_UNKNOWN(0),
    /**
     * <code>CONNECTION_STATUS_CONNECTED = 1;</code>
     */
    CONNECTION_STATUS_CONNECTED(1),
    /**
     * <code>CONNECTION_STATUS_DISCONNECTED = 2;</code>
     */
    CONNECTION_STATUS_DISCONNECTED(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>CONNECTION_STATUS_UNKNOWN = 0;</code>
     */
    public static final int CONNECTION_STATUS_UNKNOWN_VALUE = 0;
    /**
     * <code>CONNECTION_STATUS_CONNECTED = 1;</code>
     */
    public static final int CONNECTION_STATUS_CONNECTED_VALUE = 1;
    /**
     * <code>CONNECTION_STATUS_DISCONNECTED = 2;</code>
     */
    public static final int CONNECTION_STATUS_DISCONNECTED_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static ConnectionStatus valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static ConnectionStatus forNumber(int value) {
      switch (value) {
        case 0: return CONNECTION_STATUS_UNKNOWN;
        case 1: return CONNECTION_STATUS_CONNECTED;
        case 2: return CONNECTION_STATUS_DISCONNECTED;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<ConnectionStatus>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        ConnectionStatus> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<ConnectionStatus>() {
            public ConnectionStatus findValueByNumber(int number) {
              return ConnectionStatus.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Device.getDescriptor().getEnumTypes().get(0);
    }

    private static final ConnectionStatus[] VALUES = values();

    public static ConnectionStatus valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private ConnectionStatus(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:ConnectionStatus)
  }

  /**
   * Protobuf enum {@code DevicePresence}
   */
  public enum DevicePresence
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>DEVICE_PRESENCE_UNKNOWN = 0;</code>
     */
    DEVICE_PRESENCE_UNKNOWN(0),
    /**
     * <code>DEVICE_PRESENCE_ACTIVE = 1;</code>
     */
    DEVICE_PRESENCE_ACTIVE(1),
    /**
     * <code>DEVICE_PRESENCE_INACTIVE = 2;</code>
     */
    DEVICE_PRESENCE_INACTIVE(2),
    /**
     * <code>DEVICE_PRESENCE_ACCESSIBLE = 3;</code>
     */
    DEVICE_PRESENCE_ACCESSIBLE(3),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>DEVICE_PRESENCE_UNKNOWN = 0;</code>
     */
    public static final int DEVICE_PRESENCE_UNKNOWN_VALUE = 0;
    /**
     * <code>DEVICE_PRESENCE_ACTIVE = 1;</code>
     */
    public static final int DEVICE_PRESENCE_ACTIVE_VALUE = 1;
    /**
     * <code>DEVICE_PRESENCE_INACTIVE = 2;</code>
     */
    public static final int DEVICE_PRESENCE_INACTIVE_VALUE = 2;
    /**
     * <code>DEVICE_PRESENCE_ACCESSIBLE = 3;</code>
     */
    public static final int DEVICE_PRESENCE_ACCESSIBLE_VALUE = 3;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static DevicePresence valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static DevicePresence forNumber(int value) {
      switch (value) {
        case 0: return DEVICE_PRESENCE_UNKNOWN;
        case 1: return DEVICE_PRESENCE_ACTIVE;
        case 2: return DEVICE_PRESENCE_INACTIVE;
        case 3: return DEVICE_PRESENCE_ACCESSIBLE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<DevicePresence>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        DevicePresence> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<DevicePresence>() {
            public DevicePresence findValueByNumber(int number) {
              return DevicePresence.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Device.getDescriptor().getEnumTypes().get(1);
    }

    private static final DevicePresence[] VALUES = values();

    public static DevicePresence valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private DevicePresence(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:DevicePresence)
  }

  public interface DeviceBatteryOrBuilder extends
      // @@protoc_insertion_point(interface_extends:DeviceBattery)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 level = 1;</code>
     * @return The level.
     */
    int getLevel();

    /**
     * <code>uint32 scale = 2;</code>
     * @return The scale.
     */
    int getScale();

    /**
     * <code>.DeviceBattery.Status status = 3;</code>
     * @return The enum numeric value on the wire for status.
     */
    int getStatusValue();
    /**
     * <code>.DeviceBattery.Status status = 3;</code>
     * @return The status.
     */
    com.amazon.alexa.accessory.protocol.Device.DeviceBattery.Status getStatus();
  }
  /**
   * Protobuf type {@code DeviceBattery}
   */
  public static final class DeviceBattery extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:DeviceBattery)
      DeviceBatteryOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DeviceBattery.newBuilder() to construct.
    private DeviceBattery(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DeviceBattery() {
      status_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DeviceBattery();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceBattery_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceBattery_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Device.DeviceBattery.class, com.amazon.alexa.accessory.protocol.Device.DeviceBattery.Builder.class);
    }

    /**
     * Protobuf enum {@code DeviceBattery.Status}
     */
    public enum Status
        implements com.google.protobuf.ProtocolMessageEnum {
      /**
       * <code>UNKNOWN = 0;</code>
       */
      UNKNOWN(0),
      /**
       * <code>CHARGING = 1;</code>
       */
      CHARGING(1),
      /**
       * <code>DISCHARGING = 2;</code>
       */
      DISCHARGING(2),
      /**
       * <code>FULL = 3;</code>
       */
      FULL(3),
      UNRECOGNIZED(-1),
      ;

      /**
       * <code>UNKNOWN = 0;</code>
       */
      public static final int UNKNOWN_VALUE = 0;
      /**
       * <code>CHARGING = 1;</code>
       */
      public static final int CHARGING_VALUE = 1;
      /**
       * <code>DISCHARGING = 2;</code>
       */
      public static final int DISCHARGING_VALUE = 2;
      /**
       * <code>FULL = 3;</code>
       */
      public static final int FULL_VALUE = 3;


      public final int getNumber() {
        if (this == UNRECOGNIZED) {
          throw new java.lang.IllegalArgumentException(
              "Can't get the number of an unknown enum value.");
        }
        return value;
      }

      /**
       * @param value The numeric wire value of the corresponding enum entry.
       * @return The enum associated with the given numeric wire value.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static Status valueOf(int value) {
        return forNumber(value);
      }

      /**
       * @param value The numeric wire value of the corresponding enum entry.
       * @return The enum associated with the given numeric wire value.
       */
      public static Status forNumber(int value) {
        switch (value) {
          case 0: return UNKNOWN;
          case 1: return CHARGING;
          case 2: return DISCHARGING;
          case 3: return FULL;
          default: return null;
        }
      }

      public static com.google.protobuf.Internal.EnumLiteMap<Status>
          internalGetValueMap() {
        return internalValueMap;
      }
      private static final com.google.protobuf.Internal.EnumLiteMap<
          Status> internalValueMap =
            new com.google.protobuf.Internal.EnumLiteMap<Status>() {
              public Status findValueByNumber(int number) {
                return Status.forNumber(number);
              }
            };

      public final com.google.protobuf.Descriptors.EnumValueDescriptor
          getValueDescriptor() {
        if (this == UNRECOGNIZED) {
          throw new java.lang.IllegalStateException(
              "Can't get the descriptor of an unrecognized enum value.");
        }
        return getDescriptor().getValues().get(ordinal());
      }
      public final com.google.protobuf.Descriptors.EnumDescriptor
          getDescriptorForType() {
        return getDescriptor();
      }
      public static final com.google.protobuf.Descriptors.EnumDescriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Device.DeviceBattery.getDescriptor().getEnumTypes().get(0);
      }

      private static final Status[] VALUES = values();

      public static Status valueOf(
          com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
        if (desc.getType() != getDescriptor()) {
          throw new java.lang.IllegalArgumentException(
            "EnumValueDescriptor is not for this type.");
        }
        if (desc.getIndex() == -1) {
          return UNRECOGNIZED;
        }
        return VALUES[desc.getIndex()];
      }

      private final int value;

      private Status(int value) {
        this.value = value;
      }

      // @@protoc_insertion_point(enum_scope:DeviceBattery.Status)
    }

    public static final int LEVEL_FIELD_NUMBER = 1;
    private int level_;
    /**
     * <code>uint32 level = 1;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }

    public static final int SCALE_FIELD_NUMBER = 2;
    private int scale_;
    /**
     * <code>uint32 scale = 2;</code>
     * @return The scale.
     */
    @java.lang.Override
    public int getScale() {
      return scale_;
    }

    public static final int STATUS_FIELD_NUMBER = 3;
    private int status_;
    /**
     * <code>.DeviceBattery.Status status = 3;</code>
     * @return The enum numeric value on the wire for status.
     */
    @java.lang.Override public int getStatusValue() {
      return status_;
    }
    /**
     * <code>.DeviceBattery.Status status = 3;</code>
     * @return The status.
     */
    @java.lang.Override public com.amazon.alexa.accessory.protocol.Device.DeviceBattery.Status getStatus() {
      @SuppressWarnings("deprecation")
      com.amazon.alexa.accessory.protocol.Device.DeviceBattery.Status result = com.amazon.alexa.accessory.protocol.Device.DeviceBattery.Status.valueOf(status_);
      return result == null ? com.amazon.alexa.accessory.protocol.Device.DeviceBattery.Status.UNRECOGNIZED : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (level_ != 0) {
        output.writeUInt32(1, level_);
      }
      if (scale_ != 0) {
        output.writeUInt32(2, scale_);
      }
      if (status_ != com.amazon.alexa.accessory.protocol.Device.DeviceBattery.Status.UNKNOWN.getNumber()) {
        output.writeEnum(3, status_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (level_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, level_);
      }
      if (scale_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, scale_);
      }
      if (status_ != com.amazon.alexa.accessory.protocol.Device.DeviceBattery.Status.UNKNOWN.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(3, status_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Device.DeviceBattery)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Device.DeviceBattery other = (com.amazon.alexa.accessory.protocol.Device.DeviceBattery) obj;

      if (getLevel()
          != other.getLevel()) return false;
      if (getScale()
          != other.getScale()) return false;
      if (status_ != other.status_) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
      hash = (37 * hash) + SCALE_FIELD_NUMBER;
      hash = (53 * hash) + getScale();
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + status_;
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Device.DeviceBattery parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceBattery parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceBattery parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceBattery parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceBattery parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceBattery parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceBattery parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceBattery parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceBattery parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceBattery parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceBattery parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceBattery parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Device.DeviceBattery prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code DeviceBattery}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:DeviceBattery)
        com.amazon.alexa.accessory.protocol.Device.DeviceBatteryOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceBattery_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceBattery_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Device.DeviceBattery.class, com.amazon.alexa.accessory.protocol.Device.DeviceBattery.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Device.DeviceBattery.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        level_ = 0;

        scale_ = 0;

        status_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceBattery_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceBattery getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Device.DeviceBattery.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceBattery build() {
        com.amazon.alexa.accessory.protocol.Device.DeviceBattery result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceBattery buildPartial() {
        com.amazon.alexa.accessory.protocol.Device.DeviceBattery result = new com.amazon.alexa.accessory.protocol.Device.DeviceBattery(this);
        result.level_ = level_;
        result.scale_ = scale_;
        result.status_ = status_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Device.DeviceBattery) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Device.DeviceBattery)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Device.DeviceBattery other) {
        if (other == com.amazon.alexa.accessory.protocol.Device.DeviceBattery.getDefaultInstance()) return this;
        if (other.getLevel() != 0) {
          setLevel(other.getLevel());
        }
        if (other.getScale() != 0) {
          setScale(other.getScale());
        }
        if (other.status_ != 0) {
          setStatusValue(other.getStatusValue());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                level_ = input.readUInt32();

                break;
              } // case 8
              case 16: {
                scale_ = input.readUInt32();

                break;
              } // case 16
              case 24: {
                status_ = input.readEnum();

                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private int level_ ;
      /**
       * <code>uint32 level = 1;</code>
       * @return The level.
       */
      @java.lang.Override
      public int getLevel() {
        return level_;
      }
      /**
       * <code>uint32 level = 1;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(int value) {
        
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 level = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        
        level_ = 0;
        onChanged();
        return this;
      }

      private int scale_ ;
      /**
       * <code>uint32 scale = 2;</code>
       * @return The scale.
       */
      @java.lang.Override
      public int getScale() {
        return scale_;
      }
      /**
       * <code>uint32 scale = 2;</code>
       * @param value The scale to set.
       * @return This builder for chaining.
       */
      public Builder setScale(int value) {
        
        scale_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 scale = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearScale() {
        
        scale_ = 0;
        onChanged();
        return this;
      }

      private int status_ = 0;
      /**
       * <code>.DeviceBattery.Status status = 3;</code>
       * @return The enum numeric value on the wire for status.
       */
      @java.lang.Override public int getStatusValue() {
        return status_;
      }
      /**
       * <code>.DeviceBattery.Status status = 3;</code>
       * @param value The enum numeric value on the wire for status to set.
       * @return This builder for chaining.
       */
      public Builder setStatusValue(int value) {
        
        status_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.DeviceBattery.Status status = 3;</code>
       * @return The status.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceBattery.Status getStatus() {
        @SuppressWarnings("deprecation")
        com.amazon.alexa.accessory.protocol.Device.DeviceBattery.Status result = com.amazon.alexa.accessory.protocol.Device.DeviceBattery.Status.valueOf(status_);
        return result == null ? com.amazon.alexa.accessory.protocol.Device.DeviceBattery.Status.UNRECOGNIZED : result;
      }
      /**
       * <code>.DeviceBattery.Status status = 3;</code>
       * @param value The status to set.
       * @return This builder for chaining.
       */
      public Builder setStatus(com.amazon.alexa.accessory.protocol.Device.DeviceBattery.Status value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        status_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.DeviceBattery.Status status = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatus() {
        
        status_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:DeviceBattery)
    }

    // @@protoc_insertion_point(class_scope:DeviceBattery)
    private static final com.amazon.alexa.accessory.protocol.Device.DeviceBattery DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Device.DeviceBattery();
    }

    public static com.amazon.alexa.accessory.protocol.Device.DeviceBattery getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DeviceBattery>
        PARSER = new com.google.protobuf.AbstractParser<DeviceBattery>() {
      @java.lang.Override
      public DeviceBattery parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<DeviceBattery> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DeviceBattery> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.DeviceBattery getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DeviceStatusOrBuilder extends
      // @@protoc_insertion_point(interface_extends:DeviceStatus)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.ConnectionStatus link = 1;</code>
     * @return The enum numeric value on the wire for link.
     */
    int getLinkValue();
    /**
     * <code>.ConnectionStatus link = 1;</code>
     * @return The link.
     */
    com.amazon.alexa.accessory.protocol.Device.ConnectionStatus getLink();

    /**
     * <code>.ConnectionStatus nfmi = 2;</code>
     * @return The enum numeric value on the wire for nfmi.
     */
    int getNfmiValue();
    /**
     * <code>.ConnectionStatus nfmi = 2;</code>
     * @return The nfmi.
     */
    com.amazon.alexa.accessory.protocol.Device.ConnectionStatus getNfmi();

    /**
     * <code>.DevicePresence presence = 3;</code>
     * @return The enum numeric value on the wire for presence.
     */
    int getPresenceValue();
    /**
     * <code>.DevicePresence presence = 3;</code>
     * @return The presence.
     */
    com.amazon.alexa.accessory.protocol.Device.DevicePresence getPresence();
  }
  /**
   * Protobuf type {@code DeviceStatus}
   */
  public static final class DeviceStatus extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:DeviceStatus)
      DeviceStatusOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DeviceStatus.newBuilder() to construct.
    private DeviceStatus(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DeviceStatus() {
      link_ = 0;
      nfmi_ = 0;
      presence_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DeviceStatus();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceStatus_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceStatus_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Device.DeviceStatus.class, com.amazon.alexa.accessory.protocol.Device.DeviceStatus.Builder.class);
    }

    public static final int LINK_FIELD_NUMBER = 1;
    private int link_;
    /**
     * <code>.ConnectionStatus link = 1;</code>
     * @return The enum numeric value on the wire for link.
     */
    @java.lang.Override public int getLinkValue() {
      return link_;
    }
    /**
     * <code>.ConnectionStatus link = 1;</code>
     * @return The link.
     */
    @java.lang.Override public com.amazon.alexa.accessory.protocol.Device.ConnectionStatus getLink() {
      @SuppressWarnings("deprecation")
      com.amazon.alexa.accessory.protocol.Device.ConnectionStatus result = com.amazon.alexa.accessory.protocol.Device.ConnectionStatus.valueOf(link_);
      return result == null ? com.amazon.alexa.accessory.protocol.Device.ConnectionStatus.UNRECOGNIZED : result;
    }

    public static final int NFMI_FIELD_NUMBER = 2;
    private int nfmi_;
    /**
     * <code>.ConnectionStatus nfmi = 2;</code>
     * @return The enum numeric value on the wire for nfmi.
     */
    @java.lang.Override public int getNfmiValue() {
      return nfmi_;
    }
    /**
     * <code>.ConnectionStatus nfmi = 2;</code>
     * @return The nfmi.
     */
    @java.lang.Override public com.amazon.alexa.accessory.protocol.Device.ConnectionStatus getNfmi() {
      @SuppressWarnings("deprecation")
      com.amazon.alexa.accessory.protocol.Device.ConnectionStatus result = com.amazon.alexa.accessory.protocol.Device.ConnectionStatus.valueOf(nfmi_);
      return result == null ? com.amazon.alexa.accessory.protocol.Device.ConnectionStatus.UNRECOGNIZED : result;
    }

    public static final int PRESENCE_FIELD_NUMBER = 3;
    private int presence_;
    /**
     * <code>.DevicePresence presence = 3;</code>
     * @return The enum numeric value on the wire for presence.
     */
    @java.lang.Override public int getPresenceValue() {
      return presence_;
    }
    /**
     * <code>.DevicePresence presence = 3;</code>
     * @return The presence.
     */
    @java.lang.Override public com.amazon.alexa.accessory.protocol.Device.DevicePresence getPresence() {
      @SuppressWarnings("deprecation")
      com.amazon.alexa.accessory.protocol.Device.DevicePresence result = com.amazon.alexa.accessory.protocol.Device.DevicePresence.valueOf(presence_);
      return result == null ? com.amazon.alexa.accessory.protocol.Device.DevicePresence.UNRECOGNIZED : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (link_ != com.amazon.alexa.accessory.protocol.Device.ConnectionStatus.CONNECTION_STATUS_UNKNOWN.getNumber()) {
        output.writeEnum(1, link_);
      }
      if (nfmi_ != com.amazon.alexa.accessory.protocol.Device.ConnectionStatus.CONNECTION_STATUS_UNKNOWN.getNumber()) {
        output.writeEnum(2, nfmi_);
      }
      if (presence_ != com.amazon.alexa.accessory.protocol.Device.DevicePresence.DEVICE_PRESENCE_UNKNOWN.getNumber()) {
        output.writeEnum(3, presence_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (link_ != com.amazon.alexa.accessory.protocol.Device.ConnectionStatus.CONNECTION_STATUS_UNKNOWN.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, link_);
      }
      if (nfmi_ != com.amazon.alexa.accessory.protocol.Device.ConnectionStatus.CONNECTION_STATUS_UNKNOWN.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, nfmi_);
      }
      if (presence_ != com.amazon.alexa.accessory.protocol.Device.DevicePresence.DEVICE_PRESENCE_UNKNOWN.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(3, presence_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Device.DeviceStatus)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Device.DeviceStatus other = (com.amazon.alexa.accessory.protocol.Device.DeviceStatus) obj;

      if (link_ != other.link_) return false;
      if (nfmi_ != other.nfmi_) return false;
      if (presence_ != other.presence_) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + LINK_FIELD_NUMBER;
      hash = (53 * hash) + link_;
      hash = (37 * hash) + NFMI_FIELD_NUMBER;
      hash = (53 * hash) + nfmi_;
      hash = (37 * hash) + PRESENCE_FIELD_NUMBER;
      hash = (53 * hash) + presence_;
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Device.DeviceStatus parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceStatus parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceStatus parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceStatus parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceStatus parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceStatus parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceStatus parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceStatus parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceStatus parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceStatus parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceStatus parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceStatus parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Device.DeviceStatus prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code DeviceStatus}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:DeviceStatus)
        com.amazon.alexa.accessory.protocol.Device.DeviceStatusOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceStatus_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceStatus_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Device.DeviceStatus.class, com.amazon.alexa.accessory.protocol.Device.DeviceStatus.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Device.DeviceStatus.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        link_ = 0;

        nfmi_ = 0;

        presence_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceStatus_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceStatus getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Device.DeviceStatus.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceStatus build() {
        com.amazon.alexa.accessory.protocol.Device.DeviceStatus result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceStatus buildPartial() {
        com.amazon.alexa.accessory.protocol.Device.DeviceStatus result = new com.amazon.alexa.accessory.protocol.Device.DeviceStatus(this);
        result.link_ = link_;
        result.nfmi_ = nfmi_;
        result.presence_ = presence_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Device.DeviceStatus) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Device.DeviceStatus)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Device.DeviceStatus other) {
        if (other == com.amazon.alexa.accessory.protocol.Device.DeviceStatus.getDefaultInstance()) return this;
        if (other.link_ != 0) {
          setLinkValue(other.getLinkValue());
        }
        if (other.nfmi_ != 0) {
          setNfmiValue(other.getNfmiValue());
        }
        if (other.presence_ != 0) {
          setPresenceValue(other.getPresenceValue());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                link_ = input.readEnum();

                break;
              } // case 8
              case 16: {
                nfmi_ = input.readEnum();

                break;
              } // case 16
              case 24: {
                presence_ = input.readEnum();

                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private int link_ = 0;
      /**
       * <code>.ConnectionStatus link = 1;</code>
       * @return The enum numeric value on the wire for link.
       */
      @java.lang.Override public int getLinkValue() {
        return link_;
      }
      /**
       * <code>.ConnectionStatus link = 1;</code>
       * @param value The enum numeric value on the wire for link to set.
       * @return This builder for chaining.
       */
      public Builder setLinkValue(int value) {
        
        link_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.ConnectionStatus link = 1;</code>
       * @return The link.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.ConnectionStatus getLink() {
        @SuppressWarnings("deprecation")
        com.amazon.alexa.accessory.protocol.Device.ConnectionStatus result = com.amazon.alexa.accessory.protocol.Device.ConnectionStatus.valueOf(link_);
        return result == null ? com.amazon.alexa.accessory.protocol.Device.ConnectionStatus.UNRECOGNIZED : result;
      }
      /**
       * <code>.ConnectionStatus link = 1;</code>
       * @param value The link to set.
       * @return This builder for chaining.
       */
      public Builder setLink(com.amazon.alexa.accessory.protocol.Device.ConnectionStatus value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        link_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.ConnectionStatus link = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearLink() {
        
        link_ = 0;
        onChanged();
        return this;
      }

      private int nfmi_ = 0;
      /**
       * <code>.ConnectionStatus nfmi = 2;</code>
       * @return The enum numeric value on the wire for nfmi.
       */
      @java.lang.Override public int getNfmiValue() {
        return nfmi_;
      }
      /**
       * <code>.ConnectionStatus nfmi = 2;</code>
       * @param value The enum numeric value on the wire for nfmi to set.
       * @return This builder for chaining.
       */
      public Builder setNfmiValue(int value) {
        
        nfmi_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.ConnectionStatus nfmi = 2;</code>
       * @return The nfmi.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.ConnectionStatus getNfmi() {
        @SuppressWarnings("deprecation")
        com.amazon.alexa.accessory.protocol.Device.ConnectionStatus result = com.amazon.alexa.accessory.protocol.Device.ConnectionStatus.valueOf(nfmi_);
        return result == null ? com.amazon.alexa.accessory.protocol.Device.ConnectionStatus.UNRECOGNIZED : result;
      }
      /**
       * <code>.ConnectionStatus nfmi = 2;</code>
       * @param value The nfmi to set.
       * @return This builder for chaining.
       */
      public Builder setNfmi(com.amazon.alexa.accessory.protocol.Device.ConnectionStatus value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        nfmi_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.ConnectionStatus nfmi = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNfmi() {
        
        nfmi_ = 0;
        onChanged();
        return this;
      }

      private int presence_ = 0;
      /**
       * <code>.DevicePresence presence = 3;</code>
       * @return The enum numeric value on the wire for presence.
       */
      @java.lang.Override public int getPresenceValue() {
        return presence_;
      }
      /**
       * <code>.DevicePresence presence = 3;</code>
       * @param value The enum numeric value on the wire for presence to set.
       * @return This builder for chaining.
       */
      public Builder setPresenceValue(int value) {
        
        presence_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.DevicePresence presence = 3;</code>
       * @return The presence.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DevicePresence getPresence() {
        @SuppressWarnings("deprecation")
        com.amazon.alexa.accessory.protocol.Device.DevicePresence result = com.amazon.alexa.accessory.protocol.Device.DevicePresence.valueOf(presence_);
        return result == null ? com.amazon.alexa.accessory.protocol.Device.DevicePresence.UNRECOGNIZED : result;
      }
      /**
       * <code>.DevicePresence presence = 3;</code>
       * @param value The presence to set.
       * @return This builder for chaining.
       */
      public Builder setPresence(com.amazon.alexa.accessory.protocol.Device.DevicePresence value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        presence_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.DevicePresence presence = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPresence() {
        
        presence_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:DeviceStatus)
    }

    // @@protoc_insertion_point(class_scope:DeviceStatus)
    private static final com.amazon.alexa.accessory.protocol.Device.DeviceStatus DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Device.DeviceStatus();
    }

    public static com.amazon.alexa.accessory.protocol.Device.DeviceStatus getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DeviceStatus>
        PARSER = new com.google.protobuf.AbstractParser<DeviceStatus>() {
      @java.lang.Override
      public DeviceStatus parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<DeviceStatus> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DeviceStatus> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.DeviceStatus getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DeviceInformationOrBuilder extends
      // @@protoc_insertion_point(interface_extends:DeviceInformation)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string serial_number = 1;</code>
     * @return The serialNumber.
     */
    java.lang.String getSerialNumber();
    /**
     * <code>string serial_number = 1;</code>
     * @return The bytes for serialNumber.
     */
    com.google.protobuf.ByteString
        getSerialNumberBytes();

    /**
     * <code>string name = 2;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <code>repeated .Transport supported_transports = 3;</code>
     * @return A list containing the supportedTransports.
     */
    java.util.List<com.amazon.alexa.accessory.protocol.Common.Transport> getSupportedTransportsList();
    /**
     * <code>repeated .Transport supported_transports = 3;</code>
     * @return The count of supportedTransports.
     */
    int getSupportedTransportsCount();
    /**
     * <code>repeated .Transport supported_transports = 3;</code>
     * @param index The index of the element to return.
     * @return The supportedTransports at the given index.
     */
    com.amazon.alexa.accessory.protocol.Common.Transport getSupportedTransports(int index);
    /**
     * <code>repeated .Transport supported_transports = 3;</code>
     * @return A list containing the enum numeric values on the wire for supportedTransports.
     */
    java.util.List<java.lang.Integer>
    getSupportedTransportsValueList();
    /**
     * <code>repeated .Transport supported_transports = 3;</code>
     * @param index The index of the value to return.
     * @return The enum numeric value on the wire of supportedTransports at the given index.
     */
    int getSupportedTransportsValue(int index);

    /**
     * <code>string device_type = 4;</code>
     * @return The deviceType.
     */
    java.lang.String getDeviceType();
    /**
     * <code>string device_type = 4;</code>
     * @return The bytes for deviceType.
     */
    com.google.protobuf.ByteString
        getDeviceTypeBytes();

    /**
     * <code>uint32 device_id = 5;</code>
     * @return The deviceId.
     */
    int getDeviceId();

    /**
     * <code>.DeviceBattery battery = 6;</code>
     * @return Whether the battery field is set.
     */
    boolean hasBattery();
    /**
     * <code>.DeviceBattery battery = 6;</code>
     * @return The battery.
     */
    com.amazon.alexa.accessory.protocol.Device.DeviceBattery getBattery();
    /**
     * <code>.DeviceBattery battery = 6;</code>
     */
    com.amazon.alexa.accessory.protocol.Device.DeviceBatteryOrBuilder getBatteryOrBuilder();

    /**
     * <code>.DeviceStatus status = 7;</code>
     * @return Whether the status field is set.
     */
    boolean hasStatus();
    /**
     * <code>.DeviceStatus status = 7;</code>
     * @return The status.
     */
    com.amazon.alexa.accessory.protocol.Device.DeviceStatus getStatus();
    /**
     * <code>.DeviceStatus status = 7;</code>
     */
    com.amazon.alexa.accessory.protocol.Device.DeviceStatusOrBuilder getStatusOrBuilder();

    /**
     * <code>uint32 product_color = 8;</code>
     * @return The productColor.
     */
    int getProductColor();

    /**
     * <code>repeated uint32 associated_devices = 9;</code>
     * @return A list containing the associatedDevices.
     */
    java.util.List<java.lang.Integer> getAssociatedDevicesList();
    /**
     * <code>repeated uint32 associated_devices = 9;</code>
     * @return The count of associatedDevices.
     */
    int getAssociatedDevicesCount();
    /**
     * <code>repeated uint32 associated_devices = 9;</code>
     * @param index The index of the element to return.
     * @return The associatedDevices at the given index.
     */
    int getAssociatedDevices(int index);

    /**
     * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
     * @return A list containing the supportedSpeechInitiations.
     */
    java.util.List<com.amazon.alexa.accessory.protocol.Common.SpeechInitiationType> getSupportedSpeechInitiationsList();
    /**
     * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
     * @return The count of supportedSpeechInitiations.
     */
    int getSupportedSpeechInitiationsCount();
    /**
     * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
     * @param index The index of the element to return.
     * @return The supportedSpeechInitiations at the given index.
     */
    com.amazon.alexa.accessory.protocol.Common.SpeechInitiationType getSupportedSpeechInitiations(int index);
    /**
     * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
     * @return A list containing the enum numeric values on the wire for supportedSpeechInitiations.
     */
    java.util.List<java.lang.Integer>
    getSupportedSpeechInitiationsValueList();
    /**
     * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
     * @param index The index of the value to return.
     * @return The enum numeric value on the wire of supportedSpeechInitiations at the given index.
     */
    int getSupportedSpeechInitiationsValue(int index);

    /**
     * <code>repeated string supported_wakewords = 11;</code>
     * @return A list containing the supportedWakewords.
     */
    java.util.List<java.lang.String>
        getSupportedWakewordsList();
    /**
     * <code>repeated string supported_wakewords = 11;</code>
     * @return The count of supportedWakewords.
     */
    int getSupportedWakewordsCount();
    /**
     * <code>repeated string supported_wakewords = 11;</code>
     * @param index The index of the element to return.
     * @return The supportedWakewords at the given index.
     */
    java.lang.String getSupportedWakewords(int index);
    /**
     * <code>repeated string supported_wakewords = 11;</code>
     * @param index The index of the value to return.
     * @return The bytes of the supportedWakewords at the given index.
     */
    com.google.protobuf.ByteString
        getSupportedWakewordsBytes(int index);

    /**
     * <code>map&lt;string, string&gt; metadata = 12;</code>
     */
    int getMetadataCount();
    /**
     * <code>map&lt;string, string&gt; metadata = 12;</code>
     */
    boolean containsMetadata(
        java.lang.String key);
    /**
     * Use {@link #getMetadataMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.String, java.lang.String>
    getMetadata();
    /**
     * <code>map&lt;string, string&gt; metadata = 12;</code>
     */
    java.util.Map<java.lang.String, java.lang.String>
    getMetadataMap();
    /**
     * <code>map&lt;string, string&gt; metadata = 12;</code>
     */

    /* nullable */
java.lang.String getMetadataOrDefault(
        java.lang.String key,
        /* nullable */
java.lang.String defaultValue);
    /**
     * <code>map&lt;string, string&gt; metadata = 12;</code>
     */

    java.lang.String getMetadataOrThrow(
        java.lang.String key);
  }
  /**
   * Protobuf type {@code DeviceInformation}
   */
  public static final class DeviceInformation extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:DeviceInformation)
      DeviceInformationOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DeviceInformation.newBuilder() to construct.
    private DeviceInformation(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DeviceInformation() {
      serialNumber_ = "";
      name_ = "";
      supportedTransports_ = java.util.Collections.emptyList();
      deviceType_ = "";
      associatedDevices_ = emptyIntList();
      supportedSpeechInitiations_ = java.util.Collections.emptyList();
      supportedWakewords_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DeviceInformation();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceInformation_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 12:
          return internalGetMetadata();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceInformation_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Device.DeviceInformation.class, com.amazon.alexa.accessory.protocol.Device.DeviceInformation.Builder.class);
    }

    public static final int SERIAL_NUMBER_FIELD_NUMBER = 1;
    private volatile java.lang.Object serialNumber_;
    /**
     * <code>string serial_number = 1;</code>
     * @return The serialNumber.
     */
    @java.lang.Override
    public java.lang.String getSerialNumber() {
      java.lang.Object ref = serialNumber_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        serialNumber_ = s;
        return s;
      }
    }
    /**
     * <code>string serial_number = 1;</code>
     * @return The bytes for serialNumber.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getSerialNumberBytes() {
      java.lang.Object ref = serialNumber_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        serialNumber_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int NAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object name_;
    /**
     * <code>string name = 2;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SUPPORTED_TRANSPORTS_FIELD_NUMBER = 3;
    private java.util.List<java.lang.Integer> supportedTransports_;
    private static final com.google.protobuf.Internal.ListAdapter.Converter<
        java.lang.Integer, com.amazon.alexa.accessory.protocol.Common.Transport> supportedTransports_converter_ =
            new com.google.protobuf.Internal.ListAdapter.Converter<
                java.lang.Integer, com.amazon.alexa.accessory.protocol.Common.Transport>() {
              public com.amazon.alexa.accessory.protocol.Common.Transport convert(java.lang.Integer from) {
                @SuppressWarnings("deprecation")
                com.amazon.alexa.accessory.protocol.Common.Transport result = com.amazon.alexa.accessory.protocol.Common.Transport.valueOf(from);
                return result == null ? com.amazon.alexa.accessory.protocol.Common.Transport.UNRECOGNIZED : result;
              }
            };
    /**
     * <code>repeated .Transport supported_transports = 3;</code>
     * @return A list containing the supportedTransports.
     */
    @java.lang.Override
    public java.util.List<com.amazon.alexa.accessory.protocol.Common.Transport> getSupportedTransportsList() {
      return new com.google.protobuf.Internal.ListAdapter<
          java.lang.Integer, com.amazon.alexa.accessory.protocol.Common.Transport>(supportedTransports_, supportedTransports_converter_);
    }
    /**
     * <code>repeated .Transport supported_transports = 3;</code>
     * @return The count of supportedTransports.
     */
    @java.lang.Override
    public int getSupportedTransportsCount() {
      return supportedTransports_.size();
    }
    /**
     * <code>repeated .Transport supported_transports = 3;</code>
     * @param index The index of the element to return.
     * @return The supportedTransports at the given index.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Common.Transport getSupportedTransports(int index) {
      return supportedTransports_converter_.convert(supportedTransports_.get(index));
    }
    /**
     * <code>repeated .Transport supported_transports = 3;</code>
     * @return A list containing the enum numeric values on the wire for supportedTransports.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
    getSupportedTransportsValueList() {
      return supportedTransports_;
    }
    /**
     * <code>repeated .Transport supported_transports = 3;</code>
     * @param index The index of the value to return.
     * @return The enum numeric value on the wire of supportedTransports at the given index.
     */
    @java.lang.Override
    public int getSupportedTransportsValue(int index) {
      return supportedTransports_.get(index);
    }
    private int supportedTransportsMemoizedSerializedSize;

    public static final int DEVICE_TYPE_FIELD_NUMBER = 4;
    private volatile java.lang.Object deviceType_;
    /**
     * <code>string device_type = 4;</code>
     * @return The deviceType.
     */
    @java.lang.Override
    public java.lang.String getDeviceType() {
      java.lang.Object ref = deviceType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deviceType_ = s;
        return s;
      }
    }
    /**
     * <code>string device_type = 4;</code>
     * @return The bytes for deviceType.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDeviceTypeBytes() {
      java.lang.Object ref = deviceType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deviceType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DEVICE_ID_FIELD_NUMBER = 5;
    private int deviceId_;
    /**
     * <code>uint32 device_id = 5;</code>
     * @return The deviceId.
     */
    @java.lang.Override
    public int getDeviceId() {
      return deviceId_;
    }

    public static final int BATTERY_FIELD_NUMBER = 6;
    private com.amazon.alexa.accessory.protocol.Device.DeviceBattery battery_;
    /**
     * <code>.DeviceBattery battery = 6;</code>
     * @return Whether the battery field is set.
     */
    @java.lang.Override
    public boolean hasBattery() {
      return battery_ != null;
    }
    /**
     * <code>.DeviceBattery battery = 6;</code>
     * @return The battery.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.DeviceBattery getBattery() {
      return battery_ == null ? com.amazon.alexa.accessory.protocol.Device.DeviceBattery.getDefaultInstance() : battery_;
    }
    /**
     * <code>.DeviceBattery battery = 6;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.DeviceBatteryOrBuilder getBatteryOrBuilder() {
      return getBattery();
    }

    public static final int STATUS_FIELD_NUMBER = 7;
    private com.amazon.alexa.accessory.protocol.Device.DeviceStatus status_;
    /**
     * <code>.DeviceStatus status = 7;</code>
     * @return Whether the status field is set.
     */
    @java.lang.Override
    public boolean hasStatus() {
      return status_ != null;
    }
    /**
     * <code>.DeviceStatus status = 7;</code>
     * @return The status.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.DeviceStatus getStatus() {
      return status_ == null ? com.amazon.alexa.accessory.protocol.Device.DeviceStatus.getDefaultInstance() : status_;
    }
    /**
     * <code>.DeviceStatus status = 7;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.DeviceStatusOrBuilder getStatusOrBuilder() {
      return getStatus();
    }

    public static final int PRODUCT_COLOR_FIELD_NUMBER = 8;
    private int productColor_;
    /**
     * <code>uint32 product_color = 8;</code>
     * @return The productColor.
     */
    @java.lang.Override
    public int getProductColor() {
      return productColor_;
    }

    public static final int ASSOCIATED_DEVICES_FIELD_NUMBER = 9;
    private com.google.protobuf.Internal.IntList associatedDevices_;
    /**
     * <code>repeated uint32 associated_devices = 9;</code>
     * @return A list containing the associatedDevices.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getAssociatedDevicesList() {
      return associatedDevices_;
    }
    /**
     * <code>repeated uint32 associated_devices = 9;</code>
     * @return The count of associatedDevices.
     */
    public int getAssociatedDevicesCount() {
      return associatedDevices_.size();
    }
    /**
     * <code>repeated uint32 associated_devices = 9;</code>
     * @param index The index of the element to return.
     * @return The associatedDevices at the given index.
     */
    public int getAssociatedDevices(int index) {
      return associatedDevices_.getInt(index);
    }
    private int associatedDevicesMemoizedSerializedSize = -1;

    public static final int SUPPORTED_SPEECH_INITIATIONS_FIELD_NUMBER = 10;
    private java.util.List<java.lang.Integer> supportedSpeechInitiations_;
    private static final com.google.protobuf.Internal.ListAdapter.Converter<
        java.lang.Integer, com.amazon.alexa.accessory.protocol.Common.SpeechInitiationType> supportedSpeechInitiations_converter_ =
            new com.google.protobuf.Internal.ListAdapter.Converter<
                java.lang.Integer, com.amazon.alexa.accessory.protocol.Common.SpeechInitiationType>() {
              public com.amazon.alexa.accessory.protocol.Common.SpeechInitiationType convert(java.lang.Integer from) {
                @SuppressWarnings("deprecation")
                com.amazon.alexa.accessory.protocol.Common.SpeechInitiationType result = com.amazon.alexa.accessory.protocol.Common.SpeechInitiationType.valueOf(from);
                return result == null ? com.amazon.alexa.accessory.protocol.Common.SpeechInitiationType.UNRECOGNIZED : result;
              }
            };
    /**
     * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
     * @return A list containing the supportedSpeechInitiations.
     */
    @java.lang.Override
    public java.util.List<com.amazon.alexa.accessory.protocol.Common.SpeechInitiationType> getSupportedSpeechInitiationsList() {
      return new com.google.protobuf.Internal.ListAdapter<
          java.lang.Integer, com.amazon.alexa.accessory.protocol.Common.SpeechInitiationType>(supportedSpeechInitiations_, supportedSpeechInitiations_converter_);
    }
    /**
     * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
     * @return The count of supportedSpeechInitiations.
     */
    @java.lang.Override
    public int getSupportedSpeechInitiationsCount() {
      return supportedSpeechInitiations_.size();
    }
    /**
     * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
     * @param index The index of the element to return.
     * @return The supportedSpeechInitiations at the given index.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Common.SpeechInitiationType getSupportedSpeechInitiations(int index) {
      return supportedSpeechInitiations_converter_.convert(supportedSpeechInitiations_.get(index));
    }
    /**
     * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
     * @return A list containing the enum numeric values on the wire for supportedSpeechInitiations.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
    getSupportedSpeechInitiationsValueList() {
      return supportedSpeechInitiations_;
    }
    /**
     * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
     * @param index The index of the value to return.
     * @return The enum numeric value on the wire of supportedSpeechInitiations at the given index.
     */
    @java.lang.Override
    public int getSupportedSpeechInitiationsValue(int index) {
      return supportedSpeechInitiations_.get(index);
    }
    private int supportedSpeechInitiationsMemoizedSerializedSize;

    public static final int SUPPORTED_WAKEWORDS_FIELD_NUMBER = 11;
    private com.google.protobuf.LazyStringList supportedWakewords_;
    /**
     * <code>repeated string supported_wakewords = 11;</code>
     * @return A list containing the supportedWakewords.
     */
    public com.google.protobuf.ProtocolStringList
        getSupportedWakewordsList() {
      return supportedWakewords_;
    }
    /**
     * <code>repeated string supported_wakewords = 11;</code>
     * @return The count of supportedWakewords.
     */
    public int getSupportedWakewordsCount() {
      return supportedWakewords_.size();
    }
    /**
     * <code>repeated string supported_wakewords = 11;</code>
     * @param index The index of the element to return.
     * @return The supportedWakewords at the given index.
     */
    public java.lang.String getSupportedWakewords(int index) {
      return supportedWakewords_.get(index);
    }
    /**
     * <code>repeated string supported_wakewords = 11;</code>
     * @param index The index of the value to return.
     * @return The bytes of the supportedWakewords at the given index.
     */
    public com.google.protobuf.ByteString
        getSupportedWakewordsBytes(int index) {
      return supportedWakewords_.getByteString(index);
    }

    public static final int METADATA_FIELD_NUMBER = 12;
    private static final class MetadataDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.String, java.lang.String> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.String, java.lang.String>newDefaultInstance(
                  com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceInformation_MetadataEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "",
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        java.lang.String, java.lang.String> metadata_;
    private com.google.protobuf.MapField<java.lang.String, java.lang.String>
    internalGetMetadata() {
      if (metadata_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            MetadataDefaultEntryHolder.defaultEntry);
      }
      return metadata_;
    }

    public int getMetadataCount() {
      return internalGetMetadata().getMap().size();
    }
    /**
     * <code>map&lt;string, string&gt; metadata = 12;</code>
     */

    @java.lang.Override
    public boolean containsMetadata(
        java.lang.String key) {
      if (key == null) { throw new NullPointerException("map key"); }
      return internalGetMetadata().getMap().containsKey(key);
    }
    /**
     * Use {@link #getMetadataMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.String, java.lang.String> getMetadata() {
      return getMetadataMap();
    }
    /**
     * <code>map&lt;string, string&gt; metadata = 12;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.String, java.lang.String> getMetadataMap() {
      return internalGetMetadata().getMap();
    }
    /**
     * <code>map&lt;string, string&gt; metadata = 12;</code>
     */
    @java.lang.Override

    public java.lang.String getMetadataOrDefault(
        java.lang.String key,
        java.lang.String defaultValue) {
      if (key == null) { throw new NullPointerException("map key"); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetMetadata().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;string, string&gt; metadata = 12;</code>
     */
    @java.lang.Override

    public java.lang.String getMetadataOrThrow(
        java.lang.String key) {
      if (key == null) { throw new NullPointerException("map key"); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetMetadata().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(serialNumber_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, serialNumber_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
      }
      if (getSupportedTransportsList().size() > 0) {
        output.writeUInt32NoTag(26);
        output.writeUInt32NoTag(supportedTransportsMemoizedSerializedSize);
      }
      for (int i = 0; i < supportedTransports_.size(); i++) {
        output.writeEnumNoTag(supportedTransports_.get(i));
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceType_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, deviceType_);
      }
      if (deviceId_ != 0) {
        output.writeUInt32(5, deviceId_);
      }
      if (battery_ != null) {
        output.writeMessage(6, getBattery());
      }
      if (status_ != null) {
        output.writeMessage(7, getStatus());
      }
      if (productColor_ != 0) {
        output.writeUInt32(8, productColor_);
      }
      if (getAssociatedDevicesList().size() > 0) {
        output.writeUInt32NoTag(74);
        output.writeUInt32NoTag(associatedDevicesMemoizedSerializedSize);
      }
      for (int i = 0; i < associatedDevices_.size(); i++) {
        output.writeUInt32NoTag(associatedDevices_.getInt(i));
      }
      if (getSupportedSpeechInitiationsList().size() > 0) {
        output.writeUInt32NoTag(82);
        output.writeUInt32NoTag(supportedSpeechInitiationsMemoizedSerializedSize);
      }
      for (int i = 0; i < supportedSpeechInitiations_.size(); i++) {
        output.writeEnumNoTag(supportedSpeechInitiations_.get(i));
      }
      for (int i = 0; i < supportedWakewords_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 11, supportedWakewords_.getRaw(i));
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeStringMapTo(
          output,
          internalGetMetadata(),
          MetadataDefaultEntryHolder.defaultEntry,
          12);
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(serialNumber_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, serialNumber_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < supportedTransports_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeEnumSizeNoTag(supportedTransports_.get(i));
        }
        size += dataSize;
        if (!getSupportedTransportsList().isEmpty()) {  size += 1;
          size += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(dataSize);
        }supportedTransportsMemoizedSerializedSize = dataSize;
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceType_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, deviceType_);
      }
      if (deviceId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, deviceId_);
      }
      if (battery_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getBattery());
      }
      if (status_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getStatus());
      }
      if (productColor_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(8, productColor_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < associatedDevices_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(associatedDevices_.getInt(i));
        }
        size += dataSize;
        if (!getAssociatedDevicesList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        associatedDevicesMemoizedSerializedSize = dataSize;
      }
      {
        int dataSize = 0;
        for (int i = 0; i < supportedSpeechInitiations_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeEnumSizeNoTag(supportedSpeechInitiations_.get(i));
        }
        size += dataSize;
        if (!getSupportedSpeechInitiationsList().isEmpty()) {  size += 1;
          size += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(dataSize);
        }supportedSpeechInitiationsMemoizedSerializedSize = dataSize;
      }
      {
        int dataSize = 0;
        for (int i = 0; i < supportedWakewords_.size(); i++) {
          dataSize += computeStringSizeNoTag(supportedWakewords_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getSupportedWakewordsList().size();
      }
      for (java.util.Map.Entry<java.lang.String, java.lang.String> entry
           : internalGetMetadata().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
        metadata__ = MetadataDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(12, metadata__);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Device.DeviceInformation)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Device.DeviceInformation other = (com.amazon.alexa.accessory.protocol.Device.DeviceInformation) obj;

      if (!getSerialNumber()
          .equals(other.getSerialNumber())) return false;
      if (!getName()
          .equals(other.getName())) return false;
      if (!supportedTransports_.equals(other.supportedTransports_)) return false;
      if (!getDeviceType()
          .equals(other.getDeviceType())) return false;
      if (getDeviceId()
          != other.getDeviceId()) return false;
      if (hasBattery() != other.hasBattery()) return false;
      if (hasBattery()) {
        if (!getBattery()
            .equals(other.getBattery())) return false;
      }
      if (hasStatus() != other.hasStatus()) return false;
      if (hasStatus()) {
        if (!getStatus()
            .equals(other.getStatus())) return false;
      }
      if (getProductColor()
          != other.getProductColor()) return false;
      if (!getAssociatedDevicesList()
          .equals(other.getAssociatedDevicesList())) return false;
      if (!supportedSpeechInitiations_.equals(other.supportedSpeechInitiations_)) return false;
      if (!getSupportedWakewordsList()
          .equals(other.getSupportedWakewordsList())) return false;
      if (!internalGetMetadata().equals(
          other.internalGetMetadata())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SERIAL_NUMBER_FIELD_NUMBER;
      hash = (53 * hash) + getSerialNumber().hashCode();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      if (getSupportedTransportsCount() > 0) {
        hash = (37 * hash) + SUPPORTED_TRANSPORTS_FIELD_NUMBER;
        hash = (53 * hash) + supportedTransports_.hashCode();
      }
      hash = (37 * hash) + DEVICE_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getDeviceType().hashCode();
      hash = (37 * hash) + DEVICE_ID_FIELD_NUMBER;
      hash = (53 * hash) + getDeviceId();
      if (hasBattery()) {
        hash = (37 * hash) + BATTERY_FIELD_NUMBER;
        hash = (53 * hash) + getBattery().hashCode();
      }
      if (hasStatus()) {
        hash = (37 * hash) + STATUS_FIELD_NUMBER;
        hash = (53 * hash) + getStatus().hashCode();
      }
      hash = (37 * hash) + PRODUCT_COLOR_FIELD_NUMBER;
      hash = (53 * hash) + getProductColor();
      if (getAssociatedDevicesCount() > 0) {
        hash = (37 * hash) + ASSOCIATED_DEVICES_FIELD_NUMBER;
        hash = (53 * hash) + getAssociatedDevicesList().hashCode();
      }
      if (getSupportedSpeechInitiationsCount() > 0) {
        hash = (37 * hash) + SUPPORTED_SPEECH_INITIATIONS_FIELD_NUMBER;
        hash = (53 * hash) + supportedSpeechInitiations_.hashCode();
      }
      if (getSupportedWakewordsCount() > 0) {
        hash = (37 * hash) + SUPPORTED_WAKEWORDS_FIELD_NUMBER;
        hash = (53 * hash) + getSupportedWakewordsList().hashCode();
      }
      if (!internalGetMetadata().getMap().isEmpty()) {
        hash = (37 * hash) + METADATA_FIELD_NUMBER;
        hash = (53 * hash) + internalGetMetadata().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Device.DeviceInformation parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceInformation parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceInformation parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceInformation parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceInformation parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceInformation parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceInformation parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceInformation parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceInformation parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceInformation parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceInformation parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceInformation parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Device.DeviceInformation prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code DeviceInformation}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:DeviceInformation)
        com.amazon.alexa.accessory.protocol.Device.DeviceInformationOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceInformation_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 12:
            return internalGetMetadata();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 12:
            return internalGetMutableMetadata();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceInformation_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Device.DeviceInformation.class, com.amazon.alexa.accessory.protocol.Device.DeviceInformation.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Device.DeviceInformation.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        serialNumber_ = "";

        name_ = "";

        supportedTransports_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        deviceType_ = "";

        deviceId_ = 0;

        if (batteryBuilder_ == null) {
          battery_ = null;
        } else {
          battery_ = null;
          batteryBuilder_ = null;
        }
        if (statusBuilder_ == null) {
          status_ = null;
        } else {
          status_ = null;
          statusBuilder_ = null;
        }
        productColor_ = 0;

        associatedDevices_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        supportedSpeechInitiations_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        supportedWakewords_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000008);
        internalGetMutableMetadata().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceInformation_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceInformation getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Device.DeviceInformation.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceInformation build() {
        com.amazon.alexa.accessory.protocol.Device.DeviceInformation result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceInformation buildPartial() {
        com.amazon.alexa.accessory.protocol.Device.DeviceInformation result = new com.amazon.alexa.accessory.protocol.Device.DeviceInformation(this);
        int from_bitField0_ = bitField0_;
        result.serialNumber_ = serialNumber_;
        result.name_ = name_;
        if (((bitField0_ & 0x00000001) != 0)) {
          supportedTransports_ = java.util.Collections.unmodifiableList(supportedTransports_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.supportedTransports_ = supportedTransports_;
        result.deviceType_ = deviceType_;
        result.deviceId_ = deviceId_;
        if (batteryBuilder_ == null) {
          result.battery_ = battery_;
        } else {
          result.battery_ = batteryBuilder_.build();
        }
        if (statusBuilder_ == null) {
          result.status_ = status_;
        } else {
          result.status_ = statusBuilder_.build();
        }
        result.productColor_ = productColor_;
        if (((bitField0_ & 0x00000002) != 0)) {
          associatedDevices_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.associatedDevices_ = associatedDevices_;
        if (((bitField0_ & 0x00000004) != 0)) {
          supportedSpeechInitiations_ = java.util.Collections.unmodifiableList(supportedSpeechInitiations_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.supportedSpeechInitiations_ = supportedSpeechInitiations_;
        if (((bitField0_ & 0x00000008) != 0)) {
          supportedWakewords_ = supportedWakewords_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.supportedWakewords_ = supportedWakewords_;
        result.metadata_ = internalGetMetadata();
        result.metadata_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Device.DeviceInformation) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Device.DeviceInformation)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Device.DeviceInformation other) {
        if (other == com.amazon.alexa.accessory.protocol.Device.DeviceInformation.getDefaultInstance()) return this;
        if (!other.getSerialNumber().isEmpty()) {
          serialNumber_ = other.serialNumber_;
          onChanged();
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        if (!other.supportedTransports_.isEmpty()) {
          if (supportedTransports_.isEmpty()) {
            supportedTransports_ = other.supportedTransports_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureSupportedTransportsIsMutable();
            supportedTransports_.addAll(other.supportedTransports_);
          }
          onChanged();
        }
        if (!other.getDeviceType().isEmpty()) {
          deviceType_ = other.deviceType_;
          onChanged();
        }
        if (other.getDeviceId() != 0) {
          setDeviceId(other.getDeviceId());
        }
        if (other.hasBattery()) {
          mergeBattery(other.getBattery());
        }
        if (other.hasStatus()) {
          mergeStatus(other.getStatus());
        }
        if (other.getProductColor() != 0) {
          setProductColor(other.getProductColor());
        }
        if (!other.associatedDevices_.isEmpty()) {
          if (associatedDevices_.isEmpty()) {
            associatedDevices_ = other.associatedDevices_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureAssociatedDevicesIsMutable();
            associatedDevices_.addAll(other.associatedDevices_);
          }
          onChanged();
        }
        if (!other.supportedSpeechInitiations_.isEmpty()) {
          if (supportedSpeechInitiations_.isEmpty()) {
            supportedSpeechInitiations_ = other.supportedSpeechInitiations_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureSupportedSpeechInitiationsIsMutable();
            supportedSpeechInitiations_.addAll(other.supportedSpeechInitiations_);
          }
          onChanged();
        }
        if (!other.supportedWakewords_.isEmpty()) {
          if (supportedWakewords_.isEmpty()) {
            supportedWakewords_ = other.supportedWakewords_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureSupportedWakewordsIsMutable();
            supportedWakewords_.addAll(other.supportedWakewords_);
          }
          onChanged();
        }
        internalGetMutableMetadata().mergeFrom(
            other.internalGetMetadata());
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                serialNumber_ = input.readStringRequireUtf8();

                break;
              } // case 10
              case 18: {
                name_ = input.readStringRequireUtf8();

                break;
              } // case 18
              case 24: {
                int tmpRaw = input.readEnum();
                ensureSupportedTransportsIsMutable();
                supportedTransports_.add(tmpRaw);
                break;
              } // case 24
              case 26: {
                int length = input.readRawVarint32();
                int oldLimit = input.pushLimit(length);
                while(input.getBytesUntilLimit() > 0) {
                  int tmpRaw = input.readEnum();
                  ensureSupportedTransportsIsMutable();
                  supportedTransports_.add(tmpRaw);
                }
                input.popLimit(oldLimit);
                break;
              } // case 26
              case 34: {
                deviceType_ = input.readStringRequireUtf8();

                break;
              } // case 34
              case 40: {
                deviceId_ = input.readUInt32();

                break;
              } // case 40
              case 50: {
                input.readMessage(
                    getBatteryFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 50
              case 58: {
                input.readMessage(
                    getStatusFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 58
              case 64: {
                productColor_ = input.readUInt32();

                break;
              } // case 64
              case 72: {
                int v = input.readUInt32();
                ensureAssociatedDevicesIsMutable();
                associatedDevices_.addInt(v);
                break;
              } // case 72
              case 74: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureAssociatedDevicesIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  associatedDevices_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 74
              case 80: {
                int tmpRaw = input.readEnum();
                ensureSupportedSpeechInitiationsIsMutable();
                supportedSpeechInitiations_.add(tmpRaw);
                break;
              } // case 80
              case 82: {
                int length = input.readRawVarint32();
                int oldLimit = input.pushLimit(length);
                while(input.getBytesUntilLimit() > 0) {
                  int tmpRaw = input.readEnum();
                  ensureSupportedSpeechInitiationsIsMutable();
                  supportedSpeechInitiations_.add(tmpRaw);
                }
                input.popLimit(oldLimit);
                break;
              } // case 82
              case 90: {
                java.lang.String s = input.readStringRequireUtf8();
                ensureSupportedWakewordsIsMutable();
                supportedWakewords_.add(s);
                break;
              } // case 90
              case 98: {
                com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
                metadata__ = input.readMessage(
                    MetadataDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
                internalGetMutableMetadata().getMutableMap().put(
                    metadata__.getKey(), metadata__.getValue());
                break;
              } // case 98
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object serialNumber_ = "";
      /**
       * <code>string serial_number = 1;</code>
       * @return The serialNumber.
       */
      public java.lang.String getSerialNumber() {
        java.lang.Object ref = serialNumber_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          serialNumber_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string serial_number = 1;</code>
       * @return The bytes for serialNumber.
       */
      public com.google.protobuf.ByteString
          getSerialNumberBytes() {
        java.lang.Object ref = serialNumber_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          serialNumber_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string serial_number = 1;</code>
       * @param value The serialNumber to set.
       * @return This builder for chaining.
       */
      public Builder setSerialNumber(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        serialNumber_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string serial_number = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSerialNumber() {
        
        serialNumber_ = getDefaultInstance().getSerialNumber();
        onChanged();
        return this;
      }
      /**
       * <code>string serial_number = 1;</code>
       * @param value The bytes for serialNumber to set.
       * @return This builder for chaining.
       */
      public Builder setSerialNumberBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        serialNumber_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>string name = 2;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }

      private java.util.List<java.lang.Integer> supportedTransports_ =
        java.util.Collections.emptyList();
      private void ensureSupportedTransportsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          supportedTransports_ = new java.util.ArrayList<java.lang.Integer>(supportedTransports_);
          bitField0_ |= 0x00000001;
        }
      }
      /**
       * <code>repeated .Transport supported_transports = 3;</code>
       * @return A list containing the supportedTransports.
       */
      public java.util.List<com.amazon.alexa.accessory.protocol.Common.Transport> getSupportedTransportsList() {
        return new com.google.protobuf.Internal.ListAdapter<
            java.lang.Integer, com.amazon.alexa.accessory.protocol.Common.Transport>(supportedTransports_, supportedTransports_converter_);
      }
      /**
       * <code>repeated .Transport supported_transports = 3;</code>
       * @return The count of supportedTransports.
       */
      public int getSupportedTransportsCount() {
        return supportedTransports_.size();
      }
      /**
       * <code>repeated .Transport supported_transports = 3;</code>
       * @param index The index of the element to return.
       * @return The supportedTransports at the given index.
       */
      public com.amazon.alexa.accessory.protocol.Common.Transport getSupportedTransports(int index) {
        return supportedTransports_converter_.convert(supportedTransports_.get(index));
      }
      /**
       * <code>repeated .Transport supported_transports = 3;</code>
       * @param index The index to set the value at.
       * @param value The supportedTransports to set.
       * @return This builder for chaining.
       */
      public Builder setSupportedTransports(
          int index, com.amazon.alexa.accessory.protocol.Common.Transport value) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSupportedTransportsIsMutable();
        supportedTransports_.set(index, value.getNumber());
        onChanged();
        return this;
      }
      /**
       * <code>repeated .Transport supported_transports = 3;</code>
       * @param value The supportedTransports to add.
       * @return This builder for chaining.
       */
      public Builder addSupportedTransports(com.amazon.alexa.accessory.protocol.Common.Transport value) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSupportedTransportsIsMutable();
        supportedTransports_.add(value.getNumber());
        onChanged();
        return this;
      }
      /**
       * <code>repeated .Transport supported_transports = 3;</code>
       * @param values The supportedTransports to add.
       * @return This builder for chaining.
       */
      public Builder addAllSupportedTransports(
          java.lang.Iterable<? extends com.amazon.alexa.accessory.protocol.Common.Transport> values) {
        ensureSupportedTransportsIsMutable();
        for (com.amazon.alexa.accessory.protocol.Common.Transport value : values) {
          supportedTransports_.add(value.getNumber());
        }
        onChanged();
        return this;
      }
      /**
       * <code>repeated .Transport supported_transports = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSupportedTransports() {
        supportedTransports_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>repeated .Transport supported_transports = 3;</code>
       * @return A list containing the enum numeric values on the wire for supportedTransports.
       */
      public java.util.List<java.lang.Integer>
      getSupportedTransportsValueList() {
        return java.util.Collections.unmodifiableList(supportedTransports_);
      }
      /**
       * <code>repeated .Transport supported_transports = 3;</code>
       * @param index The index of the value to return.
       * @return The enum numeric value on the wire of supportedTransports at the given index.
       */
      public int getSupportedTransportsValue(int index) {
        return supportedTransports_.get(index);
      }
      /**
       * <code>repeated .Transport supported_transports = 3;</code>
       * @param index The index to set the value at.
       * @param value The enum numeric value on the wire for supportedTransports to set.
       * @return This builder for chaining.
       */
      public Builder setSupportedTransportsValue(
          int index, int value) {
        ensureSupportedTransportsIsMutable();
        supportedTransports_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated .Transport supported_transports = 3;</code>
       * @param value The enum numeric value on the wire for supportedTransports to add.
       * @return This builder for chaining.
       */
      public Builder addSupportedTransportsValue(int value) {
        ensureSupportedTransportsIsMutable();
        supportedTransports_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated .Transport supported_transports = 3;</code>
       * @param values The enum numeric values on the wire for supportedTransports to add.
       * @return This builder for chaining.
       */
      public Builder addAllSupportedTransportsValue(
          java.lang.Iterable<java.lang.Integer> values) {
        ensureSupportedTransportsIsMutable();
        for (int value : values) {
          supportedTransports_.add(value);
        }
        onChanged();
        return this;
      }

      private java.lang.Object deviceType_ = "";
      /**
       * <code>string device_type = 4;</code>
       * @return The deviceType.
       */
      public java.lang.String getDeviceType() {
        java.lang.Object ref = deviceType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          deviceType_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string device_type = 4;</code>
       * @return The bytes for deviceType.
       */
      public com.google.protobuf.ByteString
          getDeviceTypeBytes() {
        java.lang.Object ref = deviceType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          deviceType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string device_type = 4;</code>
       * @param value The deviceType to set.
       * @return This builder for chaining.
       */
      public Builder setDeviceType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        deviceType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string device_type = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeviceType() {
        
        deviceType_ = getDefaultInstance().getDeviceType();
        onChanged();
        return this;
      }
      /**
       * <code>string device_type = 4;</code>
       * @param value The bytes for deviceType to set.
       * @return This builder for chaining.
       */
      public Builder setDeviceTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        deviceType_ = value;
        onChanged();
        return this;
      }

      private int deviceId_ ;
      /**
       * <code>uint32 device_id = 5;</code>
       * @return The deviceId.
       */
      @java.lang.Override
      public int getDeviceId() {
        return deviceId_;
      }
      /**
       * <code>uint32 device_id = 5;</code>
       * @param value The deviceId to set.
       * @return This builder for chaining.
       */
      public Builder setDeviceId(int value) {
        
        deviceId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 device_id = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeviceId() {
        
        deviceId_ = 0;
        onChanged();
        return this;
      }

      private com.amazon.alexa.accessory.protocol.Device.DeviceBattery battery_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.DeviceBattery, com.amazon.alexa.accessory.protocol.Device.DeviceBattery.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceBatteryOrBuilder> batteryBuilder_;
      /**
       * <code>.DeviceBattery battery = 6;</code>
       * @return Whether the battery field is set.
       */
      public boolean hasBattery() {
        return batteryBuilder_ != null || battery_ != null;
      }
      /**
       * <code>.DeviceBattery battery = 6;</code>
       * @return The battery.
       */
      public com.amazon.alexa.accessory.protocol.Device.DeviceBattery getBattery() {
        if (batteryBuilder_ == null) {
          return battery_ == null ? com.amazon.alexa.accessory.protocol.Device.DeviceBattery.getDefaultInstance() : battery_;
        } else {
          return batteryBuilder_.getMessage();
        }
      }
      /**
       * <code>.DeviceBattery battery = 6;</code>
       */
      public Builder setBattery(com.amazon.alexa.accessory.protocol.Device.DeviceBattery value) {
        if (batteryBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          battery_ = value;
          onChanged();
        } else {
          batteryBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.DeviceBattery battery = 6;</code>
       */
      public Builder setBattery(
          com.amazon.alexa.accessory.protocol.Device.DeviceBattery.Builder builderForValue) {
        if (batteryBuilder_ == null) {
          battery_ = builderForValue.build();
          onChanged();
        } else {
          batteryBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.DeviceBattery battery = 6;</code>
       */
      public Builder mergeBattery(com.amazon.alexa.accessory.protocol.Device.DeviceBattery value) {
        if (batteryBuilder_ == null) {
          if (battery_ != null) {
            battery_ =
              com.amazon.alexa.accessory.protocol.Device.DeviceBattery.newBuilder(battery_).mergeFrom(value).buildPartial();
          } else {
            battery_ = value;
          }
          onChanged();
        } else {
          batteryBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.DeviceBattery battery = 6;</code>
       */
      public Builder clearBattery() {
        if (batteryBuilder_ == null) {
          battery_ = null;
          onChanged();
        } else {
          battery_ = null;
          batteryBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.DeviceBattery battery = 6;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.DeviceBattery.Builder getBatteryBuilder() {
        
        onChanged();
        return getBatteryFieldBuilder().getBuilder();
      }
      /**
       * <code>.DeviceBattery battery = 6;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.DeviceBatteryOrBuilder getBatteryOrBuilder() {
        if (batteryBuilder_ != null) {
          return batteryBuilder_.getMessageOrBuilder();
        } else {
          return battery_ == null ?
              com.amazon.alexa.accessory.protocol.Device.DeviceBattery.getDefaultInstance() : battery_;
        }
      }
      /**
       * <code>.DeviceBattery battery = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.DeviceBattery, com.amazon.alexa.accessory.protocol.Device.DeviceBattery.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceBatteryOrBuilder> 
          getBatteryFieldBuilder() {
        if (batteryBuilder_ == null) {
          batteryBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Device.DeviceBattery, com.amazon.alexa.accessory.protocol.Device.DeviceBattery.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceBatteryOrBuilder>(
                  getBattery(),
                  getParentForChildren(),
                  isClean());
          battery_ = null;
        }
        return batteryBuilder_;
      }

      private com.amazon.alexa.accessory.protocol.Device.DeviceStatus status_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.DeviceStatus, com.amazon.alexa.accessory.protocol.Device.DeviceStatus.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceStatusOrBuilder> statusBuilder_;
      /**
       * <code>.DeviceStatus status = 7;</code>
       * @return Whether the status field is set.
       */
      public boolean hasStatus() {
        return statusBuilder_ != null || status_ != null;
      }
      /**
       * <code>.DeviceStatus status = 7;</code>
       * @return The status.
       */
      public com.amazon.alexa.accessory.protocol.Device.DeviceStatus getStatus() {
        if (statusBuilder_ == null) {
          return status_ == null ? com.amazon.alexa.accessory.protocol.Device.DeviceStatus.getDefaultInstance() : status_;
        } else {
          return statusBuilder_.getMessage();
        }
      }
      /**
       * <code>.DeviceStatus status = 7;</code>
       */
      public Builder setStatus(com.amazon.alexa.accessory.protocol.Device.DeviceStatus value) {
        if (statusBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          status_ = value;
          onChanged();
        } else {
          statusBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.DeviceStatus status = 7;</code>
       */
      public Builder setStatus(
          com.amazon.alexa.accessory.protocol.Device.DeviceStatus.Builder builderForValue) {
        if (statusBuilder_ == null) {
          status_ = builderForValue.build();
          onChanged();
        } else {
          statusBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.DeviceStatus status = 7;</code>
       */
      public Builder mergeStatus(com.amazon.alexa.accessory.protocol.Device.DeviceStatus value) {
        if (statusBuilder_ == null) {
          if (status_ != null) {
            status_ =
              com.amazon.alexa.accessory.protocol.Device.DeviceStatus.newBuilder(status_).mergeFrom(value).buildPartial();
          } else {
            status_ = value;
          }
          onChanged();
        } else {
          statusBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.DeviceStatus status = 7;</code>
       */
      public Builder clearStatus() {
        if (statusBuilder_ == null) {
          status_ = null;
          onChanged();
        } else {
          status_ = null;
          statusBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.DeviceStatus status = 7;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.DeviceStatus.Builder getStatusBuilder() {
        
        onChanged();
        return getStatusFieldBuilder().getBuilder();
      }
      /**
       * <code>.DeviceStatus status = 7;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.DeviceStatusOrBuilder getStatusOrBuilder() {
        if (statusBuilder_ != null) {
          return statusBuilder_.getMessageOrBuilder();
        } else {
          return status_ == null ?
              com.amazon.alexa.accessory.protocol.Device.DeviceStatus.getDefaultInstance() : status_;
        }
      }
      /**
       * <code>.DeviceStatus status = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.DeviceStatus, com.amazon.alexa.accessory.protocol.Device.DeviceStatus.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceStatusOrBuilder> 
          getStatusFieldBuilder() {
        if (statusBuilder_ == null) {
          statusBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Device.DeviceStatus, com.amazon.alexa.accessory.protocol.Device.DeviceStatus.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceStatusOrBuilder>(
                  getStatus(),
                  getParentForChildren(),
                  isClean());
          status_ = null;
        }
        return statusBuilder_;
      }

      private int productColor_ ;
      /**
       * <code>uint32 product_color = 8;</code>
       * @return The productColor.
       */
      @java.lang.Override
      public int getProductColor() {
        return productColor_;
      }
      /**
       * <code>uint32 product_color = 8;</code>
       * @param value The productColor to set.
       * @return This builder for chaining.
       */
      public Builder setProductColor(int value) {
        
        productColor_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 product_color = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearProductColor() {
        
        productColor_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList associatedDevices_ = emptyIntList();
      private void ensureAssociatedDevicesIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          associatedDevices_ = mutableCopy(associatedDevices_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated uint32 associated_devices = 9;</code>
       * @return A list containing the associatedDevices.
       */
      public java.util.List<java.lang.Integer>
          getAssociatedDevicesList() {
        return ((bitField0_ & 0x00000002) != 0) ?
                 java.util.Collections.unmodifiableList(associatedDevices_) : associatedDevices_;
      }
      /**
       * <code>repeated uint32 associated_devices = 9;</code>
       * @return The count of associatedDevices.
       */
      public int getAssociatedDevicesCount() {
        return associatedDevices_.size();
      }
      /**
       * <code>repeated uint32 associated_devices = 9;</code>
       * @param index The index of the element to return.
       * @return The associatedDevices at the given index.
       */
      public int getAssociatedDevices(int index) {
        return associatedDevices_.getInt(index);
      }
      /**
       * <code>repeated uint32 associated_devices = 9;</code>
       * @param index The index to set the value at.
       * @param value The associatedDevices to set.
       * @return This builder for chaining.
       */
      public Builder setAssociatedDevices(
          int index, int value) {
        ensureAssociatedDevicesIsMutable();
        associatedDevices_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 associated_devices = 9;</code>
       * @param value The associatedDevices to add.
       * @return This builder for chaining.
       */
      public Builder addAssociatedDevices(int value) {
        ensureAssociatedDevicesIsMutable();
        associatedDevices_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 associated_devices = 9;</code>
       * @param values The associatedDevices to add.
       * @return This builder for chaining.
       */
      public Builder addAllAssociatedDevices(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureAssociatedDevicesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, associatedDevices_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 associated_devices = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearAssociatedDevices() {
        associatedDevices_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private java.util.List<java.lang.Integer> supportedSpeechInitiations_ =
        java.util.Collections.emptyList();
      private void ensureSupportedSpeechInitiationsIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          supportedSpeechInitiations_ = new java.util.ArrayList<java.lang.Integer>(supportedSpeechInitiations_);
          bitField0_ |= 0x00000004;
        }
      }
      /**
       * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
       * @return A list containing the supportedSpeechInitiations.
       */
      public java.util.List<com.amazon.alexa.accessory.protocol.Common.SpeechInitiationType> getSupportedSpeechInitiationsList() {
        return new com.google.protobuf.Internal.ListAdapter<
            java.lang.Integer, com.amazon.alexa.accessory.protocol.Common.SpeechInitiationType>(supportedSpeechInitiations_, supportedSpeechInitiations_converter_);
      }
      /**
       * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
       * @return The count of supportedSpeechInitiations.
       */
      public int getSupportedSpeechInitiationsCount() {
        return supportedSpeechInitiations_.size();
      }
      /**
       * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
       * @param index The index of the element to return.
       * @return The supportedSpeechInitiations at the given index.
       */
      public com.amazon.alexa.accessory.protocol.Common.SpeechInitiationType getSupportedSpeechInitiations(int index) {
        return supportedSpeechInitiations_converter_.convert(supportedSpeechInitiations_.get(index));
      }
      /**
       * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
       * @param index The index to set the value at.
       * @param value The supportedSpeechInitiations to set.
       * @return This builder for chaining.
       */
      public Builder setSupportedSpeechInitiations(
          int index, com.amazon.alexa.accessory.protocol.Common.SpeechInitiationType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSupportedSpeechInitiationsIsMutable();
        supportedSpeechInitiations_.set(index, value.getNumber());
        onChanged();
        return this;
      }
      /**
       * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
       * @param value The supportedSpeechInitiations to add.
       * @return This builder for chaining.
       */
      public Builder addSupportedSpeechInitiations(com.amazon.alexa.accessory.protocol.Common.SpeechInitiationType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSupportedSpeechInitiationsIsMutable();
        supportedSpeechInitiations_.add(value.getNumber());
        onChanged();
        return this;
      }
      /**
       * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
       * @param values The supportedSpeechInitiations to add.
       * @return This builder for chaining.
       */
      public Builder addAllSupportedSpeechInitiations(
          java.lang.Iterable<? extends com.amazon.alexa.accessory.protocol.Common.SpeechInitiationType> values) {
        ensureSupportedSpeechInitiationsIsMutable();
        for (com.amazon.alexa.accessory.protocol.Common.SpeechInitiationType value : values) {
          supportedSpeechInitiations_.add(value.getNumber());
        }
        onChanged();
        return this;
      }
      /**
       * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearSupportedSpeechInitiations() {
        supportedSpeechInitiations_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
       * @return A list containing the enum numeric values on the wire for supportedSpeechInitiations.
       */
      public java.util.List<java.lang.Integer>
      getSupportedSpeechInitiationsValueList() {
        return java.util.Collections.unmodifiableList(supportedSpeechInitiations_);
      }
      /**
       * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
       * @param index The index of the value to return.
       * @return The enum numeric value on the wire of supportedSpeechInitiations at the given index.
       */
      public int getSupportedSpeechInitiationsValue(int index) {
        return supportedSpeechInitiations_.get(index);
      }
      /**
       * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
       * @param index The index to set the value at.
       * @param value The enum numeric value on the wire for supportedSpeechInitiations to set.
       * @return This builder for chaining.
       */
      public Builder setSupportedSpeechInitiationsValue(
          int index, int value) {
        ensureSupportedSpeechInitiationsIsMutable();
        supportedSpeechInitiations_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
       * @param value The enum numeric value on the wire for supportedSpeechInitiations to add.
       * @return This builder for chaining.
       */
      public Builder addSupportedSpeechInitiationsValue(int value) {
        ensureSupportedSpeechInitiationsIsMutable();
        supportedSpeechInitiations_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated .SpeechInitiationType supported_speech_initiations = 10;</code>
       * @param values The enum numeric values on the wire for supportedSpeechInitiations to add.
       * @return This builder for chaining.
       */
      public Builder addAllSupportedSpeechInitiationsValue(
          java.lang.Iterable<java.lang.Integer> values) {
        ensureSupportedSpeechInitiationsIsMutable();
        for (int value : values) {
          supportedSpeechInitiations_.add(value);
        }
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList supportedWakewords_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureSupportedWakewordsIsMutable() {
        if (!((bitField0_ & 0x00000008) != 0)) {
          supportedWakewords_ = new com.google.protobuf.LazyStringArrayList(supportedWakewords_);
          bitField0_ |= 0x00000008;
         }
      }
      /**
       * <code>repeated string supported_wakewords = 11;</code>
       * @return A list containing the supportedWakewords.
       */
      public com.google.protobuf.ProtocolStringList
          getSupportedWakewordsList() {
        return supportedWakewords_.getUnmodifiableView();
      }
      /**
       * <code>repeated string supported_wakewords = 11;</code>
       * @return The count of supportedWakewords.
       */
      public int getSupportedWakewordsCount() {
        return supportedWakewords_.size();
      }
      /**
       * <code>repeated string supported_wakewords = 11;</code>
       * @param index The index of the element to return.
       * @return The supportedWakewords at the given index.
       */
      public java.lang.String getSupportedWakewords(int index) {
        return supportedWakewords_.get(index);
      }
      /**
       * <code>repeated string supported_wakewords = 11;</code>
       * @param index The index of the value to return.
       * @return The bytes of the supportedWakewords at the given index.
       */
      public com.google.protobuf.ByteString
          getSupportedWakewordsBytes(int index) {
        return supportedWakewords_.getByteString(index);
      }
      /**
       * <code>repeated string supported_wakewords = 11;</code>
       * @param index The index to set the value at.
       * @param value The supportedWakewords to set.
       * @return This builder for chaining.
       */
      public Builder setSupportedWakewords(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureSupportedWakewordsIsMutable();
        supportedWakewords_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string supported_wakewords = 11;</code>
       * @param value The supportedWakewords to add.
       * @return This builder for chaining.
       */
      public Builder addSupportedWakewords(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureSupportedWakewordsIsMutable();
        supportedWakewords_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string supported_wakewords = 11;</code>
       * @param values The supportedWakewords to add.
       * @return This builder for chaining.
       */
      public Builder addAllSupportedWakewords(
          java.lang.Iterable<java.lang.String> values) {
        ensureSupportedWakewordsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, supportedWakewords_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string supported_wakewords = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearSupportedWakewords() {
        supportedWakewords_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string supported_wakewords = 11;</code>
       * @param value The bytes of the supportedWakewords to add.
       * @return This builder for chaining.
       */
      public Builder addSupportedWakewordsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        ensureSupportedWakewordsIsMutable();
        supportedWakewords_.add(value);
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.String, java.lang.String> metadata_;
      private com.google.protobuf.MapField<java.lang.String, java.lang.String>
      internalGetMetadata() {
        if (metadata_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              MetadataDefaultEntryHolder.defaultEntry);
        }
        return metadata_;
      }
      private com.google.protobuf.MapField<java.lang.String, java.lang.String>
      internalGetMutableMetadata() {
        onChanged();;
        if (metadata_ == null) {
          metadata_ = com.google.protobuf.MapField.newMapField(
              MetadataDefaultEntryHolder.defaultEntry);
        }
        if (!metadata_.isMutable()) {
          metadata_ = metadata_.copy();
        }
        return metadata_;
      }

      public int getMetadataCount() {
        return internalGetMetadata().getMap().size();
      }
      /**
       * <code>map&lt;string, string&gt; metadata = 12;</code>
       */

      @java.lang.Override
      public boolean containsMetadata(
          java.lang.String key) {
        if (key == null) { throw new NullPointerException("map key"); }
        return internalGetMetadata().getMap().containsKey(key);
      }
      /**
       * Use {@link #getMetadataMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.String, java.lang.String> getMetadata() {
        return getMetadataMap();
      }
      /**
       * <code>map&lt;string, string&gt; metadata = 12;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.String, java.lang.String> getMetadataMap() {
        return internalGetMetadata().getMap();
      }
      /**
       * <code>map&lt;string, string&gt; metadata = 12;</code>
       */
      @java.lang.Override

      public java.lang.String getMetadataOrDefault(
          java.lang.String key,
          java.lang.String defaultValue) {
        if (key == null) { throw new NullPointerException("map key"); }
        java.util.Map<java.lang.String, java.lang.String> map =
            internalGetMetadata().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;string, string&gt; metadata = 12;</code>
       */
      @java.lang.Override

      public java.lang.String getMetadataOrThrow(
          java.lang.String key) {
        if (key == null) { throw new NullPointerException("map key"); }
        java.util.Map<java.lang.String, java.lang.String> map =
            internalGetMetadata().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearMetadata() {
        internalGetMutableMetadata().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;string, string&gt; metadata = 12;</code>
       */

      public Builder removeMetadata(
          java.lang.String key) {
        if (key == null) { throw new NullPointerException("map key"); }
        internalGetMutableMetadata().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.String, java.lang.String>
      getMutableMetadata() {
        return internalGetMutableMetadata().getMutableMap();
      }
      /**
       * <code>map&lt;string, string&gt; metadata = 12;</code>
       */
      public Builder putMetadata(
          java.lang.String key,
          java.lang.String value) {
        if (key == null) { throw new NullPointerException("map key"); }
        if (value == null) {
  throw new NullPointerException("map value");
}

        internalGetMutableMetadata().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;string, string&gt; metadata = 12;</code>
       */

      public Builder putAllMetadata(
          java.util.Map<java.lang.String, java.lang.String> values) {
        internalGetMutableMetadata().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:DeviceInformation)
    }

    // @@protoc_insertion_point(class_scope:DeviceInformation)
    private static final com.amazon.alexa.accessory.protocol.Device.DeviceInformation DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Device.DeviceInformation();
    }

    public static com.amazon.alexa.accessory.protocol.Device.DeviceInformation getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DeviceInformation>
        PARSER = new com.google.protobuf.AbstractParser<DeviceInformation>() {
      @java.lang.Override
      public DeviceInformation parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<DeviceInformation> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DeviceInformation> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.DeviceInformation getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetDeviceInformationOrBuilder extends
      // @@protoc_insertion_point(interface_extends:GetDeviceInformation)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 device_id = 1;</code>
     * @return The deviceId.
     */
    int getDeviceId();
  }
  /**
   * Protobuf type {@code GetDeviceInformation}
   */
  public static final class GetDeviceInformation extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:GetDeviceInformation)
      GetDeviceInformationOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetDeviceInformation.newBuilder() to construct.
    private GetDeviceInformation(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetDeviceInformation() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetDeviceInformation();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_GetDeviceInformation_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_GetDeviceInformation_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation.class, com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation.Builder.class);
    }

    public static final int DEVICE_ID_FIELD_NUMBER = 1;
    private int deviceId_;
    /**
     * <code>uint32 device_id = 1;</code>
     * @return The deviceId.
     */
    @java.lang.Override
    public int getDeviceId() {
      return deviceId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (deviceId_ != 0) {
        output.writeUInt32(1, deviceId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (deviceId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, deviceId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation other = (com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation) obj;

      if (getDeviceId()
          != other.getDeviceId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + DEVICE_ID_FIELD_NUMBER;
      hash = (53 * hash) + getDeviceId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code GetDeviceInformation}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:GetDeviceInformation)
        com.amazon.alexa.accessory.protocol.Device.GetDeviceInformationOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_GetDeviceInformation_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_GetDeviceInformation_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation.class, com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        deviceId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_GetDeviceInformation_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation build() {
        com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation buildPartial() {
        com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation result = new com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation(this);
        result.deviceId_ = deviceId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation other) {
        if (other == com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation.getDefaultInstance()) return this;
        if (other.getDeviceId() != 0) {
          setDeviceId(other.getDeviceId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                deviceId_ = input.readUInt32();

                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private int deviceId_ ;
      /**
       * <code>uint32 device_id = 1;</code>
       * @return The deviceId.
       */
      @java.lang.Override
      public int getDeviceId() {
        return deviceId_;
      }
      /**
       * <code>uint32 device_id = 1;</code>
       * @param value The deviceId to set.
       * @return This builder for chaining.
       */
      public Builder setDeviceId(int value) {
        
        deviceId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 device_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeviceId() {
        
        deviceId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:GetDeviceInformation)
    }

    // @@protoc_insertion_point(class_scope:GetDeviceInformation)
    private static final com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation();
    }

    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GetDeviceInformation>
        PARSER = new com.google.protobuf.AbstractParser<GetDeviceInformation>() {
      @java.lang.Override
      public GetDeviceInformation parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<GetDeviceInformation> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetDeviceInformation> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DeviceConfigurationOrBuilder extends
      // @@protoc_insertion_point(interface_extends:DeviceConfiguration)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>bool needs_assistant_override = 1;</code>
     * @return The needsAssistantOverride.
     */
    boolean getNeedsAssistantOverride();

    /**
     * <code>bool needs_setup = 2;</code>
     * @return The needsSetup.
     */
    boolean getNeedsSetup();
  }
  /**
   * Protobuf type {@code DeviceConfiguration}
   */
  public static final class DeviceConfiguration extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:DeviceConfiguration)
      DeviceConfigurationOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DeviceConfiguration.newBuilder() to construct.
    private DeviceConfiguration(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DeviceConfiguration() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DeviceConfiguration();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceConfiguration_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceConfiguration_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.class, com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.Builder.class);
    }

    public static final int NEEDS_ASSISTANT_OVERRIDE_FIELD_NUMBER = 1;
    private boolean needsAssistantOverride_;
    /**
     * <code>bool needs_assistant_override = 1;</code>
     * @return The needsAssistantOverride.
     */
    @java.lang.Override
    public boolean getNeedsAssistantOverride() {
      return needsAssistantOverride_;
    }

    public static final int NEEDS_SETUP_FIELD_NUMBER = 2;
    private boolean needsSetup_;
    /**
     * <code>bool needs_setup = 2;</code>
     * @return The needsSetup.
     */
    @java.lang.Override
    public boolean getNeedsSetup() {
      return needsSetup_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (needsAssistantOverride_ != false) {
        output.writeBool(1, needsAssistantOverride_);
      }
      if (needsSetup_ != false) {
        output.writeBool(2, needsSetup_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (needsAssistantOverride_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, needsAssistantOverride_);
      }
      if (needsSetup_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, needsSetup_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration other = (com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration) obj;

      if (getNeedsAssistantOverride()
          != other.getNeedsAssistantOverride()) return false;
      if (getNeedsSetup()
          != other.getNeedsSetup()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + NEEDS_ASSISTANT_OVERRIDE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getNeedsAssistantOverride());
      hash = (37 * hash) + NEEDS_SETUP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getNeedsSetup());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code DeviceConfiguration}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:DeviceConfiguration)
        com.amazon.alexa.accessory.protocol.Device.DeviceConfigurationOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceConfiguration_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceConfiguration_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.class, com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        needsAssistantOverride_ = false;

        needsSetup_ = false;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceConfiguration_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration build() {
        com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration buildPartial() {
        com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration result = new com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration(this);
        result.needsAssistantOverride_ = needsAssistantOverride_;
        result.needsSetup_ = needsSetup_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration other) {
        if (other == com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.getDefaultInstance()) return this;
        if (other.getNeedsAssistantOverride() != false) {
          setNeedsAssistantOverride(other.getNeedsAssistantOverride());
        }
        if (other.getNeedsSetup() != false) {
          setNeedsSetup(other.getNeedsSetup());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                needsAssistantOverride_ = input.readBool();

                break;
              } // case 8
              case 16: {
                needsSetup_ = input.readBool();

                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private boolean needsAssistantOverride_ ;
      /**
       * <code>bool needs_assistant_override = 1;</code>
       * @return The needsAssistantOverride.
       */
      @java.lang.Override
      public boolean getNeedsAssistantOverride() {
        return needsAssistantOverride_;
      }
      /**
       * <code>bool needs_assistant_override = 1;</code>
       * @param value The needsAssistantOverride to set.
       * @return This builder for chaining.
       */
      public Builder setNeedsAssistantOverride(boolean value) {
        
        needsAssistantOverride_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>bool needs_assistant_override = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearNeedsAssistantOverride() {
        
        needsAssistantOverride_ = false;
        onChanged();
        return this;
      }

      private boolean needsSetup_ ;
      /**
       * <code>bool needs_setup = 2;</code>
       * @return The needsSetup.
       */
      @java.lang.Override
      public boolean getNeedsSetup() {
        return needsSetup_;
      }
      /**
       * <code>bool needs_setup = 2;</code>
       * @param value The needsSetup to set.
       * @return This builder for chaining.
       */
      public Builder setNeedsSetup(boolean value) {
        
        needsSetup_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>bool needs_setup = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNeedsSetup() {
        
        needsSetup_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:DeviceConfiguration)
    }

    // @@protoc_insertion_point(class_scope:DeviceConfiguration)
    private static final com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration();
    }

    public static com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DeviceConfiguration>
        PARSER = new com.google.protobuf.AbstractParser<DeviceConfiguration>() {
      @java.lang.Override
      public DeviceConfiguration parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<DeviceConfiguration> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DeviceConfiguration> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetDeviceConfigurationOrBuilder extends
      // @@protoc_insertion_point(interface_extends:GetDeviceConfiguration)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code GetDeviceConfiguration}
   */
  public static final class GetDeviceConfiguration extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:GetDeviceConfiguration)
      GetDeviceConfigurationOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetDeviceConfiguration.newBuilder() to construct.
    private GetDeviceConfiguration(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetDeviceConfiguration() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetDeviceConfiguration();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_GetDeviceConfiguration_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_GetDeviceConfiguration_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration.class, com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration other = (com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code GetDeviceConfiguration}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:GetDeviceConfiguration)
        com.amazon.alexa.accessory.protocol.Device.GetDeviceConfigurationOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_GetDeviceConfiguration_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_GetDeviceConfiguration_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration.class, com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_GetDeviceConfiguration_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration build() {
        com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration buildPartial() {
        com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration result = new com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration other) {
        if (other == com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:GetDeviceConfiguration)
    }

    // @@protoc_insertion_point(class_scope:GetDeviceConfiguration)
    private static final com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration();
    }

    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GetDeviceConfiguration>
        PARSER = new com.google.protobuf.AbstractParser<GetDeviceConfiguration>() {
      @java.lang.Override
      public GetDeviceConfiguration parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<GetDeviceConfiguration> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetDeviceConfiguration> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OverrideAssistantOrBuilder extends
      // @@protoc_insertion_point(interface_extends:OverrideAssistant)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.ErrorCode error_code = 1;</code>
     * @return The enum numeric value on the wire for errorCode.
     */
    int getErrorCodeValue();
    /**
     * <code>.ErrorCode error_code = 1;</code>
     * @return The errorCode.
     */
    com.amazon.alexa.accessory.protocol.Common.ErrorCode getErrorCode();
  }
  /**
   * Protobuf type {@code OverrideAssistant}
   */
  public static final class OverrideAssistant extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:OverrideAssistant)
      OverrideAssistantOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OverrideAssistant.newBuilder() to construct.
    private OverrideAssistant(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OverrideAssistant() {
      errorCode_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OverrideAssistant();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_OverrideAssistant_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_OverrideAssistant_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Device.OverrideAssistant.class, com.amazon.alexa.accessory.protocol.Device.OverrideAssistant.Builder.class);
    }

    public static final int ERROR_CODE_FIELD_NUMBER = 1;
    private int errorCode_;
    /**
     * <code>.ErrorCode error_code = 1;</code>
     * @return The enum numeric value on the wire for errorCode.
     */
    @java.lang.Override public int getErrorCodeValue() {
      return errorCode_;
    }
    /**
     * <code>.ErrorCode error_code = 1;</code>
     * @return The errorCode.
     */
    @java.lang.Override public com.amazon.alexa.accessory.protocol.Common.ErrorCode getErrorCode() {
      @SuppressWarnings("deprecation")
      com.amazon.alexa.accessory.protocol.Common.ErrorCode result = com.amazon.alexa.accessory.protocol.Common.ErrorCode.valueOf(errorCode_);
      return result == null ? com.amazon.alexa.accessory.protocol.Common.ErrorCode.UNRECOGNIZED : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (errorCode_ != com.amazon.alexa.accessory.protocol.Common.ErrorCode.SUCCESS.getNumber()) {
        output.writeEnum(1, errorCode_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (errorCode_ != com.amazon.alexa.accessory.protocol.Common.ErrorCode.SUCCESS.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, errorCode_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Device.OverrideAssistant)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Device.OverrideAssistant other = (com.amazon.alexa.accessory.protocol.Device.OverrideAssistant) obj;

      if (errorCode_ != other.errorCode_) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ERROR_CODE_FIELD_NUMBER;
      hash = (53 * hash) + errorCode_;
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Device.OverrideAssistant parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.OverrideAssistant parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.OverrideAssistant parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.OverrideAssistant parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.OverrideAssistant parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.OverrideAssistant parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.OverrideAssistant parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.OverrideAssistant parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.OverrideAssistant parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.OverrideAssistant parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.OverrideAssistant parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.OverrideAssistant parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Device.OverrideAssistant prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code OverrideAssistant}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:OverrideAssistant)
        com.amazon.alexa.accessory.protocol.Device.OverrideAssistantOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_OverrideAssistant_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_OverrideAssistant_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Device.OverrideAssistant.class, com.amazon.alexa.accessory.protocol.Device.OverrideAssistant.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Device.OverrideAssistant.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        errorCode_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_OverrideAssistant_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.OverrideAssistant getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Device.OverrideAssistant.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.OverrideAssistant build() {
        com.amazon.alexa.accessory.protocol.Device.OverrideAssistant result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.OverrideAssistant buildPartial() {
        com.amazon.alexa.accessory.protocol.Device.OverrideAssistant result = new com.amazon.alexa.accessory.protocol.Device.OverrideAssistant(this);
        result.errorCode_ = errorCode_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Device.OverrideAssistant) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Device.OverrideAssistant)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Device.OverrideAssistant other) {
        if (other == com.amazon.alexa.accessory.protocol.Device.OverrideAssistant.getDefaultInstance()) return this;
        if (other.errorCode_ != 0) {
          setErrorCodeValue(other.getErrorCodeValue());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                errorCode_ = input.readEnum();

                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private int errorCode_ = 0;
      /**
       * <code>.ErrorCode error_code = 1;</code>
       * @return The enum numeric value on the wire for errorCode.
       */
      @java.lang.Override public int getErrorCodeValue() {
        return errorCode_;
      }
      /**
       * <code>.ErrorCode error_code = 1;</code>
       * @param value The enum numeric value on the wire for errorCode to set.
       * @return This builder for chaining.
       */
      public Builder setErrorCodeValue(int value) {
        
        errorCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.ErrorCode error_code = 1;</code>
       * @return The errorCode.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Common.ErrorCode getErrorCode() {
        @SuppressWarnings("deprecation")
        com.amazon.alexa.accessory.protocol.Common.ErrorCode result = com.amazon.alexa.accessory.protocol.Common.ErrorCode.valueOf(errorCode_);
        return result == null ? com.amazon.alexa.accessory.protocol.Common.ErrorCode.UNRECOGNIZED : result;
      }
      /**
       * <code>.ErrorCode error_code = 1;</code>
       * @param value The errorCode to set.
       * @return This builder for chaining.
       */
      public Builder setErrorCode(com.amazon.alexa.accessory.protocol.Common.ErrorCode value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        errorCode_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.ErrorCode error_code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearErrorCode() {
        
        errorCode_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:OverrideAssistant)
    }

    // @@protoc_insertion_point(class_scope:OverrideAssistant)
    private static final com.amazon.alexa.accessory.protocol.Device.OverrideAssistant DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Device.OverrideAssistant();
    }

    public static com.amazon.alexa.accessory.protocol.Device.OverrideAssistant getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<OverrideAssistant>
        PARSER = new com.google.protobuf.AbstractParser<OverrideAssistant>() {
      @java.lang.Override
      public OverrideAssistant parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<OverrideAssistant> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OverrideAssistant> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.OverrideAssistant getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface StartSetupOrBuilder extends
      // @@protoc_insertion_point(interface_extends:StartSetup)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code StartSetup}
   */
  public static final class StartSetup extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:StartSetup)
      StartSetupOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use StartSetup.newBuilder() to construct.
    private StartSetup(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private StartSetup() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new StartSetup();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_StartSetup_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_StartSetup_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Device.StartSetup.class, com.amazon.alexa.accessory.protocol.Device.StartSetup.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Device.StartSetup)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Device.StartSetup other = (com.amazon.alexa.accessory.protocol.Device.StartSetup) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Device.StartSetup parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.StartSetup parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.StartSetup parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.StartSetup parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.StartSetup parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.StartSetup parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.StartSetup parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.StartSetup parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.StartSetup parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.StartSetup parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.StartSetup parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.StartSetup parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Device.StartSetup prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code StartSetup}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:StartSetup)
        com.amazon.alexa.accessory.protocol.Device.StartSetupOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_StartSetup_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_StartSetup_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Device.StartSetup.class, com.amazon.alexa.accessory.protocol.Device.StartSetup.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Device.StartSetup.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_StartSetup_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.StartSetup getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Device.StartSetup.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.StartSetup build() {
        com.amazon.alexa.accessory.protocol.Device.StartSetup result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.StartSetup buildPartial() {
        com.amazon.alexa.accessory.protocol.Device.StartSetup result = new com.amazon.alexa.accessory.protocol.Device.StartSetup(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Device.StartSetup) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Device.StartSetup)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Device.StartSetup other) {
        if (other == com.amazon.alexa.accessory.protocol.Device.StartSetup.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:StartSetup)
    }

    // @@protoc_insertion_point(class_scope:StartSetup)
    private static final com.amazon.alexa.accessory.protocol.Device.StartSetup DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Device.StartSetup();
    }

    public static com.amazon.alexa.accessory.protocol.Device.StartSetup getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<StartSetup>
        PARSER = new com.google.protobuf.AbstractParser<StartSetup>() {
      @java.lang.Override
      public StartSetup parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<StartSetup> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<StartSetup> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.StartSetup getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CompleteSetupOrBuilder extends
      // @@protoc_insertion_point(interface_extends:CompleteSetup)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.ErrorCode error_code = 1;</code>
     * @return The enum numeric value on the wire for errorCode.
     */
    int getErrorCodeValue();
    /**
     * <code>.ErrorCode error_code = 1;</code>
     * @return The errorCode.
     */
    com.amazon.alexa.accessory.protocol.Common.ErrorCode getErrorCode();
  }
  /**
   * Protobuf type {@code CompleteSetup}
   */
  public static final class CompleteSetup extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:CompleteSetup)
      CompleteSetupOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CompleteSetup.newBuilder() to construct.
    private CompleteSetup(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CompleteSetup() {
      errorCode_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CompleteSetup();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_CompleteSetup_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_CompleteSetup_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Device.CompleteSetup.class, com.amazon.alexa.accessory.protocol.Device.CompleteSetup.Builder.class);
    }

    public static final int ERROR_CODE_FIELD_NUMBER = 1;
    private int errorCode_;
    /**
     * <code>.ErrorCode error_code = 1;</code>
     * @return The enum numeric value on the wire for errorCode.
     */
    @java.lang.Override public int getErrorCodeValue() {
      return errorCode_;
    }
    /**
     * <code>.ErrorCode error_code = 1;</code>
     * @return The errorCode.
     */
    @java.lang.Override public com.amazon.alexa.accessory.protocol.Common.ErrorCode getErrorCode() {
      @SuppressWarnings("deprecation")
      com.amazon.alexa.accessory.protocol.Common.ErrorCode result = com.amazon.alexa.accessory.protocol.Common.ErrorCode.valueOf(errorCode_);
      return result == null ? com.amazon.alexa.accessory.protocol.Common.ErrorCode.UNRECOGNIZED : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (errorCode_ != com.amazon.alexa.accessory.protocol.Common.ErrorCode.SUCCESS.getNumber()) {
        output.writeEnum(1, errorCode_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (errorCode_ != com.amazon.alexa.accessory.protocol.Common.ErrorCode.SUCCESS.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, errorCode_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Device.CompleteSetup)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Device.CompleteSetup other = (com.amazon.alexa.accessory.protocol.Device.CompleteSetup) obj;

      if (errorCode_ != other.errorCode_) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ERROR_CODE_FIELD_NUMBER;
      hash = (53 * hash) + errorCode_;
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Device.CompleteSetup parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.CompleteSetup parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.CompleteSetup parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.CompleteSetup parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.CompleteSetup parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.CompleteSetup parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.CompleteSetup parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.CompleteSetup parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.CompleteSetup parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.CompleteSetup parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.CompleteSetup parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.CompleteSetup parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Device.CompleteSetup prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code CompleteSetup}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:CompleteSetup)
        com.amazon.alexa.accessory.protocol.Device.CompleteSetupOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_CompleteSetup_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_CompleteSetup_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Device.CompleteSetup.class, com.amazon.alexa.accessory.protocol.Device.CompleteSetup.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Device.CompleteSetup.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        errorCode_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_CompleteSetup_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.CompleteSetup getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Device.CompleteSetup.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.CompleteSetup build() {
        com.amazon.alexa.accessory.protocol.Device.CompleteSetup result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.CompleteSetup buildPartial() {
        com.amazon.alexa.accessory.protocol.Device.CompleteSetup result = new com.amazon.alexa.accessory.protocol.Device.CompleteSetup(this);
        result.errorCode_ = errorCode_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Device.CompleteSetup) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Device.CompleteSetup)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Device.CompleteSetup other) {
        if (other == com.amazon.alexa.accessory.protocol.Device.CompleteSetup.getDefaultInstance()) return this;
        if (other.errorCode_ != 0) {
          setErrorCodeValue(other.getErrorCodeValue());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                errorCode_ = input.readEnum();

                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private int errorCode_ = 0;
      /**
       * <code>.ErrorCode error_code = 1;</code>
       * @return The enum numeric value on the wire for errorCode.
       */
      @java.lang.Override public int getErrorCodeValue() {
        return errorCode_;
      }
      /**
       * <code>.ErrorCode error_code = 1;</code>
       * @param value The enum numeric value on the wire for errorCode to set.
       * @return This builder for chaining.
       */
      public Builder setErrorCodeValue(int value) {
        
        errorCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.ErrorCode error_code = 1;</code>
       * @return The errorCode.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Common.ErrorCode getErrorCode() {
        @SuppressWarnings("deprecation")
        com.amazon.alexa.accessory.protocol.Common.ErrorCode result = com.amazon.alexa.accessory.protocol.Common.ErrorCode.valueOf(errorCode_);
        return result == null ? com.amazon.alexa.accessory.protocol.Common.ErrorCode.UNRECOGNIZED : result;
      }
      /**
       * <code>.ErrorCode error_code = 1;</code>
       * @param value The errorCode to set.
       * @return This builder for chaining.
       */
      public Builder setErrorCode(com.amazon.alexa.accessory.protocol.Common.ErrorCode value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        errorCode_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.ErrorCode error_code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearErrorCode() {
        
        errorCode_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:CompleteSetup)
    }

    // @@protoc_insertion_point(class_scope:CompleteSetup)
    private static final com.amazon.alexa.accessory.protocol.Device.CompleteSetup DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Device.CompleteSetup();
    }

    public static com.amazon.alexa.accessory.protocol.Device.CompleteSetup getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CompleteSetup>
        PARSER = new com.google.protobuf.AbstractParser<CompleteSetup>() {
      @java.lang.Override
      public CompleteSetup parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<CompleteSetup> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CompleteSetup> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.CompleteSetup getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface NotifyDeviceConfigurationOrBuilder extends
      // @@protoc_insertion_point(interface_extends:NotifyDeviceConfiguration)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.DeviceConfiguration device_configuration = 1;</code>
     * @return Whether the deviceConfiguration field is set.
     */
    boolean hasDeviceConfiguration();
    /**
     * <code>.DeviceConfiguration device_configuration = 1;</code>
     * @return The deviceConfiguration.
     */
    com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration getDeviceConfiguration();
    /**
     * <code>.DeviceConfiguration device_configuration = 1;</code>
     */
    com.amazon.alexa.accessory.protocol.Device.DeviceConfigurationOrBuilder getDeviceConfigurationOrBuilder();
  }
  /**
   * Protobuf type {@code NotifyDeviceConfiguration}
   */
  public static final class NotifyDeviceConfiguration extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:NotifyDeviceConfiguration)
      NotifyDeviceConfigurationOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use NotifyDeviceConfiguration.newBuilder() to construct.
    private NotifyDeviceConfiguration(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private NotifyDeviceConfiguration() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new NotifyDeviceConfiguration();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_NotifyDeviceConfiguration_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_NotifyDeviceConfiguration_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration.class, com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration.Builder.class);
    }

    public static final int DEVICE_CONFIGURATION_FIELD_NUMBER = 1;
    private com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration deviceConfiguration_;
    /**
     * <code>.DeviceConfiguration device_configuration = 1;</code>
     * @return Whether the deviceConfiguration field is set.
     */
    @java.lang.Override
    public boolean hasDeviceConfiguration() {
      return deviceConfiguration_ != null;
    }
    /**
     * <code>.DeviceConfiguration device_configuration = 1;</code>
     * @return The deviceConfiguration.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration getDeviceConfiguration() {
      return deviceConfiguration_ == null ? com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.getDefaultInstance() : deviceConfiguration_;
    }
    /**
     * <code>.DeviceConfiguration device_configuration = 1;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.DeviceConfigurationOrBuilder getDeviceConfigurationOrBuilder() {
      return getDeviceConfiguration();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (deviceConfiguration_ != null) {
        output.writeMessage(1, getDeviceConfiguration());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (deviceConfiguration_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getDeviceConfiguration());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration other = (com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration) obj;

      if (hasDeviceConfiguration() != other.hasDeviceConfiguration()) return false;
      if (hasDeviceConfiguration()) {
        if (!getDeviceConfiguration()
            .equals(other.getDeviceConfiguration())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasDeviceConfiguration()) {
        hash = (37 * hash) + DEVICE_CONFIGURATION_FIELD_NUMBER;
        hash = (53 * hash) + getDeviceConfiguration().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code NotifyDeviceConfiguration}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:NotifyDeviceConfiguration)
        com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfigurationOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_NotifyDeviceConfiguration_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_NotifyDeviceConfiguration_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration.class, com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (deviceConfigurationBuilder_ == null) {
          deviceConfiguration_ = null;
        } else {
          deviceConfiguration_ = null;
          deviceConfigurationBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_NotifyDeviceConfiguration_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration build() {
        com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration buildPartial() {
        com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration result = new com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration(this);
        if (deviceConfigurationBuilder_ == null) {
          result.deviceConfiguration_ = deviceConfiguration_;
        } else {
          result.deviceConfiguration_ = deviceConfigurationBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration other) {
        if (other == com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration.getDefaultInstance()) return this;
        if (other.hasDeviceConfiguration()) {
          mergeDeviceConfiguration(other.getDeviceConfiguration());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getDeviceConfigurationFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration deviceConfiguration_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration, com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceConfigurationOrBuilder> deviceConfigurationBuilder_;
      /**
       * <code>.DeviceConfiguration device_configuration = 1;</code>
       * @return Whether the deviceConfiguration field is set.
       */
      public boolean hasDeviceConfiguration() {
        return deviceConfigurationBuilder_ != null || deviceConfiguration_ != null;
      }
      /**
       * <code>.DeviceConfiguration device_configuration = 1;</code>
       * @return The deviceConfiguration.
       */
      public com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration getDeviceConfiguration() {
        if (deviceConfigurationBuilder_ == null) {
          return deviceConfiguration_ == null ? com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.getDefaultInstance() : deviceConfiguration_;
        } else {
          return deviceConfigurationBuilder_.getMessage();
        }
      }
      /**
       * <code>.DeviceConfiguration device_configuration = 1;</code>
       */
      public Builder setDeviceConfiguration(com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration value) {
        if (deviceConfigurationBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          deviceConfiguration_ = value;
          onChanged();
        } else {
          deviceConfigurationBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.DeviceConfiguration device_configuration = 1;</code>
       */
      public Builder setDeviceConfiguration(
          com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.Builder builderForValue) {
        if (deviceConfigurationBuilder_ == null) {
          deviceConfiguration_ = builderForValue.build();
          onChanged();
        } else {
          deviceConfigurationBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.DeviceConfiguration device_configuration = 1;</code>
       */
      public Builder mergeDeviceConfiguration(com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration value) {
        if (deviceConfigurationBuilder_ == null) {
          if (deviceConfiguration_ != null) {
            deviceConfiguration_ =
              com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.newBuilder(deviceConfiguration_).mergeFrom(value).buildPartial();
          } else {
            deviceConfiguration_ = value;
          }
          onChanged();
        } else {
          deviceConfigurationBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.DeviceConfiguration device_configuration = 1;</code>
       */
      public Builder clearDeviceConfiguration() {
        if (deviceConfigurationBuilder_ == null) {
          deviceConfiguration_ = null;
          onChanged();
        } else {
          deviceConfiguration_ = null;
          deviceConfigurationBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.DeviceConfiguration device_configuration = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.Builder getDeviceConfigurationBuilder() {
        
        onChanged();
        return getDeviceConfigurationFieldBuilder().getBuilder();
      }
      /**
       * <code>.DeviceConfiguration device_configuration = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.DeviceConfigurationOrBuilder getDeviceConfigurationOrBuilder() {
        if (deviceConfigurationBuilder_ != null) {
          return deviceConfigurationBuilder_.getMessageOrBuilder();
        } else {
          return deviceConfiguration_ == null ?
              com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.getDefaultInstance() : deviceConfiguration_;
        }
      }
      /**
       * <code>.DeviceConfiguration device_configuration = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration, com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceConfigurationOrBuilder> 
          getDeviceConfigurationFieldBuilder() {
        if (deviceConfigurationBuilder_ == null) {
          deviceConfigurationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration, com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceConfigurationOrBuilder>(
                  getDeviceConfiguration(),
                  getParentForChildren(),
                  isClean());
          deviceConfiguration_ = null;
        }
        return deviceConfigurationBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:NotifyDeviceConfiguration)
    }

    // @@protoc_insertion_point(class_scope:NotifyDeviceConfiguration)
    private static final com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration();
    }

    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<NotifyDeviceConfiguration>
        PARSER = new com.google.protobuf.AbstractParser<NotifyDeviceConfiguration>() {
      @java.lang.Override
      public NotifyDeviceConfiguration parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<NotifyDeviceConfiguration> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<NotifyDeviceConfiguration> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface UpdateDeviceInformationOrBuilder extends
      // @@protoc_insertion_point(interface_extends:UpdateDeviceInformation)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string name = 1;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <code>string name = 1;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <code>uint32 device_id = 2;</code>
     * @return The deviceId.
     */
    int getDeviceId();
  }
  /**
   * Protobuf type {@code UpdateDeviceInformation}
   */
  public static final class UpdateDeviceInformation extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:UpdateDeviceInformation)
      UpdateDeviceInformationOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use UpdateDeviceInformation.newBuilder() to construct.
    private UpdateDeviceInformation(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private UpdateDeviceInformation() {
      name_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new UpdateDeviceInformation();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_UpdateDeviceInformation_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_UpdateDeviceInformation_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation.class, com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation.Builder.class);
    }

    public static final int NAME_FIELD_NUMBER = 1;
    private volatile java.lang.Object name_;
    /**
     * <code>string name = 1;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <code>string name = 1;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DEVICE_ID_FIELD_NUMBER = 2;
    private int deviceId_;
    /**
     * <code>uint32 device_id = 2;</code>
     * @return The deviceId.
     */
    @java.lang.Override
    public int getDeviceId() {
      return deviceId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, name_);
      }
      if (deviceId_ != 0) {
        output.writeUInt32(2, deviceId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, name_);
      }
      if (deviceId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, deviceId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation other = (com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation) obj;

      if (!getName()
          .equals(other.getName())) return false;
      if (getDeviceId()
          != other.getDeviceId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (37 * hash) + DEVICE_ID_FIELD_NUMBER;
      hash = (53 * hash) + getDeviceId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code UpdateDeviceInformation}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:UpdateDeviceInformation)
        com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformationOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_UpdateDeviceInformation_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_UpdateDeviceInformation_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation.class, com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        name_ = "";

        deviceId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_UpdateDeviceInformation_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation build() {
        com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation buildPartial() {
        com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation result = new com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation(this);
        result.name_ = name_;
        result.deviceId_ = deviceId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation other) {
        if (other == com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation.getDefaultInstance()) return this;
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        if (other.getDeviceId() != 0) {
          setDeviceId(other.getDeviceId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                name_ = input.readStringRequireUtf8();

                break;
              } // case 10
              case 16: {
                deviceId_ = input.readUInt32();

                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>string name = 1;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string name = 1;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string name = 1;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string name = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>string name = 1;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }

      private int deviceId_ ;
      /**
       * <code>uint32 device_id = 2;</code>
       * @return The deviceId.
       */
      @java.lang.Override
      public int getDeviceId() {
        return deviceId_;
      }
      /**
       * <code>uint32 device_id = 2;</code>
       * @param value The deviceId to set.
       * @return This builder for chaining.
       */
      public Builder setDeviceId(int value) {
        
        deviceId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 device_id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeviceId() {
        
        deviceId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:UpdateDeviceInformation)
    }

    // @@protoc_insertion_point(class_scope:UpdateDeviceInformation)
    private static final com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation();
    }

    public static com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<UpdateDeviceInformation>
        PARSER = new com.google.protobuf.AbstractParser<UpdateDeviceInformation>() {
      @java.lang.Override
      public UpdateDeviceInformation parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<UpdateDeviceInformation> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UpdateDeviceInformation> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface NotifyDeviceInformationOrBuilder extends
      // @@protoc_insertion_point(interface_extends:NotifyDeviceInformation)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.DeviceInformation device_information = 1;</code>
     * @return Whether the deviceInformation field is set.
     */
    boolean hasDeviceInformation();
    /**
     * <code>.DeviceInformation device_information = 1;</code>
     * @return The deviceInformation.
     */
    com.amazon.alexa.accessory.protocol.Device.DeviceInformation getDeviceInformation();
    /**
     * <code>.DeviceInformation device_information = 1;</code>
     */
    com.amazon.alexa.accessory.protocol.Device.DeviceInformationOrBuilder getDeviceInformationOrBuilder();
  }
  /**
   * Protobuf type {@code NotifyDeviceInformation}
   */
  public static final class NotifyDeviceInformation extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:NotifyDeviceInformation)
      NotifyDeviceInformationOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use NotifyDeviceInformation.newBuilder() to construct.
    private NotifyDeviceInformation(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private NotifyDeviceInformation() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new NotifyDeviceInformation();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_NotifyDeviceInformation_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_NotifyDeviceInformation_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation.class, com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation.Builder.class);
    }

    public static final int DEVICE_INFORMATION_FIELD_NUMBER = 1;
    private com.amazon.alexa.accessory.protocol.Device.DeviceInformation deviceInformation_;
    /**
     * <code>.DeviceInformation device_information = 1;</code>
     * @return Whether the deviceInformation field is set.
     */
    @java.lang.Override
    public boolean hasDeviceInformation() {
      return deviceInformation_ != null;
    }
    /**
     * <code>.DeviceInformation device_information = 1;</code>
     * @return The deviceInformation.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.DeviceInformation getDeviceInformation() {
      return deviceInformation_ == null ? com.amazon.alexa.accessory.protocol.Device.DeviceInformation.getDefaultInstance() : deviceInformation_;
    }
    /**
     * <code>.DeviceInformation device_information = 1;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.DeviceInformationOrBuilder getDeviceInformationOrBuilder() {
      return getDeviceInformation();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (deviceInformation_ != null) {
        output.writeMessage(1, getDeviceInformation());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (deviceInformation_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getDeviceInformation());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation other = (com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation) obj;

      if (hasDeviceInformation() != other.hasDeviceInformation()) return false;
      if (hasDeviceInformation()) {
        if (!getDeviceInformation()
            .equals(other.getDeviceInformation())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasDeviceInformation()) {
        hash = (37 * hash) + DEVICE_INFORMATION_FIELD_NUMBER;
        hash = (53 * hash) + getDeviceInformation().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code NotifyDeviceInformation}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:NotifyDeviceInformation)
        com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformationOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_NotifyDeviceInformation_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_NotifyDeviceInformation_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation.class, com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (deviceInformationBuilder_ == null) {
          deviceInformation_ = null;
        } else {
          deviceInformation_ = null;
          deviceInformationBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_NotifyDeviceInformation_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation build() {
        com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation buildPartial() {
        com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation result = new com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation(this);
        if (deviceInformationBuilder_ == null) {
          result.deviceInformation_ = deviceInformation_;
        } else {
          result.deviceInformation_ = deviceInformationBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation other) {
        if (other == com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation.getDefaultInstance()) return this;
        if (other.hasDeviceInformation()) {
          mergeDeviceInformation(other.getDeviceInformation());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getDeviceInformationFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private com.amazon.alexa.accessory.protocol.Device.DeviceInformation deviceInformation_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.DeviceInformation, com.amazon.alexa.accessory.protocol.Device.DeviceInformation.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceInformationOrBuilder> deviceInformationBuilder_;
      /**
       * <code>.DeviceInformation device_information = 1;</code>
       * @return Whether the deviceInformation field is set.
       */
      public boolean hasDeviceInformation() {
        return deviceInformationBuilder_ != null || deviceInformation_ != null;
      }
      /**
       * <code>.DeviceInformation device_information = 1;</code>
       * @return The deviceInformation.
       */
      public com.amazon.alexa.accessory.protocol.Device.DeviceInformation getDeviceInformation() {
        if (deviceInformationBuilder_ == null) {
          return deviceInformation_ == null ? com.amazon.alexa.accessory.protocol.Device.DeviceInformation.getDefaultInstance() : deviceInformation_;
        } else {
          return deviceInformationBuilder_.getMessage();
        }
      }
      /**
       * <code>.DeviceInformation device_information = 1;</code>
       */
      public Builder setDeviceInformation(com.amazon.alexa.accessory.protocol.Device.DeviceInformation value) {
        if (deviceInformationBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          deviceInformation_ = value;
          onChanged();
        } else {
          deviceInformationBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.DeviceInformation device_information = 1;</code>
       */
      public Builder setDeviceInformation(
          com.amazon.alexa.accessory.protocol.Device.DeviceInformation.Builder builderForValue) {
        if (deviceInformationBuilder_ == null) {
          deviceInformation_ = builderForValue.build();
          onChanged();
        } else {
          deviceInformationBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.DeviceInformation device_information = 1;</code>
       */
      public Builder mergeDeviceInformation(com.amazon.alexa.accessory.protocol.Device.DeviceInformation value) {
        if (deviceInformationBuilder_ == null) {
          if (deviceInformation_ != null) {
            deviceInformation_ =
              com.amazon.alexa.accessory.protocol.Device.DeviceInformation.newBuilder(deviceInformation_).mergeFrom(value).buildPartial();
          } else {
            deviceInformation_ = value;
          }
          onChanged();
        } else {
          deviceInformationBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.DeviceInformation device_information = 1;</code>
       */
      public Builder clearDeviceInformation() {
        if (deviceInformationBuilder_ == null) {
          deviceInformation_ = null;
          onChanged();
        } else {
          deviceInformation_ = null;
          deviceInformationBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.DeviceInformation device_information = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.DeviceInformation.Builder getDeviceInformationBuilder() {
        
        onChanged();
        return getDeviceInformationFieldBuilder().getBuilder();
      }
      /**
       * <code>.DeviceInformation device_information = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.DeviceInformationOrBuilder getDeviceInformationOrBuilder() {
        if (deviceInformationBuilder_ != null) {
          return deviceInformationBuilder_.getMessageOrBuilder();
        } else {
          return deviceInformation_ == null ?
              com.amazon.alexa.accessory.protocol.Device.DeviceInformation.getDefaultInstance() : deviceInformation_;
        }
      }
      /**
       * <code>.DeviceInformation device_information = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.DeviceInformation, com.amazon.alexa.accessory.protocol.Device.DeviceInformation.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceInformationOrBuilder> 
          getDeviceInformationFieldBuilder() {
        if (deviceInformationBuilder_ == null) {
          deviceInformationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Device.DeviceInformation, com.amazon.alexa.accessory.protocol.Device.DeviceInformation.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceInformationOrBuilder>(
                  getDeviceInformation(),
                  getParentForChildren(),
                  isClean());
          deviceInformation_ = null;
        }
        return deviceInformationBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:NotifyDeviceInformation)
    }

    // @@protoc_insertion_point(class_scope:NotifyDeviceInformation)
    private static final com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation();
    }

    public static com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<NotifyDeviceInformation>
        PARSER = new com.google.protobuf.AbstractParser<NotifyDeviceInformation>() {
      @java.lang.Override
      public NotifyDeviceInformation parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<NotifyDeviceInformation> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<NotifyDeviceInformation> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FeaturePropertiesOrBuilder extends
      // @@protoc_insertion_point(interface_extends:FeatureProperties)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 feature_id = 1;</code>
     * @return The featureId.
     */
    int getFeatureId();

    /**
     * <code>uint32 envelope_version = 2;</code>
     * @return The envelopeVersion.
     */
    int getEnvelopeVersion();
  }
  /**
   * Protobuf type {@code FeatureProperties}
   */
  public static final class FeatureProperties extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:FeatureProperties)
      FeaturePropertiesOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FeatureProperties.newBuilder() to construct.
    private FeatureProperties(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FeatureProperties() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FeatureProperties();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_FeatureProperties_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_FeatureProperties_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Device.FeatureProperties.class, com.amazon.alexa.accessory.protocol.Device.FeatureProperties.Builder.class);
    }

    public static final int FEATURE_ID_FIELD_NUMBER = 1;
    private int featureId_;
    /**
     * <code>uint32 feature_id = 1;</code>
     * @return The featureId.
     */
    @java.lang.Override
    public int getFeatureId() {
      return featureId_;
    }

    public static final int ENVELOPE_VERSION_FIELD_NUMBER = 2;
    private int envelopeVersion_;
    /**
     * <code>uint32 envelope_version = 2;</code>
     * @return The envelopeVersion.
     */
    @java.lang.Override
    public int getEnvelopeVersion() {
      return envelopeVersion_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (featureId_ != 0) {
        output.writeUInt32(1, featureId_);
      }
      if (envelopeVersion_ != 0) {
        output.writeUInt32(2, envelopeVersion_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (featureId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, featureId_);
      }
      if (envelopeVersion_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, envelopeVersion_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Device.FeatureProperties)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Device.FeatureProperties other = (com.amazon.alexa.accessory.protocol.Device.FeatureProperties) obj;

      if (getFeatureId()
          != other.getFeatureId()) return false;
      if (getEnvelopeVersion()
          != other.getEnvelopeVersion()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + FEATURE_ID_FIELD_NUMBER;
      hash = (53 * hash) + getFeatureId();
      hash = (37 * hash) + ENVELOPE_VERSION_FIELD_NUMBER;
      hash = (53 * hash) + getEnvelopeVersion();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Device.FeatureProperties parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.FeatureProperties parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.FeatureProperties parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.FeatureProperties parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.FeatureProperties parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.FeatureProperties parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.FeatureProperties parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.FeatureProperties parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.FeatureProperties parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.FeatureProperties parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.FeatureProperties parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.FeatureProperties parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Device.FeatureProperties prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code FeatureProperties}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:FeatureProperties)
        com.amazon.alexa.accessory.protocol.Device.FeaturePropertiesOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_FeatureProperties_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_FeatureProperties_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Device.FeatureProperties.class, com.amazon.alexa.accessory.protocol.Device.FeatureProperties.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Device.FeatureProperties.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        featureId_ = 0;

        envelopeVersion_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_FeatureProperties_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.FeatureProperties getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Device.FeatureProperties.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.FeatureProperties build() {
        com.amazon.alexa.accessory.protocol.Device.FeatureProperties result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.FeatureProperties buildPartial() {
        com.amazon.alexa.accessory.protocol.Device.FeatureProperties result = new com.amazon.alexa.accessory.protocol.Device.FeatureProperties(this);
        result.featureId_ = featureId_;
        result.envelopeVersion_ = envelopeVersion_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Device.FeatureProperties) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Device.FeatureProperties)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Device.FeatureProperties other) {
        if (other == com.amazon.alexa.accessory.protocol.Device.FeatureProperties.getDefaultInstance()) return this;
        if (other.getFeatureId() != 0) {
          setFeatureId(other.getFeatureId());
        }
        if (other.getEnvelopeVersion() != 0) {
          setEnvelopeVersion(other.getEnvelopeVersion());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                featureId_ = input.readUInt32();

                break;
              } // case 8
              case 16: {
                envelopeVersion_ = input.readUInt32();

                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private int featureId_ ;
      /**
       * <code>uint32 feature_id = 1;</code>
       * @return The featureId.
       */
      @java.lang.Override
      public int getFeatureId() {
        return featureId_;
      }
      /**
       * <code>uint32 feature_id = 1;</code>
       * @param value The featureId to set.
       * @return This builder for chaining.
       */
      public Builder setFeatureId(int value) {
        
        featureId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 feature_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearFeatureId() {
        
        featureId_ = 0;
        onChanged();
        return this;
      }

      private int envelopeVersion_ ;
      /**
       * <code>uint32 envelope_version = 2;</code>
       * @return The envelopeVersion.
       */
      @java.lang.Override
      public int getEnvelopeVersion() {
        return envelopeVersion_;
      }
      /**
       * <code>uint32 envelope_version = 2;</code>
       * @param value The envelopeVersion to set.
       * @return This builder for chaining.
       */
      public Builder setEnvelopeVersion(int value) {
        
        envelopeVersion_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 envelope_version = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearEnvelopeVersion() {
        
        envelopeVersion_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:FeatureProperties)
    }

    // @@protoc_insertion_point(class_scope:FeatureProperties)
    private static final com.amazon.alexa.accessory.protocol.Device.FeatureProperties DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Device.FeatureProperties();
    }

    public static com.amazon.alexa.accessory.protocol.Device.FeatureProperties getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FeatureProperties>
        PARSER = new com.google.protobuf.AbstractParser<FeatureProperties>() {
      @java.lang.Override
      public FeatureProperties parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<FeatureProperties> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FeatureProperties> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.FeatureProperties getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DeviceFeaturesOrBuilder extends
      // @@protoc_insertion_point(interface_extends:DeviceFeatures)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 features = 1;</code>
     * @return The features.
     */
    int getFeatures();

    /**
     * <code>uint32 device_attributes = 2;</code>
     * @return The deviceAttributes.
     */
    int getDeviceAttributes();

    /**
     * <code>repeated .FeatureProperties feature_properties = 3;</code>
     */
    java.util.List<com.amazon.alexa.accessory.protocol.Device.FeatureProperties> 
        getFeaturePropertiesList();
    /**
     * <code>repeated .FeatureProperties feature_properties = 3;</code>
     */
    com.amazon.alexa.accessory.protocol.Device.FeatureProperties getFeatureProperties(int index);
    /**
     * <code>repeated .FeatureProperties feature_properties = 3;</code>
     */
    int getFeaturePropertiesCount();
    /**
     * <code>repeated .FeatureProperties feature_properties = 3;</code>
     */
    java.util.List<? extends com.amazon.alexa.accessory.protocol.Device.FeaturePropertiesOrBuilder> 
        getFeaturePropertiesOrBuilderList();
    /**
     * <code>repeated .FeatureProperties feature_properties = 3;</code>
     */
    com.amazon.alexa.accessory.protocol.Device.FeaturePropertiesOrBuilder getFeaturePropertiesOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code DeviceFeatures}
   */
  public static final class DeviceFeatures extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:DeviceFeatures)
      DeviceFeaturesOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DeviceFeatures.newBuilder() to construct.
    private DeviceFeatures(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DeviceFeatures() {
      featureProperties_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DeviceFeatures();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceFeatures_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceFeatures_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Device.DeviceFeatures.class, com.amazon.alexa.accessory.protocol.Device.DeviceFeatures.Builder.class);
    }

    public static final int FEATURES_FIELD_NUMBER = 1;
    private int features_;
    /**
     * <code>uint32 features = 1;</code>
     * @return The features.
     */
    @java.lang.Override
    public int getFeatures() {
      return features_;
    }

    public static final int DEVICE_ATTRIBUTES_FIELD_NUMBER = 2;
    private int deviceAttributes_;
    /**
     * <code>uint32 device_attributes = 2;</code>
     * @return The deviceAttributes.
     */
    @java.lang.Override
    public int getDeviceAttributes() {
      return deviceAttributes_;
    }

    public static final int FEATURE_PROPERTIES_FIELD_NUMBER = 3;
    private java.util.List<com.amazon.alexa.accessory.protocol.Device.FeatureProperties> featureProperties_;
    /**
     * <code>repeated .FeatureProperties feature_properties = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.amazon.alexa.accessory.protocol.Device.FeatureProperties> getFeaturePropertiesList() {
      return featureProperties_;
    }
    /**
     * <code>repeated .FeatureProperties feature_properties = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.amazon.alexa.accessory.protocol.Device.FeaturePropertiesOrBuilder> 
        getFeaturePropertiesOrBuilderList() {
      return featureProperties_;
    }
    /**
     * <code>repeated .FeatureProperties feature_properties = 3;</code>
     */
    @java.lang.Override
    public int getFeaturePropertiesCount() {
      return featureProperties_.size();
    }
    /**
     * <code>repeated .FeatureProperties feature_properties = 3;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.FeatureProperties getFeatureProperties(int index) {
      return featureProperties_.get(index);
    }
    /**
     * <code>repeated .FeatureProperties feature_properties = 3;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.FeaturePropertiesOrBuilder getFeaturePropertiesOrBuilder(
        int index) {
      return featureProperties_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (features_ != 0) {
        output.writeUInt32(1, features_);
      }
      if (deviceAttributes_ != 0) {
        output.writeUInt32(2, deviceAttributes_);
      }
      for (int i = 0; i < featureProperties_.size(); i++) {
        output.writeMessage(3, featureProperties_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (features_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, features_);
      }
      if (deviceAttributes_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, deviceAttributes_);
      }
      for (int i = 0; i < featureProperties_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, featureProperties_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Device.DeviceFeatures)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Device.DeviceFeatures other = (com.amazon.alexa.accessory.protocol.Device.DeviceFeatures) obj;

      if (getFeatures()
          != other.getFeatures()) return false;
      if (getDeviceAttributes()
          != other.getDeviceAttributes()) return false;
      if (!getFeaturePropertiesList()
          .equals(other.getFeaturePropertiesList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + FEATURES_FIELD_NUMBER;
      hash = (53 * hash) + getFeatures();
      hash = (37 * hash) + DEVICE_ATTRIBUTES_FIELD_NUMBER;
      hash = (53 * hash) + getDeviceAttributes();
      if (getFeaturePropertiesCount() > 0) {
        hash = (37 * hash) + FEATURE_PROPERTIES_FIELD_NUMBER;
        hash = (53 * hash) + getFeaturePropertiesList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Device.DeviceFeatures parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceFeatures parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceFeatures parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceFeatures parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceFeatures parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceFeatures parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceFeatures parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceFeatures parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceFeatures parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceFeatures parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceFeatures parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.DeviceFeatures parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Device.DeviceFeatures prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code DeviceFeatures}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:DeviceFeatures)
        com.amazon.alexa.accessory.protocol.Device.DeviceFeaturesOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceFeatures_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceFeatures_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Device.DeviceFeatures.class, com.amazon.alexa.accessory.protocol.Device.DeviceFeatures.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Device.DeviceFeatures.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        features_ = 0;

        deviceAttributes_ = 0;

        if (featurePropertiesBuilder_ == null) {
          featureProperties_ = java.util.Collections.emptyList();
        } else {
          featureProperties_ = null;
          featurePropertiesBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_DeviceFeatures_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceFeatures getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Device.DeviceFeatures.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceFeatures build() {
        com.amazon.alexa.accessory.protocol.Device.DeviceFeatures result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceFeatures buildPartial() {
        com.amazon.alexa.accessory.protocol.Device.DeviceFeatures result = new com.amazon.alexa.accessory.protocol.Device.DeviceFeatures(this);
        int from_bitField0_ = bitField0_;
        result.features_ = features_;
        result.deviceAttributes_ = deviceAttributes_;
        if (featurePropertiesBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            featureProperties_ = java.util.Collections.unmodifiableList(featureProperties_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.featureProperties_ = featureProperties_;
        } else {
          result.featureProperties_ = featurePropertiesBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Device.DeviceFeatures) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Device.DeviceFeatures)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Device.DeviceFeatures other) {
        if (other == com.amazon.alexa.accessory.protocol.Device.DeviceFeatures.getDefaultInstance()) return this;
        if (other.getFeatures() != 0) {
          setFeatures(other.getFeatures());
        }
        if (other.getDeviceAttributes() != 0) {
          setDeviceAttributes(other.getDeviceAttributes());
        }
        if (featurePropertiesBuilder_ == null) {
          if (!other.featureProperties_.isEmpty()) {
            if (featureProperties_.isEmpty()) {
              featureProperties_ = other.featureProperties_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureFeaturePropertiesIsMutable();
              featureProperties_.addAll(other.featureProperties_);
            }
            onChanged();
          }
        } else {
          if (!other.featureProperties_.isEmpty()) {
            if (featurePropertiesBuilder_.isEmpty()) {
              featurePropertiesBuilder_.dispose();
              featurePropertiesBuilder_ = null;
              featureProperties_ = other.featureProperties_;
              bitField0_ = (bitField0_ & ~0x00000001);
              featurePropertiesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getFeaturePropertiesFieldBuilder() : null;
            } else {
              featurePropertiesBuilder_.addAllMessages(other.featureProperties_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                features_ = input.readUInt32();

                break;
              } // case 8
              case 16: {
                deviceAttributes_ = input.readUInt32();

                break;
              } // case 16
              case 26: {
                com.amazon.alexa.accessory.protocol.Device.FeatureProperties m =
                    input.readMessage(
                        com.amazon.alexa.accessory.protocol.Device.FeatureProperties.parser(),
                        extensionRegistry);
                if (featurePropertiesBuilder_ == null) {
                  ensureFeaturePropertiesIsMutable();
                  featureProperties_.add(m);
                } else {
                  featurePropertiesBuilder_.addMessage(m);
                }
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int features_ ;
      /**
       * <code>uint32 features = 1;</code>
       * @return The features.
       */
      @java.lang.Override
      public int getFeatures() {
        return features_;
      }
      /**
       * <code>uint32 features = 1;</code>
       * @param value The features to set.
       * @return This builder for chaining.
       */
      public Builder setFeatures(int value) {
        
        features_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 features = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearFeatures() {
        
        features_ = 0;
        onChanged();
        return this;
      }

      private int deviceAttributes_ ;
      /**
       * <code>uint32 device_attributes = 2;</code>
       * @return The deviceAttributes.
       */
      @java.lang.Override
      public int getDeviceAttributes() {
        return deviceAttributes_;
      }
      /**
       * <code>uint32 device_attributes = 2;</code>
       * @param value The deviceAttributes to set.
       * @return This builder for chaining.
       */
      public Builder setDeviceAttributes(int value) {
        
        deviceAttributes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 device_attributes = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeviceAttributes() {
        
        deviceAttributes_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.amazon.alexa.accessory.protocol.Device.FeatureProperties> featureProperties_ =
        java.util.Collections.emptyList();
      private void ensureFeaturePropertiesIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          featureProperties_ = new java.util.ArrayList<com.amazon.alexa.accessory.protocol.Device.FeatureProperties>(featureProperties_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.FeatureProperties, com.amazon.alexa.accessory.protocol.Device.FeatureProperties.Builder, com.amazon.alexa.accessory.protocol.Device.FeaturePropertiesOrBuilder> featurePropertiesBuilder_;

      /**
       * <code>repeated .FeatureProperties feature_properties = 3;</code>
       */
      public java.util.List<com.amazon.alexa.accessory.protocol.Device.FeatureProperties> getFeaturePropertiesList() {
        if (featurePropertiesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(featureProperties_);
        } else {
          return featurePropertiesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .FeatureProperties feature_properties = 3;</code>
       */
      public int getFeaturePropertiesCount() {
        if (featurePropertiesBuilder_ == null) {
          return featureProperties_.size();
        } else {
          return featurePropertiesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .FeatureProperties feature_properties = 3;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.FeatureProperties getFeatureProperties(int index) {
        if (featurePropertiesBuilder_ == null) {
          return featureProperties_.get(index);
        } else {
          return featurePropertiesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .FeatureProperties feature_properties = 3;</code>
       */
      public Builder setFeatureProperties(
          int index, com.amazon.alexa.accessory.protocol.Device.FeatureProperties value) {
        if (featurePropertiesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFeaturePropertiesIsMutable();
          featureProperties_.set(index, value);
          onChanged();
        } else {
          featurePropertiesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .FeatureProperties feature_properties = 3;</code>
       */
      public Builder setFeatureProperties(
          int index, com.amazon.alexa.accessory.protocol.Device.FeatureProperties.Builder builderForValue) {
        if (featurePropertiesBuilder_ == null) {
          ensureFeaturePropertiesIsMutable();
          featureProperties_.set(index, builderForValue.build());
          onChanged();
        } else {
          featurePropertiesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .FeatureProperties feature_properties = 3;</code>
       */
      public Builder addFeatureProperties(com.amazon.alexa.accessory.protocol.Device.FeatureProperties value) {
        if (featurePropertiesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFeaturePropertiesIsMutable();
          featureProperties_.add(value);
          onChanged();
        } else {
          featurePropertiesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .FeatureProperties feature_properties = 3;</code>
       */
      public Builder addFeatureProperties(
          int index, com.amazon.alexa.accessory.protocol.Device.FeatureProperties value) {
        if (featurePropertiesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFeaturePropertiesIsMutable();
          featureProperties_.add(index, value);
          onChanged();
        } else {
          featurePropertiesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .FeatureProperties feature_properties = 3;</code>
       */
      public Builder addFeatureProperties(
          com.amazon.alexa.accessory.protocol.Device.FeatureProperties.Builder builderForValue) {
        if (featurePropertiesBuilder_ == null) {
          ensureFeaturePropertiesIsMutable();
          featureProperties_.add(builderForValue.build());
          onChanged();
        } else {
          featurePropertiesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .FeatureProperties feature_properties = 3;</code>
       */
      public Builder addFeatureProperties(
          int index, com.amazon.alexa.accessory.protocol.Device.FeatureProperties.Builder builderForValue) {
        if (featurePropertiesBuilder_ == null) {
          ensureFeaturePropertiesIsMutable();
          featureProperties_.add(index, builderForValue.build());
          onChanged();
        } else {
          featurePropertiesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .FeatureProperties feature_properties = 3;</code>
       */
      public Builder addAllFeatureProperties(
          java.lang.Iterable<? extends com.amazon.alexa.accessory.protocol.Device.FeatureProperties> values) {
        if (featurePropertiesBuilder_ == null) {
          ensureFeaturePropertiesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, featureProperties_);
          onChanged();
        } else {
          featurePropertiesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .FeatureProperties feature_properties = 3;</code>
       */
      public Builder clearFeatureProperties() {
        if (featurePropertiesBuilder_ == null) {
          featureProperties_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          featurePropertiesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .FeatureProperties feature_properties = 3;</code>
       */
      public Builder removeFeatureProperties(int index) {
        if (featurePropertiesBuilder_ == null) {
          ensureFeaturePropertiesIsMutable();
          featureProperties_.remove(index);
          onChanged();
        } else {
          featurePropertiesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .FeatureProperties feature_properties = 3;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.FeatureProperties.Builder getFeaturePropertiesBuilder(
          int index) {
        return getFeaturePropertiesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .FeatureProperties feature_properties = 3;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.FeaturePropertiesOrBuilder getFeaturePropertiesOrBuilder(
          int index) {
        if (featurePropertiesBuilder_ == null) {
          return featureProperties_.get(index);  } else {
          return featurePropertiesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .FeatureProperties feature_properties = 3;</code>
       */
      public java.util.List<? extends com.amazon.alexa.accessory.protocol.Device.FeaturePropertiesOrBuilder> 
           getFeaturePropertiesOrBuilderList() {
        if (featurePropertiesBuilder_ != null) {
          return featurePropertiesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(featureProperties_);
        }
      }
      /**
       * <code>repeated .FeatureProperties feature_properties = 3;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.FeatureProperties.Builder addFeaturePropertiesBuilder() {
        return getFeaturePropertiesFieldBuilder().addBuilder(
            com.amazon.alexa.accessory.protocol.Device.FeatureProperties.getDefaultInstance());
      }
      /**
       * <code>repeated .FeatureProperties feature_properties = 3;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.FeatureProperties.Builder addFeaturePropertiesBuilder(
          int index) {
        return getFeaturePropertiesFieldBuilder().addBuilder(
            index, com.amazon.alexa.accessory.protocol.Device.FeatureProperties.getDefaultInstance());
      }
      /**
       * <code>repeated .FeatureProperties feature_properties = 3;</code>
       */
      public java.util.List<com.amazon.alexa.accessory.protocol.Device.FeatureProperties.Builder> 
           getFeaturePropertiesBuilderList() {
        return getFeaturePropertiesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.FeatureProperties, com.amazon.alexa.accessory.protocol.Device.FeatureProperties.Builder, com.amazon.alexa.accessory.protocol.Device.FeaturePropertiesOrBuilder> 
          getFeaturePropertiesFieldBuilder() {
        if (featurePropertiesBuilder_ == null) {
          featurePropertiesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Device.FeatureProperties, com.amazon.alexa.accessory.protocol.Device.FeatureProperties.Builder, com.amazon.alexa.accessory.protocol.Device.FeaturePropertiesOrBuilder>(
                  featureProperties_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          featureProperties_ = null;
        }
        return featurePropertiesBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:DeviceFeatures)
    }

    // @@protoc_insertion_point(class_scope:DeviceFeatures)
    private static final com.amazon.alexa.accessory.protocol.Device.DeviceFeatures DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Device.DeviceFeatures();
    }

    public static com.amazon.alexa.accessory.protocol.Device.DeviceFeatures getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DeviceFeatures>
        PARSER = new com.google.protobuf.AbstractParser<DeviceFeatures>() {
      @java.lang.Override
      public DeviceFeatures parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<DeviceFeatures> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DeviceFeatures> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.DeviceFeatures getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetDeviceFeaturesOrBuilder extends
      // @@protoc_insertion_point(interface_extends:GetDeviceFeatures)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code GetDeviceFeatures}
   */
  public static final class GetDeviceFeatures extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:GetDeviceFeatures)
      GetDeviceFeaturesOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetDeviceFeatures.newBuilder() to construct.
    private GetDeviceFeatures(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetDeviceFeatures() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetDeviceFeatures();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_GetDeviceFeatures_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Device.internal_static_GetDeviceFeatures_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures.class, com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures other = (com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code GetDeviceFeatures}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:GetDeviceFeatures)
        com.amazon.alexa.accessory.protocol.Device.GetDeviceFeaturesOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_GetDeviceFeatures_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_GetDeviceFeatures_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures.class, com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Device.internal_static_GetDeviceFeatures_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures build() {
        com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures buildPartial() {
        com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures result = new com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures other) {
        if (other == com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:GetDeviceFeatures)
    }

    // @@protoc_insertion_point(class_scope:GetDeviceFeatures)
    private static final com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures();
    }

    public static com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GetDeviceFeatures>
        PARSER = new com.google.protobuf.AbstractParser<GetDeviceFeatures>() {
      @java.lang.Override
      public GetDeviceFeatures parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<GetDeviceFeatures> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetDeviceFeatures> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_DeviceBattery_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_DeviceBattery_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_DeviceStatus_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_DeviceStatus_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_DeviceInformation_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_DeviceInformation_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_DeviceInformation_MetadataEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_DeviceInformation_MetadataEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_GetDeviceInformation_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_GetDeviceInformation_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_DeviceConfiguration_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_DeviceConfiguration_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_GetDeviceConfiguration_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_GetDeviceConfiguration_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_OverrideAssistant_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_OverrideAssistant_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_StartSetup_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_StartSetup_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_CompleteSetup_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_CompleteSetup_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_NotifyDeviceConfiguration_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_NotifyDeviceConfiguration_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_UpdateDeviceInformation_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_UpdateDeviceInformation_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_NotifyDeviceInformation_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_NotifyDeviceInformation_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_FeatureProperties_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_FeatureProperties_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_DeviceFeatures_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_DeviceFeatures_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_GetDeviceFeatures_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_GetDeviceFeatures_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014device.proto\032\014common.proto\032\014speech.pro" +
      "to\"\224\001\n\rDeviceBattery\022\r\n\005level\030\001 \001(\r\022\r\n\005s" +
      "cale\030\002 \001(\r\022%\n\006status\030\003 \001(\0162\025.DeviceBatte" +
      "ry.Status\">\n\006Status\022\013\n\007UNKNOWN\020\000\022\014\n\010CHAR" +
      "GING\020\001\022\017\n\013DISCHARGING\020\002\022\010\n\004FULL\020\003\"s\n\014Dev" +
      "iceStatus\022\037\n\004link\030\001 \001(\0162\021.ConnectionStat" +
      "us\022\037\n\004nfmi\030\002 \001(\0162\021.ConnectionStatus\022!\n\010p" +
      "resence\030\003 \001(\0162\017.DevicePresence\"\274\003\n\021Devic" +
      "eInformation\022\025\n\rserial_number\030\001 \001(\t\022\014\n\004n" +
      "ame\030\002 \001(\t\022(\n\024supported_transports\030\003 \003(\0162" +
      "\n.Transport\022\023\n\013device_type\030\004 \001(\t\022\021\n\tdevi" +
      "ce_id\030\005 \001(\r\022\037\n\007battery\030\006 \001(\0132\016.DeviceBat" +
      "tery\022\035\n\006status\030\007 \001(\0132\r.DeviceStatus\022\025\n\rp" +
      "roduct_color\030\010 \001(\r\022\032\n\022associated_devices" +
      "\030\t \003(\r\022;\n\034supported_speech_initiations\030\n" +
      " \003(\0162\025.SpeechInitiationType\022\033\n\023supported" +
      "_wakewords\030\013 \003(\t\0222\n\010metadata\030\014 \003(\0132 .Dev" +
      "iceInformation.MetadataEntry\032/\n\rMetadata" +
      "Entry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\")\n" +
      "\024GetDeviceInformation\022\021\n\tdevice_id\030\001 \001(\r" +
      "\"L\n\023DeviceConfiguration\022 \n\030needs_assista" +
      "nt_override\030\001 \001(\010\022\023\n\013needs_setup\030\002 \001(\010\"\030" +
      "\n\026GetDeviceConfiguration\"3\n\021OverrideAssi" +
      "stant\022\036\n\nerror_code\030\001 \001(\0162\n.ErrorCode\"\014\n" +
      "\nStartSetup\"/\n\rCompleteSetup\022\036\n\nerror_co" +
      "de\030\001 \001(\0162\n.ErrorCode\"O\n\031NotifyDeviceConf" +
      "iguration\0222\n\024device_configuration\030\001 \001(\0132" +
      "\024.DeviceConfiguration\":\n\027UpdateDeviceInf" +
      "ormation\022\014\n\004name\030\001 \001(\t\022\021\n\tdevice_id\030\002 \001(" +
      "\r\"I\n\027NotifyDeviceInformation\022.\n\022device_i" +
      "nformation\030\001 \001(\0132\022.DeviceInformation\"A\n\021" +
      "FeatureProperties\022\022\n\nfeature_id\030\001 \001(\r\022\030\n" +
      "\020envelope_version\030\002 \001(\r\"m\n\016DeviceFeature" +
      "s\022\020\n\010features\030\001 \001(\r\022\031\n\021device_attributes" +
      "\030\002 \001(\r\022.\n\022feature_properties\030\003 \003(\0132\022.Fea" +
      "tureProperties\"\023\n\021GetDeviceFeatures*v\n\020C" +
      "onnectionStatus\022\035\n\031CONNECTION_STATUS_UNK" +
      "NOWN\020\000\022\037\n\033CONNECTION_STATUS_CONNECTED\020\001\022" +
      "\"\n\036CONNECTION_STATUS_DISCONNECTED\020\002*\207\001\n\016" +
      "DevicePresence\022\033\n\027DEVICE_PRESENCE_UNKNOW" +
      "N\020\000\022\032\n\026DEVICE_PRESENCE_ACTIVE\020\001\022\034\n\030DEVIC" +
      "E_PRESENCE_INACTIVE\020\002\022\036\n\032DEVICE_PRESENCE" +
      "_ACCESSIBLE\020\003B0\n#com.amazon.alexa.access" +
      "ory.protocolH\003\240\001\001\242\002\003AACb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.amazon.alexa.accessory.protocol.Common.getDescriptor(),
          com.amazon.alexa.accessory.protocol.Speech.getDescriptor(),
        });
    internal_static_DeviceBattery_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_DeviceBattery_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_DeviceBattery_descriptor,
        new java.lang.String[] { "Level", "Scale", "Status", });
    internal_static_DeviceStatus_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_DeviceStatus_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_DeviceStatus_descriptor,
        new java.lang.String[] { "Link", "Nfmi", "Presence", });
    internal_static_DeviceInformation_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_DeviceInformation_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_DeviceInformation_descriptor,
        new java.lang.String[] { "SerialNumber", "Name", "SupportedTransports", "DeviceType", "DeviceId", "Battery", "Status", "ProductColor", "AssociatedDevices", "SupportedSpeechInitiations", "SupportedWakewords", "Metadata", });
    internal_static_DeviceInformation_MetadataEntry_descriptor =
      internal_static_DeviceInformation_descriptor.getNestedTypes().get(0);
    internal_static_DeviceInformation_MetadataEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_DeviceInformation_MetadataEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_GetDeviceInformation_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_GetDeviceInformation_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_GetDeviceInformation_descriptor,
        new java.lang.String[] { "DeviceId", });
    internal_static_DeviceConfiguration_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_DeviceConfiguration_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_DeviceConfiguration_descriptor,
        new java.lang.String[] { "NeedsAssistantOverride", "NeedsSetup", });
    internal_static_GetDeviceConfiguration_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_GetDeviceConfiguration_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_GetDeviceConfiguration_descriptor,
        new java.lang.String[] { });
    internal_static_OverrideAssistant_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_OverrideAssistant_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_OverrideAssistant_descriptor,
        new java.lang.String[] { "ErrorCode", });
    internal_static_StartSetup_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_StartSetup_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_StartSetup_descriptor,
        new java.lang.String[] { });
    internal_static_CompleteSetup_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_CompleteSetup_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_CompleteSetup_descriptor,
        new java.lang.String[] { "ErrorCode", });
    internal_static_NotifyDeviceConfiguration_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_NotifyDeviceConfiguration_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_NotifyDeviceConfiguration_descriptor,
        new java.lang.String[] { "DeviceConfiguration", });
    internal_static_UpdateDeviceInformation_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_UpdateDeviceInformation_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_UpdateDeviceInformation_descriptor,
        new java.lang.String[] { "Name", "DeviceId", });
    internal_static_NotifyDeviceInformation_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_NotifyDeviceInformation_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_NotifyDeviceInformation_descriptor,
        new java.lang.String[] { "DeviceInformation", });
    internal_static_FeatureProperties_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_FeatureProperties_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_FeatureProperties_descriptor,
        new java.lang.String[] { "FeatureId", "EnvelopeVersion", });
    internal_static_DeviceFeatures_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_DeviceFeatures_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_DeviceFeatures_descriptor,
        new java.lang.String[] { "Features", "DeviceAttributes", "FeatureProperties", });
    internal_static_GetDeviceFeatures_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_GetDeviceFeatures_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_GetDeviceFeatures_descriptor,
        new java.lang.String[] { });
    com.amazon.alexa.accessory.protocol.Common.getDescriptor();
    com.amazon.alexa.accessory.protocol.Speech.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
