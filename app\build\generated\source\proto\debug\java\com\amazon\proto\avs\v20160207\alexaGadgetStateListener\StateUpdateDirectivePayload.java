// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: alexaGadgetStateListenerStateUpdateDirectivePayload.proto

package com.amazon.proto.avs.v20160207.alexaGadgetStateListener;

public final class StateUpdateDirectivePayload {
  private StateUpdateDirectivePayload() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface StateUpdateDirectivePayloadProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:alexaGadgetStateListener.StateUpdateDirectivePayloadProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
     */
    java.util.List<com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States> 
        getStatesList();
    /**
     * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
     */
    com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States getStates(int index);
    /**
     * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
     */
    int getStatesCount();
    /**
     * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
     */
    java.util.List<? extends com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.StatesOrBuilder> 
        getStatesOrBuilderList();
    /**
     * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
     */
    com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.StatesOrBuilder getStatesOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code alexaGadgetStateListener.StateUpdateDirectivePayloadProto}
   */
  public static final class StateUpdateDirectivePayloadProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:alexaGadgetStateListener.StateUpdateDirectivePayloadProto)
      StateUpdateDirectivePayloadProtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use StateUpdateDirectivePayloadProto.newBuilder() to construct.
    private StateUpdateDirectivePayloadProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private StateUpdateDirectivePayloadProto() {
      states_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new StateUpdateDirectivePayloadProto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.class, com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.Builder.class);
    }

    public interface StatesOrBuilder extends
        // @@protoc_insertion_point(interface_extends:alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>string name = 1;</code>
       * @return The name.
       */
      java.lang.String getName();
      /**
       * <code>string name = 1;</code>
       * @return The bytes for name.
       */
      com.google.protobuf.ByteString
          getNameBytes();

      /**
       * <code>string value = 2;</code>
       * @return The value.
       */
      java.lang.String getValue();
      /**
       * <code>string value = 2;</code>
       * @return The bytes for value.
       */
      com.google.protobuf.ByteString
          getValueBytes();
    }
    /**
     * Protobuf type {@code alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States}
     */
    public static final class States extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States)
        StatesOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use States.newBuilder() to construct.
      private States(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private States() {
        name_ = "";
        value_ = "";
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new States();
      }

      @java.lang.Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_States_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_States_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States.class, com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States.Builder.class);
      }

      public static final int NAME_FIELD_NUMBER = 1;
      private volatile java.lang.Object name_;
      /**
       * <code>string name = 1;</code>
       * @return The name.
       */
      @java.lang.Override
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        }
      }
      /**
       * <code>string name = 1;</code>
       * @return The bytes for name.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int VALUE_FIELD_NUMBER = 2;
      private volatile java.lang.Object value_;
      /**
       * <code>string value = 2;</code>
       * @return The value.
       */
      @java.lang.Override
      public java.lang.String getValue() {
        java.lang.Object ref = value_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          value_ = s;
          return s;
        }
      }
      /**
       * <code>string value = 2;</code>
       * @return The bytes for value.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getValueBytes() {
        java.lang.Object ref = value_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          value_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 1, name_);
        }
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(value_)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 2, value_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, name_);
        }
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(value_)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, value_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States)) {
          return super.equals(obj);
        }
        com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States other = (com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States) obj;

        if (!getName()
            .equals(other.getName())) return false;
        if (!getValue()
            .equals(other.getValue())) return false;
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + NAME_FIELD_NUMBER;
        hash = (53 * hash) + getName().hashCode();
        hash = (37 * hash) + VALUE_FIELD_NUMBER;
        hash = (53 * hash) + getValue().hashCode();
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States)
          com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.StatesOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_States_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_States_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States.class, com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States.Builder.class);
        }

        // Construct using com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          name_ = "";

          value_ = "";

          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_States_descriptor;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States getDefaultInstanceForType() {
          return com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States.getDefaultInstance();
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States build() {
          com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States buildPartial() {
          com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States result = new com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States(this);
          result.name_ = name_;
          result.value_ = value_;
          onBuilt();
          return result;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States) {
            return mergeFrom((com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States other) {
          if (other == com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States.getDefaultInstance()) return this;
          if (!other.getName().isEmpty()) {
            name_ = other.name_;
            onChanged();
          }
          if (!other.getValue().isEmpty()) {
            value_ = other.value_;
            onChanged();
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 10: {
                  name_ = input.readStringRequireUtf8();

                  break;
                } // case 10
                case 18: {
                  value_ = input.readStringRequireUtf8();

                  break;
                } // case 18
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }

        private java.lang.Object name_ = "";
        /**
         * <code>string name = 1;</code>
         * @return The name.
         */
        public java.lang.String getName() {
          java.lang.Object ref = name_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            name_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>string name = 1;</code>
         * @return The bytes for name.
         */
        public com.google.protobuf.ByteString
            getNameBytes() {
          java.lang.Object ref = name_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            name_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string name = 1;</code>
         * @param value The name to set.
         * @return This builder for chaining.
         */
        public Builder setName(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  
          name_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>string name = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearName() {
          
          name_ = getDefaultInstance().getName();
          onChanged();
          return this;
        }
        /**
         * <code>string name = 1;</code>
         * @param value The bytes for name to set.
         * @return This builder for chaining.
         */
        public Builder setNameBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
          
          name_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object value_ = "";
        /**
         * <code>string value = 2;</code>
         * @return The value.
         */
        public java.lang.String getValue() {
          java.lang.Object ref = value_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            value_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>string value = 2;</code>
         * @return The bytes for value.
         */
        public com.google.protobuf.ByteString
            getValueBytes() {
          java.lang.Object ref = value_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            value_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string value = 2;</code>
         * @param value The value to set.
         * @return This builder for chaining.
         */
        public Builder setValue(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  
          value_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>string value = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearValue() {
          
          value_ = getDefaultInstance().getValue();
          onChanged();
          return this;
        }
        /**
         * <code>string value = 2;</code>
         * @param value The bytes for value to set.
         * @return This builder for chaining.
         */
        public Builder setValueBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
          
          value_ = value;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States)
      }

      // @@protoc_insertion_point(class_scope:alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States)
      private static final com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States();
      }

      public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<States>
          PARSER = new com.google.protobuf.AbstractParser<States>() {
        @java.lang.Override
        public States parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<States> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<States> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public static final int STATES_FIELD_NUMBER = 1;
    private java.util.List<com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States> states_;
    /**
     * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States> getStatesList() {
      return states_;
    }
    /**
     * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.StatesOrBuilder> 
        getStatesOrBuilderList() {
      return states_;
    }
    /**
     * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
     */
    @java.lang.Override
    public int getStatesCount() {
      return states_.size();
    }
    /**
     * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States getStates(int index) {
      return states_.get(index);
    }
    /**
     * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.StatesOrBuilder getStatesOrBuilder(
        int index) {
      return states_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < states_.size(); i++) {
        output.writeMessage(1, states_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < states_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, states_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto)) {
        return super.equals(obj);
      }
      com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto other = (com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto) obj;

      if (!getStatesList()
          .equals(other.getStatesList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getStatesCount() > 0) {
        hash = (37 * hash) + STATES_FIELD_NUMBER;
        hash = (53 * hash) + getStatesList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code alexaGadgetStateListener.StateUpdateDirectivePayloadProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:alexaGadgetStateListener.StateUpdateDirectivePayloadProto)
        com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.class, com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.Builder.class);
      }

      // Construct using com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (statesBuilder_ == null) {
          states_ = java.util.Collections.emptyList();
        } else {
          states_ = null;
          statesBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_descriptor;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto getDefaultInstanceForType() {
        return com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto build() {
        com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto buildPartial() {
        com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto result = new com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto(this);
        int from_bitField0_ = bitField0_;
        if (statesBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            states_ = java.util.Collections.unmodifiableList(states_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.states_ = states_;
        } else {
          result.states_ = statesBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto) {
          return mergeFrom((com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto other) {
        if (other == com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.getDefaultInstance()) return this;
        if (statesBuilder_ == null) {
          if (!other.states_.isEmpty()) {
            if (states_.isEmpty()) {
              states_ = other.states_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureStatesIsMutable();
              states_.addAll(other.states_);
            }
            onChanged();
          }
        } else {
          if (!other.states_.isEmpty()) {
            if (statesBuilder_.isEmpty()) {
              statesBuilder_.dispose();
              statesBuilder_ = null;
              states_ = other.states_;
              bitField0_ = (bitField0_ & ~0x00000001);
              statesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getStatesFieldBuilder() : null;
            } else {
              statesBuilder_.addAllMessages(other.states_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States m =
                    input.readMessage(
                        com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States.parser(),
                        extensionRegistry);
                if (statesBuilder_ == null) {
                  ensureStatesIsMutable();
                  states_.add(m);
                } else {
                  statesBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States> states_ =
        java.util.Collections.emptyList();
      private void ensureStatesIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          states_ = new java.util.ArrayList<com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States>(states_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States, com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States.Builder, com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.StatesOrBuilder> statesBuilder_;

      /**
       * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
       */
      public java.util.List<com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States> getStatesList() {
        if (statesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(states_);
        } else {
          return statesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
       */
      public int getStatesCount() {
        if (statesBuilder_ == null) {
          return states_.size();
        } else {
          return statesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States getStates(int index) {
        if (statesBuilder_ == null) {
          return states_.get(index);
        } else {
          return statesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
       */
      public Builder setStates(
          int index, com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States value) {
        if (statesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureStatesIsMutable();
          states_.set(index, value);
          onChanged();
        } else {
          statesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
       */
      public Builder setStates(
          int index, com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States.Builder builderForValue) {
        if (statesBuilder_ == null) {
          ensureStatesIsMutable();
          states_.set(index, builderForValue.build());
          onChanged();
        } else {
          statesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
       */
      public Builder addStates(com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States value) {
        if (statesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureStatesIsMutable();
          states_.add(value);
          onChanged();
        } else {
          statesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
       */
      public Builder addStates(
          int index, com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States value) {
        if (statesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureStatesIsMutable();
          states_.add(index, value);
          onChanged();
        } else {
          statesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
       */
      public Builder addStates(
          com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States.Builder builderForValue) {
        if (statesBuilder_ == null) {
          ensureStatesIsMutable();
          states_.add(builderForValue.build());
          onChanged();
        } else {
          statesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
       */
      public Builder addStates(
          int index, com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States.Builder builderForValue) {
        if (statesBuilder_ == null) {
          ensureStatesIsMutable();
          states_.add(index, builderForValue.build());
          onChanged();
        } else {
          statesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
       */
      public Builder addAllStates(
          java.lang.Iterable<? extends com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States> values) {
        if (statesBuilder_ == null) {
          ensureStatesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, states_);
          onChanged();
        } else {
          statesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
       */
      public Builder clearStates() {
        if (statesBuilder_ == null) {
          states_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          statesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
       */
      public Builder removeStates(int index) {
        if (statesBuilder_ == null) {
          ensureStatesIsMutable();
          states_.remove(index);
          onChanged();
        } else {
          statesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States.Builder getStatesBuilder(
          int index) {
        return getStatesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.StatesOrBuilder getStatesOrBuilder(
          int index) {
        if (statesBuilder_ == null) {
          return states_.get(index);  } else {
          return statesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
       */
      public java.util.List<? extends com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.StatesOrBuilder> 
           getStatesOrBuilderList() {
        if (statesBuilder_ != null) {
          return statesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(states_);
        }
      }
      /**
       * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States.Builder addStatesBuilder() {
        return getStatesFieldBuilder().addBuilder(
            com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States.getDefaultInstance());
      }
      /**
       * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States.Builder addStatesBuilder(
          int index) {
        return getStatesFieldBuilder().addBuilder(
            index, com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States.getDefaultInstance());
      }
      /**
       * <code>repeated .alexaGadgetStateListener.StateUpdateDirectivePayloadProto.States states = 1;</code>
       */
      public java.util.List<com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States.Builder> 
           getStatesBuilderList() {
        return getStatesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States, com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States.Builder, com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.StatesOrBuilder> 
          getStatesFieldBuilder() {
        if (statesBuilder_ == null) {
          statesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States, com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.States.Builder, com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto.StatesOrBuilder>(
                  states_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          states_ = null;
        }
        return statesBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:alexaGadgetStateListener.StateUpdateDirectivePayloadProto)
    }

    // @@protoc_insertion_point(class_scope:alexaGadgetStateListener.StateUpdateDirectivePayloadProto)
    private static final com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto();
    }

    public static com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<StateUpdateDirectivePayloadProto>
        PARSER = new com.google.protobuf.AbstractParser<StateUpdateDirectivePayloadProto>() {
      @java.lang.Override
      public StateUpdateDirectivePayloadProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<StateUpdateDirectivePayloadProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<StateUpdateDirectivePayloadProto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alexaGadgetStateListener.StateUpdateDirectivePayload.StateUpdateDirectivePayloadProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_States_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_States_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n9alexaGadgetStateListenerStateUpdateDir" +
      "ectivePayload.proto\022\030alexaGadgetStateLis" +
      "tener\"\234\001\n StateUpdateDirectivePayloadPro" +
      "to\022Q\n\006states\030\001 \003(\0132A.alexaGadgetStateLis" +
      "tener.StateUpdateDirectivePayloadProto.S" +
      "tates\032%\n\006States\022\014\n\004name\030\001 \001(\t\022\r\n\005value\030\002" +
      " \001(\tBV\n7com.amazon.proto.avs.v20160207.a" +
      "lexaGadgetStateListenerB\033StateUpdateDire" +
      "ctivePayloadb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_descriptor,
        new java.lang.String[] { "States", });
    internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_States_descriptor =
      internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_descriptor.getNestedTypes().get(0);
    internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_States_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_alexaGadgetStateListener_StateUpdateDirectivePayloadProto_States_descriptor,
        new java.lang.String[] { "Name", "Value", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
