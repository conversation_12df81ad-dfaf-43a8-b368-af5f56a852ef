// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialShowBgviewBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final TextView dateTextviewBottom;

  @NonNull
  public final LinearLayout functionItemBottom;

  @NonNull
  public final LinearLayout functionItemIcon;

  @NonNull
  public final ImageView imageItem;

  @NonNull
  public final TextView timeTextviewBottom;

  private DialShowBgviewBinding(@NonNull FrameLayout rootView, @NonNull TextView dateTextviewBottom,
      @NonNull LinearLayout functionItemBottom, @NonNull LinearLayout functionItemIcon,
      @NonNull ImageView imageItem, @NonNull TextView timeTextviewBottom) {
    this.rootView = rootView;
    this.dateTextviewBottom = dateTextviewBottom;
    this.functionItemBottom = functionItemBottom;
    this.functionItemIcon = functionItemIcon;
    this.imageItem = imageItem;
    this.timeTextviewBottom = timeTextviewBottom;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialShowBgviewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialShowBgviewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dial_show_bgview, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialShowBgviewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.date_textview_bottom;
      TextView dateTextviewBottom = rootView.findViewById(id);
      if (dateTextviewBottom == null) {
        break missingId;
      }

      id = R.id.function_item_bottom;
      LinearLayout functionItemBottom = rootView.findViewById(id);
      if (functionItemBottom == null) {
        break missingId;
      }

      id = R.id.function_item_icon;
      LinearLayout functionItemIcon = rootView.findViewById(id);
      if (functionItemIcon == null) {
        break missingId;
      }

      id = R.id.image_item;
      ImageView imageItem = rootView.findViewById(id);
      if (imageItem == null) {
        break missingId;
      }

      id = R.id.time_textview_bottom;
      TextView timeTextviewBottom = rootView.findViewById(id);
      if (timeTextviewBottom == null) {
        break missingId;
      }

      return new DialShowBgviewBinding((FrameLayout) rootView, dateTextviewBottom,
          functionItemBottom, functionItemIcon, imageItem, timeTextviewBottom);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
