// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ListView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAvslwaLanguageSelectBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ListView languageListview;

  @NonNull
  public final Button languageSelectCancel;

  @NonNull
  public final Button languageSelectSave;

  private ActivityAvslwaLanguageSelectBinding(@NonNull LinearLayout rootView,
      @NonNull ListView languageListview, @NonNull Button languageSelectCancel,
      @NonNull Button languageSelectSave) {
    this.rootView = rootView;
    this.languageListview = languageListview;
    this.languageSelectCancel = languageSelectCancel;
    this.languageSelectSave = languageSelectSave;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAvslwaLanguageSelectBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAvslwaLanguageSelectBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_avslwa_language_select, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAvslwaLanguageSelectBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.language_listview;
      ListView languageListview = rootView.findViewById(id);
      if (languageListview == null) {
        break missingId;
      }

      id = R.id.language_select_cancel;
      Button languageSelectCancel = rootView.findViewById(id);
      if (languageSelectCancel == null) {
        break missingId;
      }

      id = R.id.language_select_save;
      Button languageSelectSave = rootView.findViewById(id);
      if (languageSelectSave == null) {
        break missingId;
      }

      return new ActivityAvslwaLanguageSelectBinding((LinearLayout) rootView, languageListview,
          languageSelectCancel, languageSelectSave);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
