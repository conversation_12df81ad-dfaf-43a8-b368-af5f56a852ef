// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityWatchavsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LogviewBinding loginfo;

  @NonNull
  public final ToolbarBinding tool;

  @NonNull
  public final Button watchAvsConnectDeviceBtn;

  @NonNull
  public final TextView watchAvsDeviceStateText;

  @NonNull
  public final Button watchAvsLoginBtn;

  @NonNull
  public final TextView watchAvsLoginText;

  @NonNull
  public final Button watchAvsRecordBtn;

  private ActivityWatchavsBinding(@NonNull LinearLayout rootView, @NonNull LogviewBinding loginfo,
      @NonNull ToolbarBinding tool, @NonNull Button watchAvsConnectDeviceBtn,
      @NonNull TextView watchAvsDeviceStateText, @NonNull Button watchAvsLoginBtn,
      @NonNull TextView watchAvsLoginText, @NonNull Button watchAvsRecordBtn) {
    this.rootView = rootView;
    this.loginfo = loginfo;
    this.tool = tool;
    this.watchAvsConnectDeviceBtn = watchAvsConnectDeviceBtn;
    this.watchAvsDeviceStateText = watchAvsDeviceStateText;
    this.watchAvsLoginBtn = watchAvsLoginBtn;
    this.watchAvsLoginText = watchAvsLoginText;
    this.watchAvsRecordBtn = watchAvsRecordBtn;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityWatchavsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityWatchavsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_watchavs, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityWatchavsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.loginfo;
      View loginfo = rootView.findViewById(id);
      if (loginfo == null) {
        break missingId;
      }
      LogviewBinding binding_loginfo = LogviewBinding.bind(loginfo);

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      id = R.id.watch_avs_connect_device_btn;
      Button watchAvsConnectDeviceBtn = rootView.findViewById(id);
      if (watchAvsConnectDeviceBtn == null) {
        break missingId;
      }

      id = R.id.watch_avs_device_state_text;
      TextView watchAvsDeviceStateText = rootView.findViewById(id);
      if (watchAvsDeviceStateText == null) {
        break missingId;
      }

      id = R.id.watch_avs_login_btn;
      Button watchAvsLoginBtn = rootView.findViewById(id);
      if (watchAvsLoginBtn == null) {
        break missingId;
      }

      id = R.id.watch_avs_login_text;
      TextView watchAvsLoginText = rootView.findViewById(id);
      if (watchAvsLoginText == null) {
        break missingId;
      }

      id = R.id.watch_avs_record_btn;
      Button watchAvsRecordBtn = rootView.findViewById(id);
      if (watchAvsRecordBtn == null) {
        break missingId;
      }

      return new ActivityWatchavsBinding((LinearLayout) rootView, binding_loginfo, binding_tool,
          watchAvsConnectDeviceBtn, watchAvsDeviceStateText, watchAvsLoginBtn, watchAvsLoginText,
          watchAvsRecordBtn);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
