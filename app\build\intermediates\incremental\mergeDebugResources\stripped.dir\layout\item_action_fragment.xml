<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal" android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="20dp"
    android:background="@drawable/background_item_action">
    <ImageView
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:id="@+id/icon"
        android:layout_marginRight="20dp"
        android:layout_gravity="center_vertical"
        />
<!--    android:background="@drawable/listview_click"-->
    <TextView
        android:text="TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/title"
        android:layout_weight="1"
        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
        android:layout_gravity="center_vertical"/>
</LinearLayout>