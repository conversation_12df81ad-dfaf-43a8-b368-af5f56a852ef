<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_blewifi" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_blewifi.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_blewifi_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="155" endOffset="12"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="9" startOffset="4" endLine="153" endOffset="18"/></Target><Target id="@+id/tool" tag="binding_1" include="toolbar"><Expressions/><location startLine="15" startOffset="8" endLine="18" endOffset="13"/></Target><Target id="@+id/ble_name" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="38" endOffset="31"/></Target><Target id="@+id/pick_device" view="Button"><Expressions/><location startLine="40" startOffset="8" endLine="49" endOffset="13"/></Target><Target id="@+id/connect_device" view="Button"><Expressions/><location startLine="51" startOffset="8" endLine="61" endOffset="13"/></Target><Target id="@+id/pick_wifi" view="Button"><Expressions/><location startLine="63" startOffset="8" endLine="73" endOffset="13"/></Target><Target id="@+id/wifi_name" view="TextView"><Expressions/><location startLine="84" startOffset="12" endLine="90" endOffset="17"/></Target><Target id="@+id/wifi_name_value" view="EditText"><Expressions/><location startLine="91" startOffset="12" endLine="98" endOffset="22"/></Target><Target id="@+id/wifi_pw" view="TextView"><Expressions/><location startLine="112" startOffset="12" endLine="118" endOffset="17"/></Target><Target id="@+id/wifi_pw_value" view="EditText"><Expressions/><location startLine="119" startOffset="12" endLine="126" endOffset="22"/></Target><Target id="@+id/send_data" view="Button"><Expressions/><location startLine="130" startOffset="8" endLine="140" endOffset="13"/></Target><Target id="@+id/open_wifi" view="Button"><Expressions/><location startLine="142" startOffset="8" endLine="152" endOffset="13"/></Target></Targets></Layout>