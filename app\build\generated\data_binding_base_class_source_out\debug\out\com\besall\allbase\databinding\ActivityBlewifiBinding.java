// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityBlewifiBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final TextView bleName;

  @NonNull
  public final Button connectDevice;

  @NonNull
  public final Button openWifi;

  @NonNull
  public final Button pickDevice;

  @NonNull
  public final Button pickWifi;

  @NonNull
  public final Button sendData;

  @NonNull
  public final ToolbarBinding tool;

  @NonNull
  public final TextView wifiName;

  @NonNull
  public final EditText wifiNameValue;

  @NonNull
  public final TextView wifiPw;

  @NonNull
  public final EditText wifiPwValue;

  private ActivityBlewifiBinding(@NonNull ScrollView rootView, @NonNull TextView bleName,
      @NonNull Button connectDevice, @NonNull Button openWifi, @NonNull Button pickDevice,
      @NonNull Button pickWifi, @NonNull Button sendData, @NonNull ToolbarBinding tool,
      @NonNull TextView wifiName, @NonNull EditText wifiNameValue, @NonNull TextView wifiPw,
      @NonNull EditText wifiPwValue) {
    this.rootView = rootView;
    this.bleName = bleName;
    this.connectDevice = connectDevice;
    this.openWifi = openWifi;
    this.pickDevice = pickDevice;
    this.pickWifi = pickWifi;
    this.sendData = sendData;
    this.tool = tool;
    this.wifiName = wifiName;
    this.wifiNameValue = wifiNameValue;
    this.wifiPw = wifiPw;
    this.wifiPwValue = wifiPwValue;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityBlewifiBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityBlewifiBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_blewifi, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityBlewifiBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ble_name;
      TextView bleName = rootView.findViewById(id);
      if (bleName == null) {
        break missingId;
      }

      id = R.id.connect_device;
      Button connectDevice = rootView.findViewById(id);
      if (connectDevice == null) {
        break missingId;
      }

      id = R.id.open_wifi;
      Button openWifi = rootView.findViewById(id);
      if (openWifi == null) {
        break missingId;
      }

      id = R.id.pick_device;
      Button pickDevice = rootView.findViewById(id);
      if (pickDevice == null) {
        break missingId;
      }

      id = R.id.pick_wifi;
      Button pickWifi = rootView.findViewById(id);
      if (pickWifi == null) {
        break missingId;
      }

      id = R.id.send_data;
      Button sendData = rootView.findViewById(id);
      if (sendData == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      id = R.id.wifi_name;
      TextView wifiName = rootView.findViewById(id);
      if (wifiName == null) {
        break missingId;
      }

      id = R.id.wifi_name_value;
      EditText wifiNameValue = rootView.findViewById(id);
      if (wifiNameValue == null) {
        break missingId;
      }

      id = R.id.wifi_pw;
      TextView wifiPw = rootView.findViewById(id);
      if (wifiPw == null) {
        break missingId;
      }

      id = R.id.wifi_pw_value;
      EditText wifiPwValue = rootView.findViewById(id);
      if (wifiPwValue == null) {
        break missingId;
      }

      return new ActivityBlewifiBinding((ScrollView) rootView, bleName, connectDevice, openWifi,
          pickDevice, pickWifi, sendData, binding_tool, wifiName, wifiNameValue, wifiPw,
          wifiPwValue);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
