// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RelativeLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class BisCodeDialogBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final EditText bisCodePsw;

  @NonNull
  public final Button dialogCancel;

  @NonNull
  public final Button dialogConfirm;

  private BisCodeDialogBinding(@NonNull RelativeLayout rootView, @NonNull EditText bisCodePsw,
      @NonNull Button dialogCancel, @NonNull Button dialogConfirm) {
    this.rootView = rootView;
    this.bisCodePsw = bisCodePsw;
    this.dialogCancel = dialogCancel;
    this.dialogConfirm = dialogConfirm;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static BisCodeDialogBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static BisCodeDialogBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.bis_code_dialog, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static BisCodeDialogBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bis_code_psw;
      EditText bisCodePsw = rootView.findViewById(id);
      if (bisCodePsw == null) {
        break missingId;
      }

      id = R.id.dialog_cancel;
      Button dialogCancel = rootView.findViewById(id);
      if (dialogCancel == null) {
        break missingId;
      }

      id = R.id.dialog_confirm;
      Button dialogConfirm = rootView.findViewById(id);
      if (dialogConfirm == null) {
        break missingId;
      }

      return new BisCodeDialogBinding((RelativeLayout) rootView, bisCodePsw, dialogCancel,
          dialogConfirm);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
