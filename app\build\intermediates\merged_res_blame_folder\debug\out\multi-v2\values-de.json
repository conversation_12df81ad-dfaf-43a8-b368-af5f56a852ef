{"logs": [{"outputFile": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-de\\values-de.xml", "map": [{"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\ec1a13a01684407bf169e0545c40db84\\navigation-ui-2.4.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,159", "endColumns": "103,118", "endOffsets": "154,273"}, "to": {"startLines": "140,141", "startColumns": "4,4", "startOffsets": "11985,12089", "endColumns": "103,118", "endOffsets": "12084,12203"}}, {"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\3a6c3290bf94c2fdaee81cec79dba243\\core-1.5.0-rc01\\res\\values-de\\values-de.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "144", "startColumns": "4", "startOffsets": "12372", "endColumns": "100", "endOffsets": "12468"}}, {"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\85d353c95dd38846b16194b262acf36b\\material-1.4.0-beta01\\res\\values-de\\values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,322,423,545,626,692,786,856,915,1023,1092,1150,1222,1286,1340,1468,1528,1590,1644,1722,1859,1951,2035,2150,2234,2320,2410,2477,2543,2617,2699,2792,2866,2944,3016,3090,3182,3264,3353,3442,3516,3594,3680,3735,3802,3882,3966,4028,4092,4155,4262,4366,4465,4571", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,83,100,121,80,65,93,69,58,107,68,57,71,63,53,127,59,61,53,77,136,91,83,114,83,85,89,66,65,73,81,92,73,77,71,73,91,81,88,88,73,77,85,54,66,79,83,61,63,62,106,103,98,105,81", "endOffsets": "233,317,418,540,621,687,781,851,910,1018,1087,1145,1217,1281,1335,1463,1523,1585,1639,1717,1854,1946,2030,2145,2229,2315,2405,2472,2538,2612,2694,2787,2861,2939,3011,3085,3177,3259,3348,3437,3511,3589,3675,3730,3797,3877,3961,4023,4087,4150,4257,4361,4460,4566,4648"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3015,3099,3200,3322,3403,3469,3563,3633,8329,8437,8506,8564,8636,8700,8754,8882,8942,9004,9058,9136,9273,9365,9449,9564,9648,9734,9824,9891,9957,10031,10113,10206,10280,10358,10430,10504,10596,10678,10767,10856,10930,11008,11094,11149,11216,11296,11380,11442,11506,11569,11676,11780,11879,12208", "endLines": "5,33,34,35,36,37,38,39,40,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,142", "endColumns": "12,83,100,121,80,65,93,69,58,107,68,57,71,63,53,127,59,61,53,77,136,91,83,114,83,85,89,66,65,73,81,92,73,77,71,73,91,81,88,88,73,77,85,54,66,79,83,61,63,62,106,103,98,105,81", "endOffsets": "283,3094,3195,3317,3398,3464,3558,3628,3687,8432,8501,8559,8631,8695,8749,8877,8937,8999,9053,9131,9268,9360,9444,9559,9643,9729,9819,9886,9952,10026,10108,10201,10275,10353,10425,10499,10591,10673,10762,10851,10925,11003,11089,11144,11211,11291,11375,11437,11501,11564,11671,11775,11874,11980,12285"}}, {"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\7ad5be15223e864e4511b3f7157ae4d3\\appcompat-1.2.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "288,393,491,603,689,795,910,988,1063,1155,1249,1345,1446,1553,1653,1757,1855,1953,2050,2132,2243,2345,2443,2550,2653,2757,2913,12290", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "388,486,598,684,790,905,983,1058,1150,1244,1340,1441,1548,1648,1752,1850,1948,2045,2127,2238,2340,2438,2545,2648,2752,2908,3010,12367"}}, {"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\0a678c11412547be3dd78d75dd3cb66f\\jetified-zxing-android-embedded-4.1.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,114,161,333", "endColumns": "58,46,171,108", "endOffsets": "109,156,328,437"}, "to": {"startLines": "145,146,147,148", "startColumns": "4,4,4,4", "startOffsets": "12473,12532,12579,12751", "endColumns": "58,46,171,108", "endOffsets": "12527,12574,12746,12855"}}, {"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\3b48aab2f1670fa25c5eca57579cf29d\\jetified-leakcanary-android-1.6.3\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,214,304,396,516,571,635,755,824,961,1034,1158,2548,2631,2720,2824,2892,3060,3144,3216,3290,3387,3479,3570,3720,3849,3955,4076,4204,4289,4362,4425,4488,4583", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55", "endColumns": "83,74,89,91,119,54,63,119,68,136,72,123,12,82,88,103,67,167,83,71,73,96,91,90,149,128,105,120,127,84,72,62,62,94,108", "endOffsets": "134,209,299,391,511,566,630,750,819,956,1029,1153,2543,2626,2715,2819,2887,3055,3139,3211,3285,3382,3474,3565,3715,3844,3950,4071,4199,4284,4357,4420,4483,4578,4687"}, "to": {"startLines": "41,42,43,44,45,46,47,48,49,50,51,52,53,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3692,3776,3851,3941,4033,4153,4208,4272,4392,4461,4598,4671,4795,6185,6268,6357,6461,6529,6697,6781,6853,6927,7024,7116,7207,7357,7486,7592,7713,7841,7926,7999,8062,8125,8220", "endLines": "41,42,43,44,45,46,47,48,49,50,51,52,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "endColumns": "83,74,89,91,119,54,63,119,68,136,72,123,12,82,88,103,67,167,83,71,73,96,91,90,149,128,105,120,127,84,72,62,62,94,108", "endOffsets": "3771,3846,3936,4028,4148,4203,4267,4387,4456,4593,4666,4790,6180,6263,6352,6456,6524,6692,6776,6848,6922,7019,7111,7202,7352,7481,7587,7708,7836,7921,7994,8057,8120,8215,8324"}}]}]}