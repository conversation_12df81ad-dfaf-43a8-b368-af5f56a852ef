// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import com.besall.allbase.view.activity.chipstoollevel4.customerdial.makedial.CropPhotoBgView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityCropphotoBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button buttonCancel;

  @NonNull
  public final Button buttonSure;

  @NonNull
  public final CropPhotoBgView cropPhotoBg;

  @NonNull
  public final ImageView cropPhotoImage;

  @NonNull
  public final Button cropPhotoSaveSetting;

  @NonNull
  public final TextView cropPhotoSymbol;

  @NonNull
  public final EditText curData0;

  @NonNull
  public final EditText curData1;

  @NonNull
  public final TextView curDataName0;

  @NonNull
  public final TextView curDataName1;

  @NonNull
  public final RadioButton formCircle;

  @NonNull
  public final RadioGroup formRadioGroup;

  @NonNull
  public final RadioButton formSquare;

  private ActivityCropphotoBinding(@NonNull ConstraintLayout rootView, @NonNull Button buttonCancel,
      @NonNull Button buttonSure, @NonNull CropPhotoBgView cropPhotoBg,
      @NonNull ImageView cropPhotoImage, @NonNull Button cropPhotoSaveSetting,
      @NonNull TextView cropPhotoSymbol, @NonNull EditText curData0, @NonNull EditText curData1,
      @NonNull TextView curDataName0, @NonNull TextView curDataName1,
      @NonNull RadioButton formCircle, @NonNull RadioGroup formRadioGroup,
      @NonNull RadioButton formSquare) {
    this.rootView = rootView;
    this.buttonCancel = buttonCancel;
    this.buttonSure = buttonSure;
    this.cropPhotoBg = cropPhotoBg;
    this.cropPhotoImage = cropPhotoImage;
    this.cropPhotoSaveSetting = cropPhotoSaveSetting;
    this.cropPhotoSymbol = cropPhotoSymbol;
    this.curData0 = curData0;
    this.curData1 = curData1;
    this.curDataName0 = curDataName0;
    this.curDataName1 = curDataName1;
    this.formCircle = formCircle;
    this.formRadioGroup = formRadioGroup;
    this.formSquare = formSquare;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCropphotoBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCropphotoBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_cropphoto, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCropphotoBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_cancel;
      Button buttonCancel = rootView.findViewById(id);
      if (buttonCancel == null) {
        break missingId;
      }

      id = R.id.button_sure;
      Button buttonSure = rootView.findViewById(id);
      if (buttonSure == null) {
        break missingId;
      }

      id = R.id.crop_photo_bg;
      CropPhotoBgView cropPhotoBg = rootView.findViewById(id);
      if (cropPhotoBg == null) {
        break missingId;
      }

      id = R.id.crop_photo_image;
      ImageView cropPhotoImage = rootView.findViewById(id);
      if (cropPhotoImage == null) {
        break missingId;
      }

      id = R.id.crop_photo_save_setting;
      Button cropPhotoSaveSetting = rootView.findViewById(id);
      if (cropPhotoSaveSetting == null) {
        break missingId;
      }

      id = R.id.crop_photo_symbol;
      TextView cropPhotoSymbol = rootView.findViewById(id);
      if (cropPhotoSymbol == null) {
        break missingId;
      }

      id = R.id.cur_data_0;
      EditText curData0 = rootView.findViewById(id);
      if (curData0 == null) {
        break missingId;
      }

      id = R.id.cur_data_1;
      EditText curData1 = rootView.findViewById(id);
      if (curData1 == null) {
        break missingId;
      }

      id = R.id.cur_data_name_0;
      TextView curDataName0 = rootView.findViewById(id);
      if (curDataName0 == null) {
        break missingId;
      }

      id = R.id.cur_data_name_1;
      TextView curDataName1 = rootView.findViewById(id);
      if (curDataName1 == null) {
        break missingId;
      }

      id = R.id.form_circle;
      RadioButton formCircle = rootView.findViewById(id);
      if (formCircle == null) {
        break missingId;
      }

      id = R.id.form_radio_group;
      RadioGroup formRadioGroup = rootView.findViewById(id);
      if (formRadioGroup == null) {
        break missingId;
      }

      id = R.id.form_square;
      RadioButton formSquare = rootView.findViewById(id);
      if (formSquare == null) {
        break missingId;
      }

      return new ActivityCropphotoBinding((ConstraintLayout) rootView, buttonCancel, buttonSure,
          cropPhotoBg, cropPhotoImage, cropPhotoSaveSetting, cropPhotoSymbol, curData0, curData1,
          curDataName0, curDataName1, formCircle, formRadioGroup, formSquare);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
