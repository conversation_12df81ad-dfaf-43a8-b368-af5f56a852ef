<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >
    <ImageView
        android:id="@+id/act_otaui_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bes_bg6"
        >

    </ImageView>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <include
            android:id="@+id/tool"
            layout="@layout/toolbar"
            />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            >
            <include layout="@layout/view_otaing"
                android:visibility="gone"
                android:id="@+id/view_otaing"
                android:layout_width="match_parent"
                android:layout_height="350dp"
                android:layout_gravity="center_horizontal"
                />

            <include layout="@layout/view_device"
                android:id="@+id/view_device"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                />

            <include layout="@layout/view_baseoption"
                android:id="@+id/view_baseoption"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="173dp"
                />
            <include layout="@layout/view_versionpath"
                android:visibility="gone"
                android:id="@+id/view_versionpath"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                />

            <include layout="@layout/view_button"
                android:id="@+id/view_button"
                android:layout_width="match_parent"
                android:layout_height="163dp"
                android:layout_marginTop="-173dp"
                />
            <include layout="@layout/view_otalog"
                android:visibility="gone"
                android:id="@+id/view_otalog"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                />
        </LinearLayout>


    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>