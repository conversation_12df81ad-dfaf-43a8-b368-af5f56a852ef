<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6">
    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <include
        android:id="@+id/loginfo"
        layout="@layout/logview"
        android:visibility="gone"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="20dp"
        android:orientation="vertical"
        android:padding="20dp"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:text="Alexa Device"
                    android:textColor="@color/black"
                    android:textSize="25sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/shape_background"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:text="Device SN"
                    android:textColor="@color/black"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/setting_device_sn"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginRight="20dp"
                    android:gravity="center_vertical"
                    android:text="SN199999999999"
                    android:textAlignment="viewEnd"
                    android:textColor="@color/title_color_light"
                    android:textSize="16sp"
                    tools:ignore="RtlCompat" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginTop="25dp"
                android:background="@drawable/shape_background"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:text="Software Version"
                    android:textColor="@color/black"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/setting_software_version"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginRight="20dp"
                    android:gravity="center_vertical"
                    android:text="1.1.1"
                    android:textAlignment="viewEnd"
                    android:textColor="@color/title_color_light"
                    android:textSize="16sp"
                    tools:ignore="RtlCompat" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginTop="25dp"
                android:background="@drawable/shape_background"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:text="Mac Address"
                    android:textColor="@color/black"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/setting_mac_address"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginRight="20dp"
                    android:gravity="center_vertical"
                    android:text="10:99:11:11:11:11"
                    android:textAlignment="viewEnd"
                    android:textColor="@color/title_color_light"
                    android:textSize="16sp"
                    tools:ignore="RtlCompat" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginTop="25dp"
                android:background="@drawable/shape_background"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/deregister_the_device"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="Deregister the device"
                    android:background="@null"
                    android:textColor="@color/ffff5d5d"
                    android:textAllCaps="false"
                    android:textSize="18sp"/>

            </LinearLayout>


        </LinearLayout>
    </ScrollView>
</LinearLayout>