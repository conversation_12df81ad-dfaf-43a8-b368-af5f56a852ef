// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAvslwaGuideBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button avsGuideNext;

  @NonNull
  public final TextView jumpAlexaAppTextview;

  @NonNull
  public final LogviewBinding loginfo;

  @NonNull
  public final ToolbarBinding tool;

  private ActivityAvslwaGuideBinding(@NonNull LinearLayout rootView, @NonNull Button avsGuideNext,
      @NonNull TextView jumpAlexaAppTextview, @NonNull LogviewBinding loginfo,
      @NonNull ToolbarBinding tool) {
    this.rootView = rootView;
    this.avsGuideNext = avsGuideNext;
    this.jumpAlexaAppTextview = jumpAlexaAppTextview;
    this.loginfo = loginfo;
    this.tool = tool;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAvslwaGuideBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAvslwaGuideBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_avslwa_guide, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAvslwaGuideBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.avs_guide_next;
      Button avsGuideNext = rootView.findViewById(id);
      if (avsGuideNext == null) {
        break missingId;
      }

      id = R.id.jump_alexa_app_textview;
      TextView jumpAlexaAppTextview = rootView.findViewById(id);
      if (jumpAlexaAppTextview == null) {
        break missingId;
      }

      id = R.id.loginfo;
      View loginfo = rootView.findViewById(id);
      if (loginfo == null) {
        break missingId;
      }
      LogviewBinding binding_loginfo = LogviewBinding.bind(loginfo);

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      return new ActivityAvslwaGuideBinding((LinearLayout) rootView, avsGuideNext,
          jumpAlexaAppTextview, binding_loginfo, binding_tool);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
