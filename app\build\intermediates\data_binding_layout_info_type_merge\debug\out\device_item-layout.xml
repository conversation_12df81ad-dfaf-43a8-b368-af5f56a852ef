<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="device_item" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\device_item.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/device_item_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="81" endOffset="14"/></Target><Target id="@+id/select_image" view="ImageView"><Expressions/><location startLine="11" startOffset="4" endLine="17" endOffset="62"/></Target><Target id="@+id/name" view="TextView"><Expressions/><location startLine="27" startOffset="8" endLine="34" endOffset="13"/></Target><Target id="@+id/address" view="TextView"><Expressions/><location startLine="36" startOffset="8" endLine="42" endOffset="13"/></Target><Target id="@+id/rssi_icon" view="ImageView"><Expressions/><location startLine="53" startOffset="8" endLine="57" endOffset="13"/></Target><Target id="@+id/rssi_value" view="TextView"><Expressions/><location startLine="59" startOffset="8" endLine="65" endOffset="13"/></Target><Target id="@+id/disconnect_btn" view="Button"><Expressions/><location startLine="66" startOffset="8" endLine="78" endOffset="16"/></Target></Targets></Layout>