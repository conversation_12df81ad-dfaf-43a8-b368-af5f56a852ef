// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ViewDeviceBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final EditText deviceName;

  @NonNull
  public final TextView deviceTitle;

  @NonNull
  public final LinearLayout viewDeviceBg;

  @NonNull
  public final LinearLayout viewDeviceSupport;

  private ViewDeviceBinding(@NonNull ConstraintLayout rootView, @NonNull EditText deviceName,
      @NonNull TextView deviceTitle, @NonNull LinearLayout viewDeviceBg,
      @NonNull LinearLayout viewDeviceSupport) {
    this.rootView = rootView;
    this.deviceName = deviceName;
    this.deviceTitle = deviceTitle;
    this.viewDeviceBg = viewDeviceBg;
    this.viewDeviceSupport = viewDeviceSupport;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ViewDeviceBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ViewDeviceBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.view_device, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ViewDeviceBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.device_name;
      EditText deviceName = rootView.findViewById(id);
      if (deviceName == null) {
        break missingId;
      }

      id = R.id.device_title;
      TextView deviceTitle = rootView.findViewById(id);
      if (deviceTitle == null) {
        break missingId;
      }

      id = R.id.view_device_bg;
      LinearLayout viewDeviceBg = rootView.findViewById(id);
      if (viewDeviceBg == null) {
        break missingId;
      }

      id = R.id.view_device_support;
      LinearLayout viewDeviceSupport = rootView.findViewById(id);
      if (viewDeviceSupport == null) {
        break missingId;
      }

      return new ViewDeviceBinding((ConstraintLayout) rootView, deviceName, deviceTitle,
          viewDeviceBg, viewDeviceSupport);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
