// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ViewBaseoptionBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button ackBtn0;

  @NonNull
  public final Button ackBtn1;

  @NonNull
  public final ImageView ackImage0;

  @NonNull
  public final ImageView ackImage1;

  @NonNull
  public final Button earphoneBtn0;

  @NonNull
  public final Button earphoneBtn1;

  @NonNull
  public final ImageView earphoneImage0;

  @NonNull
  public final ImageView earphoneImage1;

  @NonNull
  public final Button responseBtn0;

  @NonNull
  public final Button responseBtn1;

  @NonNull
  public final ImageView responseImage0;

  @NonNull
  public final ImageView responseImage1;

  @NonNull
  public final Button upgradetypeBtn0;

  @NonNull
  public final Button upgradetypeBtn1;

  @NonNull
  public final ImageView upgradetypeImage0;

  @NonNull
  public final ImageView upgradetypeImage1;

  @NonNull
  public final Button userBtn0;

  @NonNull
  public final Button userBtn1;

  @NonNull
  public final Button userBtn2;

  @NonNull
  public final Button userBtn3;

  @NonNull
  public final Button userBtn4;

  @NonNull
  public final Button userBtn5;

  @NonNull
  public final Button userBtn6;

  @NonNull
  public final Button userBtn7;

  @NonNull
  public final Button userBtn8;

  @NonNull
  public final ImageView userImage0;

  @NonNull
  public final ImageView userImage1;

  @NonNull
  public final ImageView userImage2;

  @NonNull
  public final ImageView userImage3;

  @NonNull
  public final ImageView userImage4;

  @NonNull
  public final ImageView userImage5;

  @NonNull
  public final ImageView userImage6;

  @NonNull
  public final ImageView userImage7;

  @NonNull
  public final ImageView userImage8;

  @NonNull
  public final LinearLayout viewAck;

  @NonNull
  public final LinearLayout viewEarphone;

  @NonNull
  public final LinearLayout viewResponse;

  @NonNull
  public final LinearLayout viewUpgradetype;

  @NonNull
  public final LinearLayout viewUser;

  private ViewBaseoptionBinding(@NonNull ConstraintLayout rootView, @NonNull Button ackBtn0,
      @NonNull Button ackBtn1, @NonNull ImageView ackImage0, @NonNull ImageView ackImage1,
      @NonNull Button earphoneBtn0, @NonNull Button earphoneBtn1, @NonNull ImageView earphoneImage0,
      @NonNull ImageView earphoneImage1, @NonNull Button responseBtn0, @NonNull Button responseBtn1,
      @NonNull ImageView responseImage0, @NonNull ImageView responseImage1,
      @NonNull Button upgradetypeBtn0, @NonNull Button upgradetypeBtn1,
      @NonNull ImageView upgradetypeImage0, @NonNull ImageView upgradetypeImage1,
      @NonNull Button userBtn0, @NonNull Button userBtn1, @NonNull Button userBtn2,
      @NonNull Button userBtn3, @NonNull Button userBtn4, @NonNull Button userBtn5,
      @NonNull Button userBtn6, @NonNull Button userBtn7, @NonNull Button userBtn8,
      @NonNull ImageView userImage0, @NonNull ImageView userImage1, @NonNull ImageView userImage2,
      @NonNull ImageView userImage3, @NonNull ImageView userImage4, @NonNull ImageView userImage5,
      @NonNull ImageView userImage6, @NonNull ImageView userImage7, @NonNull ImageView userImage8,
      @NonNull LinearLayout viewAck, @NonNull LinearLayout viewEarphone,
      @NonNull LinearLayout viewResponse, @NonNull LinearLayout viewUpgradetype,
      @NonNull LinearLayout viewUser) {
    this.rootView = rootView;
    this.ackBtn0 = ackBtn0;
    this.ackBtn1 = ackBtn1;
    this.ackImage0 = ackImage0;
    this.ackImage1 = ackImage1;
    this.earphoneBtn0 = earphoneBtn0;
    this.earphoneBtn1 = earphoneBtn1;
    this.earphoneImage0 = earphoneImage0;
    this.earphoneImage1 = earphoneImage1;
    this.responseBtn0 = responseBtn0;
    this.responseBtn1 = responseBtn1;
    this.responseImage0 = responseImage0;
    this.responseImage1 = responseImage1;
    this.upgradetypeBtn0 = upgradetypeBtn0;
    this.upgradetypeBtn1 = upgradetypeBtn1;
    this.upgradetypeImage0 = upgradetypeImage0;
    this.upgradetypeImage1 = upgradetypeImage1;
    this.userBtn0 = userBtn0;
    this.userBtn1 = userBtn1;
    this.userBtn2 = userBtn2;
    this.userBtn3 = userBtn3;
    this.userBtn4 = userBtn4;
    this.userBtn5 = userBtn5;
    this.userBtn6 = userBtn6;
    this.userBtn7 = userBtn7;
    this.userBtn8 = userBtn8;
    this.userImage0 = userImage0;
    this.userImage1 = userImage1;
    this.userImage2 = userImage2;
    this.userImage3 = userImage3;
    this.userImage4 = userImage4;
    this.userImage5 = userImage5;
    this.userImage6 = userImage6;
    this.userImage7 = userImage7;
    this.userImage8 = userImage8;
    this.viewAck = viewAck;
    this.viewEarphone = viewEarphone;
    this.viewResponse = viewResponse;
    this.viewUpgradetype = viewUpgradetype;
    this.viewUser = viewUser;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ViewBaseoptionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ViewBaseoptionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.view_baseoption, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ViewBaseoptionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ack_btn_0;
      Button ackBtn0 = rootView.findViewById(id);
      if (ackBtn0 == null) {
        break missingId;
      }

      id = R.id.ack_btn_1;
      Button ackBtn1 = rootView.findViewById(id);
      if (ackBtn1 == null) {
        break missingId;
      }

      id = R.id.ack_image_0;
      ImageView ackImage0 = rootView.findViewById(id);
      if (ackImage0 == null) {
        break missingId;
      }

      id = R.id.ack_image_1;
      ImageView ackImage1 = rootView.findViewById(id);
      if (ackImage1 == null) {
        break missingId;
      }

      id = R.id.earphone_btn_0;
      Button earphoneBtn0 = rootView.findViewById(id);
      if (earphoneBtn0 == null) {
        break missingId;
      }

      id = R.id.earphone_btn_1;
      Button earphoneBtn1 = rootView.findViewById(id);
      if (earphoneBtn1 == null) {
        break missingId;
      }

      id = R.id.earphone_image_0;
      ImageView earphoneImage0 = rootView.findViewById(id);
      if (earphoneImage0 == null) {
        break missingId;
      }

      id = R.id.earphone_image_1;
      ImageView earphoneImage1 = rootView.findViewById(id);
      if (earphoneImage1 == null) {
        break missingId;
      }

      id = R.id.response_btn_0;
      Button responseBtn0 = rootView.findViewById(id);
      if (responseBtn0 == null) {
        break missingId;
      }

      id = R.id.response_btn_1;
      Button responseBtn1 = rootView.findViewById(id);
      if (responseBtn1 == null) {
        break missingId;
      }

      id = R.id.response_image_0;
      ImageView responseImage0 = rootView.findViewById(id);
      if (responseImage0 == null) {
        break missingId;
      }

      id = R.id.response_image_1;
      ImageView responseImage1 = rootView.findViewById(id);
      if (responseImage1 == null) {
        break missingId;
      }

      id = R.id.upgradetype_btn_0;
      Button upgradetypeBtn0 = rootView.findViewById(id);
      if (upgradetypeBtn0 == null) {
        break missingId;
      }

      id = R.id.upgradetype_btn_1;
      Button upgradetypeBtn1 = rootView.findViewById(id);
      if (upgradetypeBtn1 == null) {
        break missingId;
      }

      id = R.id.upgradetype_image_0;
      ImageView upgradetypeImage0 = rootView.findViewById(id);
      if (upgradetypeImage0 == null) {
        break missingId;
      }

      id = R.id.upgradetype_image_1;
      ImageView upgradetypeImage1 = rootView.findViewById(id);
      if (upgradetypeImage1 == null) {
        break missingId;
      }

      id = R.id.user_btn_0;
      Button userBtn0 = rootView.findViewById(id);
      if (userBtn0 == null) {
        break missingId;
      }

      id = R.id.user_btn_1;
      Button userBtn1 = rootView.findViewById(id);
      if (userBtn1 == null) {
        break missingId;
      }

      id = R.id.user_btn_2;
      Button userBtn2 = rootView.findViewById(id);
      if (userBtn2 == null) {
        break missingId;
      }

      id = R.id.user_btn_3;
      Button userBtn3 = rootView.findViewById(id);
      if (userBtn3 == null) {
        break missingId;
      }

      id = R.id.user_btn_4;
      Button userBtn4 = rootView.findViewById(id);
      if (userBtn4 == null) {
        break missingId;
      }

      id = R.id.user_btn_5;
      Button userBtn5 = rootView.findViewById(id);
      if (userBtn5 == null) {
        break missingId;
      }

      id = R.id.user_btn_6;
      Button userBtn6 = rootView.findViewById(id);
      if (userBtn6 == null) {
        break missingId;
      }

      id = R.id.user_btn_7;
      Button userBtn7 = rootView.findViewById(id);
      if (userBtn7 == null) {
        break missingId;
      }

      id = R.id.user_btn_8;
      Button userBtn8 = rootView.findViewById(id);
      if (userBtn8 == null) {
        break missingId;
      }

      id = R.id.user_image_0;
      ImageView userImage0 = rootView.findViewById(id);
      if (userImage0 == null) {
        break missingId;
      }

      id = R.id.user_image_1;
      ImageView userImage1 = rootView.findViewById(id);
      if (userImage1 == null) {
        break missingId;
      }

      id = R.id.user_image_2;
      ImageView userImage2 = rootView.findViewById(id);
      if (userImage2 == null) {
        break missingId;
      }

      id = R.id.user_image_3;
      ImageView userImage3 = rootView.findViewById(id);
      if (userImage3 == null) {
        break missingId;
      }

      id = R.id.user_image_4;
      ImageView userImage4 = rootView.findViewById(id);
      if (userImage4 == null) {
        break missingId;
      }

      id = R.id.user_image_5;
      ImageView userImage5 = rootView.findViewById(id);
      if (userImage5 == null) {
        break missingId;
      }

      id = R.id.user_image_6;
      ImageView userImage6 = rootView.findViewById(id);
      if (userImage6 == null) {
        break missingId;
      }

      id = R.id.user_image_7;
      ImageView userImage7 = rootView.findViewById(id);
      if (userImage7 == null) {
        break missingId;
      }

      id = R.id.user_image_8;
      ImageView userImage8 = rootView.findViewById(id);
      if (userImage8 == null) {
        break missingId;
      }

      id = R.id.view_ack;
      LinearLayout viewAck = rootView.findViewById(id);
      if (viewAck == null) {
        break missingId;
      }

      id = R.id.view_earphone;
      LinearLayout viewEarphone = rootView.findViewById(id);
      if (viewEarphone == null) {
        break missingId;
      }

      id = R.id.view_response;
      LinearLayout viewResponse = rootView.findViewById(id);
      if (viewResponse == null) {
        break missingId;
      }

      id = R.id.view_upgradetype;
      LinearLayout viewUpgradetype = rootView.findViewById(id);
      if (viewUpgradetype == null) {
        break missingId;
      }

      id = R.id.view_user;
      LinearLayout viewUser = rootView.findViewById(id);
      if (viewUser == null) {
        break missingId;
      }

      return new ViewBaseoptionBinding((ConstraintLayout) rootView, ackBtn0, ackBtn1, ackImage0,
          ackImage1, earphoneBtn0, earphoneBtn1, earphoneImage0, earphoneImage1, responseBtn0,
          responseBtn1, responseImage0, responseImage1, upgradetypeBtn0, upgradetypeBtn1,
          upgradetypeImage0, upgradetypeImage1, userBtn0, userBtn1, userBtn2, userBtn3, userBtn4,
          userBtn5, userBtn6, userBtn7, userBtn8, userImage0, userImage1, userImage2, userImage3,
          userImage4, userImage5, userImage6, userImage7, userImage8, viewAck, viewEarphone,
          viewResponse, viewUpgradetype, viewUser);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
