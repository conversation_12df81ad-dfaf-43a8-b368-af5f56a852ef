<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_cropphoto" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_cropphoto.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_cropphoto_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="163" endOffset="51"/></Target><Target id="@+id/crop_photo_image" view="ImageView"><Expressions/><location startLine="10" startOffset="8" endLine="15" endOffset="13"/></Target><Target id="@+id/crop_photo_bg" view="com.besall.allbase.view.activity.chipstoollevel4.customerdial.makedial.CropPhotoBgView"><Expressions/><location startLine="19" startOffset="4" endLine="26" endOffset="28"/></Target><Target id="@+id/button_cancel" view="Button"><Expressions/><location startLine="33" startOffset="8" endLine="41" endOffset="37"/></Target><Target id="@+id/button_sure" view="Button"><Expressions/><location startLine="47" startOffset="8" endLine="56" endOffset="37"/></Target><Target id="@+id/form_radio_group" view="RadioGroup"><Expressions/><location startLine="67" startOffset="8" endLine="101" endOffset="20"/></Target><Target id="@+id/form_square" view="RadioButton"><Expressions/><location startLine="81" startOffset="12" endLine="86" endOffset="17"/></Target><Target id="@+id/form_circle" view="RadioButton"><Expressions/><location startLine="95" startOffset="12" endLine="100" endOffset="17"/></Target><Target id="@+id/cur_data_name_0" view="TextView"><Expressions/><location startLine="108" startOffset="12" endLine="114" endOffset="52"/></Target><Target id="@+id/cur_data_0" view="EditText"><Expressions/><location startLine="115" startOffset="12" endLine="122" endOffset="43"/></Target><Target id="@+id/crop_photo_symbol" view="TextView"><Expressions/><location startLine="124" startOffset="12" endLine="130" endOffset="52"/></Target><Target id="@+id/cur_data_1" view="EditText"><Expressions/><location startLine="132" startOffset="12" endLine="139" endOffset="43"/></Target><Target id="@+id/cur_data_name_1" view="TextView"><Expressions/><location startLine="140" startOffset="12" endLine="146" endOffset="52"/></Target><Target id="@+id/crop_photo_save_setting" view="Button"><Expressions/><location startLine="148" startOffset="12" endLine="157" endOffset="17"/></Target></Targets></Layout>