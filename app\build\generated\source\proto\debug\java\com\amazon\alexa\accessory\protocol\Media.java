// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: media.proto

package com.amazon.alexa.accessory.protocol;

public final class Media {
  private Media() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code MediaControl}
   */
  public enum MediaControl
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>PLAY = 0;</code>
     */
    PLAY(0),
    /**
     * <code>PAUSE = 1;</code>
     */
    PAUSE(1),
    /**
     * <code>NEXT = 2;</code>
     */
    NEXT(2),
    /**
     * <code>PREVIOUS = 3;</code>
     */
    PREVIOUS(3),
    /**
     * <code>PLAY_PAUSE = 4;</code>
     */
    PLAY_PAUSE(4),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>PLAY = 0;</code>
     */
    public static final int PLAY_VALUE = 0;
    /**
     * <code>PAUSE = 1;</code>
     */
    public static final int PAUSE_VALUE = 1;
    /**
     * <code>NEXT = 2;</code>
     */
    public static final int NEXT_VALUE = 2;
    /**
     * <code>PREVIOUS = 3;</code>
     */
    public static final int PREVIOUS_VALUE = 3;
    /**
     * <code>PLAY_PAUSE = 4;</code>
     */
    public static final int PLAY_PAUSE_VALUE = 4;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static MediaControl valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static MediaControl forNumber(int value) {
      switch (value) {
        case 0: return PLAY;
        case 1: return PAUSE;
        case 2: return NEXT;
        case 3: return PREVIOUS;
        case 4: return PLAY_PAUSE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<MediaControl>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        MediaControl> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<MediaControl>() {
            public MediaControl findValueByNumber(int number) {
              return MediaControl.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Media.getDescriptor().getEnumTypes().get(0);
    }

    private static final MediaControl[] VALUES = values();

    public static MediaControl valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private MediaControl(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:MediaControl)
  }

  public interface IssueMediaControlOrBuilder extends
      // @@protoc_insertion_point(interface_extends:IssueMediaControl)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.MediaControl control = 1;</code>
     * @return The enum numeric value on the wire for control.
     */
    int getControlValue();
    /**
     * <code>.MediaControl control = 1;</code>
     * @return The control.
     */
    com.amazon.alexa.accessory.protocol.Media.MediaControl getControl();
  }
  /**
   * Protobuf type {@code IssueMediaControl}
   */
  public static final class IssueMediaControl extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:IssueMediaControl)
      IssueMediaControlOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use IssueMediaControl.newBuilder() to construct.
    private IssueMediaControl(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private IssueMediaControl() {
      control_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new IssueMediaControl();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Media.internal_static_IssueMediaControl_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Media.internal_static_IssueMediaControl_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Media.IssueMediaControl.class, com.amazon.alexa.accessory.protocol.Media.IssueMediaControl.Builder.class);
    }

    public static final int CONTROL_FIELD_NUMBER = 1;
    private int control_;
    /**
     * <code>.MediaControl control = 1;</code>
     * @return The enum numeric value on the wire for control.
     */
    @java.lang.Override public int getControlValue() {
      return control_;
    }
    /**
     * <code>.MediaControl control = 1;</code>
     * @return The control.
     */
    @java.lang.Override public com.amazon.alexa.accessory.protocol.Media.MediaControl getControl() {
      @SuppressWarnings("deprecation")
      com.amazon.alexa.accessory.protocol.Media.MediaControl result = com.amazon.alexa.accessory.protocol.Media.MediaControl.valueOf(control_);
      return result == null ? com.amazon.alexa.accessory.protocol.Media.MediaControl.UNRECOGNIZED : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (control_ != com.amazon.alexa.accessory.protocol.Media.MediaControl.PLAY.getNumber()) {
        output.writeEnum(1, control_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (control_ != com.amazon.alexa.accessory.protocol.Media.MediaControl.PLAY.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, control_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Media.IssueMediaControl)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Media.IssueMediaControl other = (com.amazon.alexa.accessory.protocol.Media.IssueMediaControl) obj;

      if (control_ != other.control_) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CONTROL_FIELD_NUMBER;
      hash = (53 * hash) + control_;
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Media.IssueMediaControl parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Media.IssueMediaControl parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Media.IssueMediaControl parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Media.IssueMediaControl parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Media.IssueMediaControl parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Media.IssueMediaControl parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Media.IssueMediaControl parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Media.IssueMediaControl parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Media.IssueMediaControl parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Media.IssueMediaControl parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Media.IssueMediaControl parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Media.IssueMediaControl parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Media.IssueMediaControl prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code IssueMediaControl}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:IssueMediaControl)
        com.amazon.alexa.accessory.protocol.Media.IssueMediaControlOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Media.internal_static_IssueMediaControl_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Media.internal_static_IssueMediaControl_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Media.IssueMediaControl.class, com.amazon.alexa.accessory.protocol.Media.IssueMediaControl.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Media.IssueMediaControl.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        control_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Media.internal_static_IssueMediaControl_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Media.IssueMediaControl getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Media.IssueMediaControl.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Media.IssueMediaControl build() {
        com.amazon.alexa.accessory.protocol.Media.IssueMediaControl result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Media.IssueMediaControl buildPartial() {
        com.amazon.alexa.accessory.protocol.Media.IssueMediaControl result = new com.amazon.alexa.accessory.protocol.Media.IssueMediaControl(this);
        result.control_ = control_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Media.IssueMediaControl) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Media.IssueMediaControl)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Media.IssueMediaControl other) {
        if (other == com.amazon.alexa.accessory.protocol.Media.IssueMediaControl.getDefaultInstance()) return this;
        if (other.control_ != 0) {
          setControlValue(other.getControlValue());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                control_ = input.readEnum();

                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private int control_ = 0;
      /**
       * <code>.MediaControl control = 1;</code>
       * @return The enum numeric value on the wire for control.
       */
      @java.lang.Override public int getControlValue() {
        return control_;
      }
      /**
       * <code>.MediaControl control = 1;</code>
       * @param value The enum numeric value on the wire for control to set.
       * @return This builder for chaining.
       */
      public Builder setControlValue(int value) {
        
        control_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.MediaControl control = 1;</code>
       * @return The control.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Media.MediaControl getControl() {
        @SuppressWarnings("deprecation")
        com.amazon.alexa.accessory.protocol.Media.MediaControl result = com.amazon.alexa.accessory.protocol.Media.MediaControl.valueOf(control_);
        return result == null ? com.amazon.alexa.accessory.protocol.Media.MediaControl.UNRECOGNIZED : result;
      }
      /**
       * <code>.MediaControl control = 1;</code>
       * @param value The control to set.
       * @return This builder for chaining.
       */
      public Builder setControl(com.amazon.alexa.accessory.protocol.Media.MediaControl value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        control_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.MediaControl control = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearControl() {
        
        control_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:IssueMediaControl)
    }

    // @@protoc_insertion_point(class_scope:IssueMediaControl)
    private static final com.amazon.alexa.accessory.protocol.Media.IssueMediaControl DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Media.IssueMediaControl();
    }

    public static com.amazon.alexa.accessory.protocol.Media.IssueMediaControl getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<IssueMediaControl>
        PARSER = new com.google.protobuf.AbstractParser<IssueMediaControl>() {
      @java.lang.Override
      public IssueMediaControl parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<IssueMediaControl> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IssueMediaControl> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Media.IssueMediaControl getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_IssueMediaControl_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_IssueMediaControl_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\013media.proto\"3\n\021IssueMediaControl\022\036\n\007co" +
      "ntrol\030\001 \001(\0162\r.MediaControl*K\n\014MediaContr" +
      "ol\022\010\n\004PLAY\020\000\022\t\n\005PAUSE\020\001\022\010\n\004NEXT\020\002\022\014\n\010PRE" +
      "VIOUS\020\003\022\016\n\nPLAY_PAUSE\020\004B0\n#com.amazon.al" +
      "exa.accessory.protocolH\003\240\001\001\242\002\003AACb\006proto" +
      "3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_IssueMediaControl_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_IssueMediaControl_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_IssueMediaControl_descriptor,
        new java.lang.String[] { "Control", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
