// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAuracastassistantBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ListView bisDevices;

  @NonNull
  public final Button refresh;

  @NonNull
  public final Button scanComplete;

  @NonNull
  public final Button scanQR;

  @NonNull
  public final TextView scanQRResult;

  @NonNull
  public final ToolbarBinding tool;

  private ActivityAuracastassistantBinding(@NonNull LinearLayout rootView,
      @NonNull ListView bisDevices, @NonNull Button refresh, @NonNull Button scanComplete,
      @NonNull Button scanQR, @NonNull TextView scanQRResult, @NonNull ToolbarBinding tool) {
    this.rootView = rootView;
    this.bisDevices = bisDevices;
    this.refresh = refresh;
    this.scanComplete = scanComplete;
    this.scanQR = scanQR;
    this.scanQRResult = scanQRResult;
    this.tool = tool;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAuracastassistantBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAuracastassistantBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_auracastassistant, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAuracastassistantBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bis_devices;
      ListView bisDevices = rootView.findViewById(id);
      if (bisDevices == null) {
        break missingId;
      }

      id = R.id.refresh;
      Button refresh = rootView.findViewById(id);
      if (refresh == null) {
        break missingId;
      }

      id = R.id.scan_complete;
      Button scanComplete = rootView.findViewById(id);
      if (scanComplete == null) {
        break missingId;
      }

      id = R.id.scan_QR;
      Button scanQR = rootView.findViewById(id);
      if (scanQR == null) {
        break missingId;
      }

      id = R.id.scan_QR_result;
      TextView scanQRResult = rootView.findViewById(id);
      if (scanQRResult == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      return new ActivityAuracastassistantBinding((LinearLayout) rootView, bisDevices, refresh,
          scanComplete, scanQR, scanQRResult, binding_tool);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
