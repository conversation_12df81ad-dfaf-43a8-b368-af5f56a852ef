<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="horizontal"
    android:gravity="center"
    >

    <TextView
        android:id="@+id/language"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginRight="80dp"
        android:text="1111"
        android:textColor="@color/title_color_light"
        android:textSize="15sp"
        android:gravity="center_vertical"
        />

    <ImageView
        android:id="@+id/language_select_image"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginLeft="-80dp"
        android:background="@drawable/ota_top_nor"/>


</LinearLayout>