<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_spp" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\fragment_spp.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_spp_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="621" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/fragment_spp_0" include="toolbar"><Expressions/><location startLine="10" startOffset="4" endLine="13" endOffset="9"/></Target><Target id="@+id/loginfo" tag="layout/fragment_spp_0" include="logview"><Expressions/><location startLine="15" startOffset="4" endLine="18" endOffset="34"/></Target><Target id="@+id/spp_config_is_with_reponse_switch" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="48" startOffset="16" endLine="54" endOffset="54"/></Target><Target id="@+id/ble_config_view" view="LinearLayout"><Expressions/><location startLine="65" startOffset="12" endLine="93" endOffset="26"/></Target><Target id="@+id/ble_config_is_use_specific_connection_param_switch" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="84" startOffset="16" endLine="90" endOffset="54"/></Target><Target id="@+id/title_config_value" view="TextView"><Expressions/><location startLine="115" startOffset="20" endLine="123" endOffset="66"/></Target><Target id="@+id/test_data_title" view="TextView"><Expressions/><location startLine="125" startOffset="20" endLine="134" endOffset="25"/></Target><Target id="@+id/data_pattern1" view="RadioGroup"><Expressions/><location startLine="137" startOffset="20" endLine="184" endOffset="32"/></Target><Target id="@+id/pattern_00" view="RadioButton"><Expressions/><location startLine="144" startOffset="24" endLine="157" endOffset="61"/></Target><Target id="@+id/pattern_01" view="RadioButton"><Expressions/><location startLine="159" startOffset="24" endLine="169" endOffset="61"/></Target><Target id="@+id/pattern_02" view="RadioButton"><Expressions/><location startLine="171" startOffset="24" endLine="182" endOffset="61"/></Target><Target id="@+id/data_pattern2" view="RadioGroup"><Expressions/><location startLine="186" startOffset="20" endLine="232" endOffset="32"/></Target><Target id="@+id/pattern_03" view="RadioButton"><Expressions/><location startLine="194" startOffset="24" endLine="206" endOffset="61"/></Target><Target id="@+id/pattern_04" view="RadioButton"><Expressions/><location startLine="208" startOffset="24" endLine="218" endOffset="61"/></Target><Target id="@+id/pattern_05" view="RadioButton"><Expressions/><location startLine="220" startOffset="24" endLine="231" endOffset="61"/></Target><Target id="@+id/data_pattern3" view="RadioGroup"><Expressions/><location startLine="235" startOffset="20" endLine="256" endOffset="32"/></Target><Target id="@+id/pattern_06" view="RadioButton"><Expressions/><location startLine="243" startOffset="24" endLine="254" endOffset="61"/></Target><Target id="@+id/spp_config_lasting_time_in_second_in_ms" view="TextView"><Expressions/><location startLine="275" startOffset="16" endLine="283" endOffset="44"/></Target><Target id="@+id/intminus" view="Button"><Expressions/><location startLine="285" startOffset="16" endLine="291" endOffset="58"/></Target><Target id="@+id/ltis" view="EditText"><Expressions/><location startLine="293" startOffset="16" endLine="303" endOffset="48"/></Target><Target id="@+id/intplus" view="Button"><Expressions/><location startLine="305" startOffset="16" endLine="311" endOffset="56"/></Target><Target id="@+id/spp_config_data_size_text" view="TextView"><Expressions/><location startLine="328" startOffset="16" endLine="336" endOffset="44"/></Target><Target id="@+id/dataminus" view="Button"><Expressions/><location startLine="338" startOffset="16" endLine="344" endOffset="57"/></Target><Target id="@+id/datasize" view="EditText"><Expressions/><location startLine="346" startOffset="16" endLine="356" endOffset="48"/></Target><Target id="@+id/dataplus" view="Button"><Expressions/><location startLine="358" startOffset="16" endLine="364" endOffset="57"/></Target><Target id="@+id/title_real_value" view="TextView"><Expressions/><location startLine="381" startOffset="16" endLine="389" endOffset="60"/></Target><Target id="@+id/spp_loading" view="ProgressBar"><Expressions/><location startLine="400" startOffset="12" endLine="408" endOffset="43"/></Target><Target id="@+id/title_real_value_layout" view="LinearLayout"><Expressions/><location startLine="434" startOffset="12" endLine="492" endOffset="26"/></Target><Target id="@+id/ble_connection_interval_value_text" view="TextView"><Expressions/><location startLine="440" startOffset="16" endLine="449" endOffset="46"/></Target><Target id="@+id/spp_data_size_text" view="TextView"><Expressions/><location startLine="451" startOffset="16" endLine="459" endOffset="68"/></Target><Target id="@+id/spp_response_type_text" view="TextView"><Expressions/><location startLine="461" startOffset="16" endLine="469" endOffset="68"/></Target><Target id="@+id/spp_up_through_rate_text" view="TextView"><Expressions/><location startLine="471" startOffset="16" endLine="479" endOffset="76"/></Target><Target id="@+id/spp_down_through_rate_text" view="TextView"><Expressions/><location startLine="482" startOffset="16" endLine="490" endOffset="78"/></Target><Target id="@+id/ble_interval" view="LinearLayout"><Expressions/><location startLine="501" startOffset="12" endLine="578" endOffset="26"/></Target><Target id="@+id/ble_config_min_connection_interval_in_ms_edit" view="EditText"><Expressions/><location startLine="525" startOffset="20" endLine="536" endOffset="25"/></Target><Target id="@+id/ble_config_max_connection_interval_in_ms_edit" view="EditText"><Expressions/><location startLine="555" startOffset="20" endLine="566" endOffset="25"/></Target><Target id="@+id/spp_through_up_btn" view="Button"><Expressions/><location startLine="586" startOffset="16" endLine="597" endOffset="21"/></Target><Target id="@+id/spp_through_down_btn" view="Button"><Expressions/><location startLine="599" startOffset="16" endLine="610" endOffset="21"/></Target></Targets></Layout>