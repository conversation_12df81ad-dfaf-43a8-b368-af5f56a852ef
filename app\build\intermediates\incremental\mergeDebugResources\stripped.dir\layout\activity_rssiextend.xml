<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6"
    >

    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <include
        android:id="@+id/loginfo"
        layout="@layout/logview"
        android:visibility="gone"/>

    <ScrollView
        android:layout_marginTop="40dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="fill_parent"
        android:orientation="vertical"
        android:background="@drawable/rectangle">

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dip"
            android:background="#FFE1E6EB"
            />

        <Button
            android:id="@+id/connect_spp"
            android:layout_width="match_parent"
            android:layout_height="58dp"

            android:layout_alignParentBottom="true"
            android:text="   CLICK TO CONNECT"
            android:textSize="16sp"
            android:padding="20dp"
            android:gravity="left|center_vertical"
            android:background="@drawable/rectangle_longbtn"
            android:drawableLeft="@drawable/chips_tools_icon_connect"
            android:drawableRight="@drawable/home_icon_arrow"
            />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dip"
            android:background="#FFE1E6EB"

            />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"

                android:layout_marginLeft="20dp"
                android:layout_marginTop="13dp"
                android:text="lnterval(s)"
                android:textAlignment="gravity"
                android:textColor="#ff2c4662"
                android:textSize="20sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:focusable="true"
                android:focusableInTouchMode="true">

                <EditText
                    android:id="@+id/interval"
                    android:layout_width="77dp"
                    android:layout_height="58dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_weight="1"
                    android:hint="1-10"
                    android:inputType="number"
                    android:numeric="integer"
                    android:text="1"
                    android:textAlignment="center"
                    android:textSize="20sp" />

                <Button
                    android:id="@+id/rssi_read_start"
                    android:layout_width="103dp"
                    android:layout_height="44dp"
                    android:layout_marginRight="20dp"
                    android:text="RSSI"
                    android:textAlignment="center"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:background="@drawable/rectangle_button"/>
            </LinearLayout>
        </LinearLayout>



        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="10dp"
            android:text="RSSI(dBm)"
            android:textAlignment="center"
            android:textColor="#ff2c4662"
            android:textSize="18sp" />

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_weight="1"
                    android:text="LEFT"
                    android:textColor="#ff2c4662"
                    android:textSize="16sp"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_weight="1"
                    android:text="PHONE"
                    android:textColor="#ff2c4662"
                    android:textSize="16sp"/>

                <LinearLayout
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <TextView
                        android:textAlignment="center"
                        android:id="@+id/left_call_agc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_weight="1"
                        android:text="AGC:"
                        android:textColor="@color/rssi_data"
                        android:textSize="17sp" />

                    <TextView
                        android:textAlignment="center"
                        android:id="@+id/left_call_rssi"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_weight="1"
                        android:text="RSSI:"
                        android:textColor="@color/rssi_data"
                        android:textSize="17sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/left_call_min_rssi"
                        android:textAlignment="center"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_weight="1"
                        android:text="min_rssi:"
                        android:textColor="#ffafb8c3"
                        android:textSize="14sp" />

                    <TextView
                        android:textAlignment="center"
                        android:id="@+id/left_call_max_rssi"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_weight="1"
                        android:text="max_rssi:"
                        android:textColor="#ffafb8c3"
                        android:textSize="14sp" />
                </LinearLayout>

                <TextView
                    android:layout_marginTop="10dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_weight="1"
                    android:text="TWS"
                    android:textColor="#ff2c4662"
                    android:textSize="16sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal">

                    <TextView
                        android:textAlignment="center"
                        android:id="@+id/left_tws_agc"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_horizontal"
                        android:layout_weight="1"
                        android:text="AGC:"
                        android:textColor="@color/rssi_data"
                        android:textSize="17sp"/>

                    <TextView
                        android:textAlignment="center"
                        android:id="@+id/left_tws_rssi"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_horizontal"
                        android:layout_weight="1"
                        android:text="RSSI:"
                        android:textColor="@color/rssi_data"
                        android:textSize="17sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal">

                    <TextView
                        android:textAlignment="center"
                        android:id="@+id/left_tws_min_rssi"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_weight="1"
                        android:text="min_rssi:"
                        android:textColor="#ffafb8c3"
                        android:textSize="14sp" />

                    <TextView
                        android:textAlignment="center"
                        android:id="@+id/left_tws_max_rssi"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_weight="1"
                        android:text="max_rssi:"
                        android:textColor="#ffafb8c3"
                        android:textSize="14sp" />
                    />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_weight="1"
                    android:text="RIGHT"
                    android:textColor="#ff2c4662"
                    android:textSize="16sp"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_weight="1"
                    android:text="PHONE"
                    android:textColor="#ff2c4662"
                    android:textSize="16sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/right_call_agc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_weight="1"
                        android:text="AGC:"
                        android:textColor="@color/rssi_data"
                        android:textSize="17sp"
                        />

                    <TextView
                        android:id="@+id/right_call_rssi"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_weight="1"
                        android:text="RSSI:"
                        android:textColor="@color/rssi_data"
                        android:textSize="17sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/right_call_min_rssi"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_weight="1"
                        android:text="min_rssi:"
                        android:textColor="#ffafb8c3"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/right_call_max_rssi"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_weight="1"
                        android:text="max_rssi:"
                        android:textColor="#ffafb8c3"
                        android:textSize="14sp" />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="10dp"
                    android:layout_weight="1"
                    android:text="TWS"
                    android:textColor="#ff2c4662"
                    android:textSize="16sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/right_tws_agc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_weight="1"
                        android:text="AGC:"
                        android:textColor="@color/rssi_data"
                        android:textSize="17sp"/>

                    <TextView
                        android:id="@+id/right_tws_rssi"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_weight="1"
                        android:text="RSSI:"
                        android:textColor="@color/rssi_data"
                        android:textSize="17sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/right_tws_min_rssi"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_weight="1"
                        android:text="min_rssi:"
                        android:textColor="#ffafb8c3"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/right_tws_max_rssi"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_weight="1"
                        android:text="max_rssi"
                        android:textColor="#ffafb8c3"
                        android:textSize="14sp" />
                </LinearLayout>


            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_marginTop="5dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/left_packet_loss_rate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_weight="1"
                android:textAlignment="center"
                android:text="PACKET LOSS RATE(%):0"
                android:textColor="#ffafb8c3"
                android:textSize="14sp"/>

            <TextView
                android:id="@+id/right_packet_loss_rate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_weight="1"
                android:text="PACKET LOSS RATE(%):0"
                android:textColor="#ffafb8c3"
                android:textSize="14sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/rssi_extra_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="RSSI EXTRA INFO"
            android:textColor="#ffafb8c3"
            android:textSize="13sp" />

<!--        <TextView-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginTop="10dp"-->
<!--            android:text="CMD INPUT INFO:"-->
<!--            android:textSize="20sp" />-->

<!--        <EditText-->
<!--            android:id="@+id/cmd_input_text"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_gravity="center_horizontal"-->
<!--            android:text="&#45;&#45;"-->
<!--            android:textAlignment="center"-->
<!--            android:textSize="20sp" />-->

<!--        <Button-->
<!--            android:id="@+id/cmd_input"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="50dp"-->
<!--            android:layout_marginTop="10dp"-->
<!--            android:text="CMD INPUT SEND TO EARPHONE"-->
<!--            android:textSize="20sp" />-->

<!--        <TextView-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginTop="10dp"-->
<!--            android:text="CMD RECV INFO:"-->
<!--            android:textSize="20sp" />-->


<!--        <TextView-->
<!--            android:id="@+id/recv_cmd_info"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginTop="10dp"-->
<!--            android:text="&#45;&#45;"-->
<!--            android:textSize="20sp" />-->

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dip"
            android:background="#FFE1E6EB"
            android:layout_marginTop="20dp"
            />

        <Button
            android:id="@+id/rssi_read_stop"
            android:layout_width="match_parent"
            android:layout_height="58dp"

            android:text="   RSSI STOP"
            android:textSize="16sp"
            android:padding="20dp"
            android:gravity="left|center_vertical"
            android:background="@drawable/rectangle_longbtn"
            android:drawableLeft="@drawable/chips_tools_icon_stop2"
            android:drawableRight="@drawable/home_icon_arrow"
            />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dip"
            android:background="#FFE1E6EB"
            />

        <Button
            android:id="@+id/spp_stop"
            android:layout_width="match_parent"
            android:layout_height="58dp"
            android:layout_alignParentBottom="true"
            android:text="   CLICK TO DISCONNECT"
            android:textSize="16sp"
            android:padding="20dp"
            android:gravity="left|center_vertical"
            android:background="@drawable/rectangle_longbtn"
            android:drawableLeft="@drawable/chips_tools_icon_disconnect"
            android:drawableRight="@drawable/home_icon_arrow"
            />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dip"
            android:background="#FFE1E6EB"
            />

        <View
            android:layout_width="match_parent"
            android:layout_height="50dp"
            />

    </LinearLayout>

    </ScrollView>

</LinearLayout>



