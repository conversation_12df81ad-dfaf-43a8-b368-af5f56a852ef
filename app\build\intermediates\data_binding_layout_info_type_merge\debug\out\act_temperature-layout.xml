<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="act_temperature" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\act_temperature.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/act_temperature_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="54" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/act_temperature_0" include="toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="12" endOffset="9"/></Target><Target id="@+id/loginfo" tag="layout/act_temperature_0" include="logview"><Expressions/><location startLine="14" startOffset="4" endLine="17" endOffset="34"/></Target><Target id="@+id/temperature_start" view="Button"><Expressions/><location startLine="28" startOffset="12" endLine="39" endOffset="17"/></Target><Target id="@+id/temperatureChart" view="com.github.mikephil.charting.charts.LineChart"><Expressions/><location startLine="41" startOffset="12" endLine="45" endOffset="46"/></Target></Targets></Layout>