package com.bes.sdk.ota;

import java.io.Serializable;

/**
 * OTA configuration API.
 */
public class RemoteOTAConfig implements Serializable {

    private String pid;

    private String version;

    private String downloadUrl;

    private String localPath;

    private String localPathMaster;


    private String checkSum;

    private String whatsNewTitle;

    private String whatsNewContent;

    /**
     * Get PID. Remote OTA configuration is identified by PID.
     *
     * @return
     */
    public String getPid() { return pid;}

    /**
     * @see #getPid()
     * @param pid
     */
    public void setPid(String pid) {
        this.pid = pid;
    }

    /**
     * Get firmware version defined in remote configuration.
     *
     * @return
     */
    public String getVersion() {
        return version;
    }

    /**
     * @see #getVersion()
     * @param version
     */
    public void setVersion(String version) {
        this.version = version;
    }

    /**
     * Get OTA download URL defined in remote configuration.
     *
     * @return
     */
    public String getDownloadUrl() {
        return downloadUrl;
    }

    /**
     * @see #getDownloadUrl()
     * @param downloadUrl
     */
    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    /**
     * Local path that ScanManager downloads and store the latest firmware.
     *
     * @return
     */
    public String getLocalPath() {
        return localPath;
    }

    /**
     * @see #getLocalPath()
     * @param localPath
     */
    public void setLocalPath(String localPath) {
        this.localPath = localPath;
    }

    public String getLocalPathMaster() {
        return localPathMaster;
    }

    /**
     * @see #getLocalPath()
     * @param localPathRight
     */
    public void setLocalPathMaster(String localPathMaster) {
        this.localPathMaster = localPathMaster;
    }

    /**
     * Get firmware MD5 checksum defined in remote configuration.
     *
     * @return
     */
    public String getCheckSum() {
        return checkSum;
    }

    /**
     * @see #getCheckSum()
     * @param checkSum
     */
    public void setCheckSum(String checkSum) {
        this.checkSum = checkSum;
    }

    /**
     * Get What's New title for specific language.
     *
     * @param locale
     * @return
     */
    public String getWhatsNewTitle(String locale) {
        return whatsNewTitle;
    }

    /**
     * @see #getWhatsNewTitle(String)
     * @param whatsNewTitle
     */
    public void setWhatsNewTitle(String whatsNewTitle) {
        this.whatsNewTitle = whatsNewTitle;
    }

    /**
     * Get What's New content for specific language.
     *
     * @param locale
     * @return
     */
    public String getWhatsNewContent(String locale) {
        return whatsNewContent;
    }

    /**
     * @see #getWhatsNewContent(String)
     * @param whatsNewContent
     */
    public void setWhatsNewContent(String whatsNewContent) {
        this.whatsNewContent = whatsNewContent;
    }

    @Override
    public String toString() {
        return "RemoteOTAConfig{" +
                "pid='" + pid + '\'' +
                ", version='" + version + '\'' +
                ", downloadUrl='" + downloadUrl + '\'' +
                ", localPath='" + localPath + '\'' +
                ", checkSum='" + checkSum + '\'' +
                ", whatsNewTitle='" + whatsNewTitle + '\'' +
                ", whatsNewContent='" + whatsNewContent + '\'' +
                '}';
    }
}
