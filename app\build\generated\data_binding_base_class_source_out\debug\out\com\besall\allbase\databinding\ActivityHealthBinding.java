// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityHealthBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final LinearLayout buttonView;

  @NonNull
  public final ToolbarBinding tool;

  private ActivityHealthBinding(@NonNull ConstraintLayout rootView,
      @NonNull LinearLayout buttonView, @NonNull ToolbarBinding tool) {
    this.rootView = rootView;
    this.buttonView = buttonView;
    this.tool = tool;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityHealthBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityHealthBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_health, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityHealthBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonView;
      LinearLayout buttonView = rootView.findViewById(id);
      if (buttonView == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      return new ActivityHealthBinding((ConstraintLayout) rootView, buttonView, binding_tool);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
