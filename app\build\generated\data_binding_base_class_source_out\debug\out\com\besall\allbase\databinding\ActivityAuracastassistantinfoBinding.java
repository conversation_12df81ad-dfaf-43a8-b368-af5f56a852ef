// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAuracastassistantinfoBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView broadcastId;

  @NonNull
  public final Button leaveBtn;

  @NonNull
  public final TextView numberOfChannel;

  @NonNull
  public final TextView samplingRate;

  @NonNull
  public final ToolbarBinding tool;

  private ActivityAuracastassistantinfoBinding(@NonNull LinearLayout rootView,
      @NonNull TextView broadcastId, @NonNull Button leaveBtn, @NonNull TextView numberOfChannel,
      @NonNull TextView samplingRate, @NonNull ToolbarBinding tool) {
    this.rootView = rootView;
    this.broadcastId = broadcastId;
    this.leaveBtn = leaveBtn;
    this.numberOfChannel = numberOfChannel;
    this.samplingRate = samplingRate;
    this.tool = tool;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAuracastassistantinfoBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAuracastassistantinfoBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_auracastassistantinfo, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAuracastassistantinfoBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.broadcast_id;
      TextView broadcastId = rootView.findViewById(id);
      if (broadcastId == null) {
        break missingId;
      }

      id = R.id.leave_btn;
      Button leaveBtn = rootView.findViewById(id);
      if (leaveBtn == null) {
        break missingId;
      }

      id = R.id.number_of_channel;
      TextView numberOfChannel = rootView.findViewById(id);
      if (numberOfChannel == null) {
        break missingId;
      }

      id = R.id.sampling_rate;
      TextView samplingRate = rootView.findViewById(id);
      if (samplingRate == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      return new ActivityAuracastassistantinfoBinding((LinearLayout) rootView, broadcastId,
          leaveBtn, numberOfChannel, samplingRate, binding_tool);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
