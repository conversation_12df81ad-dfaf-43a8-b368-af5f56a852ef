<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_eq" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_eq.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_eq_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="662" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/activity_eq_0" include="toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="12" endOffset="9"/></Target><Target id="@+id/eq_all_index" view="Spinner"><Expressions/><location startLine="52" startOffset="12" endLine="61" endOffset="21"/></Target><Target id="@+id/btnBandTotal1" view="Button"><Expressions/><location startLine="180" startOffset="12" endLine="186" endOffset="48"/></Target><Target id="@+id/btnBandTotal2" view="Button"><Expressions/><location startLine="187" startOffset="12" endLine="193" endOffset="48"/></Target><Target id="@+id/btnBandTotal3" view="Button"><Expressions/><location startLine="194" startOffset="12" endLine="200" endOffset="48"/></Target><Target id="@+id/btnBandTotal4" view="Button"><Expressions/><location startLine="201" startOffset="12" endLine="207" endOffset="48"/></Target><Target id="@+id/btnBandTotal5" view="Button"><Expressions/><location startLine="208" startOffset="12" endLine="214" endOffset="48"/></Target><Target id="@+id/btnBandTotal6" view="Button"><Expressions/><location startLine="215" startOffset="12" endLine="221" endOffset="48"/></Target><Target id="@+id/btnBandTotal7" view="Button"><Expressions/><location startLine="222" startOffset="12" endLine="228" endOffset="48"/></Target><Target id="@+id/btnBandTotal8" view="Button"><Expressions/><location startLine="229" startOffset="12" endLine="235" endOffset="48"/></Target><Target id="@+id/btnBandTotal9" view="Button"><Expressions/><location startLine="236" startOffset="12" endLine="242" endOffset="48"/></Target><Target id="@+id/btnBandTotal10" view="Button"><Expressions/><location startLine="243" startOffset="12" endLine="249" endOffset="49"/></Target><Target id="@+id/editTextFreq0" view="EditText"><Expressions/><location startLine="293" startOffset="28" endLine="299" endOffset="52"/></Target><Target id="@+id/seekBar0" view="SeekBar"><Expressions/><location startLine="300" startOffset="28" endLine="305" endOffset="59"/></Target><Target id="@+id/editTextGain0" view="EditText"><Expressions/><location startLine="306" startOffset="28" endLine="313" endOffset="49"/></Target><Target id="@+id/editTextQ0" view="EditText"><Expressions/><location startLine="314" startOffset="28" endLine="320" endOffset="50"/></Target><Target id="@+id/editTextFreq1" view="EditText"><Expressions/><location startLine="330" startOffset="28" endLine="336" endOffset="52"/></Target><Target id="@+id/seekBar1" view="SeekBar"><Expressions/><location startLine="338" startOffset="28" endLine="343" endOffset="59"/></Target><Target id="@+id/editTextGain1" view="EditText"><Expressions/><location startLine="345" startOffset="28" endLine="352" endOffset="49"/></Target><Target id="@+id/editTextQ1" view="EditText"><Expressions/><location startLine="353" startOffset="28" endLine="359" endOffset="50"/></Target><Target id="@+id/editTextFreq2" view="EditText"><Expressions/><location startLine="368" startOffset="28" endLine="374" endOffset="52"/></Target><Target id="@+id/seekBar2" view="SeekBar"><Expressions/><location startLine="376" startOffset="28" endLine="381" endOffset="59"/></Target><Target id="@+id/editTextGain2" view="EditText"><Expressions/><location startLine="382" startOffset="28" endLine="389" endOffset="49"/></Target><Target id="@+id/editTextQ2" view="EditText"><Expressions/><location startLine="390" startOffset="28" endLine="396" endOffset="50"/></Target><Target id="@+id/editTextFreq3" view="EditText"><Expressions/><location startLine="405" startOffset="28" endLine="411" endOffset="52"/></Target><Target id="@+id/seekBar3" view="SeekBar"><Expressions/><location startLine="413" startOffset="28" endLine="418" endOffset="59"/></Target><Target id="@+id/editTextGain3" view="EditText"><Expressions/><location startLine="419" startOffset="28" endLine="426" endOffset="49"/></Target><Target id="@+id/editTextQ3" view="EditText"><Expressions/><location startLine="427" startOffset="28" endLine="433" endOffset="50"/></Target><Target id="@+id/editTextFreq4" view="EditText"><Expressions/><location startLine="442" startOffset="28" endLine="448" endOffset="52"/></Target><Target id="@+id/seekBar4" view="SeekBar"><Expressions/><location startLine="450" startOffset="28" endLine="455" endOffset="59"/></Target><Target id="@+id/editTextGain4" view="EditText"><Expressions/><location startLine="456" startOffset="28" endLine="463" endOffset="49"/></Target><Target id="@+id/editTextQ4" view="EditText"><Expressions/><location startLine="464" startOffset="28" endLine="470" endOffset="50"/></Target><Target id="@+id/editTextFreq5" view="EditText"><Expressions/><location startLine="479" startOffset="28" endLine="485" endOffset="53"/></Target><Target id="@+id/seekBar5" view="SeekBar"><Expressions/><location startLine="487" startOffset="28" endLine="492" endOffset="59"/></Target><Target id="@+id/editTextGain5" view="EditText"><Expressions/><location startLine="493" startOffset="28" endLine="500" endOffset="49"/></Target><Target id="@+id/editTextQ5" view="EditText"><Expressions/><location startLine="501" startOffset="28" endLine="507" endOffset="50"/></Target><Target id="@+id/editTextFreq6" view="EditText"><Expressions/><location startLine="516" startOffset="28" endLine="522" endOffset="53"/></Target><Target id="@+id/seekBar6" view="SeekBar"><Expressions/><location startLine="523" startOffset="28" endLine="528" endOffset="59"/></Target><Target id="@+id/editTextGain6" view="EditText"><Expressions/><location startLine="529" startOffset="28" endLine="536" endOffset="49"/></Target><Target id="@+id/editTextQ6" view="EditText"><Expressions/><location startLine="537" startOffset="28" endLine="543" endOffset="50"/></Target><Target id="@+id/editTextFreq7" view="EditText"><Expressions/><location startLine="553" startOffset="28" endLine="559" endOffset="53"/></Target><Target id="@+id/seekBar7" view="SeekBar"><Expressions/><location startLine="561" startOffset="28" endLine="566" endOffset="59"/></Target><Target id="@+id/editTextGain7" view="EditText"><Expressions/><location startLine="567" startOffset="28" endLine="574" endOffset="49"/></Target><Target id="@+id/editTextQ7" view="EditText"><Expressions/><location startLine="575" startOffset="28" endLine="581" endOffset="50"/></Target><Target id="@+id/editTextFreq8" view="EditText"><Expressions/><location startLine="591" startOffset="28" endLine="597" endOffset="53"/></Target><Target id="@+id/seekBar8" view="SeekBar"><Expressions/><location startLine="598" startOffset="28" endLine="603" endOffset="59"/></Target><Target id="@+id/editTextGain8" view="EditText"><Expressions/><location startLine="604" startOffset="28" endLine="611" endOffset="49"/></Target><Target id="@+id/editTextQ8" view="EditText"><Expressions/><location startLine="612" startOffset="28" endLine="618" endOffset="50"/></Target><Target id="@+id/editTextFreq9" view="EditText"><Expressions/><location startLine="628" startOffset="28" endLine="634" endOffset="54"/></Target><Target id="@+id/seekBar9" view="SeekBar"><Expressions/><location startLine="635" startOffset="28" endLine="640" endOffset="59"/></Target><Target id="@+id/editTextGain9" view="EditText"><Expressions/><location startLine="641" startOffset="28" endLine="648" endOffset="49"/></Target><Target id="@+id/editTextQ9" view="EditText"><Expressions/><location startLine="649" startOffset="28" endLine="655" endOffset="50"/></Target></Targets></Layout>