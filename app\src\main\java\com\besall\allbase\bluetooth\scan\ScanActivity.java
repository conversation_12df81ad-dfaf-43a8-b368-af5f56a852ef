package com.besall.allbase.bluetooth.scan;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Build;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ListView;
import android.widget.TextView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.bes.bessdk.connect.BleConnector;
import com.bes.bessdk.connect.SppConnector;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.scan.ScanManager;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.adapter.DeviceAdapter;
import com.besall.allbase.common.manager.PermissionManager;
import com.besall.allbase.view.base.BaseActivity;

import java.io.Serializable;
import java.util.ArrayList;

public class ScanActivity extends BaseActivity<IScanActivity, ScanPresenter> implements IScanActivity, SwipeRefreshLayout.OnRefreshListener, AdapterView.OnItemClickListener, ScanManager.ScanListener {

    private final String TAG = getClass().getSimpleName();

    private static ScanActivity instance;
    public DeviceProtocol deviceProtocol;
    private boolean isMultipleDevices = false;
    private ListView mDevices;
    SwipeRefreshLayout mSwipeRefresh;
    private DeviceAdapter mAdapter;
    private Menu mMenu;

    @Override
    protected ScanPresenter createPresenter() {
        return new ScanPresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        Intent receiveIntent = getIntent();
        isMultipleDevices = receiveIntent.getBooleanExtra(BluetoothConstants.Scan.BES_SCAN_IS_MULTIPLE_DEVICES, false);
        //得到Intent
        if (receiveIntent.getIntExtra(BluetoothConstants.Scan.BES_SCAN, 0) == BluetoothConstants.Scan.SCAN_BLE) {
            deviceProtocol = DeviceProtocol.PROTOCOL_BLE;
        } else if (receiveIntent.getIntExtra(BluetoothConstants.Scan.BES_SCAN, 0) == BluetoothConstants.Scan.SCAN_SPP) {
            deviceProtocol = DeviceProtocol.PROTOCOL_SPP;
        } else if (receiveIntent.getIntExtra(BluetoothConstants.Scan.BES_SCAN, 0) == BluetoothConstants.Scan.SCAN_USB) {
            isMultipleDevices = false;
            deviceProtocol = DeviceProtocol.PROTOCOL_USB;
        } else {

           return;
        }

    }

    @Override
    protected int getContentViewId() {
        return R.layout.act_scan;
    }

    @Override
    protected void bindView() {
        mAdapter = new DeviceAdapter(this, deviceProtocol);
        mDevices = (ListView)findViewById(R.id.devices);
        mSwipeRefresh = (SwipeRefreshLayout)findViewById(R.id.swipe_refresh);
        tv_title = (TextView)findViewById(R.id.tv_title);
    }

    @Override
    protected void initView() {
        mAdapter.setIsMultipleDevices(isMultipleDevices);
        mDevices.setAdapter(mAdapter);
        mDevices.setOnItemClickListener(this);
        mSwipeRefresh.setOnRefreshListener(this);
        inittoolbar("");
        if (deviceProtocol == DeviceProtocol.PROTOCOL_BLE) {
            tv_title.setText(R.string.activity_bluetooth_scan);
        } else if (deviceProtocol == DeviceProtocol.PROTOCOL_SPP) {
            tv_title.setText(R.string.activity_classics_devices_scan);
        } else if (deviceProtocol == DeviceProtocol.PROTOCOL_USB) {
            tv_title.setText(R.string.activity_USB_scan);
        }
        startScan();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {

        } else {
            PermissionManager.getInstance().requestPermissions(this, null, PermissionManager.Permission.Storage.WRITE_EXTERNAL_STORAGE);
        }
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        stopScan();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                Log.i(TAG, "onOptionsItemSelected: -------------");
                stopScan();
                if (deviceProtocol != DeviceProtocol.PROTOCOL_USB && mAdapter.getSelectedDevices().size() > 0 && isMultipleDevices) {
                    Intent intent = new Intent();
                    intent.putExtra(BluetoothConstants.Scan.BES_SCAN_RESULT, (Serializable) mAdapter.getSelectedDevices());
                    setResult(RESULT_OK, intent);
                }
                finish();
                break;
            case R.id.menu_select:
                if (mAdapter.getSelectedDevices().size() > 0 && isMultipleDevices) {
                    stopScan();
                    Intent intent = new Intent();
                    intent.putExtra(BluetoothConstants.Scan.BES_SCAN_RESULT, (Serializable) mAdapter.getSelectedDevices());
                    setResult(RESULT_OK, intent);
                    finish();
                }
                break;
            default:
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        if (isMultipleDevices) {
            getMenuInflater().inflate(R.menu.menu_select, menu);
            mMenu = menu;
        }
        return true;
    }

    @SuppressLint("RestrictedApi")
    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        if (deviceProtocol == DeviceProtocol.PROTOCOL_USB) {
            Intent intent = new Intent();
            intent.putExtra(BluetoothConstants.Scan.BES_SCAN_RESULT, mAdapter.getItem(position));
            setResult(RESULT_OK, intent);
            finish();
            return;
        }
        if (isMultipleDevices) {
            mAdapter.addSelectState(position);
            int size = mAdapter.getSelectedDevices().size();
            MenuItem item = mMenu.findItem(R.id.menu_select);
            item.setIcon(size == 0 ? R.drawable.ota_top_nor : R.drawable.ota_top_sele);
        } else {
            mAdapter.addSelectState(position);
            Intent intent = new Intent();
            intent.putExtra(BluetoothConstants.Scan.BES_SCAN_RESULT, mAdapter.getItem(position));
            setResult(RESULT_OK, intent);
            finish();
        }
    }

    @Override
    public void onRefresh() {
        mSwipeRefresh.setRefreshing(false);
        if (mAdapter != null) {
            mAdapter.clear();
            if (isMultipleDevices) {
                MenuItem item = mMenu.findItem(R.id.menu_select);
                item.setIcon(R.drawable.ota_top_nor);
            }
        }

        startScan();
    }

    @Override
    public void onScanResult(HmDevice scannedDevice) {
        addDevice(scannedDevice, false);
    }

    private void addDevice(HmDevice hmDevice, boolean before) {
        if (before) {
            mAdapter.addBefore(hmDevice, hmDevice.getRssi());
        } else {
            mAdapter.add(hmDevice, hmDevice.getRssi());
        }
    }

    @Override
    public void onDeviceOffline(HmDevice device) {

    }

    @Override
    public void onScanFailed(String message) {
        startScan();
    }

    private void startScan() {
//        if (mAdapter != null) {
//            mAdapter.clear();
//        }
        if (deviceProtocol == DeviceProtocol.PROTOCOL_USB) {
            mPresenter.starScanWithScanType(instance, deviceProtocol);
            return;
        }
        ArrayList<HmDevice> curConSppDevices = SppConnector.getsConnector(instance, null).getCurConnectDevices();
        for (int i = 0; i < curConSppDevices.size(); i ++) {
            HmDevice hmDevice = curConSppDevices.get(i);
            hmDevice.setRssi(1000);
            addDevice(hmDevice, true);
        }
        ArrayList<HmDevice> curConBleDevices = BleConnector.getsConnector(instance, null, null).getCurConnectDevices();
        for (int i = 0; i < curConBleDevices.size(); i ++) {
            HmDevice hmDevice = curConBleDevices.get(i);
            hmDevice.setRssi(1000);
            addDevice(hmDevice, true);
        }
        mPresenter.starScanWithScanType(instance, deviceProtocol);
    }

    private void stopScan() {
        mPresenter.stopScanWithScanType(instance, deviceProtocol);
    }
}
