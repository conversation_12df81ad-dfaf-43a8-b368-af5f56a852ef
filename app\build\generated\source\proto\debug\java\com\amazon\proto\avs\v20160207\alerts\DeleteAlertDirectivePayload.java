// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: alertsDeleteAlertDirectivePayload.proto

package com.amazon.proto.avs.v20160207.alerts;

public final class DeleteAlertDirectivePayload {
  private DeleteAlertDirectivePayload() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface DeleteAlertDirectivePayloadProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:alerts.DeleteAlertDirectivePayloadProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string token = 1;</code>
     * @return The token.
     */
    java.lang.String getToken();
    /**
     * <code>string token = 1;</code>
     * @return The bytes for token.
     */
    com.google.protobuf.ByteString
        getTokenBytes();
  }
  /**
   * Protobuf type {@code alerts.DeleteAlertDirectivePayloadProto}
   */
  public static final class DeleteAlertDirectivePayloadProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:alerts.DeleteAlertDirectivePayloadProto)
      DeleteAlertDirectivePayloadProtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DeleteAlertDirectivePayloadProto.newBuilder() to construct.
    private DeleteAlertDirectivePayloadProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DeleteAlertDirectivePayloadProto() {
      token_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DeleteAlertDirectivePayloadProto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.internal_static_alerts_DeleteAlertDirectivePayloadProto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.internal_static_alerts_DeleteAlertDirectivePayloadProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto.class, com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto.Builder.class);
    }

    public static final int TOKEN_FIELD_NUMBER = 1;
    private volatile java.lang.Object token_;
    /**
     * <code>string token = 1;</code>
     * @return The token.
     */
    @java.lang.Override
    public java.lang.String getToken() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        token_ = s;
        return s;
      }
    }
    /**
     * <code>string token = 1;</code>
     * @return The bytes for token.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTokenBytes() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        token_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(token_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, token_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(token_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, token_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto)) {
        return super.equals(obj);
      }
      com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto other = (com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto) obj;

      if (!getToken()
          .equals(other.getToken())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TOKEN_FIELD_NUMBER;
      hash = (53 * hash) + getToken().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code alerts.DeleteAlertDirectivePayloadProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:alerts.DeleteAlertDirectivePayloadProto)
        com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.internal_static_alerts_DeleteAlertDirectivePayloadProto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.internal_static_alerts_DeleteAlertDirectivePayloadProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto.class, com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto.Builder.class);
      }

      // Construct using com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        token_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.internal_static_alerts_DeleteAlertDirectivePayloadProto_descriptor;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto getDefaultInstanceForType() {
        return com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto build() {
        com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto buildPartial() {
        com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto result = new com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto(this);
        result.token_ = token_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto) {
          return mergeFrom((com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto other) {
        if (other == com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto.getDefaultInstance()) return this;
        if (!other.getToken().isEmpty()) {
          token_ = other.token_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                token_ = input.readStringRequireUtf8();

                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private java.lang.Object token_ = "";
      /**
       * <code>string token = 1;</code>
       * @return The token.
       */
      public java.lang.String getToken() {
        java.lang.Object ref = token_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          token_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string token = 1;</code>
       * @return The bytes for token.
       */
      public com.google.protobuf.ByteString
          getTokenBytes() {
        java.lang.Object ref = token_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string token = 1;</code>
       * @param value The token to set.
       * @return This builder for chaining.
       */
      public Builder setToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        token_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string token = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearToken() {
        
        token_ = getDefaultInstance().getToken();
        onChanged();
        return this;
      }
      /**
       * <code>string token = 1;</code>
       * @param value The bytes for token to set.
       * @return This builder for chaining.
       */
      public Builder setTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        token_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:alerts.DeleteAlertDirectivePayloadProto)
    }

    // @@protoc_insertion_point(class_scope:alerts.DeleteAlertDirectivePayloadProto)
    private static final com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto();
    }

    public static com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DeleteAlertDirectivePayloadProto>
        PARSER = new com.google.protobuf.AbstractParser<DeleteAlertDirectivePayloadProto>() {
      @java.lang.Override
      public DeleteAlertDirectivePayloadProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<DeleteAlertDirectivePayloadProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DeleteAlertDirectivePayloadProto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alerts.DeleteAlertDirectivePayload.DeleteAlertDirectivePayloadProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_alerts_DeleteAlertDirectivePayloadProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_alerts_DeleteAlertDirectivePayloadProto_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\'alertsDeleteAlertDirectivePayload.prot" +
      "o\022\006alerts\"1\n DeleteAlertDirectivePayload" +
      "Proto\022\r\n\005token\030\001 \001(\tBD\n%com.amazon.proto" +
      ".avs.v20160207.alertsB\033DeleteAlertDirect" +
      "ivePayloadb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_alerts_DeleteAlertDirectivePayloadProto_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_alerts_DeleteAlertDirectivePayloadProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_alerts_DeleteAlertDirectivePayloadProto_descriptor,
        new java.lang.String[] { "Token", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
