// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RelativeLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ConfirmDialogBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final Button dialogCancel;

  @NonNull
  public final Button dialogConfirm;

  @NonNull
  public final EditText dialogText;

  private ConfirmDialogBinding(@NonNull RelativeLayout rootView, @NonNull Button dialogCancel,
      @NonNull Button dialogConfirm, @NonNull EditText dialogText) {
    this.rootView = rootView;
    this.dialogCancel = dialogCancel;
    this.dialogConfirm = dialogConfirm;
    this.dialogText = dialogText;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ConfirmDialogBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ConfirmDialogBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.confirm_dialog, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ConfirmDialogBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.dialog_cancel;
      Button dialogCancel = rootView.findViewById(id);
      if (dialogCancel == null) {
        break missingId;
      }

      id = R.id.dialog_confirm;
      Button dialogConfirm = rootView.findViewById(id);
      if (dialogConfirm == null) {
        break missingId;
      }

      id = R.id.dialog_text;
      EditText dialogText = rootView.findViewById(id);
      if (dialogText == null) {
        break missingId;
      }

      return new ConfirmDialogBinding((RelativeLayout) rootView, dialogCancel, dialogConfirm,
          dialogText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
