<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        tools:ignore="MissingConstraints">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="42dp"
            tools:ignore="MissingConstraints"
            android:orientation="horizontal"
            >

        </LinearLayout>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:text="@string/current_version"
            android:textSize="16sp"
            android:textColor="@color/title_color"
            android:fontFamily="@font/sanshan_bold"
            >
        </TextView>

        <EditText
            android:id="@+id/device_version"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_marginTop="8dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:text="@string/old_ota_ways_version_tips"
            android:textSize="14sp"
            android:textColor="@color/title_color_light"
            android:fontFamily="@font/sanshan_normal"
            android:focusable="false"
            android:background="#00000000"
            >
        </EditText>
        
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="12dp"
            android:background="@color/title_color_light"
            android:alpha="0.3"
            >
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="41dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="25dp"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="14dp"
                android:text="@string/current_ota_file"
                android:textSize="16sp"
                android:textColor="@color/title_color"
                android:fontFamily="@font/sanshan_bold"
                >
            </TextView>

            <Button
                android:id="@+id/ota_file_tips_btn"
                android:layout_gravity="center"
                android:background="@null"
                android:layout_width="110dp"
                android:layout_height="40dp"
                android:textSize="8sp"
                android:textColor="@color/activityText"
                android:textAllCaps="false"
                android:text="@string/findOtaFileTips"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="right">
                <Button
                    android:id="@+id/choose_file"
                    android:layout_width="61dp"
                    android:layout_height="32dp"
                    android:layout_marginRight="20dp"
                    android:layout_marginTop="9dp"
                    android:gravity="center"
                    android:background="@drawable/btn_choose"
                    android:text="@string/choose"
                    android:textAllCaps="false"
                    android:textColor="@color/white">

                </Button>
                
            </LinearLayout>

        </LinearLayout>

<!--        <TextView-->
<!--            android:id="@+id/file_path"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="69dp"-->
<!--            android:layout_marginTop="8dp"-->
<!--            android:layout_marginLeft="20dp"-->
<!--            android:layout_marginRight="20dp"-->
<!--            android:text="&#45;&#45;"-->
<!--            android:textSize="14dp"-->
<!--            android:textColor="@color/title_color_light"-->
<!--            android:fontFamily="@font/sanshan_normal"-->
<!--            >-->
<!--        </TextView>-->


        <ListView
            android:id="@+id/file_path"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginTop="8dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"/>

    </LinearLayout>



</androidx.constraintlayout.widget.ConstraintLayout>