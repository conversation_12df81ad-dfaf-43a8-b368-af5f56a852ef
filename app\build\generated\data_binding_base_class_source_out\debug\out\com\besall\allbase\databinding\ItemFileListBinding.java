// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import com.besall.allbase.common.utils.SlideLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemFileListBinding implements ViewBinding {
  @NonNull
  private final SlideLayout rootView;

  @NonNull
  public final LinearLayout content;

  @NonNull
  public final TextView deleteButton;

  @NonNull
  public final TextView fileName;

  @NonNull
  public final ImageView itemImageview;

  private ItemFileListBinding(@NonNull SlideLayout rootView, @NonNull LinearLayout content,
      @NonNull TextView deleteButton, @NonNull TextView fileName,
      @NonNull ImageView itemImageview) {
    this.rootView = rootView;
    this.content = content;
    this.deleteButton = deleteButton;
    this.fileName = fileName;
    this.itemImageview = itemImageview;
  }

  @Override
  @NonNull
  public SlideLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemFileListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemFileListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_file_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemFileListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.content;
      LinearLayout content = rootView.findViewById(id);
      if (content == null) {
        break missingId;
      }

      id = R.id.delete_button;
      TextView deleteButton = rootView.findViewById(id);
      if (deleteButton == null) {
        break missingId;
      }

      id = R.id.file_name;
      TextView fileName = rootView.findViewById(id);
      if (fileName == null) {
        break missingId;
      }

      id = R.id.item_imageview;
      ImageView itemImageview = rootView.findViewById(id);
      if (itemImageview == null) {
        break missingId;
      }

      return new ItemFileListBinding((SlideLayout) rootView, content, deleteButton, fileName,
          itemImageview);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
