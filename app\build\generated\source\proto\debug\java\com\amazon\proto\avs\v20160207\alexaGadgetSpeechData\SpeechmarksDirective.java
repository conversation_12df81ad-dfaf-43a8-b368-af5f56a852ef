// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: alexaGadgetSpeechDataSpeechmarksDirective.proto

package com.amazon.proto.avs.v20160207.alexaGadgetSpeechData;

public final class SpeechmarksDirective {
  private SpeechmarksDirective() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SpeechmarksDirectiveProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:alexaGadgetSpeechData.SpeechmarksDirectiveProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive directive = 1;</code>
     * @return Whether the directive field is set.
     */
    boolean hasDirective();
    /**
     * <code>.alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive directive = 1;</code>
     * @return The directive.
     */
    com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive getDirective();
    /**
     * <code>.alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive directive = 1;</code>
     */
    com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.DirectiveOrBuilder getDirectiveOrBuilder();
  }
  /**
   * Protobuf type {@code alexaGadgetSpeechData.SpeechmarksDirectiveProto}
   */
  public static final class SpeechmarksDirectiveProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:alexaGadgetSpeechData.SpeechmarksDirectiveProto)
      SpeechmarksDirectiveProtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SpeechmarksDirectiveProto.newBuilder() to construct.
    private SpeechmarksDirectiveProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SpeechmarksDirectiveProto() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SpeechmarksDirectiveProto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.class, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Builder.class);
    }

    public interface DirectiveOrBuilder extends
        // @@protoc_insertion_point(interface_extends:alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>.alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto payload = 2;</code>
       * @return Whether the payload field is set.
       */
      boolean hasPayload();
      /**
       * <code>.alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto payload = 2;</code>
       * @return The payload.
       */
      com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto getPayload();
      /**
       * <code>.alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto payload = 2;</code>
       */
      com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProtoOrBuilder getPayloadOrBuilder();

      /**
       * <code>.header.DirectiveHeaderProto header = 1;</code>
       * @return Whether the header field is set.
       */
      boolean hasHeader();
      /**
       * <code>.header.DirectiveHeaderProto header = 1;</code>
       * @return The header.
       */
      com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto getHeader();
      /**
       * <code>.header.DirectiveHeaderProto header = 1;</code>
       */
      com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProtoOrBuilder getHeaderOrBuilder();
    }
    /**
     * Protobuf type {@code alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive}
     */
    public static final class Directive extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive)
        DirectiveOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use Directive.newBuilder() to construct.
      private Directive(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private Directive() {
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new Directive();
      }

      @java.lang.Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_Directive_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_Directive_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive.class, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive.Builder.class);
      }

      public static final int PAYLOAD_FIELD_NUMBER = 2;
      private com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto payload_;
      /**
       * <code>.alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto payload = 2;</code>
       * @return Whether the payload field is set.
       */
      @java.lang.Override
      public boolean hasPayload() {
        return payload_ != null;
      }
      /**
       * <code>.alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto payload = 2;</code>
       * @return The payload.
       */
      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto getPayload() {
        return payload_ == null ? com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.getDefaultInstance() : payload_;
      }
      /**
       * <code>.alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto payload = 2;</code>
       */
      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProtoOrBuilder getPayloadOrBuilder() {
        return getPayload();
      }

      public static final int HEADER_FIELD_NUMBER = 1;
      private com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto header_;
      /**
       * <code>.header.DirectiveHeaderProto header = 1;</code>
       * @return Whether the header field is set.
       */
      @java.lang.Override
      public boolean hasHeader() {
        return header_ != null;
      }
      /**
       * <code>.header.DirectiveHeaderProto header = 1;</code>
       * @return The header.
       */
      @java.lang.Override
      public com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto getHeader() {
        return header_ == null ? com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.getDefaultInstance() : header_;
      }
      /**
       * <code>.header.DirectiveHeaderProto header = 1;</code>
       */
      @java.lang.Override
      public com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProtoOrBuilder getHeaderOrBuilder() {
        return getHeader();
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (header_ != null) {
          output.writeMessage(1, getHeader());
        }
        if (payload_ != null) {
          output.writeMessage(2, getPayload());
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (header_ != null) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, getHeader());
        }
        if (payload_ != null) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, getPayload());
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive)) {
          return super.equals(obj);
        }
        com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive other = (com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive) obj;

        if (hasPayload() != other.hasPayload()) return false;
        if (hasPayload()) {
          if (!getPayload()
              .equals(other.getPayload())) return false;
        }
        if (hasHeader() != other.hasHeader()) return false;
        if (hasHeader()) {
          if (!getHeader()
              .equals(other.getHeader())) return false;
        }
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        if (hasPayload()) {
          hash = (37 * hash) + PAYLOAD_FIELD_NUMBER;
          hash = (53 * hash) + getPayload().hashCode();
        }
        if (hasHeader()) {
          hash = (37 * hash) + HEADER_FIELD_NUMBER;
          hash = (53 * hash) + getHeader().hashCode();
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive)
          com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.DirectiveOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_Directive_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_Directive_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive.class, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive.Builder.class);
        }

        // Construct using com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          if (payloadBuilder_ == null) {
            payload_ = null;
          } else {
            payload_ = null;
            payloadBuilder_ = null;
          }
          if (headerBuilder_ == null) {
            header_ = null;
          } else {
            header_ = null;
            headerBuilder_ = null;
          }
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_Directive_descriptor;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive getDefaultInstanceForType() {
          return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive.getDefaultInstance();
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive build() {
          com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive buildPartial() {
          com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive result = new com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive(this);
          if (payloadBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = payloadBuilder_.build();
          }
          if (headerBuilder_ == null) {
            result.header_ = header_;
          } else {
            result.header_ = headerBuilder_.build();
          }
          onBuilt();
          return result;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive) {
            return mergeFrom((com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive other) {
          if (other == com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive.getDefaultInstance()) return this;
          if (other.hasPayload()) {
            mergePayload(other.getPayload());
          }
          if (other.hasHeader()) {
            mergeHeader(other.getHeader());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 10: {
                  input.readMessage(
                      getHeaderFieldBuilder().getBuilder(),
                      extensionRegistry);

                  break;
                } // case 10
                case 18: {
                  input.readMessage(
                      getPayloadFieldBuilder().getBuilder(),
                      extensionRegistry);

                  break;
                } // case 18
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }

        private com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto payload_;
        private com.google.protobuf.SingleFieldBuilderV3<
            com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.Builder, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProtoOrBuilder> payloadBuilder_;
        /**
         * <code>.alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto payload = 2;</code>
         * @return Whether the payload field is set.
         */
        public boolean hasPayload() {
          return payloadBuilder_ != null || payload_ != null;
        }
        /**
         * <code>.alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto payload = 2;</code>
         * @return The payload.
         */
        public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto getPayload() {
          if (payloadBuilder_ == null) {
            return payload_ == null ? com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.getDefaultInstance() : payload_;
          } else {
            return payloadBuilder_.getMessage();
          }
        }
        /**
         * <code>.alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto payload = 2;</code>
         */
        public Builder setPayload(com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto value) {
          if (payloadBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            payload_ = value;
            onChanged();
          } else {
            payloadBuilder_.setMessage(value);
          }

          return this;
        }
        /**
         * <code>.alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto payload = 2;</code>
         */
        public Builder setPayload(
            com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.Builder builderForValue) {
          if (payloadBuilder_ == null) {
            payload_ = builderForValue.build();
            onChanged();
          } else {
            payloadBuilder_.setMessage(builderForValue.build());
          }

          return this;
        }
        /**
         * <code>.alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto payload = 2;</code>
         */
        public Builder mergePayload(com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto value) {
          if (payloadBuilder_ == null) {
            if (payload_ != null) {
              payload_ =
                com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.newBuilder(payload_).mergeFrom(value).buildPartial();
            } else {
              payload_ = value;
            }
            onChanged();
          } else {
            payloadBuilder_.mergeFrom(value);
          }

          return this;
        }
        /**
         * <code>.alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto payload = 2;</code>
         */
        public Builder clearPayload() {
          if (payloadBuilder_ == null) {
            payload_ = null;
            onChanged();
          } else {
            payload_ = null;
            payloadBuilder_ = null;
          }

          return this;
        }
        /**
         * <code>.alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto payload = 2;</code>
         */
        public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.Builder getPayloadBuilder() {
          
          onChanged();
          return getPayloadFieldBuilder().getBuilder();
        }
        /**
         * <code>.alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto payload = 2;</code>
         */
        public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProtoOrBuilder getPayloadOrBuilder() {
          if (payloadBuilder_ != null) {
            return payloadBuilder_.getMessageOrBuilder();
          } else {
            return payload_ == null ?
                com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.getDefaultInstance() : payload_;
          }
        }
        /**
         * <code>.alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto payload = 2;</code>
         */
        private com.google.protobuf.SingleFieldBuilderV3<
            com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.Builder, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProtoOrBuilder> 
            getPayloadFieldBuilder() {
          if (payloadBuilder_ == null) {
            payloadBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
                com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.Builder, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProtoOrBuilder>(
                    getPayload(),
                    getParentForChildren(),
                    isClean());
            payload_ = null;
          }
          return payloadBuilder_;
        }

        private com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto header_;
        private com.google.protobuf.SingleFieldBuilderV3<
            com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto, com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.Builder, com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProtoOrBuilder> headerBuilder_;
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         * @return Whether the header field is set.
         */
        public boolean hasHeader() {
          return headerBuilder_ != null || header_ != null;
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         * @return The header.
         */
        public com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto getHeader() {
          if (headerBuilder_ == null) {
            return header_ == null ? com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.getDefaultInstance() : header_;
          } else {
            return headerBuilder_.getMessage();
          }
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        public Builder setHeader(com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto value) {
          if (headerBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            header_ = value;
            onChanged();
          } else {
            headerBuilder_.setMessage(value);
          }

          return this;
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        public Builder setHeader(
            com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.Builder builderForValue) {
          if (headerBuilder_ == null) {
            header_ = builderForValue.build();
            onChanged();
          } else {
            headerBuilder_.setMessage(builderForValue.build());
          }

          return this;
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        public Builder mergeHeader(com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto value) {
          if (headerBuilder_ == null) {
            if (header_ != null) {
              header_ =
                com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.newBuilder(header_).mergeFrom(value).buildPartial();
            } else {
              header_ = value;
            }
            onChanged();
          } else {
            headerBuilder_.mergeFrom(value);
          }

          return this;
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        public Builder clearHeader() {
          if (headerBuilder_ == null) {
            header_ = null;
            onChanged();
          } else {
            header_ = null;
            headerBuilder_ = null;
          }

          return this;
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        public com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.Builder getHeaderBuilder() {
          
          onChanged();
          return getHeaderFieldBuilder().getBuilder();
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        public com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProtoOrBuilder getHeaderOrBuilder() {
          if (headerBuilder_ != null) {
            return headerBuilder_.getMessageOrBuilder();
          } else {
            return header_ == null ?
                com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.getDefaultInstance() : header_;
          }
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        private com.google.protobuf.SingleFieldBuilderV3<
            com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto, com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.Builder, com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProtoOrBuilder> 
            getHeaderFieldBuilder() {
          if (headerBuilder_ == null) {
            headerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
                com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto, com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.Builder, com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProtoOrBuilder>(
                    getHeader(),
                    getParentForChildren(),
                    isClean());
            header_ = null;
          }
          return headerBuilder_;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive)
      }

      // @@protoc_insertion_point(class_scope:alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive)
      private static final com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive();
      }

      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Directive>
          PARSER = new com.google.protobuf.AbstractParser<Directive>() {
        @java.lang.Override
        public Directive parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Directive> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Directive> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public static final int DIRECTIVE_FIELD_NUMBER = 1;
    private com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive directive_;
    /**
     * <code>.alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive directive = 1;</code>
     * @return Whether the directive field is set.
     */
    @java.lang.Override
    public boolean hasDirective() {
      return directive_ != null;
    }
    /**
     * <code>.alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive directive = 1;</code>
     * @return The directive.
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive getDirective() {
      return directive_ == null ? com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive.getDefaultInstance() : directive_;
    }
    /**
     * <code>.alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive directive = 1;</code>
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.DirectiveOrBuilder getDirectiveOrBuilder() {
      return getDirective();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (directive_ != null) {
        output.writeMessage(1, getDirective());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (directive_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getDirective());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto)) {
        return super.equals(obj);
      }
      com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto other = (com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto) obj;

      if (hasDirective() != other.hasDirective()) return false;
      if (hasDirective()) {
        if (!getDirective()
            .equals(other.getDirective())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasDirective()) {
        hash = (37 * hash) + DIRECTIVE_FIELD_NUMBER;
        hash = (53 * hash) + getDirective().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code alexaGadgetSpeechData.SpeechmarksDirectiveProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:alexaGadgetSpeechData.SpeechmarksDirectiveProto)
        com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.class, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Builder.class);
      }

      // Construct using com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (directiveBuilder_ == null) {
          directive_ = null;
        } else {
          directive_ = null;
          directiveBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_descriptor;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto getDefaultInstanceForType() {
        return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto build() {
        com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto buildPartial() {
        com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto result = new com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto(this);
        if (directiveBuilder_ == null) {
          result.directive_ = directive_;
        } else {
          result.directive_ = directiveBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto) {
          return mergeFrom((com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto other) {
        if (other == com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.getDefaultInstance()) return this;
        if (other.hasDirective()) {
          mergeDirective(other.getDirective());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getDirectiveFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive directive_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive.Builder, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.DirectiveOrBuilder> directiveBuilder_;
      /**
       * <code>.alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive directive = 1;</code>
       * @return Whether the directive field is set.
       */
      public boolean hasDirective() {
        return directiveBuilder_ != null || directive_ != null;
      }
      /**
       * <code>.alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive directive = 1;</code>
       * @return The directive.
       */
      public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive getDirective() {
        if (directiveBuilder_ == null) {
          return directive_ == null ? com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive.getDefaultInstance() : directive_;
        } else {
          return directiveBuilder_.getMessage();
        }
      }
      /**
       * <code>.alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive directive = 1;</code>
       */
      public Builder setDirective(com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive value) {
        if (directiveBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          directive_ = value;
          onChanged();
        } else {
          directiveBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive directive = 1;</code>
       */
      public Builder setDirective(
          com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive.Builder builderForValue) {
        if (directiveBuilder_ == null) {
          directive_ = builderForValue.build();
          onChanged();
        } else {
          directiveBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive directive = 1;</code>
       */
      public Builder mergeDirective(com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive value) {
        if (directiveBuilder_ == null) {
          if (directive_ != null) {
            directive_ =
              com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive.newBuilder(directive_).mergeFrom(value).buildPartial();
          } else {
            directive_ = value;
          }
          onChanged();
        } else {
          directiveBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive directive = 1;</code>
       */
      public Builder clearDirective() {
        if (directiveBuilder_ == null) {
          directive_ = null;
          onChanged();
        } else {
          directive_ = null;
          directiveBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive directive = 1;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive.Builder getDirectiveBuilder() {
        
        onChanged();
        return getDirectiveFieldBuilder().getBuilder();
      }
      /**
       * <code>.alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive directive = 1;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.DirectiveOrBuilder getDirectiveOrBuilder() {
        if (directiveBuilder_ != null) {
          return directiveBuilder_.getMessageOrBuilder();
        } else {
          return directive_ == null ?
              com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive.getDefaultInstance() : directive_;
        }
      }
      /**
       * <code>.alexaGadgetSpeechData.SpeechmarksDirectiveProto.Directive directive = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive.Builder, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.DirectiveOrBuilder> 
          getDirectiveFieldBuilder() {
        if (directiveBuilder_ == null) {
          directiveBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.Directive.Builder, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto.DirectiveOrBuilder>(
                  getDirective(),
                  getParentForChildren(),
                  isClean());
          directive_ = null;
        }
        return directiveBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:alexaGadgetSpeechData.SpeechmarksDirectiveProto)
    }

    // @@protoc_insertion_point(class_scope:alexaGadgetSpeechData.SpeechmarksDirectiveProto)
    private static final com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto();
    }

    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SpeechmarksDirectiveProto>
        PARSER = new com.google.protobuf.AbstractParser<SpeechmarksDirectiveProto>() {
      @java.lang.Override
      public SpeechmarksDirectiveProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SpeechmarksDirectiveProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SpeechmarksDirectiveProto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirective.SpeechmarksDirectiveProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_Directive_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_Directive_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n/alexaGadgetSpeechDataSpeechmarksDirect" +
      "ive.proto\022\025alexaGadgetSpeechData\032\025direct" +
      "iveHeader.proto\0326alexaGadgetSpeechDataSp" +
      "eechmarksDirectivePayload.proto\"\360\001\n\031Spee" +
      "chmarksDirectiveProto\022M\n\tdirective\030\001 \001(\013" +
      "2:.alexaGadgetSpeechData.SpeechmarksDire" +
      "ctiveProto.Directive\032\203\001\n\tDirective\022H\n\007pa" +
      "yload\030\002 \001(\01327.alexaGadgetSpeechData.Spee" +
      "chmarksDirectivePayloadProto\022,\n\006header\030\001" +
      " \001(\0132\034.header.DirectiveHeaderProtoBL\n4co" +
      "m.amazon.proto.avs.v20160207.alexaGadget" +
      "SpeechDataB\024SpeechmarksDirectiveb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.amazon.proto.avs.v20160207.header.DirectiveHeader.getDescriptor(),
          com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.getDescriptor(),
        });
    internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_descriptor,
        new java.lang.String[] { "Directive", });
    internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_Directive_descriptor =
      internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_descriptor.getNestedTypes().get(0);
    internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_Directive_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_alexaGadgetSpeechData_SpeechmarksDirectiveProto_Directive_descriptor,
        new java.lang.String[] { "Payload", "Header", });
    com.amazon.proto.avs.v20160207.header.DirectiveHeader.getDescriptor();
    com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
