<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6"
    >
    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <View
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:layout_marginTop="30dp"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:text="@string/current_device"
        android:textSize="20sp" />

    <TextView
        android:id="@+id/ble_name"
        android:layout_marginTop="10dp"
        android:layout_marginLeft="20dp"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:text="--" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:orientation="horizontal">

        <Button
            android:id="@+id/pick_device"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:background="@drawable/ota_click"
            android:text="@string/pleaseSelectDevice"
            android:textAllCaps="false"
            android:textColor="@color/white"
            />

        <Button
            android:id="@+id/connect_device"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:background="@drawable/ota_click"
            android:text="@string/connect_device"
            android:textAllCaps="false"
            android:textColor="@color/white"
            />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="-10dp"
        android:orientation="horizontal">

        <Button
            android:id="@+id/start_record"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:background="@drawable/ota_click"
            android:text="@string/start_record"
            android:textAllCaps="false"
            android:textColor="@color/white"
            />

        <Button
            android:id="@+id/stop_record"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:background="@drawable/ota_click"
            android:text="@string/stop_record"
            android:textAllCaps="false"
            android:textColor="@color/white"
            />

    </LinearLayout>

    <ListView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="50dp"/>

</LinearLayout>