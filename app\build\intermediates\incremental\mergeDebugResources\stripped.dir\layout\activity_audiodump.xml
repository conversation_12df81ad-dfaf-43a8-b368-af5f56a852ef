<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"

    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6"
    >

    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <include
        android:id="@+id/loginfo"
        layout="@layout/logview"
        android:visibility="gone"/>

    <Chronometer
        android:id="@+id/recordtime"
        android:layout_width="wrap_content"
        android:layout_height="58dp"
        android:layout_gravity="center"
        android:layout_marginTop="40dp"
        android:format="00:00:00"
        android:textColor="@color/ffd8e2ee"
        android:textSize="50sp"
        />

    <ImageView
        android:id="@+id/iv"
        android:layout_gravity="center"
        android:layout_marginTop="19dp"
        android:layout_width="325dp"
        android:layout_height="100dp"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="450dp"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="-450dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/package_receive_text"
            android:layout_width="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_height="30dp"
            android:text="Packet Rx: 0"
            android:textSize="16sp"
            android:textAlignment="center"/>

        <TextView
            android:id="@+id/package_lost_text"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:text="Packet Lost: 100"
            android:textSize="16sp"
            android:textAlignment="center"/>


    </LinearLayout>

    <TextView
        android:id="@+id/device_text"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:text="@string/current_device"
        android:textSize="16sp"
        android:textColor="@color/black"
        android:gravity="center|left"/>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginTop="10dp"
        android:gravity="left"
        android:orientation="vertical"
        >
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/audio_file_type"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <RadioGroup
                android:id="@+id/save_file_type"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/sanshan_normal"
                    android:text="pcm"
                    android:textColor="@color/title_color"
                    android:textSize="14sp" />

                <RadioButton
                    android:id="@+id/file_type_pcm"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:fontFamily="@font/sanshan_normal"
                    android:textColor="@color/title_color"
                    android:textSize="14sp"
                    android:text="wav"
                    />

                <RadioButton
                    android:id="@+id/file_type_wav"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:fontFamily="@font/sanshan_normal"
                    android:textColor="@color/title_color"
                    android:textSize="14sp"
                    android:text="flac"
                    />

                <RadioButton
                    android:id="@+id/file_type_flac"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.1"
                    android:gravity="right"
                    android:fontFamily="@font/sanshan_normal"
                    android:textColor="@color/title_color"
                    android:textSize="14sp"
                    android:text="custom"
                    />

                <RadioButton
                    android:id="@+id/file_type_custom"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />
            </RadioGroup>

            <EditText
                android:id="@+id/et_file_type"
                android:layout_width="60dp"
                android:layout_height="30dp"
                android:hint="type"
                android:background="@color/white"
                android:textColor="@color/black"/>

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="20dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:gravity="left"
        android:orientation="vertical"
        >
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/audio_insert_data_form"/>

        <RadioGroup
            android:id="@+id/insert_data"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/sanshan_normal"
                android:text="MAX(0x7F)"
                android:textColor="@color/title_color"
                android:textSize="14sp" />

            <RadioButton
                android:id="@+id/insert_data_max"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="right"
                android:fontFamily="@font/sanshan_normal"
                android:textColor="@color/title_color"
                android:textSize="14sp"
                android:text="ZERO(0x00)"
                />

            <RadioButton
                android:id="@+id/insert_data_zero"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                />
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="right"
                android:fontFamily="@font/sanshan_normal"
                android:textColor="@color/title_color"
                android:textSize="14sp"
                android:text="LOST"
                />

            <RadioButton
                android:id="@+id/insert_data_lost"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                />
        </RadioGroup>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:orientation="horizontal"
        android:layout_marginLeft="20dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:textSize="18sp"
            android:text="CMD:"
            android:textColor="@color/black"/>

        <EditText
            android:id="@+id/stream_start_text"
            android:layout_width="150dp"
            android:layout_height="50dp"
            android:textAlignment="center"
            android:layout_marginLeft="10dp"
            android:textColor="@color/black"
            android:background="@color/white"
            android:text="00000003"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:layout_marginLeft="86dp"
        android:layout_marginRight="86dp"
        android:gravity="center"
        >
        <Button
            android:id="@+id/start_audio_dump"
            android:layout_width="87dp"
            android:layout_height="86dp"
            android:layout_marginRight="28dp"
            android:background="@drawable/rectangle_rec"/>

        <Button
            android:id="@+id/stop_audio_dump"
            android:layout_width="87dp"
            android:layout_height="86dp"

            android:background="@drawable/rectangle_recstop"/>
    </LinearLayout>

    <Button
        android:id="@+id/spp_stop"
        android:layout_width="242dp"
        android:layout_height="68dp"
        android:layout_gravity="center"
        android:background="@drawable/ota_button_bg"
        android:text="@string/disconnect_device"
        android:textColor="@color/white"
        android:textAllCaps="false"
        android:visibility="gone"/>

</LinearLayout>