<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >
<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <EditText
        android:id="@+id/wlan_name"
        android:layout_marginTop="100dp"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="50dp"
        android:textAlignment="center"
        android:hint="WLAN Name"/>
    <Button
        android:id="@+id/test_btn"
        android:layout_marginTop="20dp"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:text="scan"
        tools:ignore="MissingConstraints">
    </Button>

<!--    <Button-->
<!--        android:id="@+id/test_two"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="80dp"-->
<!--        android:text=""-->
<!--        tools:ignore="MissingConstraints">-->
<!--    </Button>-->

<!--    <Button-->
<!--        android:id="@+id/test_three"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="80dp"-->
<!--        android:text=""-->
<!--        tools:ignore="MissingConstraints">-->
<!--    </Button>-->

<!--    <Button-->
<!--        android:id="@+id/test_four"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="80dp"-->
<!--        android:text=""-->
<!--        tools:ignore="MissingConstraints">-->
<!--    </Button>-->

</LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>