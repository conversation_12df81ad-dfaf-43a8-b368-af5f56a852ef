<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android" android:layout_width="match_parent"
    android:layout_height="58dp">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            <TextView
                android:id="@+id/setting_path"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="11dp"
                android:layout_marginRight="100dp"
                android:fontFamily="@font/sanshan_normal"
                android:textColor="@color/ff2c4662"
                android:textSize="16sp"
                >
            </TextView>

            <TextView
                android:id="@+id/setting_size"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp"
                android:fontFamily="@font/sanshan_normal"
                android:textColor="@color/title_color_light"
                android:textSize="14sp"
                >
            </TextView>

            <View
                android:layout_width="match_parent"
                android:layout_marginTop="13dp"
                android:layout_height="0.5dp"
                android:background="@color/ffe1e6eb"
                android:alpha="0.8">
            </View>

        </LinearLayout>

        <Button
            android:id="@+id/delete_button"
            android:layout_width="80dp"
            android:layout_height="34dp"
            android:layout_marginTop="12dp"
            android:layout_marginLeft="-188dp"
            android:background="@drawable/btn_delete"
            android:text="@string/delete"
            android:textSize="14sp"
            android:textColor="@color/white"
            >

        </Button>

        <Button
            android:id="@+id/path_button"
            android:layout_width="80dp"
            android:layout_height="34dp"
            android:layout_marginTop="12dp"
            android:layout_marginLeft="8dp"
            android:background="@drawable/btn_y"
            android:text="@string/path"
            android:textSize="14sp"
            android:textColor="@color/white"
            >

        </Button>


    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>