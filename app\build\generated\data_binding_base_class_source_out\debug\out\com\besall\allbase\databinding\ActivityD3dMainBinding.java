// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityD3dMainBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button classBtBtn;

  @NonNull
  public final Button clear;

  @NonNull
  public final EditText periodNum;

  @NonNull
  public final Button playFile;

  @NonNull
  public final TextView playFileName;

  @NonNull
  public final Button selectFile;

  @NonNull
  public final Button sensorConfig;

  @NonNull
  public final TextView sensorInfo;

  @NonNull
  public final Button sensorStart;

  @NonNull
  public final Button sensorStop;

  @NonNull
  public final Button stopFile;

  private ActivityD3dMainBinding(@NonNull ScrollView rootView, @NonNull Button classBtBtn,
      @NonNull Button clear, @NonNull EditText periodNum, @NonNull Button playFile,
      @NonNull TextView playFileName, @NonNull Button selectFile, @NonNull Button sensorConfig,
      @NonNull TextView sensorInfo, @NonNull Button sensorStart, @NonNull Button sensorStop,
      @NonNull Button stopFile) {
    this.rootView = rootView;
    this.classBtBtn = classBtBtn;
    this.clear = clear;
    this.periodNum = periodNum;
    this.playFile = playFile;
    this.playFileName = playFileName;
    this.selectFile = selectFile;
    this.sensorConfig = sensorConfig;
    this.sensorInfo = sensorInfo;
    this.sensorStart = sensorStart;
    this.sensorStop = sensorStop;
    this.stopFile = stopFile;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityD3dMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityD3dMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_d3d_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityD3dMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.classBt_btn;
      Button classBtBtn = rootView.findViewById(id);
      if (classBtBtn == null) {
        break missingId;
      }

      id = R.id.clear;
      Button clear = rootView.findViewById(id);
      if (clear == null) {
        break missingId;
      }

      id = R.id.period_num;
      EditText periodNum = rootView.findViewById(id);
      if (periodNum == null) {
        break missingId;
      }

      id = R.id.play_file;
      Button playFile = rootView.findViewById(id);
      if (playFile == null) {
        break missingId;
      }

      id = R.id.play_file_name;
      TextView playFileName = rootView.findViewById(id);
      if (playFileName == null) {
        break missingId;
      }

      id = R.id.select_file;
      Button selectFile = rootView.findViewById(id);
      if (selectFile == null) {
        break missingId;
      }

      id = R.id.sensor_config;
      Button sensorConfig = rootView.findViewById(id);
      if (sensorConfig == null) {
        break missingId;
      }

      id = R.id.sensor_info;
      TextView sensorInfo = rootView.findViewById(id);
      if (sensorInfo == null) {
        break missingId;
      }

      id = R.id.sensor_start;
      Button sensorStart = rootView.findViewById(id);
      if (sensorStart == null) {
        break missingId;
      }

      id = R.id.sensor_stop;
      Button sensorStop = rootView.findViewById(id);
      if (sensorStop == null) {
        break missingId;
      }

      id = R.id.stop_file;
      Button stopFile = rootView.findViewById(id);
      if (stopFile == null) {
        break missingId;
      }

      return new ActivityD3dMainBinding((ScrollView) rootView, classBtBtn, clear, periodNum,
          playFile, playFileName, selectFile, sensorConfig, sensorInfo, sensorStart, sensorStop,
          stopFile);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
