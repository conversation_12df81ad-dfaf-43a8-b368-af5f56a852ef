<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="view_otaing" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\view_otaing.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/view_otaing_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="524" endOffset="51"/></Target><Target id="@+id/view_otaover_bg" view="LinearLayout"><Expressions/><location startLine="22" startOffset="8" endLine="51" endOffset="22"/></Target><Target id="@+id/view_otaover_image" view="ImageView"><Expressions/><location startLine="28" startOffset="12" endLine="35" endOffset="23"/></Target><Target id="@+id/view_otaover_text" view="TextView"><Expressions/><location startLine="37" startOffset="12" endLine="49" endOffset="22"/></Target><Target id="@+id/view_otaing_bg" view="LinearLayout"><Expressions/><location startLine="53" startOffset="8" endLine="520" endOffset="22"/></Target><Target id="@+id/tasks_view" view="com.besall.allbase.common.utils.CircleProgressView"><Expressions/><location startLine="71" startOffset="16" endLine="81" endOffset="49"/></Target><Target id="@+id/base_view0" view="LinearLayout"><Expressions/><location startLine="85" startOffset="12" endLine="181" endOffset="26"/></Target><Target id="@+id/bgview0" view="LinearLayout"><Expressions/><location startLine="94" startOffset="16" endLine="135" endOffset="30"/></Target><Target id="@+id/text_name0" view="TextView"><Expressions/><location startLine="107" startOffset="24" endLine="115" endOffset="34"/></Target><Target id="@+id/text_percent0" view="TextView"><Expressions/><location startLine="116" startOffset="24" endLine="125" endOffset="34"/></Target><Target id="@+id/bgview1" view="LinearLayout"><Expressions/><location startLine="137" startOffset="16" endLine="179" endOffset="30"/></Target><Target id="@+id/text_name1" view="TextView"><Expressions/><location startLine="151" startOffset="24" endLine="159" endOffset="34"/></Target><Target id="@+id/text_percent1" view="TextView"><Expressions/><location startLine="160" startOffset="24" endLine="169" endOffset="34"/></Target><Target id="@+id/base_view1" view="LinearLayout"><Expressions/><location startLine="183" startOffset="12" endLine="343" endOffset="26"/></Target><Target id="@+id/text_view_0" view="LinearLayout"><Expressions/><location startLine="193" startOffset="16" endLine="221" endOffset="30"/></Target><Target id="@+id/text_name_0" view="TextView"><Expressions/><location startLine="202" startOffset="20" endLine="210" endOffset="30"/></Target><Target id="@+id/text_percent_0" view="TextView"><Expressions/><location startLine="211" startOffset="20" endLine="220" endOffset="30"/></Target><Target id="@+id/text_view_1" view="LinearLayout"><Expressions/><location startLine="223" startOffset="16" endLine="251" endOffset="30"/></Target><Target id="@+id/text_name_1" view="TextView"><Expressions/><location startLine="232" startOffset="20" endLine="240" endOffset="30"/></Target><Target id="@+id/text_percent_1" view="TextView"><Expressions/><location startLine="241" startOffset="20" endLine="250" endOffset="30"/></Target><Target id="@+id/text_view_2" view="LinearLayout"><Expressions/><location startLine="253" startOffset="16" endLine="281" endOffset="30"/></Target><Target id="@+id/text_name_2" view="TextView"><Expressions/><location startLine="262" startOffset="20" endLine="270" endOffset="30"/></Target><Target id="@+id/text_percent_2" view="TextView"><Expressions/><location startLine="271" startOffset="20" endLine="280" endOffset="30"/></Target><Target id="@+id/text_view_3" view="LinearLayout"><Expressions/><location startLine="283" startOffset="16" endLine="311" endOffset="30"/></Target><Target id="@+id/text_name_3" view="TextView"><Expressions/><location startLine="292" startOffset="20" endLine="300" endOffset="30"/></Target><Target id="@+id/text_percent_3" view="TextView"><Expressions/><location startLine="301" startOffset="20" endLine="310" endOffset="30"/></Target><Target id="@+id/text_view_4" view="LinearLayout"><Expressions/><location startLine="313" startOffset="16" endLine="341" endOffset="30"/></Target><Target id="@+id/text_name_4" view="TextView"><Expressions/><location startLine="322" startOffset="20" endLine="330" endOffset="30"/></Target><Target id="@+id/text_percent_4" view="TextView"><Expressions/><location startLine="331" startOffset="20" endLine="340" endOffset="30"/></Target><Target id="@+id/base_view2" view="LinearLayout"><Expressions/><location startLine="345" startOffset="12" endLine="505" endOffset="26"/></Target><Target id="@+id/text_view_5" view="LinearLayout"><Expressions/><location startLine="355" startOffset="16" endLine="383" endOffset="30"/></Target><Target id="@+id/text_name_5" view="TextView"><Expressions/><location startLine="364" startOffset="20" endLine="372" endOffset="30"/></Target><Target id="@+id/text_percent_5" view="TextView"><Expressions/><location startLine="373" startOffset="20" endLine="382" endOffset="30"/></Target><Target id="@+id/text_view_6" view="LinearLayout"><Expressions/><location startLine="385" startOffset="16" endLine="413" endOffset="30"/></Target><Target id="@+id/text_name_6" view="TextView"><Expressions/><location startLine="394" startOffset="20" endLine="402" endOffset="30"/></Target><Target id="@+id/text_percent_6" view="TextView"><Expressions/><location startLine="403" startOffset="20" endLine="412" endOffset="30"/></Target><Target id="@+id/text_view_7" view="LinearLayout"><Expressions/><location startLine="415" startOffset="16" endLine="443" endOffset="30"/></Target><Target id="@+id/text_name_7" view="TextView"><Expressions/><location startLine="424" startOffset="20" endLine="432" endOffset="30"/></Target><Target id="@+id/text_percent_7" view="TextView"><Expressions/><location startLine="433" startOffset="20" endLine="442" endOffset="30"/></Target><Target id="@+id/text_view_8" view="LinearLayout"><Expressions/><location startLine="445" startOffset="16" endLine="473" endOffset="30"/></Target><Target id="@+id/text_name_8" view="TextView"><Expressions/><location startLine="454" startOffset="20" endLine="462" endOffset="30"/></Target><Target id="@+id/text_percent_8" view="TextView"><Expressions/><location startLine="463" startOffset="20" endLine="472" endOffset="30"/></Target><Target id="@+id/text_view_9" view="LinearLayout"><Expressions/><location startLine="475" startOffset="16" endLine="503" endOffset="30"/></Target><Target id="@+id/text_name_9" view="TextView"><Expressions/><location startLine="484" startOffset="20" endLine="492" endOffset="30"/></Target><Target id="@+id/text_percent_9" view="TextView"><Expressions/><location startLine="493" startOffset="20" endLine="502" endOffset="30"/></Target></Targets></Layout>