<?xml version="1.0" encoding="utf-8"?>
<!--<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"-->
<!--    xmlns:tools="http://schemas.android.com/tools"-->
<!--    android:layout_width="match_parent"-->
<!--    android:layout_height="match_parent"-->
<!--    android:orientation="vertical"-->
<!--    android:background="@drawable/base_bg"-->
<!--    >-->
    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@mipmap/bes_bg6"

        >

    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="30dp"
        android:layout_marginBottom="50dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                />

            <Button
                android:id="@+id/avslwa"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:gravity="left|center_vertical"
                android:padding="20dp"
                android:background="@drawable/rectangle_longbtn"
                android:drawableLeft="@drawable/chips_tools_icon_rssi"
                android:drawableRight="@drawable/home_icon_arrow"
                android:text="    AVS LWA"
                android:textColor="@color/ff2c4662"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                />

            <Button
                android:id="@+id/smartvoice"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:gravity="left|center_vertical"
                android:padding="20dp"
                android:background="@drawable/rectangle_longbtn"
                android:drawableLeft="@drawable/chips_tools_icon_rssi"
                android:drawableRight="@drawable/home_icon_arrow"
                android:text="    SmartVoice"
                android:textColor="@color/ff2c4662"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:layout_marginLeft="20dp"
                android:background="#FFE1E6EB"

                />
            <Button
                android:id="@+id/rssi"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:gravity="left|center_vertical"
                android:padding="20dp"
                android:background="@drawable/rectangle_longbtn"
                android:drawableLeft="@drawable/chips_tools_icon_rssi"
                android:drawableRight="@drawable/home_icon_arrow"
                android:text="    RSSI"
                android:textColor="@color/ff2c4662"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:layout_marginLeft="20dp"
                android:background="#FFE1E6EB"

                />
            <Button
                android:id="@+id/rssi_extend"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:gravity="left|center_vertical"
                android:padding="20dp"
                android:drawableLeft="@drawable/chips_tools_icon_rssi_extend"
                android:drawableRight="@drawable/home_icon_arrow"
                android:background="@drawable/rectangle_longbtn"
                android:text="    RSSI EXTEND"
                android:textAllCaps="false"
                android:textColor="@color/ff2c4662"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <Button
                android:id="@+id/audio_dump"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:gravity="left|center_vertical"
                android:padding="20dp"
                android:drawableLeft="@drawable/chips_tools_icon_audio"
                android:drawableRight="@drawable/home_icon_arrow"
                android:background="@drawable/rectangle_longbtn"
                android:text="    AUDIO DUMP"
                android:textAllCaps="false"
                android:textColor="@color/ff2c4662"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <Button
                android:id="@+id/log_dump"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:gravity="left|center_vertical"
                android:padding="20dp"
                android:drawableLeft="@drawable/chips_tools_icon_log"
                android:drawableRight="@drawable/home_icon_arrow"
                android:background="@drawable/rectangle_longbtn"
                android:text="    LOG DUMP"
                android:textAllCaps="false"
                android:textColor="@color/ff2c4662"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <Button
                android:id="@+id/crash_dump"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:gravity="left|center_vertical"
                android:padding="20dp"
                android:drawableLeft="@drawable/chips_tools_icon_crash"
                android:drawableRight="@drawable/home_icon_arrow"
                android:background="@drawable/rectangle_longbtn"
                android:textColor="@color/ff2c4662"
                android:text="    CRASH DUMP"
                android:textAllCaps="false"

                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <Button
                android:id="@+id/custom_command"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:gravity="left|center_vertical"
                android:padding="20dp"
                android:drawableLeft="@drawable/chips_tools_icon_customer"
                android:drawableRight="@drawable/home_icon_arrow"
                android:background="@drawable/rectangle_longbtn"
                android:textColor="@color/ff2c4662"
                android:text="    CUSTOMER COMMAND"
                android:textAllCaps="false"
                />

            <Button
                android:id="@+id/EQ"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:gravity="left|center_vertical"
                android:padding="20dp"
                android:drawableLeft="@drawable/chips_tools_icon_customer"
                android:drawableRight="@drawable/home_icon_arrow"
                android:background="@drawable/rectangle_longbtn"
                android:textColor="@color/ff2c4662"
                android:text="    EQ"
                android:textAllCaps="false"
                android:visibility="gone"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <Button
                android:id="@+id/capsensor"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:gravity="left|center_vertical"
                android:padding="20dp"
                android:drawableLeft="@drawable/chips_tools_icon_customer"
                android:drawableRight="@drawable/home_icon_arrow"
                android:background="@drawable/rectangle_longbtn"
                android:textColor="@color/ff2c4662"
                android:text="    capsensor"
                />



            <!--        <Button-->
            <!--            android:id="@+id/opus_audio_dump"-->
            <!--            android:layout_width="match_parent"-->
            <!--            android:layout_height="wrap_content"-->
            <!--            android:padding="10dp"-->
            <!--            android:visibility="gone"-->
            <!--            android:text="AUDIO DUMP"-->
            <!--            android:textAllCaps="false"-->
            <!--            />-->

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <Button
                android:id="@+id/ble_wifi"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:gravity="left|center_vertical"
                android:padding="20dp"
                android:drawableLeft="@drawable/home_icon_chipstools"
                android:drawableRight="@drawable/home_icon_arrow"
                android:background="@drawable/rectangle_longbtn"
                android:textColor="@color/ff2c4662"
                android:text="    BLE WIFI"
                android:textAllCaps="false"
                />
            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <Button
                android:id="@+id/throughput"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:gravity="left|center_vertical"
                android:padding="20dp"
                android:drawableLeft="@drawable/chips_tools_icon_customer"
                android:drawableRight="@drawable/home_icon_arrow"
                android:background="@drawable/rectangle_longbtn"
                android:textColor="@color/ff2c4662"
                android:text="    THROUGH PUT"
                android:textAllCaps="false"
                android:visibility="visible"
                />
            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <Button
                android:id="@+id/check_crc"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:gravity="left|center_vertical"
                android:padding="20dp"
                android:drawableLeft="@drawable/chips_tools_icon_customer"
                android:drawableRight="@drawable/home_icon_arrow"
                android:background="@drawable/rectangle_longbtn"
                android:textColor="@color/ff2c4662"
                android:text="    CHECK CRC"
                android:textAllCaps="false"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <Button
                android:id="@+id/command_set"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:gravity="left|center_vertical"
                android:padding="20dp"
                android:drawableLeft="@drawable/chips_tools_icon_customer"
                android:drawableRight="@drawable/home_icon_arrow"
                android:background="@drawable/rectangle_longbtn"
                android:textColor="@color/ff2c4662"
                android:text="    COMMAND SET"
                android:textAllCaps="false"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <Button
                android:id="@+id/auracast_assistant"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:gravity="left|center_vertical"
                android:padding="20dp"
                android:drawableLeft="@drawable/chips_tools_icon_customer"
                android:drawableRight="@drawable/home_icon_arrow"
                android:background="@drawable/rectangle_longbtn"
                android:textColor="@color/ff2c4662"
                android:text="    Auracast Assistant"
                android:textAllCaps="false"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"

                />
        </LinearLayout>

    </ScrollView>


    </LinearLayout>

<!--</ScrollView>-->