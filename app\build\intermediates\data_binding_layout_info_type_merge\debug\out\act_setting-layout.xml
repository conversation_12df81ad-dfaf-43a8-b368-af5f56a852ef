<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="act_setting" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\act_setting.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/act_setting_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="885" endOffset="51"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="14" startOffset="4" endLine="881" endOffset="18"/></Target><Target id="@+id/tool" tag="binding_1" include="toolbar"><Expressions/><location startLine="18" startOffset="8" endLine="21" endOffset="13"/></Target><Target id="@+id/act_otaui_bg" view="ImageView"><Expressions/><location startLine="6" startOffset="4" endLine="13" endOffset="15"/></Target><Target id="@+id/whole_scr" view="ScrollView"><Expressions/><location startLine="33" startOffset="8" endLine="878" endOffset="20"/></Target><Target id="@+id/switchButton_tota" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="66" startOffset="20" endLine="72" endOffset="54"/></Target><Target id="@+id/switchButton_totav2" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="97" startOffset="20" endLine="103" endOffset="54"/></Target><Target id="@+id/switchButton_phy2m" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="128" startOffset="20" endLine="134" endOffset="54"/></Target><Target id="@+id/switchButton_use_normal_connect" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="159" startOffset="20" endLine="165" endOffset="54"/></Target><Target id="@+id/edittext_ble_ota_auto_test_interval" view="EditText"><Expressions/><location startLine="190" startOffset="20" endLine="199" endOffset="47"/></Target><Target id="@+id/switchButton_ble_ota_auto_test" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="201" startOffset="20" endLine="207" endOffset="54"/></Target><Target id="@+id/switchButton" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="232" startOffset="20" endLine="238" endOffset="54"/></Target><Target id="@+id/locolLog_list" view="ListView"><Expressions/><location startLine="269" startOffset="16" endLine="276" endOffset="26"/></Target><Target id="@+id/send_delay" view="LinearLayout"><Expressions/><location startLine="278" startOffset="16" endLine="420" endOffset="30"/></Target><Target id="@+id/ack_image_0" view="ImageView"><Expressions/><location startLine="301" startOffset="24" endLine="307" endOffset="35"/></Target><Target id="@+id/ack_btn_0" view="Button"><Expressions/><location startLine="308" startOffset="24" endLine="322" endOffset="32"/></Target><Target id="@+id/ble_interval" view="EditText"><Expressions/><location startLine="335" startOffset="24" endLine="343" endOffset="57"/></Target><Target id="@+id/spp_interval" view="EditText"><Expressions/><location startLine="345" startOffset="24" endLine="353" endOffset="54"/></Target><Target id="@+id/version_button" view="Button"><Expressions/><location startLine="721" startOffset="20" endLine="727" endOffset="28"/></Target><Target id="@+id/version_text" view="TextView"><Expressions/><location startLine="750" startOffset="24" endLine="760" endOffset="34"/></Target><Target id="@+id/privacy_policy" view="Button"><Expressions/><location startLine="789" startOffset="20" endLine="802" endOffset="28"/></Target><Target id="@+id/agree_view" view="LinearLayout"><Expressions/><location startLine="806" startOffset="16" endLine="873" endOffset="30"/></Target><Target id="@+id/scr_policy" view="ScrollView"><Expressions/><location startLine="818" startOffset="20" endLine="834" endOffset="32"/></Target><Target id="@+id/agreeTV" view="TextView"><Expressions/><location startLine="826" startOffset="24" endLine="832" endOffset="34"/></Target><Target id="@+id/disagree" view="Button"><Expressions/><location startLine="842" startOffset="24" endLine="851" endOffset="32"/></Target><Target id="@+id/agree" view="Button"><Expressions/><location startLine="859" startOffset="24" endLine="869" endOffset="32"/></Target></Targets></Layout>