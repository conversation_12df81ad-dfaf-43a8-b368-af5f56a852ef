// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class CmdItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView accountImage;

  @NonNull
  public final TextView accountTextview;

  @NonNull
  public final ImageView deleteAccountImage;

  @NonNull
  public final RelativeLayout deleteAccountRl;

  private CmdItemBinding(@NonNull LinearLayout rootView, @NonNull ImageView accountImage,
      @NonNull TextView accountTextview, @NonNull ImageView deleteAccountImage,
      @NonNull RelativeLayout deleteAccountRl) {
    this.rootView = rootView;
    this.accountImage = accountImage;
    this.accountTextview = accountTextview;
    this.deleteAccountImage = deleteAccountImage;
    this.deleteAccountRl = deleteAccountRl;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static CmdItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static CmdItemBinding inflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup parent,
      boolean attachToParent) {
    View root = inflater.inflate(R.layout.cmd_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static CmdItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.account_image;
      ImageView accountImage = rootView.findViewById(id);
      if (accountImage == null) {
        break missingId;
      }

      id = R.id.account_textview;
      TextView accountTextview = rootView.findViewById(id);
      if (accountTextview == null) {
        break missingId;
      }

      id = R.id.delete_account_image;
      ImageView deleteAccountImage = rootView.findViewById(id);
      if (deleteAccountImage == null) {
        break missingId;
      }

      id = R.id.delete_account_rl;
      RelativeLayout deleteAccountRl = rootView.findViewById(id);
      if (deleteAccountRl == null) {
        break missingId;
      }

      return new CmdItemBinding((LinearLayout) rootView, accountImage, accountTextview,
          deleteAccountImage, deleteAccountRl);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
