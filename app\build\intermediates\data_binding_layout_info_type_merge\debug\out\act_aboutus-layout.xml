<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="act_aboutus" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\act_aboutus.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/act_aboutus_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="164" endOffset="51"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="14" startOffset="4" endLine="161" endOffset="18"/></Target><Target id="@+id/tool" tag="binding_1" include="toolbar"><Expressions/><location startLine="18" startOffset="8" endLine="21" endOffset="13"/></Target><Target id="@+id/act_otaui_bg" view="ImageView"><Expressions/><location startLine="6" startOffset="4" endLine="13" endOffset="15"/></Target><Target id="@+id/version_text" view="TextView"><Expressions/><location startLine="56" startOffset="12" endLine="66" endOffset="22"/></Target><Target id="@+id/privacy_policy" view="Button"><Expressions/><location startLine="80" startOffset="12" endLine="90" endOffset="20"/></Target><Target id="@+id/agree_view" view="LinearLayout"><Expressions/><location startLine="94" startOffset="8" endLine="160" endOffset="22"/></Target><Target id="@+id/agreeTV" view="TextView"><Expressions/><location startLine="113" startOffset="16" endLine="119" endOffset="26"/></Target><Target id="@+id/disagree" view="Button"><Expressions/><location startLine="129" startOffset="16" endLine="138" endOffset="24"/></Target><Target id="@+id/agree" view="Button"><Expressions/><location startLine="146" startOffset="16" endLine="156" endOffset="24"/></Target></Targets></Layout>