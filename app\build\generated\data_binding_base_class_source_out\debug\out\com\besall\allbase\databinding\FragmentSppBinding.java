// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import com.suke.widget.SwitchButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentSppBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final SwitchButton bleConfigIsUseSpecificConnectionParamSwitch;

  @NonNull
  public final EditText bleConfigMaxConnectionIntervalInMsEdit;

  @NonNull
  public final EditText bleConfigMinConnectionIntervalInMsEdit;

  @NonNull
  public final LinearLayout bleConfigView;

  @NonNull
  public final TextView bleConnectionIntervalValueText;

  @NonNull
  public final LinearLayout bleInterval;

  @NonNull
  public final RadioGroup dataPattern1;

  @NonNull
  public final RadioGroup dataPattern2;

  @NonNull
  public final RadioGroup dataPattern3;

  @NonNull
  public final Button dataminus;

  @NonNull
  public final Button dataplus;

  @NonNull
  public final EditText datasize;

  @NonNull
  public final Button intminus;

  @NonNull
  public final Button intplus;

  @NonNull
  public final LogviewBinding loginfo;

  @NonNull
  public final EditText ltis;

  @NonNull
  public final RadioButton pattern00;

  @NonNull
  public final RadioButton pattern01;

  @NonNull
  public final RadioButton pattern02;

  @NonNull
  public final RadioButton pattern03;

  @NonNull
  public final RadioButton pattern04;

  @NonNull
  public final RadioButton pattern05;

  @NonNull
  public final RadioButton pattern06;

  @NonNull
  public final TextView sppConfigDataSizeText;

  @NonNull
  public final SwitchButton sppConfigIsWithReponseSwitch;

  @NonNull
  public final TextView sppConfigLastingTimeInSecondInMs;

  @NonNull
  public final TextView sppDataSizeText;

  @NonNull
  public final TextView sppDownThroughRateText;

  @NonNull
  public final ProgressBar sppLoading;

  @NonNull
  public final TextView sppResponseTypeText;

  @NonNull
  public final Button sppThroughDownBtn;

  @NonNull
  public final Button sppThroughUpBtn;

  @NonNull
  public final TextView sppUpThroughRateText;

  @NonNull
  public final TextView testDataTitle;

  @NonNull
  public final TextView titleConfigValue;

  @NonNull
  public final TextView titleRealValue;

  @NonNull
  public final LinearLayout titleRealValueLayout;

  @NonNull
  public final ToolbarBinding tool;

  private FragmentSppBinding(@NonNull LinearLayout rootView,
      @NonNull SwitchButton bleConfigIsUseSpecificConnectionParamSwitch,
      @NonNull EditText bleConfigMaxConnectionIntervalInMsEdit,
      @NonNull EditText bleConfigMinConnectionIntervalInMsEdit, @NonNull LinearLayout bleConfigView,
      @NonNull TextView bleConnectionIntervalValueText, @NonNull LinearLayout bleInterval,
      @NonNull RadioGroup dataPattern1, @NonNull RadioGroup dataPattern2,
      @NonNull RadioGroup dataPattern3, @NonNull Button dataminus, @NonNull Button dataplus,
      @NonNull EditText datasize, @NonNull Button intminus, @NonNull Button intplus,
      @NonNull LogviewBinding loginfo, @NonNull EditText ltis, @NonNull RadioButton pattern00,
      @NonNull RadioButton pattern01, @NonNull RadioButton pattern02,
      @NonNull RadioButton pattern03, @NonNull RadioButton pattern04,
      @NonNull RadioButton pattern05, @NonNull RadioButton pattern06,
      @NonNull TextView sppConfigDataSizeText, @NonNull SwitchButton sppConfigIsWithReponseSwitch,
      @NonNull TextView sppConfigLastingTimeInSecondInMs, @NonNull TextView sppDataSizeText,
      @NonNull TextView sppDownThroughRateText, @NonNull ProgressBar sppLoading,
      @NonNull TextView sppResponseTypeText, @NonNull Button sppThroughDownBtn,
      @NonNull Button sppThroughUpBtn, @NonNull TextView sppUpThroughRateText,
      @NonNull TextView testDataTitle, @NonNull TextView titleConfigValue,
      @NonNull TextView titleRealValue, @NonNull LinearLayout titleRealValueLayout,
      @NonNull ToolbarBinding tool) {
    this.rootView = rootView;
    this.bleConfigIsUseSpecificConnectionParamSwitch = bleConfigIsUseSpecificConnectionParamSwitch;
    this.bleConfigMaxConnectionIntervalInMsEdit = bleConfigMaxConnectionIntervalInMsEdit;
    this.bleConfigMinConnectionIntervalInMsEdit = bleConfigMinConnectionIntervalInMsEdit;
    this.bleConfigView = bleConfigView;
    this.bleConnectionIntervalValueText = bleConnectionIntervalValueText;
    this.bleInterval = bleInterval;
    this.dataPattern1 = dataPattern1;
    this.dataPattern2 = dataPattern2;
    this.dataPattern3 = dataPattern3;
    this.dataminus = dataminus;
    this.dataplus = dataplus;
    this.datasize = datasize;
    this.intminus = intminus;
    this.intplus = intplus;
    this.loginfo = loginfo;
    this.ltis = ltis;
    this.pattern00 = pattern00;
    this.pattern01 = pattern01;
    this.pattern02 = pattern02;
    this.pattern03 = pattern03;
    this.pattern04 = pattern04;
    this.pattern05 = pattern05;
    this.pattern06 = pattern06;
    this.sppConfigDataSizeText = sppConfigDataSizeText;
    this.sppConfigIsWithReponseSwitch = sppConfigIsWithReponseSwitch;
    this.sppConfigLastingTimeInSecondInMs = sppConfigLastingTimeInSecondInMs;
    this.sppDataSizeText = sppDataSizeText;
    this.sppDownThroughRateText = sppDownThroughRateText;
    this.sppLoading = sppLoading;
    this.sppResponseTypeText = sppResponseTypeText;
    this.sppThroughDownBtn = sppThroughDownBtn;
    this.sppThroughUpBtn = sppThroughUpBtn;
    this.sppUpThroughRateText = sppUpThroughRateText;
    this.testDataTitle = testDataTitle;
    this.titleConfigValue = titleConfigValue;
    this.titleRealValue = titleRealValue;
    this.titleRealValueLayout = titleRealValueLayout;
    this.tool = tool;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentSppBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentSppBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_spp, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentSppBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ble_config_is_use_specific_connection_param_switch;
      SwitchButton bleConfigIsUseSpecificConnectionParamSwitch = rootView.findViewById(id);
      if (bleConfigIsUseSpecificConnectionParamSwitch == null) {
        break missingId;
      }

      id = R.id.ble_config_max_connection_interval_in_ms_edit;
      EditText bleConfigMaxConnectionIntervalInMsEdit = rootView.findViewById(id);
      if (bleConfigMaxConnectionIntervalInMsEdit == null) {
        break missingId;
      }

      id = R.id.ble_config_min_connection_interval_in_ms_edit;
      EditText bleConfigMinConnectionIntervalInMsEdit = rootView.findViewById(id);
      if (bleConfigMinConnectionIntervalInMsEdit == null) {
        break missingId;
      }

      id = R.id.ble_config_view;
      LinearLayout bleConfigView = rootView.findViewById(id);
      if (bleConfigView == null) {
        break missingId;
      }

      id = R.id.ble_connection_interval_value_text;
      TextView bleConnectionIntervalValueText = rootView.findViewById(id);
      if (bleConnectionIntervalValueText == null) {
        break missingId;
      }

      id = R.id.ble_interval;
      LinearLayout bleInterval = rootView.findViewById(id);
      if (bleInterval == null) {
        break missingId;
      }

      id = R.id.data_pattern1;
      RadioGroup dataPattern1 = rootView.findViewById(id);
      if (dataPattern1 == null) {
        break missingId;
      }

      id = R.id.data_pattern2;
      RadioGroup dataPattern2 = rootView.findViewById(id);
      if (dataPattern2 == null) {
        break missingId;
      }

      id = R.id.data_pattern3;
      RadioGroup dataPattern3 = rootView.findViewById(id);
      if (dataPattern3 == null) {
        break missingId;
      }

      id = R.id.dataminus;
      Button dataminus = rootView.findViewById(id);
      if (dataminus == null) {
        break missingId;
      }

      id = R.id.dataplus;
      Button dataplus = rootView.findViewById(id);
      if (dataplus == null) {
        break missingId;
      }

      id = R.id.datasize;
      EditText datasize = rootView.findViewById(id);
      if (datasize == null) {
        break missingId;
      }

      id = R.id.intminus;
      Button intminus = rootView.findViewById(id);
      if (intminus == null) {
        break missingId;
      }

      id = R.id.intplus;
      Button intplus = rootView.findViewById(id);
      if (intplus == null) {
        break missingId;
      }

      id = R.id.loginfo;
      View loginfo = rootView.findViewById(id);
      if (loginfo == null) {
        break missingId;
      }
      LogviewBinding binding_loginfo = LogviewBinding.bind(loginfo);

      id = R.id.ltis;
      EditText ltis = rootView.findViewById(id);
      if (ltis == null) {
        break missingId;
      }

      id = R.id.pattern_00;
      RadioButton pattern00 = rootView.findViewById(id);
      if (pattern00 == null) {
        break missingId;
      }

      id = R.id.pattern_01;
      RadioButton pattern01 = rootView.findViewById(id);
      if (pattern01 == null) {
        break missingId;
      }

      id = R.id.pattern_02;
      RadioButton pattern02 = rootView.findViewById(id);
      if (pattern02 == null) {
        break missingId;
      }

      id = R.id.pattern_03;
      RadioButton pattern03 = rootView.findViewById(id);
      if (pattern03 == null) {
        break missingId;
      }

      id = R.id.pattern_04;
      RadioButton pattern04 = rootView.findViewById(id);
      if (pattern04 == null) {
        break missingId;
      }

      id = R.id.pattern_05;
      RadioButton pattern05 = rootView.findViewById(id);
      if (pattern05 == null) {
        break missingId;
      }

      id = R.id.pattern_06;
      RadioButton pattern06 = rootView.findViewById(id);
      if (pattern06 == null) {
        break missingId;
      }

      id = R.id.spp_config_data_size_text;
      TextView sppConfigDataSizeText = rootView.findViewById(id);
      if (sppConfigDataSizeText == null) {
        break missingId;
      }

      id = R.id.spp_config_is_with_reponse_switch;
      SwitchButton sppConfigIsWithReponseSwitch = rootView.findViewById(id);
      if (sppConfigIsWithReponseSwitch == null) {
        break missingId;
      }

      id = R.id.spp_config_lasting_time_in_second_in_ms;
      TextView sppConfigLastingTimeInSecondInMs = rootView.findViewById(id);
      if (sppConfigLastingTimeInSecondInMs == null) {
        break missingId;
      }

      id = R.id.spp_data_size_text;
      TextView sppDataSizeText = rootView.findViewById(id);
      if (sppDataSizeText == null) {
        break missingId;
      }

      id = R.id.spp_down_through_rate_text;
      TextView sppDownThroughRateText = rootView.findViewById(id);
      if (sppDownThroughRateText == null) {
        break missingId;
      }

      id = R.id.spp_loading;
      ProgressBar sppLoading = rootView.findViewById(id);
      if (sppLoading == null) {
        break missingId;
      }

      id = R.id.spp_response_type_text;
      TextView sppResponseTypeText = rootView.findViewById(id);
      if (sppResponseTypeText == null) {
        break missingId;
      }

      id = R.id.spp_through_down_btn;
      Button sppThroughDownBtn = rootView.findViewById(id);
      if (sppThroughDownBtn == null) {
        break missingId;
      }

      id = R.id.spp_through_up_btn;
      Button sppThroughUpBtn = rootView.findViewById(id);
      if (sppThroughUpBtn == null) {
        break missingId;
      }

      id = R.id.spp_up_through_rate_text;
      TextView sppUpThroughRateText = rootView.findViewById(id);
      if (sppUpThroughRateText == null) {
        break missingId;
      }

      id = R.id.test_data_title;
      TextView testDataTitle = rootView.findViewById(id);
      if (testDataTitle == null) {
        break missingId;
      }

      id = R.id.title_config_value;
      TextView titleConfigValue = rootView.findViewById(id);
      if (titleConfigValue == null) {
        break missingId;
      }

      id = R.id.title_real_value;
      TextView titleRealValue = rootView.findViewById(id);
      if (titleRealValue == null) {
        break missingId;
      }

      id = R.id.title_real_value_layout;
      LinearLayout titleRealValueLayout = rootView.findViewById(id);
      if (titleRealValueLayout == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      return new FragmentSppBinding((LinearLayout) rootView,
          bleConfigIsUseSpecificConnectionParamSwitch, bleConfigMaxConnectionIntervalInMsEdit,
          bleConfigMinConnectionIntervalInMsEdit, bleConfigView, bleConnectionIntervalValueText,
          bleInterval, dataPattern1, dataPattern2, dataPattern3, dataminus, dataplus, datasize,
          intminus, intplus, binding_loginfo, ltis, pattern00, pattern01, pattern02, pattern03,
          pattern04, pattern05, pattern06, sppConfigDataSizeText, sppConfigIsWithReponseSwitch,
          sppConfigLastingTimeInSecondInMs, sppDataSizeText, sppDownThroughRateText, sppLoading,
          sppResponseTypeText, sppThroughDownBtn, sppThroughUpBtn, sppUpThroughRateText,
          testDataTitle, titleConfigValue, titleRealValue, titleRealValueLayout, binding_tool);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
