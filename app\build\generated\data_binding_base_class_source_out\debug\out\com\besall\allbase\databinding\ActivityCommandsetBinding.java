// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import com.suke.widget.SwitchButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityCommandsetBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final RadioGroup ancNewType;

  @NonNull
  public final RadioButton ancNewTypeAdapt;

  @NonNull
  public final RadioButton ancNewTypeHigh;

  @NonNull
  public final RadioButton ancNewTypeLow;

  @NonNull
  public final RadioButton ancNewTypeMid;

  @NonNull
  public final RadioButton besSpatialSwitchOff;

  @NonNull
  public final RadioButton besSpatialSwitchOpen;

  @NonNull
  public final RadioGroup besSpatialSwitchType;

  @NonNull
  public final TextView btStateText;

  @NonNull
  public final Button btnGetAutoCenterMode;

  @NonNull
  public final Button btnGetHeadTracking;

  @NonNull
  public final Button btnGetImuOrientation;

  @NonNull
  public final Button btnVolume;

  @NonNull
  public final Button buttonGetBoxBattery;

  @NonNull
  public final Button buttonGetCodecType;

  @NonNull
  public final Button buttonGetLeftBattery;

  @NonNull
  public final Button buttonGetRightBattery;

  @NonNull
  public final LinearLayout buttonSettingBg;

  @NonNull
  public final TextView buttonState1;

  @NonNull
  public final TextView buttonState2;

  @NonNull
  public final TextView buttonState3;

  @NonNull
  public final TextView buttonState4;

  @NonNull
  public final TextView buttonState5;

  @NonNull
  public final TextView buttonState6;

  @NonNull
  public final TextView buttonState7;

  @NonNull
  public final TextView buttonState8;

  @NonNull
  public final Button cevaFastRecentre;

  @NonNull
  public final Button cevaRecentre;

  @NonNull
  public final Button cevaSlowRecentre;

  @NonNull
  public final RadioButton cevaSwitchOff;

  @NonNull
  public final RadioButton cevaSwitchOpen;

  @NonNull
  public final RadioGroup cevaSwitchType;

  @NonNull
  public final Button checkLeftSpeaker;

  @NonNull
  public final Button checkMicState;

  @NonNull
  public final Button checkRightSpeaker;

  @NonNull
  public final TextView commandSetCurrentProductModel;

  @NonNull
  public final TextView commandSetCurrentVersion;

  @NonNull
  public final EditText commandSetReceiveData;

  @NonNull
  public final Button disconnect;

  @NonNull
  public final TextView dolbySwitchTitle;

  @NonNull
  public final Button dolbyTurnOff;

  @NonNull
  public final Button dolbyTypeMovie;

  @NonNull
  public final Button dolbyTypeNatual;

  @NonNull
  public final RadioGroup earbudsClickFunc0;

  @NonNull
  public final RadioGroup earbudsClickFunc1;

  @NonNull
  public final RadioGroup earbudsClickFunc2;

  @NonNull
  public final RadioGroup earbudsClickFunc3;

  @NonNull
  public final RadioButton earbudsClickFuncAlgo;

  @NonNull
  public final RadioButton earbudsClickFuncAmbientMusic;

  @NonNull
  public final RadioButton earbudsClickFuncAssistant;

  @NonNull
  public final RadioButton earbudsClickFuncCallBack;

  @NonNull
  public final RadioButton earbudsClickFuncDisable;

  @NonNull
  public final RadioButton earbudsClickFuncGameMode;

  @NonNull
  public final RadioButton earbudsClickFuncLastMusic;

  @NonNull
  public final RadioButton earbudsClickFuncNextMusic;

  @NonNull
  public final RadioButton earbudsClickFuncPlayMusic;

  @NonNull
  public final RadioButton earbudsClickFuncPlayPauseMusic;

  @NonNull
  public final RadioButton earbudsClickFuncSpeakthru;

  @NonNull
  public final RadioButton earbudsClickFuncStopMusic;

  @NonNull
  public final RadioButton earbudsClickFuncVolumeAdd;

  @NonNull
  public final RadioButton earbudsClickFuncVolumeLose;

  @NonNull
  public final Button earbudsClickLeft;

  @NonNull
  public final Button earbudsClickRight;

  @NonNull
  public final Button earbudsDoubleClickLeft;

  @NonNull
  public final Button earbudsDoubleClickRight;

  @NonNull
  public final Button earbudsLongPressLeft;

  @NonNull
  public final Button earbudsLongPressRight;

  @NonNull
  public final Button earbudsTripleClickLeft;

  @NonNull
  public final Button earbudsTripleClickRight;

  @NonNull
  public final RadioGroup eqBasetype;

  @NonNull
  public final RadioGroup eqBasetype2;

  @NonNull
  public final RadioButton eqBasetype2BassBoost;

  @NonNull
  public final RadioButton eqBasetype2Classic;

  @NonNull
  public final RadioButton eqBasetype2HipHop;

  @NonNull
  public final RadioButton eqBasetype2Jazz;

  @NonNull
  public final RadioButton eqBasetype2Podcast;

  @NonNull
  public final RadioButton eqBasetype2Pop;

  @NonNull
  public final RadioButton eqBasetype2Rock;

  @NonNull
  public final RadioButton eqBasetypeClassic;

  @NonNull
  public final RadioButton eqBasetypeCountry;

  @NonNull
  public final RadioButton eqBasetypeJazz;

  @NonNull
  public final RadioButton eqBasetypePop;

  @NonNull
  public final RadioButton eqBasetypeRock;

  @NonNull
  public final LinearLayout eqBgview;

  @NonNull
  public final RadioButton eqSwitchOff;

  @NonNull
  public final RadioButton eqSwitchOpen;

  @NonNull
  public final RadioGroup eqSwitchType;

  @NonNull
  public final Button eqTest;

  @NonNull
  public final EditText eqText;

  @NonNull
  public final Button factoryResetCmdSet;

  @NonNull
  public final RadioButton gameModeClose;

  @NonNull
  public final RadioButton gameModeOpen;

  @NonNull
  public final RadioGroup gameModeType;

  @NonNull
  public final Button getBtState;

  @NonNull
  public final Button getSppState;

  @NonNull
  public final Button getTechLevel;

  @NonNull
  public final Button headTrackingOff;

  @NonNull
  public final Button headTrackingOffPro;

  @NonNull
  public final Button headTrackingOn;

  @NonNull
  public final Button headTrackingOnPro;

  @NonNull
  public final RadioButton ledSwitch2Flash;

  @NonNull
  public final RadioButton ledSwitch2Off;

  @NonNull
  public final RadioButton ledSwitch2Open0;

  @NonNull
  public final RadioButton ledSwitch2Open1;

  @NonNull
  public final RadioButton ledSwitchFlash;

  @NonNull
  public final RadioButton ledSwitchOff;

  @NonNull
  public final RadioButton ledSwitchOpen;

  @NonNull
  public final RadioGroup ledSwitchType;

  @NonNull
  public final RadioGroup ledSwitchType2;

  @NonNull
  public final View lineWearDetect;

  @NonNull
  public final LinearLayout linearAncNewState;

  @NonNull
  public final LinearLayout linearAncState;

  @NonNull
  public final LinearLayout linearBesSpatialState;

  @NonNull
  public final LinearLayout linearCevaState;

  @NonNull
  public final LinearLayout linearDisableSwipe;

  @NonNull
  public final LinearLayout linearDolbyState;

  @NonNull
  public final LinearLayout linearFunctionBtn;

  @NonNull
  public final LinearLayout linearGameMode;

  @NonNull
  public final LinearLayout linearGetCodecType;

  @NonNull
  public final LinearLayout linearHeadTrackingPro;

  @NonNull
  public final LinearLayout linearLedOnoffState;

  @NonNull
  public final LinearLayout linearMimiState;

  @NonNull
  public final LinearLayout linearMultiPointBg;

  @NonNull
  public final LinearLayout linearShareModeOnoff;

  @NonNull
  public final LinearLayout linearSpatialAudioState;

  @NonNull
  public final LinearLayout linearTouchOnoff;

  @NonNull
  public final LinearLayout linearVolume;

  @NonNull
  public final LogviewBinding loginfo;

  @NonNull
  public final TextView micStateText;

  @NonNull
  public final RadioButton mimiSwitchOff;

  @NonNull
  public final RadioButton mimiSwitchOpen;

  @NonNull
  public final RadioGroup mimiSwitchType;

  @NonNull
  public final Button next;

  @NonNull
  public final Button pause;

  @NonNull
  public final Button play;

  @NonNull
  public final Button prev;

  @NonNull
  public final RadioButton regulateAncAmbient;

  @NonNull
  public final RadioButton regulateAncAnc;

  @NonNull
  public final RadioButton regulateAncDefault;

  @NonNull
  public final RadioButton regulateAncOff;

  @NonNull
  public final RadioButton regulateAncSpeakthru;

  @NonNull
  public final RadioGroup regulateAncType;

  @NonNull
  public final SeekBar seekbarEq120;

  @NonNull
  public final SeekBar seekbarEq15k;

  @NonNull
  public final SeekBar seekbarEq2125;

  @NonNull
  public final SeekBar seekbarEq216k;

  @NonNull
  public final SeekBar seekbarEq21k;

  @NonNull
  public final SeekBar seekbarEq2250;

  @NonNull
  public final SeekBar seekbarEq22k;

  @NonNull
  public final SeekBar seekbarEq232;

  @NonNull
  public final SeekBar seekbarEq24k;

  @NonNull
  public final SeekBar seekbarEq250;

  @NonNull
  public final SeekBar seekbarEq2500;

  @NonNull
  public final SeekBar seekbarEq264;

  @NonNull
  public final SeekBar seekbarEq28k;

  @NonNull
  public final LinearLayout seekbarEq2Bg;

  @NonNull
  public final SeekBar seekbarEq2k;

  @NonNull
  public final SeekBar seekbarEq5k;

  @NonNull
  public final SeekBar seekbarEq60;

  @NonNull
  public final SeekBar seekbarEq750;

  @NonNull
  public final LinearLayout seekbarEqBg;

  @NonNull
  public final SeekBar seekbarMimiSwitchIntensity;

  @NonNull
  public final SeekBar seekbarMimiSwitchPreset;

  @NonNull
  public final SeekBar seekbarVolume;

  @NonNull
  public final RadioButton shareModeSwitchOff;

  @NonNull
  public final RadioButton shareModeSwitchOpen;

  @NonNull
  public final RadioGroup shareModeSwitchType;

  @NonNull
  public final RadioButton spatialAudioSwitch360;

  @NonNull
  public final RadioButton spatialAudioSwitchOff;

  @NonNull
  public final RadioButton spatialAudioSwitchOff2;

  @NonNull
  public final RadioButton spatialAudioSwitchOpen;

  @NonNull
  public final RadioButton spatialAudioSwitchSpa;

  @NonNull
  public final RadioGroup spatialAudioSwitchType;

  @NonNull
  public final RadioGroup spatialAudioSwitchType2;

  @NonNull
  public final TextView sppStateText;

  @NonNull
  public final Button startOtaBtn;

  @NonNull
  public final SwitchButton switchButtonDisableSwipeLeft;

  @NonNull
  public final SwitchButton switchButtonDisableSwipeRight;

  @NonNull
  public final SwitchButton switchButtonInEarDetectionAll;

  @NonNull
  public final SwitchButton switchButtonInEarDetectionLeft;

  @NonNull
  public final SwitchButton switchButtonInEarDetectionRight;

  @NonNull
  public final SwitchButton switchButtonMultiPoint;

  @NonNull
  public final SwitchButton switchButtonTouchOnoff;

  @NonNull
  public final SwitchButton switchButtonWdPrompt;

  @NonNull
  public final TextView techLevelText;

  @NonNull
  public final TextView textAncDefault;

  @NonNull
  public final TextView textAncOff;

  @NonNull
  public final TextView textAncSpeakthru;

  @NonNull
  public final TextView textBoxBattery;

  @NonNull
  public final TextView textCodecType;

  @NonNull
  public final TextView textEq120;

  @NonNull
  public final TextView textEq15k;

  @NonNull
  public final TextView textEq2125;

  @NonNull
  public final TextView textEq216k;

  @NonNull
  public final TextView textEq21k;

  @NonNull
  public final TextView textEq2250;

  @NonNull
  public final TextView textEq22k;

  @NonNull
  public final TextView textEq232;

  @NonNull
  public final TextView textEq24k;

  @NonNull
  public final TextView textEq250;

  @NonNull
  public final TextView textEq2500;

  @NonNull
  public final TextView textEq264;

  @NonNull
  public final TextView textEq28k;

  @NonNull
  public final TextView textEq2k;

  @NonNull
  public final TextView textEq5k;

  @NonNull
  public final TextView textEq60;

  @NonNull
  public final TextView textEq750;

  @NonNull
  public final TextView textInEarDetectionLeft;

  @NonNull
  public final TextView textInEarDetectionRight;

  @NonNull
  public final TextView textLeftBattery;

  @NonNull
  public final TextView textMasterEar;

  @NonNull
  public final TextView textMimiSwitchIntensity;

  @NonNull
  public final TextView textMimiSwitchPreset;

  @NonNull
  public final TextView textRightBattery;

  @NonNull
  public final TextView textSlaveEar;

  @NonNull
  public final TextView textTwsConnectState;

  @NonNull
  public final ToolbarBinding tool;

  @NonNull
  public final TextView tvClickFuncDisable;

  @NonNull
  public final TextView tvGetAutoCenterModeResult;

  @NonNull
  public final TextView tvGetHeadTrackingResult;

  @NonNull
  public final TextView tvGetImuOrientationResult;

  @NonNull
  public final TextView tvVolume;

  @NonNull
  public final TextView tvVolumeCur;

  @NonNull
  public final LinearLayout wearBg1;

  @NonNull
  public final LinearLayout wearBg2;

  @NonNull
  public final LinearLayout wearBg3;

  @NonNull
  public final LinearLayout wearBg4;

  private ActivityCommandsetBinding(@NonNull LinearLayout rootView, @NonNull RadioGroup ancNewType,
      @NonNull RadioButton ancNewTypeAdapt, @NonNull RadioButton ancNewTypeHigh,
      @NonNull RadioButton ancNewTypeLow, @NonNull RadioButton ancNewTypeMid,
      @NonNull RadioButton besSpatialSwitchOff, @NonNull RadioButton besSpatialSwitchOpen,
      @NonNull RadioGroup besSpatialSwitchType, @NonNull TextView btStateText,
      @NonNull Button btnGetAutoCenterMode, @NonNull Button btnGetHeadTracking,
      @NonNull Button btnGetImuOrientation, @NonNull Button btnVolume,
      @NonNull Button buttonGetBoxBattery, @NonNull Button buttonGetCodecType,
      @NonNull Button buttonGetLeftBattery, @NonNull Button buttonGetRightBattery,
      @NonNull LinearLayout buttonSettingBg, @NonNull TextView buttonState1,
      @NonNull TextView buttonState2, @NonNull TextView buttonState3,
      @NonNull TextView buttonState4, @NonNull TextView buttonState5,
      @NonNull TextView buttonState6, @NonNull TextView buttonState7,
      @NonNull TextView buttonState8, @NonNull Button cevaFastRecentre,
      @NonNull Button cevaRecentre, @NonNull Button cevaSlowRecentre,
      @NonNull RadioButton cevaSwitchOff, @NonNull RadioButton cevaSwitchOpen,
      @NonNull RadioGroup cevaSwitchType, @NonNull Button checkLeftSpeaker,
      @NonNull Button checkMicState, @NonNull Button checkRightSpeaker,
      @NonNull TextView commandSetCurrentProductModel, @NonNull TextView commandSetCurrentVersion,
      @NonNull EditText commandSetReceiveData, @NonNull Button disconnect,
      @NonNull TextView dolbySwitchTitle, @NonNull Button dolbyTurnOff,
      @NonNull Button dolbyTypeMovie, @NonNull Button dolbyTypeNatual,
      @NonNull RadioGroup earbudsClickFunc0, @NonNull RadioGroup earbudsClickFunc1,
      @NonNull RadioGroup earbudsClickFunc2, @NonNull RadioGroup earbudsClickFunc3,
      @NonNull RadioButton earbudsClickFuncAlgo, @NonNull RadioButton earbudsClickFuncAmbientMusic,
      @NonNull RadioButton earbudsClickFuncAssistant, @NonNull RadioButton earbudsClickFuncCallBack,
      @NonNull RadioButton earbudsClickFuncDisable, @NonNull RadioButton earbudsClickFuncGameMode,
      @NonNull RadioButton earbudsClickFuncLastMusic,
      @NonNull RadioButton earbudsClickFuncNextMusic,
      @NonNull RadioButton earbudsClickFuncPlayMusic,
      @NonNull RadioButton earbudsClickFuncPlayPauseMusic,
      @NonNull RadioButton earbudsClickFuncSpeakthru,
      @NonNull RadioButton earbudsClickFuncStopMusic,
      @NonNull RadioButton earbudsClickFuncVolumeAdd,
      @NonNull RadioButton earbudsClickFuncVolumeLose, @NonNull Button earbudsClickLeft,
      @NonNull Button earbudsClickRight, @NonNull Button earbudsDoubleClickLeft,
      @NonNull Button earbudsDoubleClickRight, @NonNull Button earbudsLongPressLeft,
      @NonNull Button earbudsLongPressRight, @NonNull Button earbudsTripleClickLeft,
      @NonNull Button earbudsTripleClickRight, @NonNull RadioGroup eqBasetype,
      @NonNull RadioGroup eqBasetype2, @NonNull RadioButton eqBasetype2BassBoost,
      @NonNull RadioButton eqBasetype2Classic, @NonNull RadioButton eqBasetype2HipHop,
      @NonNull RadioButton eqBasetype2Jazz, @NonNull RadioButton eqBasetype2Podcast,
      @NonNull RadioButton eqBasetype2Pop, @NonNull RadioButton eqBasetype2Rock,
      @NonNull RadioButton eqBasetypeClassic, @NonNull RadioButton eqBasetypeCountry,
      @NonNull RadioButton eqBasetypeJazz, @NonNull RadioButton eqBasetypePop,
      @NonNull RadioButton eqBasetypeRock, @NonNull LinearLayout eqBgview,
      @NonNull RadioButton eqSwitchOff, @NonNull RadioButton eqSwitchOpen,
      @NonNull RadioGroup eqSwitchType, @NonNull Button eqTest, @NonNull EditText eqText,
      @NonNull Button factoryResetCmdSet, @NonNull RadioButton gameModeClose,
      @NonNull RadioButton gameModeOpen, @NonNull RadioGroup gameModeType,
      @NonNull Button getBtState, @NonNull Button getSppState, @NonNull Button getTechLevel,
      @NonNull Button headTrackingOff, @NonNull Button headTrackingOffPro,
      @NonNull Button headTrackingOn, @NonNull Button headTrackingOnPro,
      @NonNull RadioButton ledSwitch2Flash, @NonNull RadioButton ledSwitch2Off,
      @NonNull RadioButton ledSwitch2Open0, @NonNull RadioButton ledSwitch2Open1,
      @NonNull RadioButton ledSwitchFlash, @NonNull RadioButton ledSwitchOff,
      @NonNull RadioButton ledSwitchOpen, @NonNull RadioGroup ledSwitchType,
      @NonNull RadioGroup ledSwitchType2, @NonNull View lineWearDetect,
      @NonNull LinearLayout linearAncNewState, @NonNull LinearLayout linearAncState,
      @NonNull LinearLayout linearBesSpatialState, @NonNull LinearLayout linearCevaState,
      @NonNull LinearLayout linearDisableSwipe, @NonNull LinearLayout linearDolbyState,
      @NonNull LinearLayout linearFunctionBtn, @NonNull LinearLayout linearGameMode,
      @NonNull LinearLayout linearGetCodecType, @NonNull LinearLayout linearHeadTrackingPro,
      @NonNull LinearLayout linearLedOnoffState, @NonNull LinearLayout linearMimiState,
      @NonNull LinearLayout linearMultiPointBg, @NonNull LinearLayout linearShareModeOnoff,
      @NonNull LinearLayout linearSpatialAudioState, @NonNull LinearLayout linearTouchOnoff,
      @NonNull LinearLayout linearVolume, @NonNull LogviewBinding loginfo,
      @NonNull TextView micStateText, @NonNull RadioButton mimiSwitchOff,
      @NonNull RadioButton mimiSwitchOpen, @NonNull RadioGroup mimiSwitchType, @NonNull Button next,
      @NonNull Button pause, @NonNull Button play, @NonNull Button prev,
      @NonNull RadioButton regulateAncAmbient, @NonNull RadioButton regulateAncAnc,
      @NonNull RadioButton regulateAncDefault, @NonNull RadioButton regulateAncOff,
      @NonNull RadioButton regulateAncSpeakthru, @NonNull RadioGroup regulateAncType,
      @NonNull SeekBar seekbarEq120, @NonNull SeekBar seekbarEq15k, @NonNull SeekBar seekbarEq2125,
      @NonNull SeekBar seekbarEq216k, @NonNull SeekBar seekbarEq21k, @NonNull SeekBar seekbarEq2250,
      @NonNull SeekBar seekbarEq22k, @NonNull SeekBar seekbarEq232, @NonNull SeekBar seekbarEq24k,
      @NonNull SeekBar seekbarEq250, @NonNull SeekBar seekbarEq2500, @NonNull SeekBar seekbarEq264,
      @NonNull SeekBar seekbarEq28k, @NonNull LinearLayout seekbarEq2Bg,
      @NonNull SeekBar seekbarEq2k, @NonNull SeekBar seekbarEq5k, @NonNull SeekBar seekbarEq60,
      @NonNull SeekBar seekbarEq750, @NonNull LinearLayout seekbarEqBg,
      @NonNull SeekBar seekbarMimiSwitchIntensity, @NonNull SeekBar seekbarMimiSwitchPreset,
      @NonNull SeekBar seekbarVolume, @NonNull RadioButton shareModeSwitchOff,
      @NonNull RadioButton shareModeSwitchOpen, @NonNull RadioGroup shareModeSwitchType,
      @NonNull RadioButton spatialAudioSwitch360, @NonNull RadioButton spatialAudioSwitchOff,
      @NonNull RadioButton spatialAudioSwitchOff2, @NonNull RadioButton spatialAudioSwitchOpen,
      @NonNull RadioButton spatialAudioSwitchSpa, @NonNull RadioGroup spatialAudioSwitchType,
      @NonNull RadioGroup spatialAudioSwitchType2, @NonNull TextView sppStateText,
      @NonNull Button startOtaBtn, @NonNull SwitchButton switchButtonDisableSwipeLeft,
      @NonNull SwitchButton switchButtonDisableSwipeRight,
      @NonNull SwitchButton switchButtonInEarDetectionAll,
      @NonNull SwitchButton switchButtonInEarDetectionLeft,
      @NonNull SwitchButton switchButtonInEarDetectionRight,
      @NonNull SwitchButton switchButtonMultiPoint, @NonNull SwitchButton switchButtonTouchOnoff,
      @NonNull SwitchButton switchButtonWdPrompt, @NonNull TextView techLevelText,
      @NonNull TextView textAncDefault, @NonNull TextView textAncOff,
      @NonNull TextView textAncSpeakthru, @NonNull TextView textBoxBattery,
      @NonNull TextView textCodecType, @NonNull TextView textEq120, @NonNull TextView textEq15k,
      @NonNull TextView textEq2125, @NonNull TextView textEq216k, @NonNull TextView textEq21k,
      @NonNull TextView textEq2250, @NonNull TextView textEq22k, @NonNull TextView textEq232,
      @NonNull TextView textEq24k, @NonNull TextView textEq250, @NonNull TextView textEq2500,
      @NonNull TextView textEq264, @NonNull TextView textEq28k, @NonNull TextView textEq2k,
      @NonNull TextView textEq5k, @NonNull TextView textEq60, @NonNull TextView textEq750,
      @NonNull TextView textInEarDetectionLeft, @NonNull TextView textInEarDetectionRight,
      @NonNull TextView textLeftBattery, @NonNull TextView textMasterEar,
      @NonNull TextView textMimiSwitchIntensity, @NonNull TextView textMimiSwitchPreset,
      @NonNull TextView textRightBattery, @NonNull TextView textSlaveEar,
      @NonNull TextView textTwsConnectState, @NonNull ToolbarBinding tool,
      @NonNull TextView tvClickFuncDisable, @NonNull TextView tvGetAutoCenterModeResult,
      @NonNull TextView tvGetHeadTrackingResult, @NonNull TextView tvGetImuOrientationResult,
      @NonNull TextView tvVolume, @NonNull TextView tvVolumeCur, @NonNull LinearLayout wearBg1,
      @NonNull LinearLayout wearBg2, @NonNull LinearLayout wearBg3, @NonNull LinearLayout wearBg4) {
    this.rootView = rootView;
    this.ancNewType = ancNewType;
    this.ancNewTypeAdapt = ancNewTypeAdapt;
    this.ancNewTypeHigh = ancNewTypeHigh;
    this.ancNewTypeLow = ancNewTypeLow;
    this.ancNewTypeMid = ancNewTypeMid;
    this.besSpatialSwitchOff = besSpatialSwitchOff;
    this.besSpatialSwitchOpen = besSpatialSwitchOpen;
    this.besSpatialSwitchType = besSpatialSwitchType;
    this.btStateText = btStateText;
    this.btnGetAutoCenterMode = btnGetAutoCenterMode;
    this.btnGetHeadTracking = btnGetHeadTracking;
    this.btnGetImuOrientation = btnGetImuOrientation;
    this.btnVolume = btnVolume;
    this.buttonGetBoxBattery = buttonGetBoxBattery;
    this.buttonGetCodecType = buttonGetCodecType;
    this.buttonGetLeftBattery = buttonGetLeftBattery;
    this.buttonGetRightBattery = buttonGetRightBattery;
    this.buttonSettingBg = buttonSettingBg;
    this.buttonState1 = buttonState1;
    this.buttonState2 = buttonState2;
    this.buttonState3 = buttonState3;
    this.buttonState4 = buttonState4;
    this.buttonState5 = buttonState5;
    this.buttonState6 = buttonState6;
    this.buttonState7 = buttonState7;
    this.buttonState8 = buttonState8;
    this.cevaFastRecentre = cevaFastRecentre;
    this.cevaRecentre = cevaRecentre;
    this.cevaSlowRecentre = cevaSlowRecentre;
    this.cevaSwitchOff = cevaSwitchOff;
    this.cevaSwitchOpen = cevaSwitchOpen;
    this.cevaSwitchType = cevaSwitchType;
    this.checkLeftSpeaker = checkLeftSpeaker;
    this.checkMicState = checkMicState;
    this.checkRightSpeaker = checkRightSpeaker;
    this.commandSetCurrentProductModel = commandSetCurrentProductModel;
    this.commandSetCurrentVersion = commandSetCurrentVersion;
    this.commandSetReceiveData = commandSetReceiveData;
    this.disconnect = disconnect;
    this.dolbySwitchTitle = dolbySwitchTitle;
    this.dolbyTurnOff = dolbyTurnOff;
    this.dolbyTypeMovie = dolbyTypeMovie;
    this.dolbyTypeNatual = dolbyTypeNatual;
    this.earbudsClickFunc0 = earbudsClickFunc0;
    this.earbudsClickFunc1 = earbudsClickFunc1;
    this.earbudsClickFunc2 = earbudsClickFunc2;
    this.earbudsClickFunc3 = earbudsClickFunc3;
    this.earbudsClickFuncAlgo = earbudsClickFuncAlgo;
    this.earbudsClickFuncAmbientMusic = earbudsClickFuncAmbientMusic;
    this.earbudsClickFuncAssistant = earbudsClickFuncAssistant;
    this.earbudsClickFuncCallBack = earbudsClickFuncCallBack;
    this.earbudsClickFuncDisable = earbudsClickFuncDisable;
    this.earbudsClickFuncGameMode = earbudsClickFuncGameMode;
    this.earbudsClickFuncLastMusic = earbudsClickFuncLastMusic;
    this.earbudsClickFuncNextMusic = earbudsClickFuncNextMusic;
    this.earbudsClickFuncPlayMusic = earbudsClickFuncPlayMusic;
    this.earbudsClickFuncPlayPauseMusic = earbudsClickFuncPlayPauseMusic;
    this.earbudsClickFuncSpeakthru = earbudsClickFuncSpeakthru;
    this.earbudsClickFuncStopMusic = earbudsClickFuncStopMusic;
    this.earbudsClickFuncVolumeAdd = earbudsClickFuncVolumeAdd;
    this.earbudsClickFuncVolumeLose = earbudsClickFuncVolumeLose;
    this.earbudsClickLeft = earbudsClickLeft;
    this.earbudsClickRight = earbudsClickRight;
    this.earbudsDoubleClickLeft = earbudsDoubleClickLeft;
    this.earbudsDoubleClickRight = earbudsDoubleClickRight;
    this.earbudsLongPressLeft = earbudsLongPressLeft;
    this.earbudsLongPressRight = earbudsLongPressRight;
    this.earbudsTripleClickLeft = earbudsTripleClickLeft;
    this.earbudsTripleClickRight = earbudsTripleClickRight;
    this.eqBasetype = eqBasetype;
    this.eqBasetype2 = eqBasetype2;
    this.eqBasetype2BassBoost = eqBasetype2BassBoost;
    this.eqBasetype2Classic = eqBasetype2Classic;
    this.eqBasetype2HipHop = eqBasetype2HipHop;
    this.eqBasetype2Jazz = eqBasetype2Jazz;
    this.eqBasetype2Podcast = eqBasetype2Podcast;
    this.eqBasetype2Pop = eqBasetype2Pop;
    this.eqBasetype2Rock = eqBasetype2Rock;
    this.eqBasetypeClassic = eqBasetypeClassic;
    this.eqBasetypeCountry = eqBasetypeCountry;
    this.eqBasetypeJazz = eqBasetypeJazz;
    this.eqBasetypePop = eqBasetypePop;
    this.eqBasetypeRock = eqBasetypeRock;
    this.eqBgview = eqBgview;
    this.eqSwitchOff = eqSwitchOff;
    this.eqSwitchOpen = eqSwitchOpen;
    this.eqSwitchType = eqSwitchType;
    this.eqTest = eqTest;
    this.eqText = eqText;
    this.factoryResetCmdSet = factoryResetCmdSet;
    this.gameModeClose = gameModeClose;
    this.gameModeOpen = gameModeOpen;
    this.gameModeType = gameModeType;
    this.getBtState = getBtState;
    this.getSppState = getSppState;
    this.getTechLevel = getTechLevel;
    this.headTrackingOff = headTrackingOff;
    this.headTrackingOffPro = headTrackingOffPro;
    this.headTrackingOn = headTrackingOn;
    this.headTrackingOnPro = headTrackingOnPro;
    this.ledSwitch2Flash = ledSwitch2Flash;
    this.ledSwitch2Off = ledSwitch2Off;
    this.ledSwitch2Open0 = ledSwitch2Open0;
    this.ledSwitch2Open1 = ledSwitch2Open1;
    this.ledSwitchFlash = ledSwitchFlash;
    this.ledSwitchOff = ledSwitchOff;
    this.ledSwitchOpen = ledSwitchOpen;
    this.ledSwitchType = ledSwitchType;
    this.ledSwitchType2 = ledSwitchType2;
    this.lineWearDetect = lineWearDetect;
    this.linearAncNewState = linearAncNewState;
    this.linearAncState = linearAncState;
    this.linearBesSpatialState = linearBesSpatialState;
    this.linearCevaState = linearCevaState;
    this.linearDisableSwipe = linearDisableSwipe;
    this.linearDolbyState = linearDolbyState;
    this.linearFunctionBtn = linearFunctionBtn;
    this.linearGameMode = linearGameMode;
    this.linearGetCodecType = linearGetCodecType;
    this.linearHeadTrackingPro = linearHeadTrackingPro;
    this.linearLedOnoffState = linearLedOnoffState;
    this.linearMimiState = linearMimiState;
    this.linearMultiPointBg = linearMultiPointBg;
    this.linearShareModeOnoff = linearShareModeOnoff;
    this.linearSpatialAudioState = linearSpatialAudioState;
    this.linearTouchOnoff = linearTouchOnoff;
    this.linearVolume = linearVolume;
    this.loginfo = loginfo;
    this.micStateText = micStateText;
    this.mimiSwitchOff = mimiSwitchOff;
    this.mimiSwitchOpen = mimiSwitchOpen;
    this.mimiSwitchType = mimiSwitchType;
    this.next = next;
    this.pause = pause;
    this.play = play;
    this.prev = prev;
    this.regulateAncAmbient = regulateAncAmbient;
    this.regulateAncAnc = regulateAncAnc;
    this.regulateAncDefault = regulateAncDefault;
    this.regulateAncOff = regulateAncOff;
    this.regulateAncSpeakthru = regulateAncSpeakthru;
    this.regulateAncType = regulateAncType;
    this.seekbarEq120 = seekbarEq120;
    this.seekbarEq15k = seekbarEq15k;
    this.seekbarEq2125 = seekbarEq2125;
    this.seekbarEq216k = seekbarEq216k;
    this.seekbarEq21k = seekbarEq21k;
    this.seekbarEq2250 = seekbarEq2250;
    this.seekbarEq22k = seekbarEq22k;
    this.seekbarEq232 = seekbarEq232;
    this.seekbarEq24k = seekbarEq24k;
    this.seekbarEq250 = seekbarEq250;
    this.seekbarEq2500 = seekbarEq2500;
    this.seekbarEq264 = seekbarEq264;
    this.seekbarEq28k = seekbarEq28k;
    this.seekbarEq2Bg = seekbarEq2Bg;
    this.seekbarEq2k = seekbarEq2k;
    this.seekbarEq5k = seekbarEq5k;
    this.seekbarEq60 = seekbarEq60;
    this.seekbarEq750 = seekbarEq750;
    this.seekbarEqBg = seekbarEqBg;
    this.seekbarMimiSwitchIntensity = seekbarMimiSwitchIntensity;
    this.seekbarMimiSwitchPreset = seekbarMimiSwitchPreset;
    this.seekbarVolume = seekbarVolume;
    this.shareModeSwitchOff = shareModeSwitchOff;
    this.shareModeSwitchOpen = shareModeSwitchOpen;
    this.shareModeSwitchType = shareModeSwitchType;
    this.spatialAudioSwitch360 = spatialAudioSwitch360;
    this.spatialAudioSwitchOff = spatialAudioSwitchOff;
    this.spatialAudioSwitchOff2 = spatialAudioSwitchOff2;
    this.spatialAudioSwitchOpen = spatialAudioSwitchOpen;
    this.spatialAudioSwitchSpa = spatialAudioSwitchSpa;
    this.spatialAudioSwitchType = spatialAudioSwitchType;
    this.spatialAudioSwitchType2 = spatialAudioSwitchType2;
    this.sppStateText = sppStateText;
    this.startOtaBtn = startOtaBtn;
    this.switchButtonDisableSwipeLeft = switchButtonDisableSwipeLeft;
    this.switchButtonDisableSwipeRight = switchButtonDisableSwipeRight;
    this.switchButtonInEarDetectionAll = switchButtonInEarDetectionAll;
    this.switchButtonInEarDetectionLeft = switchButtonInEarDetectionLeft;
    this.switchButtonInEarDetectionRight = switchButtonInEarDetectionRight;
    this.switchButtonMultiPoint = switchButtonMultiPoint;
    this.switchButtonTouchOnoff = switchButtonTouchOnoff;
    this.switchButtonWdPrompt = switchButtonWdPrompt;
    this.techLevelText = techLevelText;
    this.textAncDefault = textAncDefault;
    this.textAncOff = textAncOff;
    this.textAncSpeakthru = textAncSpeakthru;
    this.textBoxBattery = textBoxBattery;
    this.textCodecType = textCodecType;
    this.textEq120 = textEq120;
    this.textEq15k = textEq15k;
    this.textEq2125 = textEq2125;
    this.textEq216k = textEq216k;
    this.textEq21k = textEq21k;
    this.textEq2250 = textEq2250;
    this.textEq22k = textEq22k;
    this.textEq232 = textEq232;
    this.textEq24k = textEq24k;
    this.textEq250 = textEq250;
    this.textEq2500 = textEq2500;
    this.textEq264 = textEq264;
    this.textEq28k = textEq28k;
    this.textEq2k = textEq2k;
    this.textEq5k = textEq5k;
    this.textEq60 = textEq60;
    this.textEq750 = textEq750;
    this.textInEarDetectionLeft = textInEarDetectionLeft;
    this.textInEarDetectionRight = textInEarDetectionRight;
    this.textLeftBattery = textLeftBattery;
    this.textMasterEar = textMasterEar;
    this.textMimiSwitchIntensity = textMimiSwitchIntensity;
    this.textMimiSwitchPreset = textMimiSwitchPreset;
    this.textRightBattery = textRightBattery;
    this.textSlaveEar = textSlaveEar;
    this.textTwsConnectState = textTwsConnectState;
    this.tool = tool;
    this.tvClickFuncDisable = tvClickFuncDisable;
    this.tvGetAutoCenterModeResult = tvGetAutoCenterModeResult;
    this.tvGetHeadTrackingResult = tvGetHeadTrackingResult;
    this.tvGetImuOrientationResult = tvGetImuOrientationResult;
    this.tvVolume = tvVolume;
    this.tvVolumeCur = tvVolumeCur;
    this.wearBg1 = wearBg1;
    this.wearBg2 = wearBg2;
    this.wearBg3 = wearBg3;
    this.wearBg4 = wearBg4;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCommandsetBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCommandsetBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_commandset, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCommandsetBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.anc_new_type;
      RadioGroup ancNewType = rootView.findViewById(id);
      if (ancNewType == null) {
        break missingId;
      }

      id = R.id.anc_new_type_adapt;
      RadioButton ancNewTypeAdapt = rootView.findViewById(id);
      if (ancNewTypeAdapt == null) {
        break missingId;
      }

      id = R.id.anc_new_type_high;
      RadioButton ancNewTypeHigh = rootView.findViewById(id);
      if (ancNewTypeHigh == null) {
        break missingId;
      }

      id = R.id.anc_new_type_low;
      RadioButton ancNewTypeLow = rootView.findViewById(id);
      if (ancNewTypeLow == null) {
        break missingId;
      }

      id = R.id.anc_new_type_mid;
      RadioButton ancNewTypeMid = rootView.findViewById(id);
      if (ancNewTypeMid == null) {
        break missingId;
      }

      id = R.id.bes_spatial_switch_off;
      RadioButton besSpatialSwitchOff = rootView.findViewById(id);
      if (besSpatialSwitchOff == null) {
        break missingId;
      }

      id = R.id.bes_spatial_switch_open;
      RadioButton besSpatialSwitchOpen = rootView.findViewById(id);
      if (besSpatialSwitchOpen == null) {
        break missingId;
      }

      id = R.id.bes_spatial_switch_type;
      RadioGroup besSpatialSwitchType = rootView.findViewById(id);
      if (besSpatialSwitchType == null) {
        break missingId;
      }

      id = R.id.bt_state_text;
      TextView btStateText = rootView.findViewById(id);
      if (btStateText == null) {
        break missingId;
      }

      id = R.id.btn_get_auto_center_mode;
      Button btnGetAutoCenterMode = rootView.findViewById(id);
      if (btnGetAutoCenterMode == null) {
        break missingId;
      }

      id = R.id.btn_get_head_tracking;
      Button btnGetHeadTracking = rootView.findViewById(id);
      if (btnGetHeadTracking == null) {
        break missingId;
      }

      id = R.id.btn_get_imu_orientation;
      Button btnGetImuOrientation = rootView.findViewById(id);
      if (btnGetImuOrientation == null) {
        break missingId;
      }

      id = R.id.btn_volume;
      Button btnVolume = rootView.findViewById(id);
      if (btnVolume == null) {
        break missingId;
      }

      id = R.id.button_get_box_battery;
      Button buttonGetBoxBattery = rootView.findViewById(id);
      if (buttonGetBoxBattery == null) {
        break missingId;
      }

      id = R.id.button_get_codec_type;
      Button buttonGetCodecType = rootView.findViewById(id);
      if (buttonGetCodecType == null) {
        break missingId;
      }

      id = R.id.button_get_left_battery;
      Button buttonGetLeftBattery = rootView.findViewById(id);
      if (buttonGetLeftBattery == null) {
        break missingId;
      }

      id = R.id.button_get_right_battery;
      Button buttonGetRightBattery = rootView.findViewById(id);
      if (buttonGetRightBattery == null) {
        break missingId;
      }

      id = R.id.button_setting_bg;
      LinearLayout buttonSettingBg = rootView.findViewById(id);
      if (buttonSettingBg == null) {
        break missingId;
      }

      id = R.id.button_state_1;
      TextView buttonState1 = rootView.findViewById(id);
      if (buttonState1 == null) {
        break missingId;
      }

      id = R.id.button_state_2;
      TextView buttonState2 = rootView.findViewById(id);
      if (buttonState2 == null) {
        break missingId;
      }

      id = R.id.button_state_3;
      TextView buttonState3 = rootView.findViewById(id);
      if (buttonState3 == null) {
        break missingId;
      }

      id = R.id.button_state_4;
      TextView buttonState4 = rootView.findViewById(id);
      if (buttonState4 == null) {
        break missingId;
      }

      id = R.id.button_state_5;
      TextView buttonState5 = rootView.findViewById(id);
      if (buttonState5 == null) {
        break missingId;
      }

      id = R.id.button_state_6;
      TextView buttonState6 = rootView.findViewById(id);
      if (buttonState6 == null) {
        break missingId;
      }

      id = R.id.button_state_7;
      TextView buttonState7 = rootView.findViewById(id);
      if (buttonState7 == null) {
        break missingId;
      }

      id = R.id.button_state_8;
      TextView buttonState8 = rootView.findViewById(id);
      if (buttonState8 == null) {
        break missingId;
      }

      id = R.id.ceva_fast_recentre;
      Button cevaFastRecentre = rootView.findViewById(id);
      if (cevaFastRecentre == null) {
        break missingId;
      }

      id = R.id.ceva_recentre;
      Button cevaRecentre = rootView.findViewById(id);
      if (cevaRecentre == null) {
        break missingId;
      }

      id = R.id.ceva_slow_recentre;
      Button cevaSlowRecentre = rootView.findViewById(id);
      if (cevaSlowRecentre == null) {
        break missingId;
      }

      id = R.id.ceva_switch_off;
      RadioButton cevaSwitchOff = rootView.findViewById(id);
      if (cevaSwitchOff == null) {
        break missingId;
      }

      id = R.id.ceva_switch_open;
      RadioButton cevaSwitchOpen = rootView.findViewById(id);
      if (cevaSwitchOpen == null) {
        break missingId;
      }

      id = R.id.ceva_switch_type;
      RadioGroup cevaSwitchType = rootView.findViewById(id);
      if (cevaSwitchType == null) {
        break missingId;
      }

      id = R.id.check_left_speaker;
      Button checkLeftSpeaker = rootView.findViewById(id);
      if (checkLeftSpeaker == null) {
        break missingId;
      }

      id = R.id.check_mic_state;
      Button checkMicState = rootView.findViewById(id);
      if (checkMicState == null) {
        break missingId;
      }

      id = R.id.check_right_speaker;
      Button checkRightSpeaker = rootView.findViewById(id);
      if (checkRightSpeaker == null) {
        break missingId;
      }

      id = R.id.command_set_current_product_model;
      TextView commandSetCurrentProductModel = rootView.findViewById(id);
      if (commandSetCurrentProductModel == null) {
        break missingId;
      }

      id = R.id.command_set_current_version;
      TextView commandSetCurrentVersion = rootView.findViewById(id);
      if (commandSetCurrentVersion == null) {
        break missingId;
      }

      id = R.id.command_set_receive_data;
      EditText commandSetReceiveData = rootView.findViewById(id);
      if (commandSetReceiveData == null) {
        break missingId;
      }

      id = R.id.disconnect;
      Button disconnect = rootView.findViewById(id);
      if (disconnect == null) {
        break missingId;
      }

      id = R.id.dolby_switch_title;
      TextView dolbySwitchTitle = rootView.findViewById(id);
      if (dolbySwitchTitle == null) {
        break missingId;
      }

      id = R.id.dolby_turn_off;
      Button dolbyTurnOff = rootView.findViewById(id);
      if (dolbyTurnOff == null) {
        break missingId;
      }

      id = R.id.dolby_type_movie;
      Button dolbyTypeMovie = rootView.findViewById(id);
      if (dolbyTypeMovie == null) {
        break missingId;
      }

      id = R.id.dolby_type_natual;
      Button dolbyTypeNatual = rootView.findViewById(id);
      if (dolbyTypeNatual == null) {
        break missingId;
      }

      id = R.id.earbuds_click_func_0;
      RadioGroup earbudsClickFunc0 = rootView.findViewById(id);
      if (earbudsClickFunc0 == null) {
        break missingId;
      }

      id = R.id.earbuds_click_func_1;
      RadioGroup earbudsClickFunc1 = rootView.findViewById(id);
      if (earbudsClickFunc1 == null) {
        break missingId;
      }

      id = R.id.earbuds_click_func_2;
      RadioGroup earbudsClickFunc2 = rootView.findViewById(id);
      if (earbudsClickFunc2 == null) {
        break missingId;
      }

      id = R.id.earbuds_click_func_3;
      RadioGroup earbudsClickFunc3 = rootView.findViewById(id);
      if (earbudsClickFunc3 == null) {
        break missingId;
      }

      id = R.id.earbuds_click_func_algo;
      RadioButton earbudsClickFuncAlgo = rootView.findViewById(id);
      if (earbudsClickFuncAlgo == null) {
        break missingId;
      }

      id = R.id.earbuds_click_func_ambient_music;
      RadioButton earbudsClickFuncAmbientMusic = rootView.findViewById(id);
      if (earbudsClickFuncAmbientMusic == null) {
        break missingId;
      }

      id = R.id.earbuds_click_func_assistant;
      RadioButton earbudsClickFuncAssistant = rootView.findViewById(id);
      if (earbudsClickFuncAssistant == null) {
        break missingId;
      }

      id = R.id.earbuds_click_func_call_back;
      RadioButton earbudsClickFuncCallBack = rootView.findViewById(id);
      if (earbudsClickFuncCallBack == null) {
        break missingId;
      }

      id = R.id.earbuds_click_func_disable;
      RadioButton earbudsClickFuncDisable = rootView.findViewById(id);
      if (earbudsClickFuncDisable == null) {
        break missingId;
      }

      id = R.id.earbuds_click_func_game_mode;
      RadioButton earbudsClickFuncGameMode = rootView.findViewById(id);
      if (earbudsClickFuncGameMode == null) {
        break missingId;
      }

      id = R.id.earbuds_click_func_last_music;
      RadioButton earbudsClickFuncLastMusic = rootView.findViewById(id);
      if (earbudsClickFuncLastMusic == null) {
        break missingId;
      }

      id = R.id.earbuds_click_func_next_music;
      RadioButton earbudsClickFuncNextMusic = rootView.findViewById(id);
      if (earbudsClickFuncNextMusic == null) {
        break missingId;
      }

      id = R.id.earbuds_click_func_play_music;
      RadioButton earbudsClickFuncPlayMusic = rootView.findViewById(id);
      if (earbudsClickFuncPlayMusic == null) {
        break missingId;
      }

      id = R.id.earbuds_click_func_play_pause_music;
      RadioButton earbudsClickFuncPlayPauseMusic = rootView.findViewById(id);
      if (earbudsClickFuncPlayPauseMusic == null) {
        break missingId;
      }

      id = R.id.earbuds_click_func_speakthru;
      RadioButton earbudsClickFuncSpeakthru = rootView.findViewById(id);
      if (earbudsClickFuncSpeakthru == null) {
        break missingId;
      }

      id = R.id.earbuds_click_func_stop_music;
      RadioButton earbudsClickFuncStopMusic = rootView.findViewById(id);
      if (earbudsClickFuncStopMusic == null) {
        break missingId;
      }

      id = R.id.earbuds_click_func_volume_add;
      RadioButton earbudsClickFuncVolumeAdd = rootView.findViewById(id);
      if (earbudsClickFuncVolumeAdd == null) {
        break missingId;
      }

      id = R.id.earbuds_click_func_volume_lose;
      RadioButton earbudsClickFuncVolumeLose = rootView.findViewById(id);
      if (earbudsClickFuncVolumeLose == null) {
        break missingId;
      }

      id = R.id.earbuds_click_left;
      Button earbudsClickLeft = rootView.findViewById(id);
      if (earbudsClickLeft == null) {
        break missingId;
      }

      id = R.id.earbuds_click_right;
      Button earbudsClickRight = rootView.findViewById(id);
      if (earbudsClickRight == null) {
        break missingId;
      }

      id = R.id.earbuds_double_click_left;
      Button earbudsDoubleClickLeft = rootView.findViewById(id);
      if (earbudsDoubleClickLeft == null) {
        break missingId;
      }

      id = R.id.earbuds_double_click_right;
      Button earbudsDoubleClickRight = rootView.findViewById(id);
      if (earbudsDoubleClickRight == null) {
        break missingId;
      }

      id = R.id.earbuds_long_press_left;
      Button earbudsLongPressLeft = rootView.findViewById(id);
      if (earbudsLongPressLeft == null) {
        break missingId;
      }

      id = R.id.earbuds_long_press_right;
      Button earbudsLongPressRight = rootView.findViewById(id);
      if (earbudsLongPressRight == null) {
        break missingId;
      }

      id = R.id.earbuds_triple_click_left;
      Button earbudsTripleClickLeft = rootView.findViewById(id);
      if (earbudsTripleClickLeft == null) {
        break missingId;
      }

      id = R.id.earbuds_triple_click_right;
      Button earbudsTripleClickRight = rootView.findViewById(id);
      if (earbudsTripleClickRight == null) {
        break missingId;
      }

      id = R.id.eq_basetype;
      RadioGroup eqBasetype = rootView.findViewById(id);
      if (eqBasetype == null) {
        break missingId;
      }

      id = R.id.eq_basetype2;
      RadioGroup eqBasetype2 = rootView.findViewById(id);
      if (eqBasetype2 == null) {
        break missingId;
      }

      id = R.id.eq_basetype2_bass_boost;
      RadioButton eqBasetype2BassBoost = rootView.findViewById(id);
      if (eqBasetype2BassBoost == null) {
        break missingId;
      }

      id = R.id.eq_basetype2_classic;
      RadioButton eqBasetype2Classic = rootView.findViewById(id);
      if (eqBasetype2Classic == null) {
        break missingId;
      }

      id = R.id.eq_basetype2_hip_hop;
      RadioButton eqBasetype2HipHop = rootView.findViewById(id);
      if (eqBasetype2HipHop == null) {
        break missingId;
      }

      id = R.id.eq_basetype2_jazz;
      RadioButton eqBasetype2Jazz = rootView.findViewById(id);
      if (eqBasetype2Jazz == null) {
        break missingId;
      }

      id = R.id.eq_basetype2_podcast;
      RadioButton eqBasetype2Podcast = rootView.findViewById(id);
      if (eqBasetype2Podcast == null) {
        break missingId;
      }

      id = R.id.eq_basetype2_pop;
      RadioButton eqBasetype2Pop = rootView.findViewById(id);
      if (eqBasetype2Pop == null) {
        break missingId;
      }

      id = R.id.eq_basetype2_rock;
      RadioButton eqBasetype2Rock = rootView.findViewById(id);
      if (eqBasetype2Rock == null) {
        break missingId;
      }

      id = R.id.eq_basetype_classic;
      RadioButton eqBasetypeClassic = rootView.findViewById(id);
      if (eqBasetypeClassic == null) {
        break missingId;
      }

      id = R.id.eq_basetype_country;
      RadioButton eqBasetypeCountry = rootView.findViewById(id);
      if (eqBasetypeCountry == null) {
        break missingId;
      }

      id = R.id.eq_basetype_jazz;
      RadioButton eqBasetypeJazz = rootView.findViewById(id);
      if (eqBasetypeJazz == null) {
        break missingId;
      }

      id = R.id.eq_basetype_pop;
      RadioButton eqBasetypePop = rootView.findViewById(id);
      if (eqBasetypePop == null) {
        break missingId;
      }

      id = R.id.eq_basetype_rock;
      RadioButton eqBasetypeRock = rootView.findViewById(id);
      if (eqBasetypeRock == null) {
        break missingId;
      }

      id = R.id.eq_bgview;
      LinearLayout eqBgview = rootView.findViewById(id);
      if (eqBgview == null) {
        break missingId;
      }

      id = R.id.eq_switch_off;
      RadioButton eqSwitchOff = rootView.findViewById(id);
      if (eqSwitchOff == null) {
        break missingId;
      }

      id = R.id.eq_switch_open;
      RadioButton eqSwitchOpen = rootView.findViewById(id);
      if (eqSwitchOpen == null) {
        break missingId;
      }

      id = R.id.eq_switch_type;
      RadioGroup eqSwitchType = rootView.findViewById(id);
      if (eqSwitchType == null) {
        break missingId;
      }

      id = R.id.eq_test;
      Button eqTest = rootView.findViewById(id);
      if (eqTest == null) {
        break missingId;
      }

      id = R.id.eq_text;
      EditText eqText = rootView.findViewById(id);
      if (eqText == null) {
        break missingId;
      }

      id = R.id.factory_reset_cmd_set;
      Button factoryResetCmdSet = rootView.findViewById(id);
      if (factoryResetCmdSet == null) {
        break missingId;
      }

      id = R.id.game_mode_close;
      RadioButton gameModeClose = rootView.findViewById(id);
      if (gameModeClose == null) {
        break missingId;
      }

      id = R.id.game_mode_open;
      RadioButton gameModeOpen = rootView.findViewById(id);
      if (gameModeOpen == null) {
        break missingId;
      }

      id = R.id.game_mode_type;
      RadioGroup gameModeType = rootView.findViewById(id);
      if (gameModeType == null) {
        break missingId;
      }

      id = R.id.get_bt_state;
      Button getBtState = rootView.findViewById(id);
      if (getBtState == null) {
        break missingId;
      }

      id = R.id.get_spp_state;
      Button getSppState = rootView.findViewById(id);
      if (getSppState == null) {
        break missingId;
      }

      id = R.id.get_tech_level;
      Button getTechLevel = rootView.findViewById(id);
      if (getTechLevel == null) {
        break missingId;
      }

      id = R.id.head_tracking_off;
      Button headTrackingOff = rootView.findViewById(id);
      if (headTrackingOff == null) {
        break missingId;
      }

      id = R.id.head_tracking_off_pro;
      Button headTrackingOffPro = rootView.findViewById(id);
      if (headTrackingOffPro == null) {
        break missingId;
      }

      id = R.id.head_tracking_on;
      Button headTrackingOn = rootView.findViewById(id);
      if (headTrackingOn == null) {
        break missingId;
      }

      id = R.id.head_tracking_on_pro;
      Button headTrackingOnPro = rootView.findViewById(id);
      if (headTrackingOnPro == null) {
        break missingId;
      }

      id = R.id.led_switch_2_flash;
      RadioButton ledSwitch2Flash = rootView.findViewById(id);
      if (ledSwitch2Flash == null) {
        break missingId;
      }

      id = R.id.led_switch_2_off;
      RadioButton ledSwitch2Off = rootView.findViewById(id);
      if (ledSwitch2Off == null) {
        break missingId;
      }

      id = R.id.led_switch_2_open_0;
      RadioButton ledSwitch2Open0 = rootView.findViewById(id);
      if (ledSwitch2Open0 == null) {
        break missingId;
      }

      id = R.id.led_switch_2_open_1;
      RadioButton ledSwitch2Open1 = rootView.findViewById(id);
      if (ledSwitch2Open1 == null) {
        break missingId;
      }

      id = R.id.led_switch_flash;
      RadioButton ledSwitchFlash = rootView.findViewById(id);
      if (ledSwitchFlash == null) {
        break missingId;
      }

      id = R.id.led_switch_off;
      RadioButton ledSwitchOff = rootView.findViewById(id);
      if (ledSwitchOff == null) {
        break missingId;
      }

      id = R.id.led_switch_open;
      RadioButton ledSwitchOpen = rootView.findViewById(id);
      if (ledSwitchOpen == null) {
        break missingId;
      }

      id = R.id.led_switch_type;
      RadioGroup ledSwitchType = rootView.findViewById(id);
      if (ledSwitchType == null) {
        break missingId;
      }

      id = R.id.led_switch_type_2;
      RadioGroup ledSwitchType2 = rootView.findViewById(id);
      if (ledSwitchType2 == null) {
        break missingId;
      }

      id = R.id.line_wear_detect;
      View lineWearDetect = rootView.findViewById(id);
      if (lineWearDetect == null) {
        break missingId;
      }

      id = R.id.linear_anc_new_state;
      LinearLayout linearAncNewState = rootView.findViewById(id);
      if (linearAncNewState == null) {
        break missingId;
      }

      id = R.id.linear_anc_state;
      LinearLayout linearAncState = rootView.findViewById(id);
      if (linearAncState == null) {
        break missingId;
      }

      id = R.id.linear_bes_spatial_state;
      LinearLayout linearBesSpatialState = rootView.findViewById(id);
      if (linearBesSpatialState == null) {
        break missingId;
      }

      id = R.id.linear_ceva_state;
      LinearLayout linearCevaState = rootView.findViewById(id);
      if (linearCevaState == null) {
        break missingId;
      }

      id = R.id.linear_disable_swipe;
      LinearLayout linearDisableSwipe = rootView.findViewById(id);
      if (linearDisableSwipe == null) {
        break missingId;
      }

      id = R.id.linear_dolby_state;
      LinearLayout linearDolbyState = rootView.findViewById(id);
      if (linearDolbyState == null) {
        break missingId;
      }

      id = R.id.linear_function_btn;
      LinearLayout linearFunctionBtn = rootView.findViewById(id);
      if (linearFunctionBtn == null) {
        break missingId;
      }

      id = R.id.linear_game_mode;
      LinearLayout linearGameMode = rootView.findViewById(id);
      if (linearGameMode == null) {
        break missingId;
      }

      id = R.id.linear_get_codec_type;
      LinearLayout linearGetCodecType = rootView.findViewById(id);
      if (linearGetCodecType == null) {
        break missingId;
      }

      id = R.id.linear_head_tracking_pro;
      LinearLayout linearHeadTrackingPro = rootView.findViewById(id);
      if (linearHeadTrackingPro == null) {
        break missingId;
      }

      id = R.id.linear_led_onoff_state;
      LinearLayout linearLedOnoffState = rootView.findViewById(id);
      if (linearLedOnoffState == null) {
        break missingId;
      }

      id = R.id.linear_mimi_state;
      LinearLayout linearMimiState = rootView.findViewById(id);
      if (linearMimiState == null) {
        break missingId;
      }

      id = R.id.linear_multiPoint_bg;
      LinearLayout linearMultiPointBg = rootView.findViewById(id);
      if (linearMultiPointBg == null) {
        break missingId;
      }

      id = R.id.linear_share_mode_onoff;
      LinearLayout linearShareModeOnoff = rootView.findViewById(id);
      if (linearShareModeOnoff == null) {
        break missingId;
      }

      id = R.id.linear_spatial_audio_state;
      LinearLayout linearSpatialAudioState = rootView.findViewById(id);
      if (linearSpatialAudioState == null) {
        break missingId;
      }

      id = R.id.linear_touch_onoff;
      LinearLayout linearTouchOnoff = rootView.findViewById(id);
      if (linearTouchOnoff == null) {
        break missingId;
      }

      id = R.id.linear_volume;
      LinearLayout linearVolume = rootView.findViewById(id);
      if (linearVolume == null) {
        break missingId;
      }

      id = R.id.loginfo;
      View loginfo = rootView.findViewById(id);
      if (loginfo == null) {
        break missingId;
      }
      LogviewBinding binding_loginfo = LogviewBinding.bind(loginfo);

      id = R.id.mic_state_text;
      TextView micStateText = rootView.findViewById(id);
      if (micStateText == null) {
        break missingId;
      }

      id = R.id.mimi_switch_off;
      RadioButton mimiSwitchOff = rootView.findViewById(id);
      if (mimiSwitchOff == null) {
        break missingId;
      }

      id = R.id.mimi_switch_open;
      RadioButton mimiSwitchOpen = rootView.findViewById(id);
      if (mimiSwitchOpen == null) {
        break missingId;
      }

      id = R.id.mimi_switch_type;
      RadioGroup mimiSwitchType = rootView.findViewById(id);
      if (mimiSwitchType == null) {
        break missingId;
      }

      id = R.id.next;
      Button next = rootView.findViewById(id);
      if (next == null) {
        break missingId;
      }

      id = R.id.pause;
      Button pause = rootView.findViewById(id);
      if (pause == null) {
        break missingId;
      }

      id = R.id.play;
      Button play = rootView.findViewById(id);
      if (play == null) {
        break missingId;
      }

      id = R.id.prev;
      Button prev = rootView.findViewById(id);
      if (prev == null) {
        break missingId;
      }

      id = R.id.regulate_anc_ambient;
      RadioButton regulateAncAmbient = rootView.findViewById(id);
      if (regulateAncAmbient == null) {
        break missingId;
      }

      id = R.id.regulate_anc_anc;
      RadioButton regulateAncAnc = rootView.findViewById(id);
      if (regulateAncAnc == null) {
        break missingId;
      }

      id = R.id.regulate_anc_default;
      RadioButton regulateAncDefault = rootView.findViewById(id);
      if (regulateAncDefault == null) {
        break missingId;
      }

      id = R.id.regulate_anc_off;
      RadioButton regulateAncOff = rootView.findViewById(id);
      if (regulateAncOff == null) {
        break missingId;
      }

      id = R.id.regulate_anc_speakthru;
      RadioButton regulateAncSpeakthru = rootView.findViewById(id);
      if (regulateAncSpeakthru == null) {
        break missingId;
      }

      id = R.id.regulate_anc_type;
      RadioGroup regulateAncType = rootView.findViewById(id);
      if (regulateAncType == null) {
        break missingId;
      }

      id = R.id.seekbar_eq_120;
      SeekBar seekbarEq120 = rootView.findViewById(id);
      if (seekbarEq120 == null) {
        break missingId;
      }

      id = R.id.seekbar_eq_15k;
      SeekBar seekbarEq15k = rootView.findViewById(id);
      if (seekbarEq15k == null) {
        break missingId;
      }

      id = R.id.seekbar_eq_2_125;
      SeekBar seekbarEq2125 = rootView.findViewById(id);
      if (seekbarEq2125 == null) {
        break missingId;
      }

      id = R.id.seekbar_eq_2_16k;
      SeekBar seekbarEq216k = rootView.findViewById(id);
      if (seekbarEq216k == null) {
        break missingId;
      }

      id = R.id.seekbar_eq_2_1k;
      SeekBar seekbarEq21k = rootView.findViewById(id);
      if (seekbarEq21k == null) {
        break missingId;
      }

      id = R.id.seekbar_eq_2_250;
      SeekBar seekbarEq2250 = rootView.findViewById(id);
      if (seekbarEq2250 == null) {
        break missingId;
      }

      id = R.id.seekbar_eq_2_2k;
      SeekBar seekbarEq22k = rootView.findViewById(id);
      if (seekbarEq22k == null) {
        break missingId;
      }

      id = R.id.seekbar_eq_2_32;
      SeekBar seekbarEq232 = rootView.findViewById(id);
      if (seekbarEq232 == null) {
        break missingId;
      }

      id = R.id.seekbar_eq_2_4k;
      SeekBar seekbarEq24k = rootView.findViewById(id);
      if (seekbarEq24k == null) {
        break missingId;
      }

      id = R.id.seekbar_eq_250;
      SeekBar seekbarEq250 = rootView.findViewById(id);
      if (seekbarEq250 == null) {
        break missingId;
      }

      id = R.id.seekbar_eq_2_500;
      SeekBar seekbarEq2500 = rootView.findViewById(id);
      if (seekbarEq2500 == null) {
        break missingId;
      }

      id = R.id.seekbar_eq_2_64;
      SeekBar seekbarEq264 = rootView.findViewById(id);
      if (seekbarEq264 == null) {
        break missingId;
      }

      id = R.id.seekbar_eq_2_8k;
      SeekBar seekbarEq28k = rootView.findViewById(id);
      if (seekbarEq28k == null) {
        break missingId;
      }

      id = R.id.seekbar_eq_2_bg;
      LinearLayout seekbarEq2Bg = rootView.findViewById(id);
      if (seekbarEq2Bg == null) {
        break missingId;
      }

      id = R.id.seekbar_eq_2k;
      SeekBar seekbarEq2k = rootView.findViewById(id);
      if (seekbarEq2k == null) {
        break missingId;
      }

      id = R.id.seekbar_eq_5k;
      SeekBar seekbarEq5k = rootView.findViewById(id);
      if (seekbarEq5k == null) {
        break missingId;
      }

      id = R.id.seekbar_eq_60;
      SeekBar seekbarEq60 = rootView.findViewById(id);
      if (seekbarEq60 == null) {
        break missingId;
      }

      id = R.id.seekbar_eq_750;
      SeekBar seekbarEq750 = rootView.findViewById(id);
      if (seekbarEq750 == null) {
        break missingId;
      }

      id = R.id.seekbar_eq_bg;
      LinearLayout seekbarEqBg = rootView.findViewById(id);
      if (seekbarEqBg == null) {
        break missingId;
      }

      id = R.id.seekbar_mimi_switch_intensity;
      SeekBar seekbarMimiSwitchIntensity = rootView.findViewById(id);
      if (seekbarMimiSwitchIntensity == null) {
        break missingId;
      }

      id = R.id.seekbar_mimi_switch_preset;
      SeekBar seekbarMimiSwitchPreset = rootView.findViewById(id);
      if (seekbarMimiSwitchPreset == null) {
        break missingId;
      }

      id = R.id.seekbar_volume;
      SeekBar seekbarVolume = rootView.findViewById(id);
      if (seekbarVolume == null) {
        break missingId;
      }

      id = R.id.share_mode_switch_off;
      RadioButton shareModeSwitchOff = rootView.findViewById(id);
      if (shareModeSwitchOff == null) {
        break missingId;
      }

      id = R.id.share_mode_switch_open;
      RadioButton shareModeSwitchOpen = rootView.findViewById(id);
      if (shareModeSwitchOpen == null) {
        break missingId;
      }

      id = R.id.share_mode_switch_type;
      RadioGroup shareModeSwitchType = rootView.findViewById(id);
      if (shareModeSwitchType == null) {
        break missingId;
      }

      id = R.id.spatial_audio_switch_360;
      RadioButton spatialAudioSwitch360 = rootView.findViewById(id);
      if (spatialAudioSwitch360 == null) {
        break missingId;
      }

      id = R.id.spatial_audio_switch_off;
      RadioButton spatialAudioSwitchOff = rootView.findViewById(id);
      if (spatialAudioSwitchOff == null) {
        break missingId;
      }

      id = R.id.spatial_audio_switch_off_2;
      RadioButton spatialAudioSwitchOff2 = rootView.findViewById(id);
      if (spatialAudioSwitchOff2 == null) {
        break missingId;
      }

      id = R.id.spatial_audio_switch_open;
      RadioButton spatialAudioSwitchOpen = rootView.findViewById(id);
      if (spatialAudioSwitchOpen == null) {
        break missingId;
      }

      id = R.id.spatial_audio_switch_spa;
      RadioButton spatialAudioSwitchSpa = rootView.findViewById(id);
      if (spatialAudioSwitchSpa == null) {
        break missingId;
      }

      id = R.id.spatial_audio_switch_type;
      RadioGroup spatialAudioSwitchType = rootView.findViewById(id);
      if (spatialAudioSwitchType == null) {
        break missingId;
      }

      id = R.id.spatial_audio_switch_type_2;
      RadioGroup spatialAudioSwitchType2 = rootView.findViewById(id);
      if (spatialAudioSwitchType2 == null) {
        break missingId;
      }

      id = R.id.spp_state_text;
      TextView sppStateText = rootView.findViewById(id);
      if (sppStateText == null) {
        break missingId;
      }

      id = R.id.start_ota_btn;
      Button startOtaBtn = rootView.findViewById(id);
      if (startOtaBtn == null) {
        break missingId;
      }

      id = R.id.switchButton_disable_swipe_left;
      SwitchButton switchButtonDisableSwipeLeft = rootView.findViewById(id);
      if (switchButtonDisableSwipeLeft == null) {
        break missingId;
      }

      id = R.id.switchButton_disable_swipe_right;
      SwitchButton switchButtonDisableSwipeRight = rootView.findViewById(id);
      if (switchButtonDisableSwipeRight == null) {
        break missingId;
      }

      id = R.id.switchButton_in_ear_detection_all;
      SwitchButton switchButtonInEarDetectionAll = rootView.findViewById(id);
      if (switchButtonInEarDetectionAll == null) {
        break missingId;
      }

      id = R.id.switchButton_in_ear_detection_left;
      SwitchButton switchButtonInEarDetectionLeft = rootView.findViewById(id);
      if (switchButtonInEarDetectionLeft == null) {
        break missingId;
      }

      id = R.id.switchButton_in_ear_detection_right;
      SwitchButton switchButtonInEarDetectionRight = rootView.findViewById(id);
      if (switchButtonInEarDetectionRight == null) {
        break missingId;
      }

      id = R.id.switchButton_multiPoint;
      SwitchButton switchButtonMultiPoint = rootView.findViewById(id);
      if (switchButtonMultiPoint == null) {
        break missingId;
      }

      id = R.id.switchButton_touch_onoff;
      SwitchButton switchButtonTouchOnoff = rootView.findViewById(id);
      if (switchButtonTouchOnoff == null) {
        break missingId;
      }

      id = R.id.switchButton_wd_prompt;
      SwitchButton switchButtonWdPrompt = rootView.findViewById(id);
      if (switchButtonWdPrompt == null) {
        break missingId;
      }

      id = R.id.tech_level_text;
      TextView techLevelText = rootView.findViewById(id);
      if (techLevelText == null) {
        break missingId;
      }

      id = R.id.text_anc_default;
      TextView textAncDefault = rootView.findViewById(id);
      if (textAncDefault == null) {
        break missingId;
      }

      id = R.id.text_anc_off;
      TextView textAncOff = rootView.findViewById(id);
      if (textAncOff == null) {
        break missingId;
      }

      id = R.id.text_anc_speakthru;
      TextView textAncSpeakthru = rootView.findViewById(id);
      if (textAncSpeakthru == null) {
        break missingId;
      }

      id = R.id.text_box_battery;
      TextView textBoxBattery = rootView.findViewById(id);
      if (textBoxBattery == null) {
        break missingId;
      }

      id = R.id.text_codec_type;
      TextView textCodecType = rootView.findViewById(id);
      if (textCodecType == null) {
        break missingId;
      }

      id = R.id.text_eq_120;
      TextView textEq120 = rootView.findViewById(id);
      if (textEq120 == null) {
        break missingId;
      }

      id = R.id.text_eq_15k;
      TextView textEq15k = rootView.findViewById(id);
      if (textEq15k == null) {
        break missingId;
      }

      id = R.id.text_eq_2_125;
      TextView textEq2125 = rootView.findViewById(id);
      if (textEq2125 == null) {
        break missingId;
      }

      id = R.id.text_eq_2_16k;
      TextView textEq216k = rootView.findViewById(id);
      if (textEq216k == null) {
        break missingId;
      }

      id = R.id.text_eq_2_1k;
      TextView textEq21k = rootView.findViewById(id);
      if (textEq21k == null) {
        break missingId;
      }

      id = R.id.text_eq_2_250;
      TextView textEq2250 = rootView.findViewById(id);
      if (textEq2250 == null) {
        break missingId;
      }

      id = R.id.text_eq_2_2k;
      TextView textEq22k = rootView.findViewById(id);
      if (textEq22k == null) {
        break missingId;
      }

      id = R.id.text_eq_2_32;
      TextView textEq232 = rootView.findViewById(id);
      if (textEq232 == null) {
        break missingId;
      }

      id = R.id.text_eq_2_4k;
      TextView textEq24k = rootView.findViewById(id);
      if (textEq24k == null) {
        break missingId;
      }

      id = R.id.text_eq_250;
      TextView textEq250 = rootView.findViewById(id);
      if (textEq250 == null) {
        break missingId;
      }

      id = R.id.text_eq_2_500;
      TextView textEq2500 = rootView.findViewById(id);
      if (textEq2500 == null) {
        break missingId;
      }

      id = R.id.text_eq_2_64;
      TextView textEq264 = rootView.findViewById(id);
      if (textEq264 == null) {
        break missingId;
      }

      id = R.id.text_eq_2_8k;
      TextView textEq28k = rootView.findViewById(id);
      if (textEq28k == null) {
        break missingId;
      }

      id = R.id.text_eq_2k;
      TextView textEq2k = rootView.findViewById(id);
      if (textEq2k == null) {
        break missingId;
      }

      id = R.id.text_eq_5k;
      TextView textEq5k = rootView.findViewById(id);
      if (textEq5k == null) {
        break missingId;
      }

      id = R.id.text_eq_60;
      TextView textEq60 = rootView.findViewById(id);
      if (textEq60 == null) {
        break missingId;
      }

      id = R.id.text_eq_750;
      TextView textEq750 = rootView.findViewById(id);
      if (textEq750 == null) {
        break missingId;
      }

      id = R.id.text_in_ear_detection_left;
      TextView textInEarDetectionLeft = rootView.findViewById(id);
      if (textInEarDetectionLeft == null) {
        break missingId;
      }

      id = R.id.text_in_ear_detection_right;
      TextView textInEarDetectionRight = rootView.findViewById(id);
      if (textInEarDetectionRight == null) {
        break missingId;
      }

      id = R.id.text_left_battery;
      TextView textLeftBattery = rootView.findViewById(id);
      if (textLeftBattery == null) {
        break missingId;
      }

      id = R.id.text_master_ear;
      TextView textMasterEar = rootView.findViewById(id);
      if (textMasterEar == null) {
        break missingId;
      }

      id = R.id.text_mimi_switch_intensity;
      TextView textMimiSwitchIntensity = rootView.findViewById(id);
      if (textMimiSwitchIntensity == null) {
        break missingId;
      }

      id = R.id.text_mimi_switch_preset;
      TextView textMimiSwitchPreset = rootView.findViewById(id);
      if (textMimiSwitchPreset == null) {
        break missingId;
      }

      id = R.id.text_right_battery;
      TextView textRightBattery = rootView.findViewById(id);
      if (textRightBattery == null) {
        break missingId;
      }

      id = R.id.text_slave_ear;
      TextView textSlaveEar = rootView.findViewById(id);
      if (textSlaveEar == null) {
        break missingId;
      }

      id = R.id.text_tws_connect_state;
      TextView textTwsConnectState = rootView.findViewById(id);
      if (textTwsConnectState == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      id = R.id.tv_click_func_disable;
      TextView tvClickFuncDisable = rootView.findViewById(id);
      if (tvClickFuncDisable == null) {
        break missingId;
      }

      id = R.id.tv_get_auto_center_mode_result;
      TextView tvGetAutoCenterModeResult = rootView.findViewById(id);
      if (tvGetAutoCenterModeResult == null) {
        break missingId;
      }

      id = R.id.tv_get_head_tracking_result;
      TextView tvGetHeadTrackingResult = rootView.findViewById(id);
      if (tvGetHeadTrackingResult == null) {
        break missingId;
      }

      id = R.id.tv_get_imu_orientation_result;
      TextView tvGetImuOrientationResult = rootView.findViewById(id);
      if (tvGetImuOrientationResult == null) {
        break missingId;
      }

      id = R.id.tv_volume;
      TextView tvVolume = rootView.findViewById(id);
      if (tvVolume == null) {
        break missingId;
      }

      id = R.id.tv_volume_cur;
      TextView tvVolumeCur = rootView.findViewById(id);
      if (tvVolumeCur == null) {
        break missingId;
      }

      id = R.id.wear_bg1;
      LinearLayout wearBg1 = rootView.findViewById(id);
      if (wearBg1 == null) {
        break missingId;
      }

      id = R.id.wear_bg2;
      LinearLayout wearBg2 = rootView.findViewById(id);
      if (wearBg2 == null) {
        break missingId;
      }

      id = R.id.wear_bg3;
      LinearLayout wearBg3 = rootView.findViewById(id);
      if (wearBg3 == null) {
        break missingId;
      }

      id = R.id.wear_bg4;
      LinearLayout wearBg4 = rootView.findViewById(id);
      if (wearBg4 == null) {
        break missingId;
      }

      return new ActivityCommandsetBinding((LinearLayout) rootView, ancNewType, ancNewTypeAdapt,
          ancNewTypeHigh, ancNewTypeLow, ancNewTypeMid, besSpatialSwitchOff, besSpatialSwitchOpen,
          besSpatialSwitchType, btStateText, btnGetAutoCenterMode, btnGetHeadTracking,
          btnGetImuOrientation, btnVolume, buttonGetBoxBattery, buttonGetCodecType,
          buttonGetLeftBattery, buttonGetRightBattery, buttonSettingBg, buttonState1, buttonState2,
          buttonState3, buttonState4, buttonState5, buttonState6, buttonState7, buttonState8,
          cevaFastRecentre, cevaRecentre, cevaSlowRecentre, cevaSwitchOff, cevaSwitchOpen,
          cevaSwitchType, checkLeftSpeaker, checkMicState, checkRightSpeaker,
          commandSetCurrentProductModel, commandSetCurrentVersion, commandSetReceiveData,
          disconnect, dolbySwitchTitle, dolbyTurnOff, dolbyTypeMovie, dolbyTypeNatual,
          earbudsClickFunc0, earbudsClickFunc1, earbudsClickFunc2, earbudsClickFunc3,
          earbudsClickFuncAlgo, earbudsClickFuncAmbientMusic, earbudsClickFuncAssistant,
          earbudsClickFuncCallBack, earbudsClickFuncDisable, earbudsClickFuncGameMode,
          earbudsClickFuncLastMusic, earbudsClickFuncNextMusic, earbudsClickFuncPlayMusic,
          earbudsClickFuncPlayPauseMusic, earbudsClickFuncSpeakthru, earbudsClickFuncStopMusic,
          earbudsClickFuncVolumeAdd, earbudsClickFuncVolumeLose, earbudsClickLeft,
          earbudsClickRight, earbudsDoubleClickLeft, earbudsDoubleClickRight, earbudsLongPressLeft,
          earbudsLongPressRight, earbudsTripleClickLeft, earbudsTripleClickRight, eqBasetype,
          eqBasetype2, eqBasetype2BassBoost, eqBasetype2Classic, eqBasetype2HipHop, eqBasetype2Jazz,
          eqBasetype2Podcast, eqBasetype2Pop, eqBasetype2Rock, eqBasetypeClassic, eqBasetypeCountry,
          eqBasetypeJazz, eqBasetypePop, eqBasetypeRock, eqBgview, eqSwitchOff, eqSwitchOpen,
          eqSwitchType, eqTest, eqText, factoryResetCmdSet, gameModeClose, gameModeOpen,
          gameModeType, getBtState, getSppState, getTechLevel, headTrackingOff, headTrackingOffPro,
          headTrackingOn, headTrackingOnPro, ledSwitch2Flash, ledSwitch2Off, ledSwitch2Open0,
          ledSwitch2Open1, ledSwitchFlash, ledSwitchOff, ledSwitchOpen, ledSwitchType,
          ledSwitchType2, lineWearDetect, linearAncNewState, linearAncState, linearBesSpatialState,
          linearCevaState, linearDisableSwipe, linearDolbyState, linearFunctionBtn, linearGameMode,
          linearGetCodecType, linearHeadTrackingPro, linearLedOnoffState, linearMimiState,
          linearMultiPointBg, linearShareModeOnoff, linearSpatialAudioState, linearTouchOnoff,
          linearVolume, binding_loginfo, micStateText, mimiSwitchOff, mimiSwitchOpen,
          mimiSwitchType, next, pause, play, prev, regulateAncAmbient, regulateAncAnc,
          regulateAncDefault, regulateAncOff, regulateAncSpeakthru, regulateAncType, seekbarEq120,
          seekbarEq15k, seekbarEq2125, seekbarEq216k, seekbarEq21k, seekbarEq2250, seekbarEq22k,
          seekbarEq232, seekbarEq24k, seekbarEq250, seekbarEq2500, seekbarEq264, seekbarEq28k,
          seekbarEq2Bg, seekbarEq2k, seekbarEq5k, seekbarEq60, seekbarEq750, seekbarEqBg,
          seekbarMimiSwitchIntensity, seekbarMimiSwitchPreset, seekbarVolume, shareModeSwitchOff,
          shareModeSwitchOpen, shareModeSwitchType, spatialAudioSwitch360, spatialAudioSwitchOff,
          spatialAudioSwitchOff2, spatialAudioSwitchOpen, spatialAudioSwitchSpa,
          spatialAudioSwitchType, spatialAudioSwitchType2, sppStateText, startOtaBtn,
          switchButtonDisableSwipeLeft, switchButtonDisableSwipeRight,
          switchButtonInEarDetectionAll, switchButtonInEarDetectionLeft,
          switchButtonInEarDetectionRight, switchButtonMultiPoint, switchButtonTouchOnoff,
          switchButtonWdPrompt, techLevelText, textAncDefault, textAncOff, textAncSpeakthru,
          textBoxBattery, textCodecType, textEq120, textEq15k, textEq2125, textEq216k, textEq21k,
          textEq2250, textEq22k, textEq232, textEq24k, textEq250, textEq2500, textEq264, textEq28k,
          textEq2k, textEq5k, textEq60, textEq750, textInEarDetectionLeft, textInEarDetectionRight,
          textLeftBattery, textMasterEar, textMimiSwitchIntensity, textMimiSwitchPreset,
          textRightBattery, textSlaveEar, textTwsConnectState, binding_tool, tvClickFuncDisable,
          tvGetAutoCenterModeResult, tvGetHeadTrackingResult, tvGetImuOrientationResult, tvVolume,
          tvVolumeCur, wearBg1, wearBg2, wearBg3, wearBg4);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
