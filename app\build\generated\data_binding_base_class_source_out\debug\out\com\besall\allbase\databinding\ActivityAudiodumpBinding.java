// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.Chronometer;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAudiodumpBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView deviceText;

  @NonNull
  public final EditText etFileType;

  @NonNull
  public final RadioButton fileTypeCustom;

  @NonNull
  public final RadioButton fileTypeFlac;

  @NonNull
  public final RadioButton fileTypePcm;

  @NonNull
  public final RadioButton fileTypeWav;

  @NonNull
  public final RadioGroup insertData;

  @NonNull
  public final RadioButton insertDataLost;

  @NonNull
  public final RadioButton insertDataMax;

  @NonNull
  public final RadioButton insertDataZero;

  @NonNull
  public final ImageView iv;

  @NonNull
  public final LogviewBinding loginfo;

  @NonNull
  public final TextView packageLostText;

  @NonNull
  public final TextView packageReceiveText;

  @NonNull
  public final Chronometer recordtime;

  @NonNull
  public final RadioGroup saveFileType;

  @NonNull
  public final Button sppStop;

  @NonNull
  public final Button startAudioDump;

  @NonNull
  public final Button stopAudioDump;

  @NonNull
  public final EditText streamStartText;

  @NonNull
  public final ToolbarBinding tool;

  private ActivityAudiodumpBinding(@NonNull LinearLayout rootView, @NonNull TextView deviceText,
      @NonNull EditText etFileType, @NonNull RadioButton fileTypeCustom,
      @NonNull RadioButton fileTypeFlac, @NonNull RadioButton fileTypePcm,
      @NonNull RadioButton fileTypeWav, @NonNull RadioGroup insertData,
      @NonNull RadioButton insertDataLost, @NonNull RadioButton insertDataMax,
      @NonNull RadioButton insertDataZero, @NonNull ImageView iv, @NonNull LogviewBinding loginfo,
      @NonNull TextView packageLostText, @NonNull TextView packageReceiveText,
      @NonNull Chronometer recordtime, @NonNull RadioGroup saveFileType, @NonNull Button sppStop,
      @NonNull Button startAudioDump, @NonNull Button stopAudioDump,
      @NonNull EditText streamStartText, @NonNull ToolbarBinding tool) {
    this.rootView = rootView;
    this.deviceText = deviceText;
    this.etFileType = etFileType;
    this.fileTypeCustom = fileTypeCustom;
    this.fileTypeFlac = fileTypeFlac;
    this.fileTypePcm = fileTypePcm;
    this.fileTypeWav = fileTypeWav;
    this.insertData = insertData;
    this.insertDataLost = insertDataLost;
    this.insertDataMax = insertDataMax;
    this.insertDataZero = insertDataZero;
    this.iv = iv;
    this.loginfo = loginfo;
    this.packageLostText = packageLostText;
    this.packageReceiveText = packageReceiveText;
    this.recordtime = recordtime;
    this.saveFileType = saveFileType;
    this.sppStop = sppStop;
    this.startAudioDump = startAudioDump;
    this.stopAudioDump = stopAudioDump;
    this.streamStartText = streamStartText;
    this.tool = tool;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAudiodumpBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAudiodumpBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_audiodump, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAudiodumpBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.device_text;
      TextView deviceText = rootView.findViewById(id);
      if (deviceText == null) {
        break missingId;
      }

      id = R.id.et_file_type;
      EditText etFileType = rootView.findViewById(id);
      if (etFileType == null) {
        break missingId;
      }

      id = R.id.file_type_custom;
      RadioButton fileTypeCustom = rootView.findViewById(id);
      if (fileTypeCustom == null) {
        break missingId;
      }

      id = R.id.file_type_flac;
      RadioButton fileTypeFlac = rootView.findViewById(id);
      if (fileTypeFlac == null) {
        break missingId;
      }

      id = R.id.file_type_pcm;
      RadioButton fileTypePcm = rootView.findViewById(id);
      if (fileTypePcm == null) {
        break missingId;
      }

      id = R.id.file_type_wav;
      RadioButton fileTypeWav = rootView.findViewById(id);
      if (fileTypeWav == null) {
        break missingId;
      }

      id = R.id.insert_data;
      RadioGroup insertData = rootView.findViewById(id);
      if (insertData == null) {
        break missingId;
      }

      id = R.id.insert_data_lost;
      RadioButton insertDataLost = rootView.findViewById(id);
      if (insertDataLost == null) {
        break missingId;
      }

      id = R.id.insert_data_max;
      RadioButton insertDataMax = rootView.findViewById(id);
      if (insertDataMax == null) {
        break missingId;
      }

      id = R.id.insert_data_zero;
      RadioButton insertDataZero = rootView.findViewById(id);
      if (insertDataZero == null) {
        break missingId;
      }

      id = R.id.iv;
      ImageView iv = rootView.findViewById(id);
      if (iv == null) {
        break missingId;
      }

      id = R.id.loginfo;
      View loginfo = rootView.findViewById(id);
      if (loginfo == null) {
        break missingId;
      }
      LogviewBinding binding_loginfo = LogviewBinding.bind(loginfo);

      id = R.id.package_lost_text;
      TextView packageLostText = rootView.findViewById(id);
      if (packageLostText == null) {
        break missingId;
      }

      id = R.id.package_receive_text;
      TextView packageReceiveText = rootView.findViewById(id);
      if (packageReceiveText == null) {
        break missingId;
      }

      id = R.id.recordtime;
      Chronometer recordtime = rootView.findViewById(id);
      if (recordtime == null) {
        break missingId;
      }

      id = R.id.save_file_type;
      RadioGroup saveFileType = rootView.findViewById(id);
      if (saveFileType == null) {
        break missingId;
      }

      id = R.id.spp_stop;
      Button sppStop = rootView.findViewById(id);
      if (sppStop == null) {
        break missingId;
      }

      id = R.id.start_audio_dump;
      Button startAudioDump = rootView.findViewById(id);
      if (startAudioDump == null) {
        break missingId;
      }

      id = R.id.stop_audio_dump;
      Button stopAudioDump = rootView.findViewById(id);
      if (stopAudioDump == null) {
        break missingId;
      }

      id = R.id.stream_start_text;
      EditText streamStartText = rootView.findViewById(id);
      if (streamStartText == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      return new ActivityAudiodumpBinding((LinearLayout) rootView, deviceText, etFileType,
          fileTypeCustom, fileTypeFlac, fileTypePcm, fileTypeWav, insertData, insertDataLost,
          insertDataMax, insertDataZero, iv, binding_loginfo, packageLostText, packageReceiveText,
          recordtime, saveFileType, sppStop, startAudioDump, stopAudioDump, streamStartText,
          binding_tool);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
