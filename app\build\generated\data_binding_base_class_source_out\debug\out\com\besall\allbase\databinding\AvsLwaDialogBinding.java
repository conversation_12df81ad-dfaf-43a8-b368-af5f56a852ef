// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RelativeLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class AvsLwaDialogBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final EditText avsLwaWifiName;

  @NonNull
  public final EditText avsLwaWifiPsw;

  @NonNull
  public final Button dialogCancel;

  @NonNull
  public final Button dialogConfirm;

  private AvsLwaDialogBinding(@NonNull RelativeLayout rootView, @NonNull EditText avsLwaWifiName,
      @NonNull EditText avsLwaWifiPsw, @NonNull Button dialogCancel,
      @NonNull Button dialogConfirm) {
    this.rootView = rootView;
    this.avsLwaWifiName = avsLwaWifiName;
    this.avsLwaWifiPsw = avsLwaWifiPsw;
    this.dialogCancel = dialogCancel;
    this.dialogConfirm = dialogConfirm;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static AvsLwaDialogBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static AvsLwaDialogBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.avs_lwa_dialog, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static AvsLwaDialogBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.avs_lwa_wifi_name;
      EditText avsLwaWifiName = rootView.findViewById(id);
      if (avsLwaWifiName == null) {
        break missingId;
      }

      id = R.id.avs_lwa_wifi_psw;
      EditText avsLwaWifiPsw = rootView.findViewById(id);
      if (avsLwaWifiPsw == null) {
        break missingId;
      }

      id = R.id.dialog_cancel;
      Button dialogCancel = rootView.findViewById(id);
      if (dialogCancel == null) {
        break missingId;
      }

      id = R.id.dialog_confirm;
      Button dialogConfirm = rootView.findViewById(id);
      if (dialogConfirm == null) {
        break missingId;
      }

      return new AvsLwaDialogBinding((RelativeLayout) rootView, avsLwaWifiName, avsLwaWifiPsw,
          dialogCancel, dialogConfirm);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
