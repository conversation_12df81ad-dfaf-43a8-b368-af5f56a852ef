<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="act_ota" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\act_ota.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/act_ota_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="396" endOffset="12"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="10" startOffset="4" endLine="395" endOffset="14"/></Target><Target id="@+id/tool" tag="binding_1" include="toolbar"><Expressions/><location startLine="15" startOffset="8" endLine="18" endOffset="13"/></Target><Target id="@+id/usertext" view="TextView"><Expressions/><location startLine="24" startOffset="12" endLine="32" endOffset="41"/></Target><Target id="@+id/userchoose" view="RadioGroup"><Expressions/><location startLine="34" startOffset="12" endLine="67" endOffset="24"/></Target><Target id="@+id/user_fw" view="RadioButton"><Expressions/><location startLine="40" startOffset="16" endLine="47" endOffset="45"/></Target><Target id="@+id/user_lg" view="RadioButton"><Expressions/><location startLine="49" startOffset="16" endLine="55" endOffset="49"/></Target><Target id="@+id/user_combo" view="RadioButton"><Expressions/><location startLine="59" startOffset="16" endLine="65" endOffset="42"/></Target><Target id="@+id/transferacktext" view="TextView"><Expressions/><location startLine="69" startOffset="12" endLine="77" endOffset="41"/></Target><Target id="@+id/transferack" view="RadioGroup"><Expressions/><location startLine="78" startOffset="12" endLine="102" endOffset="24"/></Target><Target id="@+id/withoutack" view="RadioButton"><Expressions/><location startLine="84" startOffset="16" endLine="91" endOffset="44"/></Target><Target id="@+id/withack" view="RadioButton"><Expressions/><location startLine="93" startOffset="16" endLine="99" endOffset="41"/></Target><Target id="@+id/upgradetext" view="TextView"><Expressions/><location startLine="104" startOffset="12" endLine="112" endOffset="41"/></Target><Target id="@+id/upgradechoose" view="RadioGroup"><Expressions/><location startLine="114" startOffset="12" endLine="138" endOffset="24"/></Target><Target id="@+id/slow_mod" view="RadioButton"><Expressions/><location startLine="120" startOffset="16" endLine="127" endOffset="47"/></Target><Target id="@+id/fast_mod" view="RadioButton"><Expressions/><location startLine="129" startOffset="16" endLine="135" endOffset="45"/></Target><Target id="@+id/oldotaupgradetype" view="RadioGroup"><Expressions/><location startLine="140" startOffset="12" endLine="182" endOffset="24"/></Target><Target id="@+id/onlyleftearbud" view="RadioButton"><Expressions/><location startLine="146" startOffset="16" endLine="153" endOffset="61"/></Target><Target id="@+id/onlyrightearbud" view="RadioButton"><Expressions/><location startLine="155" startOffset="16" endLine="161" endOffset="62"/></Target><Target id="@+id/bothearbudsamebin" view="RadioButton"><Expressions/><location startLine="164" startOffset="16" endLine="170" endOffset="68"/></Target><Target id="@+id/bothearbuddiffbin" view="RadioButton"><Expressions/><location startLine="172" startOffset="16" endLine="178" endOffset="69"/></Target><Target id="@+id/device_address" view="TextView"><Expressions/><location startLine="202" startOffset="12" endLine="207" endOffset="35"/></Target><Target id="@+id/device_name" view="TextView"><Expressions/><location startLine="209" startOffset="12" endLine="215" endOffset="34"/></Target><Target id="@+id/pick_device" view="Button"><Expressions/><location startLine="224" startOffset="12" endLine="232" endOffset="54"/></Target><Target id="@+id/connect_device" view="Button"><Expressions/><location startLine="239" startOffset="12" endLine="249" endOffset="55"/></Target><Target id="@+id/current_version_details" view="TextView"><Expressions/><location startLine="262" startOffset="8" endLine="269" endOffset="37"/></Target><Target id="@+id/current_ota_file_title" view="TextView"><Expressions/><location startLine="271" startOffset="8" endLine="278" endOffset="37"/></Target><Target id="@+id/ota_file" view="TextView"><Expressions/><location startLine="280" startOffset="8" endLine="286" endOffset="31"/></Target><Target id="@+id/pick_ota_file" view="Button"><Expressions/><location startLine="295" startOffset="12" endLine="304" endOffset="54"/></Target><Target id="@+id/ota_file_right" view="TextView"><Expressions/><location startLine="307" startOffset="8" endLine="313" endOffset="31"/></Target><Target id="@+id/pick_ota_file_right" view="Button"><Expressions/><location startLine="322" startOffset="12" endLine="331" endOffset="60"/></Target><Target id="@+id/start_ota" view="Button"><Expressions/><location startLine="349" startOffset="12" endLine="357" endOffset="50"/></Target><Target id="@+id/ota_info" view="TextView"><Expressions/><location startLine="360" startOffset="4" endLine="366" endOffset="27"/></Target><Target id="@+id/ota_progress" view="ProgressBar"><Expressions/><location startLine="369" startOffset="4" endLine="376" endOffset="59"/></Target><Target id="@+id/ota_status" view="TextView"><Expressions/><location startLine="378" startOffset="8" endLine="385" endOffset="41"/></Target><Target id="@+id/ota_info_list" view="TextView"><Expressions/><location startLine="388" startOffset="4" endLine="394" endOffset="14"/></Target></Targets></Layout>