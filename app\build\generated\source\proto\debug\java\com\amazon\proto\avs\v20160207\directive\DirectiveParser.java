// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: directiveParser.proto

package com.amazon.proto.avs.v20160207.directive;

public final class DirectiveParser {
  private DirectiveParser() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface DirectiveParserProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:directive.DirectiveParserProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.directive.DirectiveParserProto.Directive directive = 1;</code>
     * @return Whether the directive field is set.
     */
    boolean hasDirective();
    /**
     * <code>.directive.DirectiveParserProto.Directive directive = 1;</code>
     * @return The directive.
     */
    com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive getDirective();
    /**
     * <code>.directive.DirectiveParserProto.Directive directive = 1;</code>
     */
    com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.DirectiveOrBuilder getDirectiveOrBuilder();
  }
  /**
   * Protobuf type {@code directive.DirectiveParserProto}
   */
  public static final class DirectiveParserProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:directive.DirectiveParserProto)
      DirectiveParserProtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DirectiveParserProto.newBuilder() to construct.
    private DirectiveParserProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DirectiveParserProto() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DirectiveParserProto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.proto.avs.v20160207.directive.DirectiveParser.internal_static_directive_DirectiveParserProto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.proto.avs.v20160207.directive.DirectiveParser.internal_static_directive_DirectiveParserProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.class, com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Builder.class);
    }

    public interface DirectiveOrBuilder extends
        // @@protoc_insertion_point(interface_extends:directive.DirectiveParserProto.Directive)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>bytes payload = 2;</code>
       * @return The payload.
       */
      com.google.protobuf.ByteString getPayload();

      /**
       * <code>.header.DirectiveHeaderProto header = 1;</code>
       * @return Whether the header field is set.
       */
      boolean hasHeader();
      /**
       * <code>.header.DirectiveHeaderProto header = 1;</code>
       * @return The header.
       */
      com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto getHeader();
      /**
       * <code>.header.DirectiveHeaderProto header = 1;</code>
       */
      com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProtoOrBuilder getHeaderOrBuilder();
    }
    /**
     * Protobuf type {@code directive.DirectiveParserProto.Directive}
     */
    public static final class Directive extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:directive.DirectiveParserProto.Directive)
        DirectiveOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use Directive.newBuilder() to construct.
      private Directive(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private Directive() {
        payload_ = com.google.protobuf.ByteString.EMPTY;
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new Directive();
      }

      @java.lang.Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.directive.DirectiveParser.internal_static_directive_DirectiveParserProto_Directive_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.directive.DirectiveParser.internal_static_directive_DirectiveParserProto_Directive_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive.class, com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive.Builder.class);
      }

      public static final int PAYLOAD_FIELD_NUMBER = 2;
      private com.google.protobuf.ByteString payload_;
      /**
       * <code>bytes payload = 2;</code>
       * @return The payload.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getPayload() {
        return payload_;
      }

      public static final int HEADER_FIELD_NUMBER = 1;
      private com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto header_;
      /**
       * <code>.header.DirectiveHeaderProto header = 1;</code>
       * @return Whether the header field is set.
       */
      @java.lang.Override
      public boolean hasHeader() {
        return header_ != null;
      }
      /**
       * <code>.header.DirectiveHeaderProto header = 1;</code>
       * @return The header.
       */
      @java.lang.Override
      public com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto getHeader() {
        return header_ == null ? com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.getDefaultInstance() : header_;
      }
      /**
       * <code>.header.DirectiveHeaderProto header = 1;</code>
       */
      @java.lang.Override
      public com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProtoOrBuilder getHeaderOrBuilder() {
        return getHeader();
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (header_ != null) {
          output.writeMessage(1, getHeader());
        }
        if (!payload_.isEmpty()) {
          output.writeBytes(2, payload_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (header_ != null) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, getHeader());
        }
        if (!payload_.isEmpty()) {
          size += com.google.protobuf.CodedOutputStream
            .computeBytesSize(2, payload_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive)) {
          return super.equals(obj);
        }
        com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive other = (com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive) obj;

        if (!getPayload()
            .equals(other.getPayload())) return false;
        if (hasHeader() != other.hasHeader()) return false;
        if (hasHeader()) {
          if (!getHeader()
              .equals(other.getHeader())) return false;
        }
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + PAYLOAD_FIELD_NUMBER;
        hash = (53 * hash) + getPayload().hashCode();
        if (hasHeader()) {
          hash = (37 * hash) + HEADER_FIELD_NUMBER;
          hash = (53 * hash) + getHeader().hashCode();
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code directive.DirectiveParserProto.Directive}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:directive.DirectiveParserProto.Directive)
          com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.DirectiveOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.amazon.proto.avs.v20160207.directive.DirectiveParser.internal_static_directive_DirectiveParserProto_Directive_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.amazon.proto.avs.v20160207.directive.DirectiveParser.internal_static_directive_DirectiveParserProto_Directive_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive.class, com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive.Builder.class);
        }

        // Construct using com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          payload_ = com.google.protobuf.ByteString.EMPTY;

          if (headerBuilder_ == null) {
            header_ = null;
          } else {
            header_ = null;
            headerBuilder_ = null;
          }
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.amazon.proto.avs.v20160207.directive.DirectiveParser.internal_static_directive_DirectiveParserProto_Directive_descriptor;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive getDefaultInstanceForType() {
          return com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive.getDefaultInstance();
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive build() {
          com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive buildPartial() {
          com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive result = new com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive(this);
          result.payload_ = payload_;
          if (headerBuilder_ == null) {
            result.header_ = header_;
          } else {
            result.header_ = headerBuilder_.build();
          }
          onBuilt();
          return result;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive) {
            return mergeFrom((com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive other) {
          if (other == com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive.getDefaultInstance()) return this;
          if (other.getPayload() != com.google.protobuf.ByteString.EMPTY) {
            setPayload(other.getPayload());
          }
          if (other.hasHeader()) {
            mergeHeader(other.getHeader());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 10: {
                  input.readMessage(
                      getHeaderFieldBuilder().getBuilder(),
                      extensionRegistry);

                  break;
                } // case 10
                case 18: {
                  payload_ = input.readBytes();

                  break;
                } // case 18
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }

        private com.google.protobuf.ByteString payload_ = com.google.protobuf.ByteString.EMPTY;
        /**
         * <code>bytes payload = 2;</code>
         * @return The payload.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString getPayload() {
          return payload_;
        }
        /**
         * <code>bytes payload = 2;</code>
         * @param value The payload to set.
         * @return This builder for chaining.
         */
        public Builder setPayload(com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  
          payload_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>bytes payload = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearPayload() {
          
          payload_ = getDefaultInstance().getPayload();
          onChanged();
          return this;
        }

        private com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto header_;
        private com.google.protobuf.SingleFieldBuilderV3<
            com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto, com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.Builder, com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProtoOrBuilder> headerBuilder_;
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         * @return Whether the header field is set.
         */
        public boolean hasHeader() {
          return headerBuilder_ != null || header_ != null;
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         * @return The header.
         */
        public com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto getHeader() {
          if (headerBuilder_ == null) {
            return header_ == null ? com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.getDefaultInstance() : header_;
          } else {
            return headerBuilder_.getMessage();
          }
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        public Builder setHeader(com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto value) {
          if (headerBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            header_ = value;
            onChanged();
          } else {
            headerBuilder_.setMessage(value);
          }

          return this;
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        public Builder setHeader(
            com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.Builder builderForValue) {
          if (headerBuilder_ == null) {
            header_ = builderForValue.build();
            onChanged();
          } else {
            headerBuilder_.setMessage(builderForValue.build());
          }

          return this;
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        public Builder mergeHeader(com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto value) {
          if (headerBuilder_ == null) {
            if (header_ != null) {
              header_ =
                com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.newBuilder(header_).mergeFrom(value).buildPartial();
            } else {
              header_ = value;
            }
            onChanged();
          } else {
            headerBuilder_.mergeFrom(value);
          }

          return this;
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        public Builder clearHeader() {
          if (headerBuilder_ == null) {
            header_ = null;
            onChanged();
          } else {
            header_ = null;
            headerBuilder_ = null;
          }

          return this;
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        public com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.Builder getHeaderBuilder() {
          
          onChanged();
          return getHeaderFieldBuilder().getBuilder();
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        public com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProtoOrBuilder getHeaderOrBuilder() {
          if (headerBuilder_ != null) {
            return headerBuilder_.getMessageOrBuilder();
          } else {
            return header_ == null ?
                com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.getDefaultInstance() : header_;
          }
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        private com.google.protobuf.SingleFieldBuilderV3<
            com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto, com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.Builder, com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProtoOrBuilder> 
            getHeaderFieldBuilder() {
          if (headerBuilder_ == null) {
            headerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
                com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto, com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.Builder, com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProtoOrBuilder>(
                    getHeader(),
                    getParentForChildren(),
                    isClean());
            header_ = null;
          }
          return headerBuilder_;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:directive.DirectiveParserProto.Directive)
      }

      // @@protoc_insertion_point(class_scope:directive.DirectiveParserProto.Directive)
      private static final com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive();
      }

      public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Directive>
          PARSER = new com.google.protobuf.AbstractParser<Directive>() {
        @java.lang.Override
        public Directive parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Directive> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Directive> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public static final int DIRECTIVE_FIELD_NUMBER = 1;
    private com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive directive_;
    /**
     * <code>.directive.DirectiveParserProto.Directive directive = 1;</code>
     * @return Whether the directive field is set.
     */
    @java.lang.Override
    public boolean hasDirective() {
      return directive_ != null;
    }
    /**
     * <code>.directive.DirectiveParserProto.Directive directive = 1;</code>
     * @return The directive.
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive getDirective() {
      return directive_ == null ? com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive.getDefaultInstance() : directive_;
    }
    /**
     * <code>.directive.DirectiveParserProto.Directive directive = 1;</code>
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.DirectiveOrBuilder getDirectiveOrBuilder() {
      return getDirective();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (directive_ != null) {
        output.writeMessage(1, getDirective());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (directive_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getDirective());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto)) {
        return super.equals(obj);
      }
      com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto other = (com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto) obj;

      if (hasDirective() != other.hasDirective()) return false;
      if (hasDirective()) {
        if (!getDirective()
            .equals(other.getDirective())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasDirective()) {
        hash = (37 * hash) + DIRECTIVE_FIELD_NUMBER;
        hash = (53 * hash) + getDirective().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code directive.DirectiveParserProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:directive.DirectiveParserProto)
        com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.directive.DirectiveParser.internal_static_directive_DirectiveParserProto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.directive.DirectiveParser.internal_static_directive_DirectiveParserProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.class, com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Builder.class);
      }

      // Construct using com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (directiveBuilder_ == null) {
          directive_ = null;
        } else {
          directive_ = null;
          directiveBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.proto.avs.v20160207.directive.DirectiveParser.internal_static_directive_DirectiveParserProto_descriptor;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto getDefaultInstanceForType() {
        return com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto build() {
        com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto buildPartial() {
        com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto result = new com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto(this);
        if (directiveBuilder_ == null) {
          result.directive_ = directive_;
        } else {
          result.directive_ = directiveBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto) {
          return mergeFrom((com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto other) {
        if (other == com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.getDefaultInstance()) return this;
        if (other.hasDirective()) {
          mergeDirective(other.getDirective());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getDirectiveFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive directive_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive, com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive.Builder, com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.DirectiveOrBuilder> directiveBuilder_;
      /**
       * <code>.directive.DirectiveParserProto.Directive directive = 1;</code>
       * @return Whether the directive field is set.
       */
      public boolean hasDirective() {
        return directiveBuilder_ != null || directive_ != null;
      }
      /**
       * <code>.directive.DirectiveParserProto.Directive directive = 1;</code>
       * @return The directive.
       */
      public com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive getDirective() {
        if (directiveBuilder_ == null) {
          return directive_ == null ? com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive.getDefaultInstance() : directive_;
        } else {
          return directiveBuilder_.getMessage();
        }
      }
      /**
       * <code>.directive.DirectiveParserProto.Directive directive = 1;</code>
       */
      public Builder setDirective(com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive value) {
        if (directiveBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          directive_ = value;
          onChanged();
        } else {
          directiveBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.directive.DirectiveParserProto.Directive directive = 1;</code>
       */
      public Builder setDirective(
          com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive.Builder builderForValue) {
        if (directiveBuilder_ == null) {
          directive_ = builderForValue.build();
          onChanged();
        } else {
          directiveBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.directive.DirectiveParserProto.Directive directive = 1;</code>
       */
      public Builder mergeDirective(com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive value) {
        if (directiveBuilder_ == null) {
          if (directive_ != null) {
            directive_ =
              com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive.newBuilder(directive_).mergeFrom(value).buildPartial();
          } else {
            directive_ = value;
          }
          onChanged();
        } else {
          directiveBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.directive.DirectiveParserProto.Directive directive = 1;</code>
       */
      public Builder clearDirective() {
        if (directiveBuilder_ == null) {
          directive_ = null;
          onChanged();
        } else {
          directive_ = null;
          directiveBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.directive.DirectiveParserProto.Directive directive = 1;</code>
       */
      public com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive.Builder getDirectiveBuilder() {
        
        onChanged();
        return getDirectiveFieldBuilder().getBuilder();
      }
      /**
       * <code>.directive.DirectiveParserProto.Directive directive = 1;</code>
       */
      public com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.DirectiveOrBuilder getDirectiveOrBuilder() {
        if (directiveBuilder_ != null) {
          return directiveBuilder_.getMessageOrBuilder();
        } else {
          return directive_ == null ?
              com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive.getDefaultInstance() : directive_;
        }
      }
      /**
       * <code>.directive.DirectiveParserProto.Directive directive = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive, com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive.Builder, com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.DirectiveOrBuilder> 
          getDirectiveFieldBuilder() {
        if (directiveBuilder_ == null) {
          directiveBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive, com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.Directive.Builder, com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto.DirectiveOrBuilder>(
                  getDirective(),
                  getParentForChildren(),
                  isClean());
          directive_ = null;
        }
        return directiveBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:directive.DirectiveParserProto)
    }

    // @@protoc_insertion_point(class_scope:directive.DirectiveParserProto)
    private static final com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto();
    }

    public static com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DirectiveParserProto>
        PARSER = new com.google.protobuf.AbstractParser<DirectiveParserProto>() {
      @java.lang.Override
      public DirectiveParserProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<DirectiveParserProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DirectiveParserProto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.proto.avs.v20160207.directive.DirectiveParser.DirectiveParserProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_directive_DirectiveParserProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_directive_DirectiveParserProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_directive_DirectiveParserProto_Directive_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_directive_DirectiveParserProto_Directive_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\025directiveParser.proto\022\tdirective\032\025dire" +
      "ctiveHeader.proto\"\240\001\n\024DirectiveParserPro" +
      "to\022<\n\tdirective\030\001 \001(\0132).directive.Direct" +
      "iveParserProto.Directive\032J\n\tDirective\022\017\n" +
      "\007payload\030\002 \001(\014\022,\n\006header\030\001 \001(\0132\034.header." +
      "DirectiveHeaderProtoB;\n(com.amazon.proto" +
      ".avs.v20160207.directiveB\017DirectiveParse" +
      "rb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.amazon.proto.avs.v20160207.header.DirectiveHeader.getDescriptor(),
        });
    internal_static_directive_DirectiveParserProto_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_directive_DirectiveParserProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_directive_DirectiveParserProto_descriptor,
        new java.lang.String[] { "Directive", });
    internal_static_directive_DirectiveParserProto_Directive_descriptor =
      internal_static_directive_DirectiveParserProto_descriptor.getNestedTypes().get(0);
    internal_static_directive_DirectiveParserProto_Directive_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_directive_DirectiveParserProto_Directive_descriptor,
        new java.lang.String[] { "Payload", "Header", });
    com.amazon.proto.avs.v20160207.header.DirectiveHeader.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
