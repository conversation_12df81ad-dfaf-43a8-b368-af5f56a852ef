<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="avs_lwa_dialog" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\avs_lwa_dialog.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/avs_lwa_dialog_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="89" endOffset="16"/></Target><Target id="@+id/avs_lwa_wifi_name" view="EditText"><Expressions/><location startLine="28" startOffset="12" endLine="38" endOffset="41"/></Target><Target id="@+id/avs_lwa_wifi_psw" view="EditText"><Expressions/><location startLine="40" startOffset="12" endLine="50" endOffset="41"/></Target><Target id="@+id/dialog_cancel" view="Button"><Expressions/><location startLine="62" startOffset="12" endLine="73" endOffset="20"/></Target><Target id="@+id/dialog_confirm" view="Button"><Expressions/><location startLine="76" startOffset="12" endLine="85" endOffset="48"/></Target></Targets></Layout>