// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: alexaDiscoveryDiscoverResponseEventPayload.proto

package com.amazon.proto.avs.v20160207.alexaDiscovery;

public final class DiscoverResponseEventPayload {
  private DiscoverResponseEventPayload() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface DiscoverResponseEventPayloadProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:alexaDiscovery.DiscoverResponseEventPayloadProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
     */
    java.util.List<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints> 
        getEndpointsList();
    /**
     * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
     */
    com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints getEndpoints(int index);
    /**
     * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
     */
    int getEndpointsCount();
    /**
     * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
     */
    java.util.List<? extends com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.EndpointsOrBuilder> 
        getEndpointsOrBuilderList();
    /**
     * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
     */
    com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.EndpointsOrBuilder getEndpointsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code alexaDiscovery.DiscoverResponseEventPayloadProto}
   */
  public static final class DiscoverResponseEventPayloadProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:alexaDiscovery.DiscoverResponseEventPayloadProto)
      DiscoverResponseEventPayloadProtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DiscoverResponseEventPayloadProto.newBuilder() to construct.
    private DiscoverResponseEventPayloadProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DiscoverResponseEventPayloadProto() {
      endpoints_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DiscoverResponseEventPayloadProto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.class, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Builder.class);
    }

    public interface EndpointsOrBuilder extends
        // @@protoc_insertion_point(interface_extends:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
       */
      java.util.List<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities> 
          getCapabilitiesList();
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
       */
      com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities getCapabilities(int index);
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
       */
      int getCapabilitiesCount();
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
       */
      java.util.List<? extends com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.CapabilitiesOrBuilder> 
          getCapabilitiesOrBuilderList();
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
       */
      com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.CapabilitiesOrBuilder getCapabilitiesOrBuilder(
          int index);

      /**
       * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification additionalIdentification = 12;</code>
       * @return Whether the additionalIdentification field is set.
       */
      boolean hasAdditionalIdentification();
      /**
       * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification additionalIdentification = 12;</code>
       * @return The additionalIdentification.
       */
      com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification getAdditionalIdentification();
      /**
       * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification additionalIdentification = 12;</code>
       */
      com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentificationOrBuilder getAdditionalIdentificationOrBuilder();

      /**
       * <code>string endpointId = 1;</code>
       * @return The endpointId.
       */
      java.lang.String getEndpointId();
      /**
       * <code>string endpointId = 1;</code>
       * @return The bytes for endpointId.
       */
      com.google.protobuf.ByteString
          getEndpointIdBytes();

      /**
       * <code>string manufacturerName = 4;</code>
       * @return The manufacturerName.
       */
      java.lang.String getManufacturerName();
      /**
       * <code>string manufacturerName = 4;</code>
       * @return The bytes for manufacturerName.
       */
      com.google.protobuf.ByteString
          getManufacturerNameBytes();

      /**
       * <code>string description = 3;</code>
       * @return The description.
       */
      java.lang.String getDescription();
      /**
       * <code>string description = 3;</code>
       * @return The bytes for description.
       */
      com.google.protobuf.ByteString
          getDescriptionBytes();

      /**
       * <code>string friendlyName = 2;</code>
       * @return The friendlyName.
       */
      java.lang.String getFriendlyName();
      /**
       * <code>string friendlyName = 2;</code>
       * @return The bytes for friendlyName.
       */
      com.google.protobuf.ByteString
          getFriendlyNameBytes();
    }
    /**
     * Protobuf type {@code alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints}
     */
    public static final class Endpoints extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints)
        EndpointsOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use Endpoints.newBuilder() to construct.
      private Endpoints(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private Endpoints() {
        capabilities_ = java.util.Collections.emptyList();
        endpointId_ = "";
        manufacturerName_ = "";
        description_ = "";
        friendlyName_ = "";
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new Endpoints();
      }

      @java.lang.Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.class, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Builder.class);
      }

      public interface CapabilitiesOrBuilder extends
          // @@protoc_insertion_point(interface_extends:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities)
          com.google.protobuf.MessageOrBuilder {

        /**
         * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration configuration = 4;</code>
         * @return Whether the configuration field is set.
         */
        boolean hasConfiguration();
        /**
         * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration configuration = 4;</code>
         * @return The configuration.
         */
        com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration getConfiguration();
        /**
         * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration configuration = 4;</code>
         */
        com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.ConfigurationOrBuilder getConfigurationOrBuilder();

        /**
         * <code>string type = 1;</code>
         * @return The type.
         */
        java.lang.String getType();
        /**
         * <code>string type = 1;</code>
         * @return The bytes for type.
         */
        com.google.protobuf.ByteString
            getTypeBytes();

        /**
         * <code>string interface = 2;</code>
         * @return The interface.
         */
        java.lang.String getInterface();
        /**
         * <code>string interface = 2;</code>
         * @return The bytes for interface.
         */
        com.google.protobuf.ByteString
            getInterfaceBytes();

        /**
         * <code>string version = 3;</code>
         * @return The version.
         */
        java.lang.String getVersion();
        /**
         * <code>string version = 3;</code>
         * @return The bytes for version.
         */
        com.google.protobuf.ByteString
            getVersionBytes();
      }
      /**
       * Protobuf type {@code alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities}
       */
      public static final class Capabilities extends
          com.google.protobuf.GeneratedMessageV3 implements
          // @@protoc_insertion_point(message_implements:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities)
          CapabilitiesOrBuilder {
      private static final long serialVersionUID = 0L;
        // Use Capabilities.newBuilder() to construct.
        private Capabilities(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
          super(builder);
        }
        private Capabilities() {
          type_ = "";
          interface_ = "";
          version_ = "";
        }

        @java.lang.Override
        @SuppressWarnings({"unused"})
        protected java.lang.Object newInstance(
            UnusedPrivateParameter unused) {
          return new Capabilities();
        }

        @java.lang.Override
        public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
          return this.unknownFields;
        }
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.class, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Builder.class);
        }

        public interface ConfigurationOrBuilder extends
            // @@protoc_insertion_point(interface_extends:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration)
            com.google.protobuf.MessageOrBuilder {

          /**
           * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
           */
          java.util.List<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes> 
              getSupportedTypesList();
          /**
           * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
           */
          com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes getSupportedTypes(int index);
          /**
           * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
           */
          int getSupportedTypesCount();
          /**
           * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
           */
          java.util.List<? extends com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypesOrBuilder> 
              getSupportedTypesOrBuilderList();
          /**
           * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
           */
          com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypesOrBuilder getSupportedTypesOrBuilder(
              int index);
        }
        /**
         * Protobuf type {@code alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration}
         */
        public static final class Configuration extends
            com.google.protobuf.GeneratedMessageV3 implements
            // @@protoc_insertion_point(message_implements:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration)
            ConfigurationOrBuilder {
        private static final long serialVersionUID = 0L;
          // Use Configuration.newBuilder() to construct.
          private Configuration(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
            super(builder);
          }
          private Configuration() {
            supportedTypes_ = java.util.Collections.emptyList();
          }

          @java.lang.Override
          @SuppressWarnings({"unused"})
          protected java.lang.Object newInstance(
              UnusedPrivateParameter unused) {
            return new Configuration();
          }

          @java.lang.Override
          public final com.google.protobuf.UnknownFieldSet
          getUnknownFields() {
            return this.unknownFields;
          }
          public static final com.google.protobuf.Descriptors.Descriptor
              getDescriptor() {
            return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_descriptor;
          }

          @java.lang.Override
          protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
              internalGetFieldAccessorTable() {
            return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_fieldAccessorTable
                .ensureFieldAccessorsInitialized(
                    com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.class, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.Builder.class);
          }

          public interface SupportedTypesOrBuilder extends
              // @@protoc_insertion_point(interface_extends:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes)
              com.google.protobuf.MessageOrBuilder {

            /**
             * <code>string name = 1;</code>
             * @return The name.
             */
            java.lang.String getName();
            /**
             * <code>string name = 1;</code>
             * @return The bytes for name.
             */
            com.google.protobuf.ByteString
                getNameBytes();
          }
          /**
           * Protobuf type {@code alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes}
           */
          public static final class SupportedTypes extends
              com.google.protobuf.GeneratedMessageV3 implements
              // @@protoc_insertion_point(message_implements:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes)
              SupportedTypesOrBuilder {
          private static final long serialVersionUID = 0L;
            // Use SupportedTypes.newBuilder() to construct.
            private SupportedTypes(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
              super(builder);
            }
            private SupportedTypes() {
              name_ = "";
            }

            @java.lang.Override
            @SuppressWarnings({"unused"})
            protected java.lang.Object newInstance(
                UnusedPrivateParameter unused) {
              return new SupportedTypes();
            }

            @java.lang.Override
            public final com.google.protobuf.UnknownFieldSet
            getUnknownFields() {
              return this.unknownFields;
            }
            public static final com.google.protobuf.Descriptors.Descriptor
                getDescriptor() {
              return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_SupportedTypes_descriptor;
            }

            @java.lang.Override
            protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
                internalGetFieldAccessorTable() {
              return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_SupportedTypes_fieldAccessorTable
                  .ensureFieldAccessorsInitialized(
                      com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes.class, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes.Builder.class);
            }

            public static final int NAME_FIELD_NUMBER = 1;
            private volatile java.lang.Object name_;
            /**
             * <code>string name = 1;</code>
             * @return The name.
             */
            @java.lang.Override
            public java.lang.String getName() {
              java.lang.Object ref = name_;
              if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
              } else {
                com.google.protobuf.ByteString bs = 
                    (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                name_ = s;
                return s;
              }
            }
            /**
             * <code>string name = 1;</code>
             * @return The bytes for name.
             */
            @java.lang.Override
            public com.google.protobuf.ByteString
                getNameBytes() {
              java.lang.Object ref = name_;
              if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b = 
                    com.google.protobuf.ByteString.copyFromUtf8(
                        (java.lang.String) ref);
                name_ = b;
                return b;
              } else {
                return (com.google.protobuf.ByteString) ref;
              }
            }

            private byte memoizedIsInitialized = -1;
            @java.lang.Override
            public final boolean isInitialized() {
              byte isInitialized = memoizedIsInitialized;
              if (isInitialized == 1) return true;
              if (isInitialized == 0) return false;

              memoizedIsInitialized = 1;
              return true;
            }

            @java.lang.Override
            public void writeTo(com.google.protobuf.CodedOutputStream output)
                                throws java.io.IOException {
              if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 1, name_);
              }
              getUnknownFields().writeTo(output);
            }

            @java.lang.Override
            public int getSerializedSize() {
              int size = memoizedSize;
              if (size != -1) return size;

              size = 0;
              if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, name_);
              }
              size += getUnknownFields().getSerializedSize();
              memoizedSize = size;
              return size;
            }

            @java.lang.Override
            public boolean equals(final java.lang.Object obj) {
              if (obj == this) {
               return true;
              }
              if (!(obj instanceof com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes)) {
                return super.equals(obj);
              }
              com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes other = (com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes) obj;

              if (!getName()
                  .equals(other.getName())) return false;
              if (!getUnknownFields().equals(other.getUnknownFields())) return false;
              return true;
            }

            @java.lang.Override
            public int hashCode() {
              if (memoizedHashCode != 0) {
                return memoizedHashCode;
              }
              int hash = 41;
              hash = (19 * hash) + getDescriptor().hashCode();
              hash = (37 * hash) + NAME_FIELD_NUMBER;
              hash = (53 * hash) + getName().hashCode();
              hash = (29 * hash) + getUnknownFields().hashCode();
              memoizedHashCode = hash;
              return hash;
            }

            public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
              return PARSER.parseFrom(data);
            }
            public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
              return PARSER.parseFrom(data, extensionRegistry);
            }
            public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
              return PARSER.parseFrom(data);
            }
            public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
              return PARSER.parseFrom(data, extensionRegistry);
            }
            public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
              return PARSER.parseFrom(data);
            }
            public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
              return PARSER.parseFrom(data, extensionRegistry);
            }
            public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes parseFrom(java.io.InputStream input)
                throws java.io.IOException {
              return com.google.protobuf.GeneratedMessageV3
                  .parseWithIOException(PARSER, input);
            }
            public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
              return com.google.protobuf.GeneratedMessageV3
                  .parseWithIOException(PARSER, input, extensionRegistry);
            }
            public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
              return com.google.protobuf.GeneratedMessageV3
                  .parseDelimitedWithIOException(PARSER, input);
            }
            public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
              return com.google.protobuf.GeneratedMessageV3
                  .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
            }
            public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
              return com.google.protobuf.GeneratedMessageV3
                  .parseWithIOException(PARSER, input);
            }
            public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
              return com.google.protobuf.GeneratedMessageV3
                  .parseWithIOException(PARSER, input, extensionRegistry);
            }

            @java.lang.Override
            public Builder newBuilderForType() { return newBuilder(); }
            public static Builder newBuilder() {
              return DEFAULT_INSTANCE.toBuilder();
            }
            public static Builder newBuilder(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes prototype) {
              return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
            }
            @java.lang.Override
            public Builder toBuilder() {
              return this == DEFAULT_INSTANCE
                  ? new Builder() : new Builder().mergeFrom(this);
            }

            @java.lang.Override
            protected Builder newBuilderForType(
                com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
              Builder builder = new Builder(parent);
              return builder;
            }
            /**
             * Protobuf type {@code alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes}
             */
            public static final class Builder extends
                com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
                // @@protoc_insertion_point(builder_implements:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes)
                com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypesOrBuilder {
              public static final com.google.protobuf.Descriptors.Descriptor
                  getDescriptor() {
                return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_SupportedTypes_descriptor;
              }

              @java.lang.Override
              protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
                  internalGetFieldAccessorTable() {
                return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_SupportedTypes_fieldAccessorTable
                    .ensureFieldAccessorsInitialized(
                        com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes.class, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes.Builder.class);
              }

              // Construct using com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes.newBuilder()
              private Builder() {

              }

              private Builder(
                  com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
                super(parent);

              }
              @java.lang.Override
              public Builder clear() {
                super.clear();
                name_ = "";

                return this;
              }

              @java.lang.Override
              public com.google.protobuf.Descriptors.Descriptor
                  getDescriptorForType() {
                return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_SupportedTypes_descriptor;
              }

              @java.lang.Override
              public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes getDefaultInstanceForType() {
                return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes.getDefaultInstance();
              }

              @java.lang.Override
              public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes build() {
                com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes result = buildPartial();
                if (!result.isInitialized()) {
                  throw newUninitializedMessageException(result);
                }
                return result;
              }

              @java.lang.Override
              public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes buildPartial() {
                com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes result = new com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes(this);
                result.name_ = name_;
                onBuilt();
                return result;
              }

              @java.lang.Override
              public Builder clone() {
                return super.clone();
              }
              @java.lang.Override
              public Builder setField(
                  com.google.protobuf.Descriptors.FieldDescriptor field,
                  java.lang.Object value) {
                return super.setField(field, value);
              }
              @java.lang.Override
              public Builder clearField(
                  com.google.protobuf.Descriptors.FieldDescriptor field) {
                return super.clearField(field);
              }
              @java.lang.Override
              public Builder clearOneof(
                  com.google.protobuf.Descriptors.OneofDescriptor oneof) {
                return super.clearOneof(oneof);
              }
              @java.lang.Override
              public Builder setRepeatedField(
                  com.google.protobuf.Descriptors.FieldDescriptor field,
                  int index, java.lang.Object value) {
                return super.setRepeatedField(field, index, value);
              }
              @java.lang.Override
              public Builder addRepeatedField(
                  com.google.protobuf.Descriptors.FieldDescriptor field,
                  java.lang.Object value) {
                return super.addRepeatedField(field, value);
              }
              @java.lang.Override
              public Builder mergeFrom(com.google.protobuf.Message other) {
                if (other instanceof com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes) {
                  return mergeFrom((com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes)other);
                } else {
                  super.mergeFrom(other);
                  return this;
                }
              }

              public Builder mergeFrom(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes other) {
                if (other == com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes.getDefaultInstance()) return this;
                if (!other.getName().isEmpty()) {
                  name_ = other.name_;
                  onChanged();
                }
                this.mergeUnknownFields(other.getUnknownFields());
                onChanged();
                return this;
              }

              @java.lang.Override
              public final boolean isInitialized() {
                return true;
              }

              @java.lang.Override
              public Builder mergeFrom(
                  com.google.protobuf.CodedInputStream input,
                  com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                  throws java.io.IOException {
                if (extensionRegistry == null) {
                  throw new java.lang.NullPointerException();
                }
                try {
                  boolean done = false;
                  while (!done) {
                    int tag = input.readTag();
                    switch (tag) {
                      case 0:
                        done = true;
                        break;
                      case 10: {
                        name_ = input.readStringRequireUtf8();

                        break;
                      } // case 10
                      default: {
                        if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                          done = true; // was an endgroup tag
                        }
                        break;
                      } // default:
                    } // switch (tag)
                  } // while (!done)
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                  throw e.unwrapIOException();
                } finally {
                  onChanged();
                } // finally
                return this;
              }

              private java.lang.Object name_ = "";
              /**
               * <code>string name = 1;</code>
               * @return The name.
               */
              public java.lang.String getName() {
                java.lang.Object ref = name_;
                if (!(ref instanceof java.lang.String)) {
                  com.google.protobuf.ByteString bs =
                      (com.google.protobuf.ByteString) ref;
                  java.lang.String s = bs.toStringUtf8();
                  name_ = s;
                  return s;
                } else {
                  return (java.lang.String) ref;
                }
              }
              /**
               * <code>string name = 1;</code>
               * @return The bytes for name.
               */
              public com.google.protobuf.ByteString
                  getNameBytes() {
                java.lang.Object ref = name_;
                if (ref instanceof String) {
                  com.google.protobuf.ByteString b = 
                      com.google.protobuf.ByteString.copyFromUtf8(
                          (java.lang.String) ref);
                  name_ = b;
                  return b;
                } else {
                  return (com.google.protobuf.ByteString) ref;
                }
              }
              /**
               * <code>string name = 1;</code>
               * @param value The name to set.
               * @return This builder for chaining.
               */
              public Builder setName(
                  java.lang.String value) {
                if (value == null) {
    throw new NullPointerException();
  }
  
                name_ = value;
                onChanged();
                return this;
              }
              /**
               * <code>string name = 1;</code>
               * @return This builder for chaining.
               */
              public Builder clearName() {
                
                name_ = getDefaultInstance().getName();
                onChanged();
                return this;
              }
              /**
               * <code>string name = 1;</code>
               * @param value The bytes for name to set.
               * @return This builder for chaining.
               */
              public Builder setNameBytes(
                  com.google.protobuf.ByteString value) {
                if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
                
                name_ = value;
                onChanged();
                return this;
              }
              @java.lang.Override
              public final Builder setUnknownFields(
                  final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.setUnknownFields(unknownFields);
              }

              @java.lang.Override
              public final Builder mergeUnknownFields(
                  final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.mergeUnknownFields(unknownFields);
              }


              // @@protoc_insertion_point(builder_scope:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes)
            }

            // @@protoc_insertion_point(class_scope:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes)
            private static final com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes DEFAULT_INSTANCE;
            static {
              DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes();
            }

            public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes getDefaultInstance() {
              return DEFAULT_INSTANCE;
            }

            private static final com.google.protobuf.Parser<SupportedTypes>
                PARSER = new com.google.protobuf.AbstractParser<SupportedTypes>() {
              @java.lang.Override
              public SupportedTypes parsePartialFrom(
                  com.google.protobuf.CodedInputStream input,
                  com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                  throws com.google.protobuf.InvalidProtocolBufferException {
                Builder builder = newBuilder();
                try {
                  builder.mergeFrom(input, extensionRegistry);
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                  throw e.setUnfinishedMessage(builder.buildPartial());
                } catch (com.google.protobuf.UninitializedMessageException e) {
                  throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
                } catch (java.io.IOException e) {
                  throw new com.google.protobuf.InvalidProtocolBufferException(e)
                      .setUnfinishedMessage(builder.buildPartial());
                }
                return builder.buildPartial();
              }
            };

            public static com.google.protobuf.Parser<SupportedTypes> parser() {
              return PARSER;
            }

            @java.lang.Override
            public com.google.protobuf.Parser<SupportedTypes> getParserForType() {
              return PARSER;
            }

            @java.lang.Override
            public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes getDefaultInstanceForType() {
              return DEFAULT_INSTANCE;
            }

          }

          public static final int SUPPORTEDTYPES_FIELD_NUMBER = 1;
          private java.util.List<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes> supportedTypes_;
          /**
           * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
           */
          @java.lang.Override
          public java.util.List<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes> getSupportedTypesList() {
            return supportedTypes_;
          }
          /**
           * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
           */
          @java.lang.Override
          public java.util.List<? extends com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypesOrBuilder> 
              getSupportedTypesOrBuilderList() {
            return supportedTypes_;
          }
          /**
           * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
           */
          @java.lang.Override
          public int getSupportedTypesCount() {
            return supportedTypes_.size();
          }
          /**
           * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
           */
          @java.lang.Override
          public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes getSupportedTypes(int index) {
            return supportedTypes_.get(index);
          }
          /**
           * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
           */
          @java.lang.Override
          public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypesOrBuilder getSupportedTypesOrBuilder(
              int index) {
            return supportedTypes_.get(index);
          }

          private byte memoizedIsInitialized = -1;
          @java.lang.Override
          public final boolean isInitialized() {
            byte isInitialized = memoizedIsInitialized;
            if (isInitialized == 1) return true;
            if (isInitialized == 0) return false;

            memoizedIsInitialized = 1;
            return true;
          }

          @java.lang.Override
          public void writeTo(com.google.protobuf.CodedOutputStream output)
                              throws java.io.IOException {
            for (int i = 0; i < supportedTypes_.size(); i++) {
              output.writeMessage(1, supportedTypes_.get(i));
            }
            getUnknownFields().writeTo(output);
          }

          @java.lang.Override
          public int getSerializedSize() {
            int size = memoizedSize;
            if (size != -1) return size;

            size = 0;
            for (int i = 0; i < supportedTypes_.size(); i++) {
              size += com.google.protobuf.CodedOutputStream
                .computeMessageSize(1, supportedTypes_.get(i));
            }
            size += getUnknownFields().getSerializedSize();
            memoizedSize = size;
            return size;
          }

          @java.lang.Override
          public boolean equals(final java.lang.Object obj) {
            if (obj == this) {
             return true;
            }
            if (!(obj instanceof com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration)) {
              return super.equals(obj);
            }
            com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration other = (com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration) obj;

            if (!getSupportedTypesList()
                .equals(other.getSupportedTypesList())) return false;
            if (!getUnknownFields().equals(other.getUnknownFields())) return false;
            return true;
          }

          @java.lang.Override
          public int hashCode() {
            if (memoizedHashCode != 0) {
              return memoizedHashCode;
            }
            int hash = 41;
            hash = (19 * hash) + getDescriptor().hashCode();
            if (getSupportedTypesCount() > 0) {
              hash = (37 * hash) + SUPPORTEDTYPES_FIELD_NUMBER;
              hash = (53 * hash) + getSupportedTypesList().hashCode();
            }
            hash = (29 * hash) + getUnknownFields().hashCode();
            memoizedHashCode = hash;
            return hash;
          }

          public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration parseFrom(
              java.nio.ByteBuffer data)
              throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
          }
          public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration parseFrom(
              java.nio.ByteBuffer data,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
          }
          public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration parseFrom(
              com.google.protobuf.ByteString data)
              throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
          }
          public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration parseFrom(
              com.google.protobuf.ByteString data,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
          }
          public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration parseFrom(byte[] data)
              throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
          }
          public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration parseFrom(
              byte[] data,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
          }
          public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration parseFrom(java.io.InputStream input)
              throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                .parseWithIOException(PARSER, input);
          }
          public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration parseFrom(
              java.io.InputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                .parseWithIOException(PARSER, input, extensionRegistry);
          }
          public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration parseDelimitedFrom(java.io.InputStream input)
              throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                .parseDelimitedWithIOException(PARSER, input);
          }
          public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration parseDelimitedFrom(
              java.io.InputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
          }
          public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration parseFrom(
              com.google.protobuf.CodedInputStream input)
              throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                .parseWithIOException(PARSER, input);
          }
          public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration parseFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                .parseWithIOException(PARSER, input, extensionRegistry);
          }

          @java.lang.Override
          public Builder newBuilderForType() { return newBuilder(); }
          public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
          }
          public static Builder newBuilder(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
          }
          @java.lang.Override
          public Builder toBuilder() {
            return this == DEFAULT_INSTANCE
                ? new Builder() : new Builder().mergeFrom(this);
          }

          @java.lang.Override
          protected Builder newBuilderForType(
              com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
          }
          /**
           * Protobuf type {@code alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration}
           */
          public static final class Builder extends
              com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
              // @@protoc_insertion_point(builder_implements:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration)
              com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.ConfigurationOrBuilder {
            public static final com.google.protobuf.Descriptors.Descriptor
                getDescriptor() {
              return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_descriptor;
            }

            @java.lang.Override
            protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
                internalGetFieldAccessorTable() {
              return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_fieldAccessorTable
                  .ensureFieldAccessorsInitialized(
                      com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.class, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.Builder.class);
            }

            // Construct using com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.newBuilder()
            private Builder() {

            }

            private Builder(
                com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
              super(parent);

            }
            @java.lang.Override
            public Builder clear() {
              super.clear();
              if (supportedTypesBuilder_ == null) {
                supportedTypes_ = java.util.Collections.emptyList();
              } else {
                supportedTypes_ = null;
                supportedTypesBuilder_.clear();
              }
              bitField0_ = (bitField0_ & ~0x00000001);
              return this;
            }

            @java.lang.Override
            public com.google.protobuf.Descriptors.Descriptor
                getDescriptorForType() {
              return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_descriptor;
            }

            @java.lang.Override
            public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration getDefaultInstanceForType() {
              return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.getDefaultInstance();
            }

            @java.lang.Override
            public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration build() {
              com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration result = buildPartial();
              if (!result.isInitialized()) {
                throw newUninitializedMessageException(result);
              }
              return result;
            }

            @java.lang.Override
            public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration buildPartial() {
              com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration result = new com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration(this);
              int from_bitField0_ = bitField0_;
              if (supportedTypesBuilder_ == null) {
                if (((bitField0_ & 0x00000001) != 0)) {
                  supportedTypes_ = java.util.Collections.unmodifiableList(supportedTypes_);
                  bitField0_ = (bitField0_ & ~0x00000001);
                }
                result.supportedTypes_ = supportedTypes_;
              } else {
                result.supportedTypes_ = supportedTypesBuilder_.build();
              }
              onBuilt();
              return result;
            }

            @java.lang.Override
            public Builder clone() {
              return super.clone();
            }
            @java.lang.Override
            public Builder setField(
                com.google.protobuf.Descriptors.FieldDescriptor field,
                java.lang.Object value) {
              return super.setField(field, value);
            }
            @java.lang.Override
            public Builder clearField(
                com.google.protobuf.Descriptors.FieldDescriptor field) {
              return super.clearField(field);
            }
            @java.lang.Override
            public Builder clearOneof(
                com.google.protobuf.Descriptors.OneofDescriptor oneof) {
              return super.clearOneof(oneof);
            }
            @java.lang.Override
            public Builder setRepeatedField(
                com.google.protobuf.Descriptors.FieldDescriptor field,
                int index, java.lang.Object value) {
              return super.setRepeatedField(field, index, value);
            }
            @java.lang.Override
            public Builder addRepeatedField(
                com.google.protobuf.Descriptors.FieldDescriptor field,
                java.lang.Object value) {
              return super.addRepeatedField(field, value);
            }
            @java.lang.Override
            public Builder mergeFrom(com.google.protobuf.Message other) {
              if (other instanceof com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration) {
                return mergeFrom((com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration)other);
              } else {
                super.mergeFrom(other);
                return this;
              }
            }

            public Builder mergeFrom(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration other) {
              if (other == com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.getDefaultInstance()) return this;
              if (supportedTypesBuilder_ == null) {
                if (!other.supportedTypes_.isEmpty()) {
                  if (supportedTypes_.isEmpty()) {
                    supportedTypes_ = other.supportedTypes_;
                    bitField0_ = (bitField0_ & ~0x00000001);
                  } else {
                    ensureSupportedTypesIsMutable();
                    supportedTypes_.addAll(other.supportedTypes_);
                  }
                  onChanged();
                }
              } else {
                if (!other.supportedTypes_.isEmpty()) {
                  if (supportedTypesBuilder_.isEmpty()) {
                    supportedTypesBuilder_.dispose();
                    supportedTypesBuilder_ = null;
                    supportedTypes_ = other.supportedTypes_;
                    bitField0_ = (bitField0_ & ~0x00000001);
                    supportedTypesBuilder_ = 
                      com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                         getSupportedTypesFieldBuilder() : null;
                  } else {
                    supportedTypesBuilder_.addAllMessages(other.supportedTypes_);
                  }
                }
              }
              this.mergeUnknownFields(other.getUnknownFields());
              onChanged();
              return this;
            }

            @java.lang.Override
            public final boolean isInitialized() {
              return true;
            }

            @java.lang.Override
            public Builder mergeFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
              if (extensionRegistry == null) {
                throw new java.lang.NullPointerException();
              }
              try {
                boolean done = false;
                while (!done) {
                  int tag = input.readTag();
                  switch (tag) {
                    case 0:
                      done = true;
                      break;
                    case 10: {
                      com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes m =
                          input.readMessage(
                              com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes.parser(),
                              extensionRegistry);
                      if (supportedTypesBuilder_ == null) {
                        ensureSupportedTypesIsMutable();
                        supportedTypes_.add(m);
                      } else {
                        supportedTypesBuilder_.addMessage(m);
                      }
                      break;
                    } // case 10
                    default: {
                      if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                        done = true; // was an endgroup tag
                      }
                      break;
                    } // default:
                  } // switch (tag)
                } // while (!done)
              } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                throw e.unwrapIOException();
              } finally {
                onChanged();
              } // finally
              return this;
            }
            private int bitField0_;

            private java.util.List<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes> supportedTypes_ =
              java.util.Collections.emptyList();
            private void ensureSupportedTypesIsMutable() {
              if (!((bitField0_ & 0x00000001) != 0)) {
                supportedTypes_ = new java.util.ArrayList<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes>(supportedTypes_);
                bitField0_ |= 0x00000001;
               }
            }

            private com.google.protobuf.RepeatedFieldBuilderV3<
                com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes.Builder, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypesOrBuilder> supportedTypesBuilder_;

            /**
             * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
             */
            public java.util.List<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes> getSupportedTypesList() {
              if (supportedTypesBuilder_ == null) {
                return java.util.Collections.unmodifiableList(supportedTypes_);
              } else {
                return supportedTypesBuilder_.getMessageList();
              }
            }
            /**
             * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
             */
            public int getSupportedTypesCount() {
              if (supportedTypesBuilder_ == null) {
                return supportedTypes_.size();
              } else {
                return supportedTypesBuilder_.getCount();
              }
            }
            /**
             * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
             */
            public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes getSupportedTypes(int index) {
              if (supportedTypesBuilder_ == null) {
                return supportedTypes_.get(index);
              } else {
                return supportedTypesBuilder_.getMessage(index);
              }
            }
            /**
             * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
             */
            public Builder setSupportedTypes(
                int index, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes value) {
              if (supportedTypesBuilder_ == null) {
                if (value == null) {
                  throw new NullPointerException();
                }
                ensureSupportedTypesIsMutable();
                supportedTypes_.set(index, value);
                onChanged();
              } else {
                supportedTypesBuilder_.setMessage(index, value);
              }
              return this;
            }
            /**
             * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
             */
            public Builder setSupportedTypes(
                int index, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes.Builder builderForValue) {
              if (supportedTypesBuilder_ == null) {
                ensureSupportedTypesIsMutable();
                supportedTypes_.set(index, builderForValue.build());
                onChanged();
              } else {
                supportedTypesBuilder_.setMessage(index, builderForValue.build());
              }
              return this;
            }
            /**
             * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
             */
            public Builder addSupportedTypes(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes value) {
              if (supportedTypesBuilder_ == null) {
                if (value == null) {
                  throw new NullPointerException();
                }
                ensureSupportedTypesIsMutable();
                supportedTypes_.add(value);
                onChanged();
              } else {
                supportedTypesBuilder_.addMessage(value);
              }
              return this;
            }
            /**
             * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
             */
            public Builder addSupportedTypes(
                int index, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes value) {
              if (supportedTypesBuilder_ == null) {
                if (value == null) {
                  throw new NullPointerException();
                }
                ensureSupportedTypesIsMutable();
                supportedTypes_.add(index, value);
                onChanged();
              } else {
                supportedTypesBuilder_.addMessage(index, value);
              }
              return this;
            }
            /**
             * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
             */
            public Builder addSupportedTypes(
                com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes.Builder builderForValue) {
              if (supportedTypesBuilder_ == null) {
                ensureSupportedTypesIsMutable();
                supportedTypes_.add(builderForValue.build());
                onChanged();
              } else {
                supportedTypesBuilder_.addMessage(builderForValue.build());
              }
              return this;
            }
            /**
             * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
             */
            public Builder addSupportedTypes(
                int index, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes.Builder builderForValue) {
              if (supportedTypesBuilder_ == null) {
                ensureSupportedTypesIsMutable();
                supportedTypes_.add(index, builderForValue.build());
                onChanged();
              } else {
                supportedTypesBuilder_.addMessage(index, builderForValue.build());
              }
              return this;
            }
            /**
             * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
             */
            public Builder addAllSupportedTypes(
                java.lang.Iterable<? extends com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes> values) {
              if (supportedTypesBuilder_ == null) {
                ensureSupportedTypesIsMutable();
                com.google.protobuf.AbstractMessageLite.Builder.addAll(
                    values, supportedTypes_);
                onChanged();
              } else {
                supportedTypesBuilder_.addAllMessages(values);
              }
              return this;
            }
            /**
             * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
             */
            public Builder clearSupportedTypes() {
              if (supportedTypesBuilder_ == null) {
                supportedTypes_ = java.util.Collections.emptyList();
                bitField0_ = (bitField0_ & ~0x00000001);
                onChanged();
              } else {
                supportedTypesBuilder_.clear();
              }
              return this;
            }
            /**
             * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
             */
            public Builder removeSupportedTypes(int index) {
              if (supportedTypesBuilder_ == null) {
                ensureSupportedTypesIsMutable();
                supportedTypes_.remove(index);
                onChanged();
              } else {
                supportedTypesBuilder_.remove(index);
              }
              return this;
            }
            /**
             * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
             */
            public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes.Builder getSupportedTypesBuilder(
                int index) {
              return getSupportedTypesFieldBuilder().getBuilder(index);
            }
            /**
             * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
             */
            public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypesOrBuilder getSupportedTypesOrBuilder(
                int index) {
              if (supportedTypesBuilder_ == null) {
                return supportedTypes_.get(index);  } else {
                return supportedTypesBuilder_.getMessageOrBuilder(index);
              }
            }
            /**
             * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
             */
            public java.util.List<? extends com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypesOrBuilder> 
                 getSupportedTypesOrBuilderList() {
              if (supportedTypesBuilder_ != null) {
                return supportedTypesBuilder_.getMessageOrBuilderList();
              } else {
                return java.util.Collections.unmodifiableList(supportedTypes_);
              }
            }
            /**
             * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
             */
            public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes.Builder addSupportedTypesBuilder() {
              return getSupportedTypesFieldBuilder().addBuilder(
                  com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes.getDefaultInstance());
            }
            /**
             * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
             */
            public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes.Builder addSupportedTypesBuilder(
                int index) {
              return getSupportedTypesFieldBuilder().addBuilder(
                  index, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes.getDefaultInstance());
            }
            /**
             * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes supportedTypes = 1;</code>
             */
            public java.util.List<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes.Builder> 
                 getSupportedTypesBuilderList() {
              return getSupportedTypesFieldBuilder().getBuilderList();
            }
            private com.google.protobuf.RepeatedFieldBuilderV3<
                com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes.Builder, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypesOrBuilder> 
                getSupportedTypesFieldBuilder() {
              if (supportedTypesBuilder_ == null) {
                supportedTypesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
                    com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypes.Builder, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.SupportedTypesOrBuilder>(
                        supportedTypes_,
                        ((bitField0_ & 0x00000001) != 0),
                        getParentForChildren(),
                        isClean());
                supportedTypes_ = null;
              }
              return supportedTypesBuilder_;
            }
            @java.lang.Override
            public final Builder setUnknownFields(
                final com.google.protobuf.UnknownFieldSet unknownFields) {
              return super.setUnknownFields(unknownFields);
            }

            @java.lang.Override
            public final Builder mergeUnknownFields(
                final com.google.protobuf.UnknownFieldSet unknownFields) {
              return super.mergeUnknownFields(unknownFields);
            }


            // @@protoc_insertion_point(builder_scope:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration)
          }

          // @@protoc_insertion_point(class_scope:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration)
          private static final com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration DEFAULT_INSTANCE;
          static {
            DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration();
          }

          public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration getDefaultInstance() {
            return DEFAULT_INSTANCE;
          }

          private static final com.google.protobuf.Parser<Configuration>
              PARSER = new com.google.protobuf.AbstractParser<Configuration>() {
            @java.lang.Override
            public Configuration parsePartialFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
              Builder builder = newBuilder();
              try {
                builder.mergeFrom(input, extensionRegistry);
              } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                throw e.setUnfinishedMessage(builder.buildPartial());
              } catch (com.google.protobuf.UninitializedMessageException e) {
                throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
              } catch (java.io.IOException e) {
                throw new com.google.protobuf.InvalidProtocolBufferException(e)
                    .setUnfinishedMessage(builder.buildPartial());
              }
              return builder.buildPartial();
            }
          };

          public static com.google.protobuf.Parser<Configuration> parser() {
            return PARSER;
          }

          @java.lang.Override
          public com.google.protobuf.Parser<Configuration> getParserForType() {
            return PARSER;
          }

          @java.lang.Override
          public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
          }

        }

        public static final int CONFIGURATION_FIELD_NUMBER = 4;
        private com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration configuration_;
        /**
         * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration configuration = 4;</code>
         * @return Whether the configuration field is set.
         */
        @java.lang.Override
        public boolean hasConfiguration() {
          return configuration_ != null;
        }
        /**
         * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration configuration = 4;</code>
         * @return The configuration.
         */
        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration getConfiguration() {
          return configuration_ == null ? com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.getDefaultInstance() : configuration_;
        }
        /**
         * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration configuration = 4;</code>
         */
        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.ConfigurationOrBuilder getConfigurationOrBuilder() {
          return getConfiguration();
        }

        public static final int TYPE_FIELD_NUMBER = 1;
        private volatile java.lang.Object type_;
        /**
         * <code>string type = 1;</code>
         * @return The type.
         */
        @java.lang.Override
        public java.lang.String getType() {
          java.lang.Object ref = type_;
          if (ref instanceof java.lang.String) {
            return (java.lang.String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            type_ = s;
            return s;
          }
        }
        /**
         * <code>string type = 1;</code>
         * @return The bytes for type.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
            getTypeBytes() {
          java.lang.Object ref = type_;
          if (ref instanceof java.lang.String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            type_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        public static final int INTERFACE_FIELD_NUMBER = 2;
        private volatile java.lang.Object interface_;
        /**
         * <code>string interface = 2;</code>
         * @return The interface.
         */
        @java.lang.Override
        public java.lang.String getInterface() {
          java.lang.Object ref = interface_;
          if (ref instanceof java.lang.String) {
            return (java.lang.String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            interface_ = s;
            return s;
          }
        }
        /**
         * <code>string interface = 2;</code>
         * @return The bytes for interface.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
            getInterfaceBytes() {
          java.lang.Object ref = interface_;
          if (ref instanceof java.lang.String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            interface_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        public static final int VERSION_FIELD_NUMBER = 3;
        private volatile java.lang.Object version_;
        /**
         * <code>string version = 3;</code>
         * @return The version.
         */
        @java.lang.Override
        public java.lang.String getVersion() {
          java.lang.Object ref = version_;
          if (ref instanceof java.lang.String) {
            return (java.lang.String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            version_ = s;
            return s;
          }
        }
        /**
         * <code>string version = 3;</code>
         * @return The bytes for version.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
            getVersionBytes() {
          java.lang.Object ref = version_;
          if (ref instanceof java.lang.String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            version_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        private byte memoizedIsInitialized = -1;
        @java.lang.Override
        public final boolean isInitialized() {
          byte isInitialized = memoizedIsInitialized;
          if (isInitialized == 1) return true;
          if (isInitialized == 0) return false;

          memoizedIsInitialized = 1;
          return true;
        }

        @java.lang.Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                            throws java.io.IOException {
          if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(type_)) {
            com.google.protobuf.GeneratedMessageV3.writeString(output, 1, type_);
          }
          if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(interface_)) {
            com.google.protobuf.GeneratedMessageV3.writeString(output, 2, interface_);
          }
          if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(version_)) {
            com.google.protobuf.GeneratedMessageV3.writeString(output, 3, version_);
          }
          if (configuration_ != null) {
            output.writeMessage(4, getConfiguration());
          }
          getUnknownFields().writeTo(output);
        }

        @java.lang.Override
        public int getSerializedSize() {
          int size = memoizedSize;
          if (size != -1) return size;

          size = 0;
          if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(type_)) {
            size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, type_);
          }
          if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(interface_)) {
            size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, interface_);
          }
          if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(version_)) {
            size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, version_);
          }
          if (configuration_ != null) {
            size += com.google.protobuf.CodedOutputStream
              .computeMessageSize(4, getConfiguration());
          }
          size += getUnknownFields().getSerializedSize();
          memoizedSize = size;
          return size;
        }

        @java.lang.Override
        public boolean equals(final java.lang.Object obj) {
          if (obj == this) {
           return true;
          }
          if (!(obj instanceof com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities)) {
            return super.equals(obj);
          }
          com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities other = (com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities) obj;

          if (hasConfiguration() != other.hasConfiguration()) return false;
          if (hasConfiguration()) {
            if (!getConfiguration()
                .equals(other.getConfiguration())) return false;
          }
          if (!getType()
              .equals(other.getType())) return false;
          if (!getInterface()
              .equals(other.getInterface())) return false;
          if (!getVersion()
              .equals(other.getVersion())) return false;
          if (!getUnknownFields().equals(other.getUnknownFields())) return false;
          return true;
        }

        @java.lang.Override
        public int hashCode() {
          if (memoizedHashCode != 0) {
            return memoizedHashCode;
          }
          int hash = 41;
          hash = (19 * hash) + getDescriptor().hashCode();
          if (hasConfiguration()) {
            hash = (37 * hash) + CONFIGURATION_FIELD_NUMBER;
            hash = (53 * hash) + getConfiguration().hashCode();
          }
          hash = (37 * hash) + TYPE_FIELD_NUMBER;
          hash = (53 * hash) + getType().hashCode();
          hash = (37 * hash) + INTERFACE_FIELD_NUMBER;
          hash = (53 * hash) + getInterface().hashCode();
          hash = (37 * hash) + VERSION_FIELD_NUMBER;
          hash = (53 * hash) + getVersion().hashCode();
          hash = (29 * hash) + getUnknownFields().hashCode();
          memoizedHashCode = hash;
          return hash;
        }

        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities parseFrom(
            java.nio.ByteBuffer data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities parseFrom(
            java.nio.ByteBuffer data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities parseFrom(
            com.google.protobuf.ByteString data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities parseFrom(
            com.google.protobuf.ByteString data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities parseFrom(byte[] data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities parseFrom(
            byte[] data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities parseFrom(java.io.InputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities parseFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input, extensionRegistry);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities parseDelimitedFrom(java.io.InputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseDelimitedWithIOException(PARSER, input);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities parseDelimitedFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities parseFrom(
            com.google.protobuf.CodedInputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities parseFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @java.lang.Override
        public Builder newBuilderForType() { return newBuilder(); }
        public static Builder newBuilder() {
          return DEFAULT_INSTANCE.toBuilder();
        }
        public static Builder newBuilder(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities prototype) {
          return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }
        @java.lang.Override
        public Builder toBuilder() {
          return this == DEFAULT_INSTANCE
              ? new Builder() : new Builder().mergeFrom(this);
        }

        @java.lang.Override
        protected Builder newBuilderForType(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          Builder builder = new Builder(parent);
          return builder;
        }
        /**
         * Protobuf type {@code alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities}
         */
        public static final class Builder extends
            com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
            // @@protoc_insertion_point(builder_implements:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities)
            com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.CapabilitiesOrBuilder {
          public static final com.google.protobuf.Descriptors.Descriptor
              getDescriptor() {
            return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_descriptor;
          }

          @java.lang.Override
          protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
              internalGetFieldAccessorTable() {
            return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_fieldAccessorTable
                .ensureFieldAccessorsInitialized(
                    com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.class, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Builder.class);
          }

          // Construct using com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.newBuilder()
          private Builder() {

          }

          private Builder(
              com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
            super(parent);

          }
          @java.lang.Override
          public Builder clear() {
            super.clear();
            if (configurationBuilder_ == null) {
              configuration_ = null;
            } else {
              configuration_ = null;
              configurationBuilder_ = null;
            }
            type_ = "";

            interface_ = "";

            version_ = "";

            return this;
          }

          @java.lang.Override
          public com.google.protobuf.Descriptors.Descriptor
              getDescriptorForType() {
            return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_descriptor;
          }

          @java.lang.Override
          public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities getDefaultInstanceForType() {
            return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.getDefaultInstance();
          }

          @java.lang.Override
          public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities build() {
            com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities result = buildPartial();
            if (!result.isInitialized()) {
              throw newUninitializedMessageException(result);
            }
            return result;
          }

          @java.lang.Override
          public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities buildPartial() {
            com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities result = new com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities(this);
            if (configurationBuilder_ == null) {
              result.configuration_ = configuration_;
            } else {
              result.configuration_ = configurationBuilder_.build();
            }
            result.type_ = type_;
            result.interface_ = interface_;
            result.version_ = version_;
            onBuilt();
            return result;
          }

          @java.lang.Override
          public Builder clone() {
            return super.clone();
          }
          @java.lang.Override
          public Builder setField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              java.lang.Object value) {
            return super.setField(field, value);
          }
          @java.lang.Override
          public Builder clearField(
              com.google.protobuf.Descriptors.FieldDescriptor field) {
            return super.clearField(field);
          }
          @java.lang.Override
          public Builder clearOneof(
              com.google.protobuf.Descriptors.OneofDescriptor oneof) {
            return super.clearOneof(oneof);
          }
          @java.lang.Override
          public Builder setRepeatedField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              int index, java.lang.Object value) {
            return super.setRepeatedField(field, index, value);
          }
          @java.lang.Override
          public Builder addRepeatedField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              java.lang.Object value) {
            return super.addRepeatedField(field, value);
          }
          @java.lang.Override
          public Builder mergeFrom(com.google.protobuf.Message other) {
            if (other instanceof com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities) {
              return mergeFrom((com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities)other);
            } else {
              super.mergeFrom(other);
              return this;
            }
          }

          public Builder mergeFrom(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities other) {
            if (other == com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.getDefaultInstance()) return this;
            if (other.hasConfiguration()) {
              mergeConfiguration(other.getConfiguration());
            }
            if (!other.getType().isEmpty()) {
              type_ = other.type_;
              onChanged();
            }
            if (!other.getInterface().isEmpty()) {
              interface_ = other.interface_;
              onChanged();
            }
            if (!other.getVersion().isEmpty()) {
              version_ = other.version_;
              onChanged();
            }
            this.mergeUnknownFields(other.getUnknownFields());
            onChanged();
            return this;
          }

          @java.lang.Override
          public final boolean isInitialized() {
            return true;
          }

          @java.lang.Override
          public Builder mergeFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws java.io.IOException {
            if (extensionRegistry == null) {
              throw new java.lang.NullPointerException();
            }
            try {
              boolean done = false;
              while (!done) {
                int tag = input.readTag();
                switch (tag) {
                  case 0:
                    done = true;
                    break;
                  case 10: {
                    type_ = input.readStringRequireUtf8();

                    break;
                  } // case 10
                  case 18: {
                    interface_ = input.readStringRequireUtf8();

                    break;
                  } // case 18
                  case 26: {
                    version_ = input.readStringRequireUtf8();

                    break;
                  } // case 26
                  case 34: {
                    input.readMessage(
                        getConfigurationFieldBuilder().getBuilder(),
                        extensionRegistry);

                    break;
                  } // case 34
                  default: {
                    if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                      done = true; // was an endgroup tag
                    }
                    break;
                  } // default:
                } // switch (tag)
              } // while (!done)
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
              throw e.unwrapIOException();
            } finally {
              onChanged();
            } // finally
            return this;
          }

          private com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration configuration_;
          private com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.Builder, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.ConfigurationOrBuilder> configurationBuilder_;
          /**
           * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration configuration = 4;</code>
           * @return Whether the configuration field is set.
           */
          public boolean hasConfiguration() {
            return configurationBuilder_ != null || configuration_ != null;
          }
          /**
           * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration configuration = 4;</code>
           * @return The configuration.
           */
          public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration getConfiguration() {
            if (configurationBuilder_ == null) {
              return configuration_ == null ? com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.getDefaultInstance() : configuration_;
            } else {
              return configurationBuilder_.getMessage();
            }
          }
          /**
           * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration configuration = 4;</code>
           */
          public Builder setConfiguration(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration value) {
            if (configurationBuilder_ == null) {
              if (value == null) {
                throw new NullPointerException();
              }
              configuration_ = value;
              onChanged();
            } else {
              configurationBuilder_.setMessage(value);
            }

            return this;
          }
          /**
           * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration configuration = 4;</code>
           */
          public Builder setConfiguration(
              com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.Builder builderForValue) {
            if (configurationBuilder_ == null) {
              configuration_ = builderForValue.build();
              onChanged();
            } else {
              configurationBuilder_.setMessage(builderForValue.build());
            }

            return this;
          }
          /**
           * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration configuration = 4;</code>
           */
          public Builder mergeConfiguration(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration value) {
            if (configurationBuilder_ == null) {
              if (configuration_ != null) {
                configuration_ =
                  com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.newBuilder(configuration_).mergeFrom(value).buildPartial();
              } else {
                configuration_ = value;
              }
              onChanged();
            } else {
              configurationBuilder_.mergeFrom(value);
            }

            return this;
          }
          /**
           * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration configuration = 4;</code>
           */
          public Builder clearConfiguration() {
            if (configurationBuilder_ == null) {
              configuration_ = null;
              onChanged();
            } else {
              configuration_ = null;
              configurationBuilder_ = null;
            }

            return this;
          }
          /**
           * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration configuration = 4;</code>
           */
          public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.Builder getConfigurationBuilder() {
            
            onChanged();
            return getConfigurationFieldBuilder().getBuilder();
          }
          /**
           * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration configuration = 4;</code>
           */
          public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.ConfigurationOrBuilder getConfigurationOrBuilder() {
            if (configurationBuilder_ != null) {
              return configurationBuilder_.getMessageOrBuilder();
            } else {
              return configuration_ == null ?
                  com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.getDefaultInstance() : configuration_;
            }
          }
          /**
           * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration configuration = 4;</code>
           */
          private com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.Builder, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.ConfigurationOrBuilder> 
              getConfigurationFieldBuilder() {
            if (configurationBuilder_ == null) {
              configurationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
                  com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Configuration.Builder, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.ConfigurationOrBuilder>(
                      getConfiguration(),
                      getParentForChildren(),
                      isClean());
              configuration_ = null;
            }
            return configurationBuilder_;
          }

          private java.lang.Object type_ = "";
          /**
           * <code>string type = 1;</code>
           * @return The type.
           */
          public java.lang.String getType() {
            java.lang.Object ref = type_;
            if (!(ref instanceof java.lang.String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              java.lang.String s = bs.toStringUtf8();
              type_ = s;
              return s;
            } else {
              return (java.lang.String) ref;
            }
          }
          /**
           * <code>string type = 1;</code>
           * @return The bytes for type.
           */
          public com.google.protobuf.ByteString
              getTypeBytes() {
            java.lang.Object ref = type_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (java.lang.String) ref);
              type_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <code>string type = 1;</code>
           * @param value The type to set.
           * @return This builder for chaining.
           */
          public Builder setType(
              java.lang.String value) {
            if (value == null) {
    throw new NullPointerException();
  }
  
            type_ = value;
            onChanged();
            return this;
          }
          /**
           * <code>string type = 1;</code>
           * @return This builder for chaining.
           */
          public Builder clearType() {
            
            type_ = getDefaultInstance().getType();
            onChanged();
            return this;
          }
          /**
           * <code>string type = 1;</code>
           * @param value The bytes for type to set.
           * @return This builder for chaining.
           */
          public Builder setTypeBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
            
            type_ = value;
            onChanged();
            return this;
          }

          private java.lang.Object interface_ = "";
          /**
           * <code>string interface = 2;</code>
           * @return The interface.
           */
          public java.lang.String getInterface() {
            java.lang.Object ref = interface_;
            if (!(ref instanceof java.lang.String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              java.lang.String s = bs.toStringUtf8();
              interface_ = s;
              return s;
            } else {
              return (java.lang.String) ref;
            }
          }
          /**
           * <code>string interface = 2;</code>
           * @return The bytes for interface.
           */
          public com.google.protobuf.ByteString
              getInterfaceBytes() {
            java.lang.Object ref = interface_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (java.lang.String) ref);
              interface_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <code>string interface = 2;</code>
           * @param value The interface to set.
           * @return This builder for chaining.
           */
          public Builder setInterface(
              java.lang.String value) {
            if (value == null) {
    throw new NullPointerException();
  }
  
            interface_ = value;
            onChanged();
            return this;
          }
          /**
           * <code>string interface = 2;</code>
           * @return This builder for chaining.
           */
          public Builder clearInterface() {
            
            interface_ = getDefaultInstance().getInterface();
            onChanged();
            return this;
          }
          /**
           * <code>string interface = 2;</code>
           * @param value The bytes for interface to set.
           * @return This builder for chaining.
           */
          public Builder setInterfaceBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
            
            interface_ = value;
            onChanged();
            return this;
          }

          private java.lang.Object version_ = "";
          /**
           * <code>string version = 3;</code>
           * @return The version.
           */
          public java.lang.String getVersion() {
            java.lang.Object ref = version_;
            if (!(ref instanceof java.lang.String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              java.lang.String s = bs.toStringUtf8();
              version_ = s;
              return s;
            } else {
              return (java.lang.String) ref;
            }
          }
          /**
           * <code>string version = 3;</code>
           * @return The bytes for version.
           */
          public com.google.protobuf.ByteString
              getVersionBytes() {
            java.lang.Object ref = version_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (java.lang.String) ref);
              version_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <code>string version = 3;</code>
           * @param value The version to set.
           * @return This builder for chaining.
           */
          public Builder setVersion(
              java.lang.String value) {
            if (value == null) {
    throw new NullPointerException();
  }
  
            version_ = value;
            onChanged();
            return this;
          }
          /**
           * <code>string version = 3;</code>
           * @return This builder for chaining.
           */
          public Builder clearVersion() {
            
            version_ = getDefaultInstance().getVersion();
            onChanged();
            return this;
          }
          /**
           * <code>string version = 3;</code>
           * @param value The bytes for version to set.
           * @return This builder for chaining.
           */
          public Builder setVersionBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
            
            version_ = value;
            onChanged();
            return this;
          }
          @java.lang.Override
          public final Builder setUnknownFields(
              final com.google.protobuf.UnknownFieldSet unknownFields) {
            return super.setUnknownFields(unknownFields);
          }

          @java.lang.Override
          public final Builder mergeUnknownFields(
              final com.google.protobuf.UnknownFieldSet unknownFields) {
            return super.mergeUnknownFields(unknownFields);
          }


          // @@protoc_insertion_point(builder_scope:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities)
        }

        // @@protoc_insertion_point(class_scope:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities)
        private static final com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities DEFAULT_INSTANCE;
        static {
          DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities();
        }

        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities getDefaultInstance() {
          return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<Capabilities>
            PARSER = new com.google.protobuf.AbstractParser<Capabilities>() {
          @java.lang.Override
          public Capabilities parsePartialFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
            Builder builder = newBuilder();
            try {
              builder.mergeFrom(input, extensionRegistry);
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
              throw e.setUnfinishedMessage(builder.buildPartial());
            } catch (com.google.protobuf.UninitializedMessageException e) {
              throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
            } catch (java.io.IOException e) {
              throw new com.google.protobuf.InvalidProtocolBufferException(e)
                  .setUnfinishedMessage(builder.buildPartial());
            }
            return builder.buildPartial();
          }
        };

        public static com.google.protobuf.Parser<Capabilities> parser() {
          return PARSER;
        }

        @java.lang.Override
        public com.google.protobuf.Parser<Capabilities> getParserForType() {
          return PARSER;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities getDefaultInstanceForType() {
          return DEFAULT_INSTANCE;
        }

      }

      public interface AdditionalIdentificationOrBuilder extends
          // @@protoc_insertion_point(interface_extends:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification)
          com.google.protobuf.MessageOrBuilder {

        /**
         * <code>string modelName = 5;</code>
         * @return The modelName.
         */
        java.lang.String getModelName();
        /**
         * <code>string modelName = 5;</code>
         * @return The bytes for modelName.
         */
        com.google.protobuf.ByteString
            getModelNameBytes();

        /**
         * <code>string deviceTokenEncryptionType = 3;</code>
         * @return The deviceTokenEncryptionType.
         */
        java.lang.String getDeviceTokenEncryptionType();
        /**
         * <code>string deviceTokenEncryptionType = 3;</code>
         * @return The bytes for deviceTokenEncryptionType.
         */
        com.google.protobuf.ByteString
            getDeviceTokenEncryptionTypeBytes();

        /**
         * <code>string firmwareVersion = 1;</code>
         * @return The firmwareVersion.
         */
        java.lang.String getFirmwareVersion();
        /**
         * <code>string firmwareVersion = 1;</code>
         * @return The bytes for firmwareVersion.
         */
        com.google.protobuf.ByteString
            getFirmwareVersionBytes();

        /**
         * <code>string amazonDeviceType = 4;</code>
         * @return The amazonDeviceType.
         */
        java.lang.String getAmazonDeviceType();
        /**
         * <code>string amazonDeviceType = 4;</code>
         * @return The bytes for amazonDeviceType.
         */
        com.google.protobuf.ByteString
            getAmazonDeviceTypeBytes();

        /**
         * <code>string radioAddress = 6;</code>
         * @return The radioAddress.
         */
        java.lang.String getRadioAddress();
        /**
         * <code>string radioAddress = 6;</code>
         * @return The bytes for radioAddress.
         */
        com.google.protobuf.ByteString
            getRadioAddressBytes();

        /**
         * <code>string deviceToken = 2;</code>
         * @return The deviceToken.
         */
        java.lang.String getDeviceToken();
        /**
         * <code>string deviceToken = 2;</code>
         * @return The bytes for deviceToken.
         */
        com.google.protobuf.ByteString
            getDeviceTokenBytes();
      }
      /**
       * Protobuf type {@code alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification}
       */
      public static final class AdditionalIdentification extends
          com.google.protobuf.GeneratedMessageV3 implements
          // @@protoc_insertion_point(message_implements:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification)
          AdditionalIdentificationOrBuilder {
      private static final long serialVersionUID = 0L;
        // Use AdditionalIdentification.newBuilder() to construct.
        private AdditionalIdentification(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
          super(builder);
        }
        private AdditionalIdentification() {
          modelName_ = "";
          deviceTokenEncryptionType_ = "";
          firmwareVersion_ = "";
          amazonDeviceType_ = "";
          radioAddress_ = "";
          deviceToken_ = "";
        }

        @java.lang.Override
        @SuppressWarnings({"unused"})
        protected java.lang.Object newInstance(
            UnusedPrivateParameter unused) {
          return new AdditionalIdentification();
        }

        @java.lang.Override
        public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
          return this.unknownFields;
        }
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_AdditionalIdentification_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_AdditionalIdentification_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification.class, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification.Builder.class);
        }

        public static final int MODELNAME_FIELD_NUMBER = 5;
        private volatile java.lang.Object modelName_;
        /**
         * <code>string modelName = 5;</code>
         * @return The modelName.
         */
        @java.lang.Override
        public java.lang.String getModelName() {
          java.lang.Object ref = modelName_;
          if (ref instanceof java.lang.String) {
            return (java.lang.String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            modelName_ = s;
            return s;
          }
        }
        /**
         * <code>string modelName = 5;</code>
         * @return The bytes for modelName.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
            getModelNameBytes() {
          java.lang.Object ref = modelName_;
          if (ref instanceof java.lang.String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            modelName_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        public static final int DEVICETOKENENCRYPTIONTYPE_FIELD_NUMBER = 3;
        private volatile java.lang.Object deviceTokenEncryptionType_;
        /**
         * <code>string deviceTokenEncryptionType = 3;</code>
         * @return The deviceTokenEncryptionType.
         */
        @java.lang.Override
        public java.lang.String getDeviceTokenEncryptionType() {
          java.lang.Object ref = deviceTokenEncryptionType_;
          if (ref instanceof java.lang.String) {
            return (java.lang.String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            deviceTokenEncryptionType_ = s;
            return s;
          }
        }
        /**
         * <code>string deviceTokenEncryptionType = 3;</code>
         * @return The bytes for deviceTokenEncryptionType.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
            getDeviceTokenEncryptionTypeBytes() {
          java.lang.Object ref = deviceTokenEncryptionType_;
          if (ref instanceof java.lang.String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            deviceTokenEncryptionType_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        public static final int FIRMWAREVERSION_FIELD_NUMBER = 1;
        private volatile java.lang.Object firmwareVersion_;
        /**
         * <code>string firmwareVersion = 1;</code>
         * @return The firmwareVersion.
         */
        @java.lang.Override
        public java.lang.String getFirmwareVersion() {
          java.lang.Object ref = firmwareVersion_;
          if (ref instanceof java.lang.String) {
            return (java.lang.String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            firmwareVersion_ = s;
            return s;
          }
        }
        /**
         * <code>string firmwareVersion = 1;</code>
         * @return The bytes for firmwareVersion.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
            getFirmwareVersionBytes() {
          java.lang.Object ref = firmwareVersion_;
          if (ref instanceof java.lang.String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            firmwareVersion_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        public static final int AMAZONDEVICETYPE_FIELD_NUMBER = 4;
        private volatile java.lang.Object amazonDeviceType_;
        /**
         * <code>string amazonDeviceType = 4;</code>
         * @return The amazonDeviceType.
         */
        @java.lang.Override
        public java.lang.String getAmazonDeviceType() {
          java.lang.Object ref = amazonDeviceType_;
          if (ref instanceof java.lang.String) {
            return (java.lang.String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            amazonDeviceType_ = s;
            return s;
          }
        }
        /**
         * <code>string amazonDeviceType = 4;</code>
         * @return The bytes for amazonDeviceType.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
            getAmazonDeviceTypeBytes() {
          java.lang.Object ref = amazonDeviceType_;
          if (ref instanceof java.lang.String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            amazonDeviceType_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        public static final int RADIOADDRESS_FIELD_NUMBER = 6;
        private volatile java.lang.Object radioAddress_;
        /**
         * <code>string radioAddress = 6;</code>
         * @return The radioAddress.
         */
        @java.lang.Override
        public java.lang.String getRadioAddress() {
          java.lang.Object ref = radioAddress_;
          if (ref instanceof java.lang.String) {
            return (java.lang.String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            radioAddress_ = s;
            return s;
          }
        }
        /**
         * <code>string radioAddress = 6;</code>
         * @return The bytes for radioAddress.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
            getRadioAddressBytes() {
          java.lang.Object ref = radioAddress_;
          if (ref instanceof java.lang.String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            radioAddress_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        public static final int DEVICETOKEN_FIELD_NUMBER = 2;
        private volatile java.lang.Object deviceToken_;
        /**
         * <code>string deviceToken = 2;</code>
         * @return The deviceToken.
         */
        @java.lang.Override
        public java.lang.String getDeviceToken() {
          java.lang.Object ref = deviceToken_;
          if (ref instanceof java.lang.String) {
            return (java.lang.String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            deviceToken_ = s;
            return s;
          }
        }
        /**
         * <code>string deviceToken = 2;</code>
         * @return The bytes for deviceToken.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
            getDeviceTokenBytes() {
          java.lang.Object ref = deviceToken_;
          if (ref instanceof java.lang.String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            deviceToken_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        private byte memoizedIsInitialized = -1;
        @java.lang.Override
        public final boolean isInitialized() {
          byte isInitialized = memoizedIsInitialized;
          if (isInitialized == 1) return true;
          if (isInitialized == 0) return false;

          memoizedIsInitialized = 1;
          return true;
        }

        @java.lang.Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                            throws java.io.IOException {
          if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(firmwareVersion_)) {
            com.google.protobuf.GeneratedMessageV3.writeString(output, 1, firmwareVersion_);
          }
          if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceToken_)) {
            com.google.protobuf.GeneratedMessageV3.writeString(output, 2, deviceToken_);
          }
          if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceTokenEncryptionType_)) {
            com.google.protobuf.GeneratedMessageV3.writeString(output, 3, deviceTokenEncryptionType_);
          }
          if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(amazonDeviceType_)) {
            com.google.protobuf.GeneratedMessageV3.writeString(output, 4, amazonDeviceType_);
          }
          if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(modelName_)) {
            com.google.protobuf.GeneratedMessageV3.writeString(output, 5, modelName_);
          }
          if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(radioAddress_)) {
            com.google.protobuf.GeneratedMessageV3.writeString(output, 6, radioAddress_);
          }
          getUnknownFields().writeTo(output);
        }

        @java.lang.Override
        public int getSerializedSize() {
          int size = memoizedSize;
          if (size != -1) return size;

          size = 0;
          if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(firmwareVersion_)) {
            size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, firmwareVersion_);
          }
          if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceToken_)) {
            size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, deviceToken_);
          }
          if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceTokenEncryptionType_)) {
            size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, deviceTokenEncryptionType_);
          }
          if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(amazonDeviceType_)) {
            size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, amazonDeviceType_);
          }
          if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(modelName_)) {
            size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, modelName_);
          }
          if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(radioAddress_)) {
            size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, radioAddress_);
          }
          size += getUnknownFields().getSerializedSize();
          memoizedSize = size;
          return size;
        }

        @java.lang.Override
        public boolean equals(final java.lang.Object obj) {
          if (obj == this) {
           return true;
          }
          if (!(obj instanceof com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification)) {
            return super.equals(obj);
          }
          com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification other = (com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification) obj;

          if (!getModelName()
              .equals(other.getModelName())) return false;
          if (!getDeviceTokenEncryptionType()
              .equals(other.getDeviceTokenEncryptionType())) return false;
          if (!getFirmwareVersion()
              .equals(other.getFirmwareVersion())) return false;
          if (!getAmazonDeviceType()
              .equals(other.getAmazonDeviceType())) return false;
          if (!getRadioAddress()
              .equals(other.getRadioAddress())) return false;
          if (!getDeviceToken()
              .equals(other.getDeviceToken())) return false;
          if (!getUnknownFields().equals(other.getUnknownFields())) return false;
          return true;
        }

        @java.lang.Override
        public int hashCode() {
          if (memoizedHashCode != 0) {
            return memoizedHashCode;
          }
          int hash = 41;
          hash = (19 * hash) + getDescriptor().hashCode();
          hash = (37 * hash) + MODELNAME_FIELD_NUMBER;
          hash = (53 * hash) + getModelName().hashCode();
          hash = (37 * hash) + DEVICETOKENENCRYPTIONTYPE_FIELD_NUMBER;
          hash = (53 * hash) + getDeviceTokenEncryptionType().hashCode();
          hash = (37 * hash) + FIRMWAREVERSION_FIELD_NUMBER;
          hash = (53 * hash) + getFirmwareVersion().hashCode();
          hash = (37 * hash) + AMAZONDEVICETYPE_FIELD_NUMBER;
          hash = (53 * hash) + getAmazonDeviceType().hashCode();
          hash = (37 * hash) + RADIOADDRESS_FIELD_NUMBER;
          hash = (53 * hash) + getRadioAddress().hashCode();
          hash = (37 * hash) + DEVICETOKEN_FIELD_NUMBER;
          hash = (53 * hash) + getDeviceToken().hashCode();
          hash = (29 * hash) + getUnknownFields().hashCode();
          memoizedHashCode = hash;
          return hash;
        }

        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification parseFrom(
            java.nio.ByteBuffer data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification parseFrom(
            java.nio.ByteBuffer data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification parseFrom(
            com.google.protobuf.ByteString data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification parseFrom(
            com.google.protobuf.ByteString data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification parseFrom(byte[] data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification parseFrom(
            byte[] data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification parseFrom(java.io.InputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification parseFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input, extensionRegistry);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification parseDelimitedFrom(java.io.InputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseDelimitedWithIOException(PARSER, input);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification parseDelimitedFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification parseFrom(
            com.google.protobuf.CodedInputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input);
        }
        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification parseFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @java.lang.Override
        public Builder newBuilderForType() { return newBuilder(); }
        public static Builder newBuilder() {
          return DEFAULT_INSTANCE.toBuilder();
        }
        public static Builder newBuilder(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification prototype) {
          return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }
        @java.lang.Override
        public Builder toBuilder() {
          return this == DEFAULT_INSTANCE
              ? new Builder() : new Builder().mergeFrom(this);
        }

        @java.lang.Override
        protected Builder newBuilderForType(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          Builder builder = new Builder(parent);
          return builder;
        }
        /**
         * Protobuf type {@code alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification}
         */
        public static final class Builder extends
            com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
            // @@protoc_insertion_point(builder_implements:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification)
            com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentificationOrBuilder {
          public static final com.google.protobuf.Descriptors.Descriptor
              getDescriptor() {
            return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_AdditionalIdentification_descriptor;
          }

          @java.lang.Override
          protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
              internalGetFieldAccessorTable() {
            return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_AdditionalIdentification_fieldAccessorTable
                .ensureFieldAccessorsInitialized(
                    com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification.class, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification.Builder.class);
          }

          // Construct using com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification.newBuilder()
          private Builder() {

          }

          private Builder(
              com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
            super(parent);

          }
          @java.lang.Override
          public Builder clear() {
            super.clear();
            modelName_ = "";

            deviceTokenEncryptionType_ = "";

            firmwareVersion_ = "";

            amazonDeviceType_ = "";

            radioAddress_ = "";

            deviceToken_ = "";

            return this;
          }

          @java.lang.Override
          public com.google.protobuf.Descriptors.Descriptor
              getDescriptorForType() {
            return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_AdditionalIdentification_descriptor;
          }

          @java.lang.Override
          public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification getDefaultInstanceForType() {
            return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification.getDefaultInstance();
          }

          @java.lang.Override
          public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification build() {
            com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification result = buildPartial();
            if (!result.isInitialized()) {
              throw newUninitializedMessageException(result);
            }
            return result;
          }

          @java.lang.Override
          public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification buildPartial() {
            com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification result = new com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification(this);
            result.modelName_ = modelName_;
            result.deviceTokenEncryptionType_ = deviceTokenEncryptionType_;
            result.firmwareVersion_ = firmwareVersion_;
            result.amazonDeviceType_ = amazonDeviceType_;
            result.radioAddress_ = radioAddress_;
            result.deviceToken_ = deviceToken_;
            onBuilt();
            return result;
          }

          @java.lang.Override
          public Builder clone() {
            return super.clone();
          }
          @java.lang.Override
          public Builder setField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              java.lang.Object value) {
            return super.setField(field, value);
          }
          @java.lang.Override
          public Builder clearField(
              com.google.protobuf.Descriptors.FieldDescriptor field) {
            return super.clearField(field);
          }
          @java.lang.Override
          public Builder clearOneof(
              com.google.protobuf.Descriptors.OneofDescriptor oneof) {
            return super.clearOneof(oneof);
          }
          @java.lang.Override
          public Builder setRepeatedField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              int index, java.lang.Object value) {
            return super.setRepeatedField(field, index, value);
          }
          @java.lang.Override
          public Builder addRepeatedField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              java.lang.Object value) {
            return super.addRepeatedField(field, value);
          }
          @java.lang.Override
          public Builder mergeFrom(com.google.protobuf.Message other) {
            if (other instanceof com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification) {
              return mergeFrom((com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification)other);
            } else {
              super.mergeFrom(other);
              return this;
            }
          }

          public Builder mergeFrom(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification other) {
            if (other == com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification.getDefaultInstance()) return this;
            if (!other.getModelName().isEmpty()) {
              modelName_ = other.modelName_;
              onChanged();
            }
            if (!other.getDeviceTokenEncryptionType().isEmpty()) {
              deviceTokenEncryptionType_ = other.deviceTokenEncryptionType_;
              onChanged();
            }
            if (!other.getFirmwareVersion().isEmpty()) {
              firmwareVersion_ = other.firmwareVersion_;
              onChanged();
            }
            if (!other.getAmazonDeviceType().isEmpty()) {
              amazonDeviceType_ = other.amazonDeviceType_;
              onChanged();
            }
            if (!other.getRadioAddress().isEmpty()) {
              radioAddress_ = other.radioAddress_;
              onChanged();
            }
            if (!other.getDeviceToken().isEmpty()) {
              deviceToken_ = other.deviceToken_;
              onChanged();
            }
            this.mergeUnknownFields(other.getUnknownFields());
            onChanged();
            return this;
          }

          @java.lang.Override
          public final boolean isInitialized() {
            return true;
          }

          @java.lang.Override
          public Builder mergeFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws java.io.IOException {
            if (extensionRegistry == null) {
              throw new java.lang.NullPointerException();
            }
            try {
              boolean done = false;
              while (!done) {
                int tag = input.readTag();
                switch (tag) {
                  case 0:
                    done = true;
                    break;
                  case 10: {
                    firmwareVersion_ = input.readStringRequireUtf8();

                    break;
                  } // case 10
                  case 18: {
                    deviceToken_ = input.readStringRequireUtf8();

                    break;
                  } // case 18
                  case 26: {
                    deviceTokenEncryptionType_ = input.readStringRequireUtf8();

                    break;
                  } // case 26
                  case 34: {
                    amazonDeviceType_ = input.readStringRequireUtf8();

                    break;
                  } // case 34
                  case 42: {
                    modelName_ = input.readStringRequireUtf8();

                    break;
                  } // case 42
                  case 50: {
                    radioAddress_ = input.readStringRequireUtf8();

                    break;
                  } // case 50
                  default: {
                    if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                      done = true; // was an endgroup tag
                    }
                    break;
                  } // default:
                } // switch (tag)
              } // while (!done)
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
              throw e.unwrapIOException();
            } finally {
              onChanged();
            } // finally
            return this;
          }

          private java.lang.Object modelName_ = "";
          /**
           * <code>string modelName = 5;</code>
           * @return The modelName.
           */
          public java.lang.String getModelName() {
            java.lang.Object ref = modelName_;
            if (!(ref instanceof java.lang.String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              java.lang.String s = bs.toStringUtf8();
              modelName_ = s;
              return s;
            } else {
              return (java.lang.String) ref;
            }
          }
          /**
           * <code>string modelName = 5;</code>
           * @return The bytes for modelName.
           */
          public com.google.protobuf.ByteString
              getModelNameBytes() {
            java.lang.Object ref = modelName_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (java.lang.String) ref);
              modelName_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <code>string modelName = 5;</code>
           * @param value The modelName to set.
           * @return This builder for chaining.
           */
          public Builder setModelName(
              java.lang.String value) {
            if (value == null) {
    throw new NullPointerException();
  }
  
            modelName_ = value;
            onChanged();
            return this;
          }
          /**
           * <code>string modelName = 5;</code>
           * @return This builder for chaining.
           */
          public Builder clearModelName() {
            
            modelName_ = getDefaultInstance().getModelName();
            onChanged();
            return this;
          }
          /**
           * <code>string modelName = 5;</code>
           * @param value The bytes for modelName to set.
           * @return This builder for chaining.
           */
          public Builder setModelNameBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
            
            modelName_ = value;
            onChanged();
            return this;
          }

          private java.lang.Object deviceTokenEncryptionType_ = "";
          /**
           * <code>string deviceTokenEncryptionType = 3;</code>
           * @return The deviceTokenEncryptionType.
           */
          public java.lang.String getDeviceTokenEncryptionType() {
            java.lang.Object ref = deviceTokenEncryptionType_;
            if (!(ref instanceof java.lang.String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              java.lang.String s = bs.toStringUtf8();
              deviceTokenEncryptionType_ = s;
              return s;
            } else {
              return (java.lang.String) ref;
            }
          }
          /**
           * <code>string deviceTokenEncryptionType = 3;</code>
           * @return The bytes for deviceTokenEncryptionType.
           */
          public com.google.protobuf.ByteString
              getDeviceTokenEncryptionTypeBytes() {
            java.lang.Object ref = deviceTokenEncryptionType_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (java.lang.String) ref);
              deviceTokenEncryptionType_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <code>string deviceTokenEncryptionType = 3;</code>
           * @param value The deviceTokenEncryptionType to set.
           * @return This builder for chaining.
           */
          public Builder setDeviceTokenEncryptionType(
              java.lang.String value) {
            if (value == null) {
    throw new NullPointerException();
  }
  
            deviceTokenEncryptionType_ = value;
            onChanged();
            return this;
          }
          /**
           * <code>string deviceTokenEncryptionType = 3;</code>
           * @return This builder for chaining.
           */
          public Builder clearDeviceTokenEncryptionType() {
            
            deviceTokenEncryptionType_ = getDefaultInstance().getDeviceTokenEncryptionType();
            onChanged();
            return this;
          }
          /**
           * <code>string deviceTokenEncryptionType = 3;</code>
           * @param value The bytes for deviceTokenEncryptionType to set.
           * @return This builder for chaining.
           */
          public Builder setDeviceTokenEncryptionTypeBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
            
            deviceTokenEncryptionType_ = value;
            onChanged();
            return this;
          }

          private java.lang.Object firmwareVersion_ = "";
          /**
           * <code>string firmwareVersion = 1;</code>
           * @return The firmwareVersion.
           */
          public java.lang.String getFirmwareVersion() {
            java.lang.Object ref = firmwareVersion_;
            if (!(ref instanceof java.lang.String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              java.lang.String s = bs.toStringUtf8();
              firmwareVersion_ = s;
              return s;
            } else {
              return (java.lang.String) ref;
            }
          }
          /**
           * <code>string firmwareVersion = 1;</code>
           * @return The bytes for firmwareVersion.
           */
          public com.google.protobuf.ByteString
              getFirmwareVersionBytes() {
            java.lang.Object ref = firmwareVersion_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (java.lang.String) ref);
              firmwareVersion_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <code>string firmwareVersion = 1;</code>
           * @param value The firmwareVersion to set.
           * @return This builder for chaining.
           */
          public Builder setFirmwareVersion(
              java.lang.String value) {
            if (value == null) {
    throw new NullPointerException();
  }
  
            firmwareVersion_ = value;
            onChanged();
            return this;
          }
          /**
           * <code>string firmwareVersion = 1;</code>
           * @return This builder for chaining.
           */
          public Builder clearFirmwareVersion() {
            
            firmwareVersion_ = getDefaultInstance().getFirmwareVersion();
            onChanged();
            return this;
          }
          /**
           * <code>string firmwareVersion = 1;</code>
           * @param value The bytes for firmwareVersion to set.
           * @return This builder for chaining.
           */
          public Builder setFirmwareVersionBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
            
            firmwareVersion_ = value;
            onChanged();
            return this;
          }

          private java.lang.Object amazonDeviceType_ = "";
          /**
           * <code>string amazonDeviceType = 4;</code>
           * @return The amazonDeviceType.
           */
          public java.lang.String getAmazonDeviceType() {
            java.lang.Object ref = amazonDeviceType_;
            if (!(ref instanceof java.lang.String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              java.lang.String s = bs.toStringUtf8();
              amazonDeviceType_ = s;
              return s;
            } else {
              return (java.lang.String) ref;
            }
          }
          /**
           * <code>string amazonDeviceType = 4;</code>
           * @return The bytes for amazonDeviceType.
           */
          public com.google.protobuf.ByteString
              getAmazonDeviceTypeBytes() {
            java.lang.Object ref = amazonDeviceType_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (java.lang.String) ref);
              amazonDeviceType_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <code>string amazonDeviceType = 4;</code>
           * @param value The amazonDeviceType to set.
           * @return This builder for chaining.
           */
          public Builder setAmazonDeviceType(
              java.lang.String value) {
            if (value == null) {
    throw new NullPointerException();
  }
  
            amazonDeviceType_ = value;
            onChanged();
            return this;
          }
          /**
           * <code>string amazonDeviceType = 4;</code>
           * @return This builder for chaining.
           */
          public Builder clearAmazonDeviceType() {
            
            amazonDeviceType_ = getDefaultInstance().getAmazonDeviceType();
            onChanged();
            return this;
          }
          /**
           * <code>string amazonDeviceType = 4;</code>
           * @param value The bytes for amazonDeviceType to set.
           * @return This builder for chaining.
           */
          public Builder setAmazonDeviceTypeBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
            
            amazonDeviceType_ = value;
            onChanged();
            return this;
          }

          private java.lang.Object radioAddress_ = "";
          /**
           * <code>string radioAddress = 6;</code>
           * @return The radioAddress.
           */
          public java.lang.String getRadioAddress() {
            java.lang.Object ref = radioAddress_;
            if (!(ref instanceof java.lang.String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              java.lang.String s = bs.toStringUtf8();
              radioAddress_ = s;
              return s;
            } else {
              return (java.lang.String) ref;
            }
          }
          /**
           * <code>string radioAddress = 6;</code>
           * @return The bytes for radioAddress.
           */
          public com.google.protobuf.ByteString
              getRadioAddressBytes() {
            java.lang.Object ref = radioAddress_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (java.lang.String) ref);
              radioAddress_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <code>string radioAddress = 6;</code>
           * @param value The radioAddress to set.
           * @return This builder for chaining.
           */
          public Builder setRadioAddress(
              java.lang.String value) {
            if (value == null) {
    throw new NullPointerException();
  }
  
            radioAddress_ = value;
            onChanged();
            return this;
          }
          /**
           * <code>string radioAddress = 6;</code>
           * @return This builder for chaining.
           */
          public Builder clearRadioAddress() {
            
            radioAddress_ = getDefaultInstance().getRadioAddress();
            onChanged();
            return this;
          }
          /**
           * <code>string radioAddress = 6;</code>
           * @param value The bytes for radioAddress to set.
           * @return This builder for chaining.
           */
          public Builder setRadioAddressBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
            
            radioAddress_ = value;
            onChanged();
            return this;
          }

          private java.lang.Object deviceToken_ = "";
          /**
           * <code>string deviceToken = 2;</code>
           * @return The deviceToken.
           */
          public java.lang.String getDeviceToken() {
            java.lang.Object ref = deviceToken_;
            if (!(ref instanceof java.lang.String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              java.lang.String s = bs.toStringUtf8();
              deviceToken_ = s;
              return s;
            } else {
              return (java.lang.String) ref;
            }
          }
          /**
           * <code>string deviceToken = 2;</code>
           * @return The bytes for deviceToken.
           */
          public com.google.protobuf.ByteString
              getDeviceTokenBytes() {
            java.lang.Object ref = deviceToken_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (java.lang.String) ref);
              deviceToken_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <code>string deviceToken = 2;</code>
           * @param value The deviceToken to set.
           * @return This builder for chaining.
           */
          public Builder setDeviceToken(
              java.lang.String value) {
            if (value == null) {
    throw new NullPointerException();
  }
  
            deviceToken_ = value;
            onChanged();
            return this;
          }
          /**
           * <code>string deviceToken = 2;</code>
           * @return This builder for chaining.
           */
          public Builder clearDeviceToken() {
            
            deviceToken_ = getDefaultInstance().getDeviceToken();
            onChanged();
            return this;
          }
          /**
           * <code>string deviceToken = 2;</code>
           * @param value The bytes for deviceToken to set.
           * @return This builder for chaining.
           */
          public Builder setDeviceTokenBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
            
            deviceToken_ = value;
            onChanged();
            return this;
          }
          @java.lang.Override
          public final Builder setUnknownFields(
              final com.google.protobuf.UnknownFieldSet unknownFields) {
            return super.setUnknownFields(unknownFields);
          }

          @java.lang.Override
          public final Builder mergeUnknownFields(
              final com.google.protobuf.UnknownFieldSet unknownFields) {
            return super.mergeUnknownFields(unknownFields);
          }


          // @@protoc_insertion_point(builder_scope:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification)
        }

        // @@protoc_insertion_point(class_scope:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification)
        private static final com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification DEFAULT_INSTANCE;
        static {
          DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification();
        }

        public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification getDefaultInstance() {
          return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<AdditionalIdentification>
            PARSER = new com.google.protobuf.AbstractParser<AdditionalIdentification>() {
          @java.lang.Override
          public AdditionalIdentification parsePartialFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
            Builder builder = newBuilder();
            try {
              builder.mergeFrom(input, extensionRegistry);
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
              throw e.setUnfinishedMessage(builder.buildPartial());
            } catch (com.google.protobuf.UninitializedMessageException e) {
              throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
            } catch (java.io.IOException e) {
              throw new com.google.protobuf.InvalidProtocolBufferException(e)
                  .setUnfinishedMessage(builder.buildPartial());
            }
            return builder.buildPartial();
          }
        };

        public static com.google.protobuf.Parser<AdditionalIdentification> parser() {
          return PARSER;
        }

        @java.lang.Override
        public com.google.protobuf.Parser<AdditionalIdentification> getParserForType() {
          return PARSER;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification getDefaultInstanceForType() {
          return DEFAULT_INSTANCE;
        }

      }

      public static final int CAPABILITIES_FIELD_NUMBER = 11;
      private java.util.List<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities> capabilities_;
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
       */
      @java.lang.Override
      public java.util.List<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities> getCapabilitiesList() {
        return capabilities_;
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
       */
      @java.lang.Override
      public java.util.List<? extends com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.CapabilitiesOrBuilder> 
          getCapabilitiesOrBuilderList() {
        return capabilities_;
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
       */
      @java.lang.Override
      public int getCapabilitiesCount() {
        return capabilities_.size();
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
       */
      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities getCapabilities(int index) {
        return capabilities_.get(index);
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
       */
      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.CapabilitiesOrBuilder getCapabilitiesOrBuilder(
          int index) {
        return capabilities_.get(index);
      }

      public static final int ADDITIONALIDENTIFICATION_FIELD_NUMBER = 12;
      private com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification additionalIdentification_;
      /**
       * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification additionalIdentification = 12;</code>
       * @return Whether the additionalIdentification field is set.
       */
      @java.lang.Override
      public boolean hasAdditionalIdentification() {
        return additionalIdentification_ != null;
      }
      /**
       * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification additionalIdentification = 12;</code>
       * @return The additionalIdentification.
       */
      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification getAdditionalIdentification() {
        return additionalIdentification_ == null ? com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification.getDefaultInstance() : additionalIdentification_;
      }
      /**
       * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification additionalIdentification = 12;</code>
       */
      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentificationOrBuilder getAdditionalIdentificationOrBuilder() {
        return getAdditionalIdentification();
      }

      public static final int ENDPOINTID_FIELD_NUMBER = 1;
      private volatile java.lang.Object endpointId_;
      /**
       * <code>string endpointId = 1;</code>
       * @return The endpointId.
       */
      @java.lang.Override
      public java.lang.String getEndpointId() {
        java.lang.Object ref = endpointId_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          endpointId_ = s;
          return s;
        }
      }
      /**
       * <code>string endpointId = 1;</code>
       * @return The bytes for endpointId.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getEndpointIdBytes() {
        java.lang.Object ref = endpointId_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          endpointId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int MANUFACTURERNAME_FIELD_NUMBER = 4;
      private volatile java.lang.Object manufacturerName_;
      /**
       * <code>string manufacturerName = 4;</code>
       * @return The manufacturerName.
       */
      @java.lang.Override
      public java.lang.String getManufacturerName() {
        java.lang.Object ref = manufacturerName_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          manufacturerName_ = s;
          return s;
        }
      }
      /**
       * <code>string manufacturerName = 4;</code>
       * @return The bytes for manufacturerName.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getManufacturerNameBytes() {
        java.lang.Object ref = manufacturerName_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          manufacturerName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int DESCRIPTION_FIELD_NUMBER = 3;
      private volatile java.lang.Object description_;
      /**
       * <code>string description = 3;</code>
       * @return The description.
       */
      @java.lang.Override
      public java.lang.String getDescription() {
        java.lang.Object ref = description_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          description_ = s;
          return s;
        }
      }
      /**
       * <code>string description = 3;</code>
       * @return The bytes for description.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getDescriptionBytes() {
        java.lang.Object ref = description_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          description_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int FRIENDLYNAME_FIELD_NUMBER = 2;
      private volatile java.lang.Object friendlyName_;
      /**
       * <code>string friendlyName = 2;</code>
       * @return The friendlyName.
       */
      @java.lang.Override
      public java.lang.String getFriendlyName() {
        java.lang.Object ref = friendlyName_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          friendlyName_ = s;
          return s;
        }
      }
      /**
       * <code>string friendlyName = 2;</code>
       * @return The bytes for friendlyName.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getFriendlyNameBytes() {
        java.lang.Object ref = friendlyName_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          friendlyName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(endpointId_)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 1, endpointId_);
        }
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(friendlyName_)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 2, friendlyName_);
        }
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(description_)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 3, description_);
        }
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(manufacturerName_)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 4, manufacturerName_);
        }
        for (int i = 0; i < capabilities_.size(); i++) {
          output.writeMessage(11, capabilities_.get(i));
        }
        if (additionalIdentification_ != null) {
          output.writeMessage(12, getAdditionalIdentification());
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(endpointId_)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, endpointId_);
        }
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(friendlyName_)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, friendlyName_);
        }
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(description_)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, description_);
        }
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(manufacturerName_)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, manufacturerName_);
        }
        for (int i = 0; i < capabilities_.size(); i++) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(11, capabilities_.get(i));
        }
        if (additionalIdentification_ != null) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(12, getAdditionalIdentification());
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints)) {
          return super.equals(obj);
        }
        com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints other = (com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints) obj;

        if (!getCapabilitiesList()
            .equals(other.getCapabilitiesList())) return false;
        if (hasAdditionalIdentification() != other.hasAdditionalIdentification()) return false;
        if (hasAdditionalIdentification()) {
          if (!getAdditionalIdentification()
              .equals(other.getAdditionalIdentification())) return false;
        }
        if (!getEndpointId()
            .equals(other.getEndpointId())) return false;
        if (!getManufacturerName()
            .equals(other.getManufacturerName())) return false;
        if (!getDescription()
            .equals(other.getDescription())) return false;
        if (!getFriendlyName()
            .equals(other.getFriendlyName())) return false;
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        if (getCapabilitiesCount() > 0) {
          hash = (37 * hash) + CAPABILITIES_FIELD_NUMBER;
          hash = (53 * hash) + getCapabilitiesList().hashCode();
        }
        if (hasAdditionalIdentification()) {
          hash = (37 * hash) + ADDITIONALIDENTIFICATION_FIELD_NUMBER;
          hash = (53 * hash) + getAdditionalIdentification().hashCode();
        }
        hash = (37 * hash) + ENDPOINTID_FIELD_NUMBER;
        hash = (53 * hash) + getEndpointId().hashCode();
        hash = (37 * hash) + MANUFACTURERNAME_FIELD_NUMBER;
        hash = (53 * hash) + getManufacturerName().hashCode();
        hash = (37 * hash) + DESCRIPTION_FIELD_NUMBER;
        hash = (53 * hash) + getDescription().hashCode();
        hash = (37 * hash) + FRIENDLYNAME_FIELD_NUMBER;
        hash = (53 * hash) + getFriendlyName().hashCode();
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints)
          com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.EndpointsOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.class, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Builder.class);
        }

        // Construct using com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          if (capabilitiesBuilder_ == null) {
            capabilities_ = java.util.Collections.emptyList();
          } else {
            capabilities_ = null;
            capabilitiesBuilder_.clear();
          }
          bitField0_ = (bitField0_ & ~0x00000001);
          if (additionalIdentificationBuilder_ == null) {
            additionalIdentification_ = null;
          } else {
            additionalIdentification_ = null;
            additionalIdentificationBuilder_ = null;
          }
          endpointId_ = "";

          manufacturerName_ = "";

          description_ = "";

          friendlyName_ = "";

          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_descriptor;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints getDefaultInstanceForType() {
          return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.getDefaultInstance();
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints build() {
          com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints buildPartial() {
          com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints result = new com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints(this);
          int from_bitField0_ = bitField0_;
          if (capabilitiesBuilder_ == null) {
            if (((bitField0_ & 0x00000001) != 0)) {
              capabilities_ = java.util.Collections.unmodifiableList(capabilities_);
              bitField0_ = (bitField0_ & ~0x00000001);
            }
            result.capabilities_ = capabilities_;
          } else {
            result.capabilities_ = capabilitiesBuilder_.build();
          }
          if (additionalIdentificationBuilder_ == null) {
            result.additionalIdentification_ = additionalIdentification_;
          } else {
            result.additionalIdentification_ = additionalIdentificationBuilder_.build();
          }
          result.endpointId_ = endpointId_;
          result.manufacturerName_ = manufacturerName_;
          result.description_ = description_;
          result.friendlyName_ = friendlyName_;
          onBuilt();
          return result;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints) {
            return mergeFrom((com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints other) {
          if (other == com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.getDefaultInstance()) return this;
          if (capabilitiesBuilder_ == null) {
            if (!other.capabilities_.isEmpty()) {
              if (capabilities_.isEmpty()) {
                capabilities_ = other.capabilities_;
                bitField0_ = (bitField0_ & ~0x00000001);
              } else {
                ensureCapabilitiesIsMutable();
                capabilities_.addAll(other.capabilities_);
              }
              onChanged();
            }
          } else {
            if (!other.capabilities_.isEmpty()) {
              if (capabilitiesBuilder_.isEmpty()) {
                capabilitiesBuilder_.dispose();
                capabilitiesBuilder_ = null;
                capabilities_ = other.capabilities_;
                bitField0_ = (bitField0_ & ~0x00000001);
                capabilitiesBuilder_ = 
                  com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                     getCapabilitiesFieldBuilder() : null;
              } else {
                capabilitiesBuilder_.addAllMessages(other.capabilities_);
              }
            }
          }
          if (other.hasAdditionalIdentification()) {
            mergeAdditionalIdentification(other.getAdditionalIdentification());
          }
          if (!other.getEndpointId().isEmpty()) {
            endpointId_ = other.endpointId_;
            onChanged();
          }
          if (!other.getManufacturerName().isEmpty()) {
            manufacturerName_ = other.manufacturerName_;
            onChanged();
          }
          if (!other.getDescription().isEmpty()) {
            description_ = other.description_;
            onChanged();
          }
          if (!other.getFriendlyName().isEmpty()) {
            friendlyName_ = other.friendlyName_;
            onChanged();
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 10: {
                  endpointId_ = input.readStringRequireUtf8();

                  break;
                } // case 10
                case 18: {
                  friendlyName_ = input.readStringRequireUtf8();

                  break;
                } // case 18
                case 26: {
                  description_ = input.readStringRequireUtf8();

                  break;
                } // case 26
                case 34: {
                  manufacturerName_ = input.readStringRequireUtf8();

                  break;
                } // case 34
                case 90: {
                  com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities m =
                      input.readMessage(
                          com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.parser(),
                          extensionRegistry);
                  if (capabilitiesBuilder_ == null) {
                    ensureCapabilitiesIsMutable();
                    capabilities_.add(m);
                  } else {
                    capabilitiesBuilder_.addMessage(m);
                  }
                  break;
                } // case 90
                case 98: {
                  input.readMessage(
                      getAdditionalIdentificationFieldBuilder().getBuilder(),
                      extensionRegistry);

                  break;
                } // case 98
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private java.util.List<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities> capabilities_ =
          java.util.Collections.emptyList();
        private void ensureCapabilitiesIsMutable() {
          if (!((bitField0_ & 0x00000001) != 0)) {
            capabilities_ = new java.util.ArrayList<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities>(capabilities_);
            bitField0_ |= 0x00000001;
           }
        }

        private com.google.protobuf.RepeatedFieldBuilderV3<
            com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Builder, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.CapabilitiesOrBuilder> capabilitiesBuilder_;

        /**
         * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
         */
        public java.util.List<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities> getCapabilitiesList() {
          if (capabilitiesBuilder_ == null) {
            return java.util.Collections.unmodifiableList(capabilities_);
          } else {
            return capabilitiesBuilder_.getMessageList();
          }
        }
        /**
         * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
         */
        public int getCapabilitiesCount() {
          if (capabilitiesBuilder_ == null) {
            return capabilities_.size();
          } else {
            return capabilitiesBuilder_.getCount();
          }
        }
        /**
         * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
         */
        public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities getCapabilities(int index) {
          if (capabilitiesBuilder_ == null) {
            return capabilities_.get(index);
          } else {
            return capabilitiesBuilder_.getMessage(index);
          }
        }
        /**
         * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
         */
        public Builder setCapabilities(
            int index, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities value) {
          if (capabilitiesBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureCapabilitiesIsMutable();
            capabilities_.set(index, value);
            onChanged();
          } else {
            capabilitiesBuilder_.setMessage(index, value);
          }
          return this;
        }
        /**
         * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
         */
        public Builder setCapabilities(
            int index, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Builder builderForValue) {
          if (capabilitiesBuilder_ == null) {
            ensureCapabilitiesIsMutable();
            capabilities_.set(index, builderForValue.build());
            onChanged();
          } else {
            capabilitiesBuilder_.setMessage(index, builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
         */
        public Builder addCapabilities(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities value) {
          if (capabilitiesBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureCapabilitiesIsMutable();
            capabilities_.add(value);
            onChanged();
          } else {
            capabilitiesBuilder_.addMessage(value);
          }
          return this;
        }
        /**
         * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
         */
        public Builder addCapabilities(
            int index, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities value) {
          if (capabilitiesBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureCapabilitiesIsMutable();
            capabilities_.add(index, value);
            onChanged();
          } else {
            capabilitiesBuilder_.addMessage(index, value);
          }
          return this;
        }
        /**
         * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
         */
        public Builder addCapabilities(
            com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Builder builderForValue) {
          if (capabilitiesBuilder_ == null) {
            ensureCapabilitiesIsMutable();
            capabilities_.add(builderForValue.build());
            onChanged();
          } else {
            capabilitiesBuilder_.addMessage(builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
         */
        public Builder addCapabilities(
            int index, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Builder builderForValue) {
          if (capabilitiesBuilder_ == null) {
            ensureCapabilitiesIsMutable();
            capabilities_.add(index, builderForValue.build());
            onChanged();
          } else {
            capabilitiesBuilder_.addMessage(index, builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
         */
        public Builder addAllCapabilities(
            java.lang.Iterable<? extends com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities> values) {
          if (capabilitiesBuilder_ == null) {
            ensureCapabilitiesIsMutable();
            com.google.protobuf.AbstractMessageLite.Builder.addAll(
                values, capabilities_);
            onChanged();
          } else {
            capabilitiesBuilder_.addAllMessages(values);
          }
          return this;
        }
        /**
         * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
         */
        public Builder clearCapabilities() {
          if (capabilitiesBuilder_ == null) {
            capabilities_ = java.util.Collections.emptyList();
            bitField0_ = (bitField0_ & ~0x00000001);
            onChanged();
          } else {
            capabilitiesBuilder_.clear();
          }
          return this;
        }
        /**
         * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
         */
        public Builder removeCapabilities(int index) {
          if (capabilitiesBuilder_ == null) {
            ensureCapabilitiesIsMutable();
            capabilities_.remove(index);
            onChanged();
          } else {
            capabilitiesBuilder_.remove(index);
          }
          return this;
        }
        /**
         * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
         */
        public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Builder getCapabilitiesBuilder(
            int index) {
          return getCapabilitiesFieldBuilder().getBuilder(index);
        }
        /**
         * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
         */
        public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.CapabilitiesOrBuilder getCapabilitiesOrBuilder(
            int index) {
          if (capabilitiesBuilder_ == null) {
            return capabilities_.get(index);  } else {
            return capabilitiesBuilder_.getMessageOrBuilder(index);
          }
        }
        /**
         * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
         */
        public java.util.List<? extends com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.CapabilitiesOrBuilder> 
             getCapabilitiesOrBuilderList() {
          if (capabilitiesBuilder_ != null) {
            return capabilitiesBuilder_.getMessageOrBuilderList();
          } else {
            return java.util.Collections.unmodifiableList(capabilities_);
          }
        }
        /**
         * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
         */
        public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Builder addCapabilitiesBuilder() {
          return getCapabilitiesFieldBuilder().addBuilder(
              com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.getDefaultInstance());
        }
        /**
         * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
         */
        public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Builder addCapabilitiesBuilder(
            int index) {
          return getCapabilitiesFieldBuilder().addBuilder(
              index, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.getDefaultInstance());
        }
        /**
         * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.Capabilities capabilities = 11;</code>
         */
        public java.util.List<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Builder> 
             getCapabilitiesBuilderList() {
          return getCapabilitiesFieldBuilder().getBuilderList();
        }
        private com.google.protobuf.RepeatedFieldBuilderV3<
            com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Builder, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.CapabilitiesOrBuilder> 
            getCapabilitiesFieldBuilder() {
          if (capabilitiesBuilder_ == null) {
            capabilitiesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
                com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Capabilities.Builder, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.CapabilitiesOrBuilder>(
                    capabilities_,
                    ((bitField0_ & 0x00000001) != 0),
                    getParentForChildren(),
                    isClean());
            capabilities_ = null;
          }
          return capabilitiesBuilder_;
        }

        private com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification additionalIdentification_;
        private com.google.protobuf.SingleFieldBuilderV3<
            com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification.Builder, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentificationOrBuilder> additionalIdentificationBuilder_;
        /**
         * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification additionalIdentification = 12;</code>
         * @return Whether the additionalIdentification field is set.
         */
        public boolean hasAdditionalIdentification() {
          return additionalIdentificationBuilder_ != null || additionalIdentification_ != null;
        }
        /**
         * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification additionalIdentification = 12;</code>
         * @return The additionalIdentification.
         */
        public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification getAdditionalIdentification() {
          if (additionalIdentificationBuilder_ == null) {
            return additionalIdentification_ == null ? com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification.getDefaultInstance() : additionalIdentification_;
          } else {
            return additionalIdentificationBuilder_.getMessage();
          }
        }
        /**
         * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification additionalIdentification = 12;</code>
         */
        public Builder setAdditionalIdentification(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification value) {
          if (additionalIdentificationBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            additionalIdentification_ = value;
            onChanged();
          } else {
            additionalIdentificationBuilder_.setMessage(value);
          }

          return this;
        }
        /**
         * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification additionalIdentification = 12;</code>
         */
        public Builder setAdditionalIdentification(
            com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification.Builder builderForValue) {
          if (additionalIdentificationBuilder_ == null) {
            additionalIdentification_ = builderForValue.build();
            onChanged();
          } else {
            additionalIdentificationBuilder_.setMessage(builderForValue.build());
          }

          return this;
        }
        /**
         * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification additionalIdentification = 12;</code>
         */
        public Builder mergeAdditionalIdentification(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification value) {
          if (additionalIdentificationBuilder_ == null) {
            if (additionalIdentification_ != null) {
              additionalIdentification_ =
                com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification.newBuilder(additionalIdentification_).mergeFrom(value).buildPartial();
            } else {
              additionalIdentification_ = value;
            }
            onChanged();
          } else {
            additionalIdentificationBuilder_.mergeFrom(value);
          }

          return this;
        }
        /**
         * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification additionalIdentification = 12;</code>
         */
        public Builder clearAdditionalIdentification() {
          if (additionalIdentificationBuilder_ == null) {
            additionalIdentification_ = null;
            onChanged();
          } else {
            additionalIdentification_ = null;
            additionalIdentificationBuilder_ = null;
          }

          return this;
        }
        /**
         * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification additionalIdentification = 12;</code>
         */
        public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification.Builder getAdditionalIdentificationBuilder() {
          
          onChanged();
          return getAdditionalIdentificationFieldBuilder().getBuilder();
        }
        /**
         * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification additionalIdentification = 12;</code>
         */
        public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentificationOrBuilder getAdditionalIdentificationOrBuilder() {
          if (additionalIdentificationBuilder_ != null) {
            return additionalIdentificationBuilder_.getMessageOrBuilder();
          } else {
            return additionalIdentification_ == null ?
                com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification.getDefaultInstance() : additionalIdentification_;
          }
        }
        /**
         * <code>.alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification additionalIdentification = 12;</code>
         */
        private com.google.protobuf.SingleFieldBuilderV3<
            com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification.Builder, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentificationOrBuilder> 
            getAdditionalIdentificationFieldBuilder() {
          if (additionalIdentificationBuilder_ == null) {
            additionalIdentificationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
                com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentification.Builder, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.AdditionalIdentificationOrBuilder>(
                    getAdditionalIdentification(),
                    getParentForChildren(),
                    isClean());
            additionalIdentification_ = null;
          }
          return additionalIdentificationBuilder_;
        }

        private java.lang.Object endpointId_ = "";
        /**
         * <code>string endpointId = 1;</code>
         * @return The endpointId.
         */
        public java.lang.String getEndpointId() {
          java.lang.Object ref = endpointId_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            endpointId_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>string endpointId = 1;</code>
         * @return The bytes for endpointId.
         */
        public com.google.protobuf.ByteString
            getEndpointIdBytes() {
          java.lang.Object ref = endpointId_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            endpointId_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string endpointId = 1;</code>
         * @param value The endpointId to set.
         * @return This builder for chaining.
         */
        public Builder setEndpointId(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  
          endpointId_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>string endpointId = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearEndpointId() {
          
          endpointId_ = getDefaultInstance().getEndpointId();
          onChanged();
          return this;
        }
        /**
         * <code>string endpointId = 1;</code>
         * @param value The bytes for endpointId to set.
         * @return This builder for chaining.
         */
        public Builder setEndpointIdBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
          
          endpointId_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object manufacturerName_ = "";
        /**
         * <code>string manufacturerName = 4;</code>
         * @return The manufacturerName.
         */
        public java.lang.String getManufacturerName() {
          java.lang.Object ref = manufacturerName_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            manufacturerName_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>string manufacturerName = 4;</code>
         * @return The bytes for manufacturerName.
         */
        public com.google.protobuf.ByteString
            getManufacturerNameBytes() {
          java.lang.Object ref = manufacturerName_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            manufacturerName_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string manufacturerName = 4;</code>
         * @param value The manufacturerName to set.
         * @return This builder for chaining.
         */
        public Builder setManufacturerName(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  
          manufacturerName_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>string manufacturerName = 4;</code>
         * @return This builder for chaining.
         */
        public Builder clearManufacturerName() {
          
          manufacturerName_ = getDefaultInstance().getManufacturerName();
          onChanged();
          return this;
        }
        /**
         * <code>string manufacturerName = 4;</code>
         * @param value The bytes for manufacturerName to set.
         * @return This builder for chaining.
         */
        public Builder setManufacturerNameBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
          
          manufacturerName_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object description_ = "";
        /**
         * <code>string description = 3;</code>
         * @return The description.
         */
        public java.lang.String getDescription() {
          java.lang.Object ref = description_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            description_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>string description = 3;</code>
         * @return The bytes for description.
         */
        public com.google.protobuf.ByteString
            getDescriptionBytes() {
          java.lang.Object ref = description_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            description_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string description = 3;</code>
         * @param value The description to set.
         * @return This builder for chaining.
         */
        public Builder setDescription(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  
          description_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>string description = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearDescription() {
          
          description_ = getDefaultInstance().getDescription();
          onChanged();
          return this;
        }
        /**
         * <code>string description = 3;</code>
         * @param value The bytes for description to set.
         * @return This builder for chaining.
         */
        public Builder setDescriptionBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
          
          description_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object friendlyName_ = "";
        /**
         * <code>string friendlyName = 2;</code>
         * @return The friendlyName.
         */
        public java.lang.String getFriendlyName() {
          java.lang.Object ref = friendlyName_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            friendlyName_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>string friendlyName = 2;</code>
         * @return The bytes for friendlyName.
         */
        public com.google.protobuf.ByteString
            getFriendlyNameBytes() {
          java.lang.Object ref = friendlyName_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            friendlyName_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string friendlyName = 2;</code>
         * @param value The friendlyName to set.
         * @return This builder for chaining.
         */
        public Builder setFriendlyName(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  
          friendlyName_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>string friendlyName = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearFriendlyName() {
          
          friendlyName_ = getDefaultInstance().getFriendlyName();
          onChanged();
          return this;
        }
        /**
         * <code>string friendlyName = 2;</code>
         * @param value The bytes for friendlyName to set.
         * @return This builder for chaining.
         */
        public Builder setFriendlyNameBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
          
          friendlyName_ = value;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints)
      }

      // @@protoc_insertion_point(class_scope:alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints)
      private static final com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints();
      }

      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Endpoints>
          PARSER = new com.google.protobuf.AbstractParser<Endpoints>() {
        @java.lang.Override
        public Endpoints parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Endpoints> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Endpoints> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public static final int ENDPOINTS_FIELD_NUMBER = 1;
    private java.util.List<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints> endpoints_;
    /**
     * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints> getEndpointsList() {
      return endpoints_;
    }
    /**
     * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.EndpointsOrBuilder> 
        getEndpointsOrBuilderList() {
      return endpoints_;
    }
    /**
     * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
     */
    @java.lang.Override
    public int getEndpointsCount() {
      return endpoints_.size();
    }
    /**
     * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints getEndpoints(int index) {
      return endpoints_.get(index);
    }
    /**
     * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.EndpointsOrBuilder getEndpointsOrBuilder(
        int index) {
      return endpoints_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < endpoints_.size(); i++) {
        output.writeMessage(1, endpoints_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < endpoints_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, endpoints_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto)) {
        return super.equals(obj);
      }
      com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto other = (com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto) obj;

      if (!getEndpointsList()
          .equals(other.getEndpointsList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getEndpointsCount() > 0) {
        hash = (37 * hash) + ENDPOINTS_FIELD_NUMBER;
        hash = (53 * hash) + getEndpointsList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code alexaDiscovery.DiscoverResponseEventPayloadProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:alexaDiscovery.DiscoverResponseEventPayloadProto)
        com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.class, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Builder.class);
      }

      // Construct using com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (endpointsBuilder_ == null) {
          endpoints_ = java.util.Collections.emptyList();
        } else {
          endpoints_ = null;
          endpointsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_descriptor;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto getDefaultInstanceForType() {
        return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto build() {
        com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto buildPartial() {
        com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto result = new com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto(this);
        int from_bitField0_ = bitField0_;
        if (endpointsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            endpoints_ = java.util.Collections.unmodifiableList(endpoints_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.endpoints_ = endpoints_;
        } else {
          result.endpoints_ = endpointsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto) {
          return mergeFrom((com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto other) {
        if (other == com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.getDefaultInstance()) return this;
        if (endpointsBuilder_ == null) {
          if (!other.endpoints_.isEmpty()) {
            if (endpoints_.isEmpty()) {
              endpoints_ = other.endpoints_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureEndpointsIsMutable();
              endpoints_.addAll(other.endpoints_);
            }
            onChanged();
          }
        } else {
          if (!other.endpoints_.isEmpty()) {
            if (endpointsBuilder_.isEmpty()) {
              endpointsBuilder_.dispose();
              endpointsBuilder_ = null;
              endpoints_ = other.endpoints_;
              bitField0_ = (bitField0_ & ~0x00000001);
              endpointsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getEndpointsFieldBuilder() : null;
            } else {
              endpointsBuilder_.addAllMessages(other.endpoints_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints m =
                    input.readMessage(
                        com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.parser(),
                        extensionRegistry);
                if (endpointsBuilder_ == null) {
                  ensureEndpointsIsMutable();
                  endpoints_.add(m);
                } else {
                  endpointsBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints> endpoints_ =
        java.util.Collections.emptyList();
      private void ensureEndpointsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          endpoints_ = new java.util.ArrayList<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints>(endpoints_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Builder, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.EndpointsOrBuilder> endpointsBuilder_;

      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
       */
      public java.util.List<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints> getEndpointsList() {
        if (endpointsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(endpoints_);
        } else {
          return endpointsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
       */
      public int getEndpointsCount() {
        if (endpointsBuilder_ == null) {
          return endpoints_.size();
        } else {
          return endpointsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints getEndpoints(int index) {
        if (endpointsBuilder_ == null) {
          return endpoints_.get(index);
        } else {
          return endpointsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
       */
      public Builder setEndpoints(
          int index, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints value) {
        if (endpointsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEndpointsIsMutable();
          endpoints_.set(index, value);
          onChanged();
        } else {
          endpointsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
       */
      public Builder setEndpoints(
          int index, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Builder builderForValue) {
        if (endpointsBuilder_ == null) {
          ensureEndpointsIsMutable();
          endpoints_.set(index, builderForValue.build());
          onChanged();
        } else {
          endpointsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
       */
      public Builder addEndpoints(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints value) {
        if (endpointsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEndpointsIsMutable();
          endpoints_.add(value);
          onChanged();
        } else {
          endpointsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
       */
      public Builder addEndpoints(
          int index, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints value) {
        if (endpointsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEndpointsIsMutable();
          endpoints_.add(index, value);
          onChanged();
        } else {
          endpointsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
       */
      public Builder addEndpoints(
          com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Builder builderForValue) {
        if (endpointsBuilder_ == null) {
          ensureEndpointsIsMutable();
          endpoints_.add(builderForValue.build());
          onChanged();
        } else {
          endpointsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
       */
      public Builder addEndpoints(
          int index, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Builder builderForValue) {
        if (endpointsBuilder_ == null) {
          ensureEndpointsIsMutable();
          endpoints_.add(index, builderForValue.build());
          onChanged();
        } else {
          endpointsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
       */
      public Builder addAllEndpoints(
          java.lang.Iterable<? extends com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints> values) {
        if (endpointsBuilder_ == null) {
          ensureEndpointsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, endpoints_);
          onChanged();
        } else {
          endpointsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
       */
      public Builder clearEndpoints() {
        if (endpointsBuilder_ == null) {
          endpoints_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          endpointsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
       */
      public Builder removeEndpoints(int index) {
        if (endpointsBuilder_ == null) {
          ensureEndpointsIsMutable();
          endpoints_.remove(index);
          onChanged();
        } else {
          endpointsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Builder getEndpointsBuilder(
          int index) {
        return getEndpointsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.EndpointsOrBuilder getEndpointsOrBuilder(
          int index) {
        if (endpointsBuilder_ == null) {
          return endpoints_.get(index);  } else {
          return endpointsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
       */
      public java.util.List<? extends com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.EndpointsOrBuilder> 
           getEndpointsOrBuilderList() {
        if (endpointsBuilder_ != null) {
          return endpointsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(endpoints_);
        }
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Builder addEndpointsBuilder() {
        return getEndpointsFieldBuilder().addBuilder(
            com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.getDefaultInstance());
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Builder addEndpointsBuilder(
          int index) {
        return getEndpointsFieldBuilder().addBuilder(
            index, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.getDefaultInstance());
      }
      /**
       * <code>repeated .alexaDiscovery.DiscoverResponseEventPayloadProto.Endpoints endpoints = 1;</code>
       */
      public java.util.List<com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Builder> 
           getEndpointsBuilderList() {
        return getEndpointsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Builder, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.EndpointsOrBuilder> 
          getEndpointsFieldBuilder() {
        if (endpointsBuilder_ == null) {
          endpointsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.Endpoints.Builder, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto.EndpointsOrBuilder>(
                  endpoints_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          endpoints_ = null;
        }
        return endpointsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:alexaDiscovery.DiscoverResponseEventPayloadProto)
    }

    // @@protoc_insertion_point(class_scope:alexaDiscovery.DiscoverResponseEventPayloadProto)
    private static final com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto();
    }

    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DiscoverResponseEventPayloadProto>
        PARSER = new com.google.protobuf.AbstractParser<DiscoverResponseEventPayloadProto>() {
      @java.lang.Override
      public DiscoverResponseEventPayloadProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<DiscoverResponseEventPayloadProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DiscoverResponseEventPayloadProto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverResponseEventPayload.DiscoverResponseEventPayloadProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_SupportedTypes_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_SupportedTypes_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_AdditionalIdentification_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_AdditionalIdentification_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n0alexaDiscoveryDiscoverResponseEventPay" +
      "load.proto\022\016alexaDiscovery\"\306\007\n!DiscoverR" +
      "esponseEventPayloadProto\022N\n\tendpoints\030\001 " +
      "\003(\0132;.alexaDiscovery.DiscoverResponseEve" +
      "ntPayloadProto.Endpoints\032\320\006\n\tEndpoints\022^" +
      "\n\014capabilities\030\013 \003(\0132H.alexaDiscovery.Di" +
      "scoverResponseEventPayloadProto.Endpoint" +
      "s.Capabilities\022v\n\030additionalIdentificati" +
      "on\030\014 \001(\0132T.alexaDiscovery.DiscoverRespon" +
      "seEventPayloadProto.Endpoints.Additional" +
      "Identification\022\022\n\nendpointId\030\001 \001(\t\022\030\n\020ma" +
      "nufacturerName\030\004 \001(\t\022\023\n\013description\030\003 \001(" +
      "\t\022\024\n\014friendlyName\030\002 \001(\t\032\340\002\n\014Capabilities" +
      "\022m\n\rconfiguration\030\004 \001(\0132V.alexaDiscovery" +
      ".DiscoverResponseEventPayloadProto.Endpo" +
      "ints.Capabilities.Configuration\022\014\n\004type\030" +
      "\001 \001(\t\022\021\n\tinterface\030\002 \001(\t\022\017\n\007version\030\003 \001(" +
      "\t\032\256\001\n\rConfiguration\022}\n\016supportedTypes\030\001 " +
      "\003(\0132e.alexaDiscovery.DiscoverResponseEve" +
      "ntPayloadProto.Endpoints.Capabilities.Co" +
      "nfiguration.SupportedTypes\032\036\n\016SupportedT" +
      "ypes\022\014\n\004name\030\001 \001(\t\032\256\001\n\030AdditionalIdentif" +
      "ication\022\021\n\tmodelName\030\005 \001(\t\022!\n\031deviceToke" +
      "nEncryptionType\030\003 \001(\t\022\027\n\017firmwareVersion" +
      "\030\001 \001(\t\022\030\n\020amazonDeviceType\030\004 \001(\t\022\024\n\014radi" +
      "oAddress\030\006 \001(\t\022\023\n\013deviceToken\030\002 \001(\tBM\n-c" +
      "om.amazon.proto.avs.v20160207.alexaDisco" +
      "veryB\034DiscoverResponseEventPayloadb\006prot" +
      "o3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_descriptor,
        new java.lang.String[] { "Endpoints", });
    internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_descriptor =
      internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_descriptor.getNestedTypes().get(0);
    internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_descriptor,
        new java.lang.String[] { "Capabilities", "AdditionalIdentification", "EndpointId", "ManufacturerName", "Description", "FriendlyName", });
    internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_descriptor =
      internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_descriptor.getNestedTypes().get(0);
    internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_descriptor,
        new java.lang.String[] { "Configuration", "Type", "Interface", "Version", });
    internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_descriptor =
      internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_descriptor.getNestedTypes().get(0);
    internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_descriptor,
        new java.lang.String[] { "SupportedTypes", });
    internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_SupportedTypes_descriptor =
      internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_descriptor.getNestedTypes().get(0);
    internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_SupportedTypes_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_Capabilities_Configuration_SupportedTypes_descriptor,
        new java.lang.String[] { "Name", });
    internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_AdditionalIdentification_descriptor =
      internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_descriptor.getNestedTypes().get(1);
    internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_AdditionalIdentification_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_alexaDiscovery_DiscoverResponseEventPayloadProto_Endpoints_AdditionalIdentification_descriptor,
        new java.lang.String[] { "ModelName", "DeviceTokenEncryptionType", "FirmwareVersion", "AmazonDeviceType", "RadioAddress", "DeviceToken", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
