<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_customerdial" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_customerdial.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_customerdial_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="275" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/activity_customerdial_0" include="toolbar"><Expressions/><location startLine="8" startOffset="4" endLine="10" endOffset="34"/></Target><Target id="@+id/loginfo" tag="layout/activity_customerdial_0" include="logview"><Expressions/><location startLine="12" startOffset="4" endLine="15" endOffset="35"/></Target><Target id="@+id/current_device" view="TextView"><Expressions/><location startLine="28" startOffset="12" endLine="36" endOffset="41"/></Target><Target id="@+id/address_text" view="EditText"><Expressions/><location startLine="38" startOffset="12" endLine="42" endOffset="47"/></Target><Target id="@+id/choose_device" view="Button"><Expressions/><location startLine="43" startOffset="12" endLine="51" endOffset="50"/></Target><Target id="@+id/show_dial_photo" view="ImageView"><Expressions/><location startLine="53" startOffset="12" endLine="58" endOffset="43"/></Target><Target id="@+id/choose_dial" view="Button"><Expressions/><location startLine="60" startOffset="12" endLine="68" endOffset="50"/></Target><Target id="@+id/radio_group_file_type" view="RadioGroup"><Expressions/><location startLine="77" startOffset="16" endLine="159" endOffset="28"/></Target><Target id="@+id/radio_button_file_type_online" view="RadioButton"><Expressions/><location startLine="88" startOffset="20" endLine="91" endOffset="62"/></Target><Target id="@+id/radio_button_file_type_picture" view="RadioButton"><Expressions/><location startLine="99" startOffset="20" endLine="102" endOffset="62"/></Target><Target id="@+id/radio_button_file_type_font" view="RadioButton"><Expressions/><location startLine="110" startOffset="20" endLine="113" endOffset="62"/></Target><Target id="@+id/radio_button_file_type_tp" view="RadioButton"><Expressions/><location startLine="121" startOffset="20" endLine="124" endOffset="62"/></Target><Target id="@+id/radio_button_file_type_heart_rate" view="RadioButton"><Expressions/><location startLine="132" startOffset="20" endLine="135" endOffset="62"/></Target><Target id="@+id/radio_button_file_type_language" view="RadioButton"><Expressions/><location startLine="143" startOffset="20" endLine="146" endOffset="62"/></Target><Target id="@+id/radio_button_file_type_ota_boot" view="RadioButton"><Expressions/><location startLine="154" startOffset="20" endLine="157" endOffset="62"/></Target><Target id="@+id/switchButton_use_diff_upgrade" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="178" startOffset="16" endLine="184" endOffset="51"/></Target><Target id="@+id/show_old_file_path" view="TextView"><Expressions/><location startLine="189" startOffset="12" endLine="197" endOffset="43"/></Target><Target id="@+id/show_dial_dfu_path" view="TextView"><Expressions/><location startLine="199" startOffset="12" endLine="206" endOffset="41"/></Target><Target id="@+id/choose_old_file" view="Button"><Expressions/><location startLine="215" startOffset="16" endLine="225" endOffset="47"/></Target><Target id="@+id/choose_dfu_file" view="Button"><Expressions/><location startLine="227" startOffset="16" endLine="236" endOffset="54"/></Target><Target id="@+id/start_transfer" view="Button"><Expressions/><location startLine="240" startOffset="12" endLine="249" endOffset="50"/></Target><Target id="@+id/transfer_progress" view="ProgressBar"><Expressions/><location startLine="251" startOffset="12" endLine="259" endOffset="38"/></Target><Target id="@+id/transfer_percent" view="TextView"><Expressions/><location startLine="261" startOffset="12" endLine="269" endOffset="41"/></Target></Targets></Layout>