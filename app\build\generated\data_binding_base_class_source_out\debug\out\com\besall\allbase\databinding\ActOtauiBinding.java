// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActOtauiBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView actOtauiBg;

  @NonNull
  public final ToolbarBinding tool;

  @NonNull
  public final ViewBaseoptionBinding viewBaseoption;

  @NonNull
  public final ViewButtonBinding viewButton;

  @NonNull
  public final ViewDeviceBinding viewDevice;

  @NonNull
  public final ViewOtaingBinding viewOtaing;

  @NonNull
  public final ViewOtalogBinding viewOtalog;

  @NonNull
  public final ViewVersionpathBinding viewVersionpath;

  private ActOtauiBinding(@NonNull ConstraintLayout rootView, @NonNull ImageView actOtauiBg,
      @NonNull ToolbarBinding tool, @NonNull ViewBaseoptionBinding viewBaseoption,
      @NonNull ViewButtonBinding viewButton, @NonNull ViewDeviceBinding viewDevice,
      @NonNull ViewOtaingBinding viewOtaing, @NonNull ViewOtalogBinding viewOtalog,
      @NonNull ViewVersionpathBinding viewVersionpath) {
    this.rootView = rootView;
    this.actOtauiBg = actOtauiBg;
    this.tool = tool;
    this.viewBaseoption = viewBaseoption;
    this.viewButton = viewButton;
    this.viewDevice = viewDevice;
    this.viewOtaing = viewOtaing;
    this.viewOtalog = viewOtalog;
    this.viewVersionpath = viewVersionpath;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActOtauiBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActOtauiBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.act_otaui, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActOtauiBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.act_otaui_bg;
      ImageView actOtauiBg = rootView.findViewById(id);
      if (actOtauiBg == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      id = R.id.view_baseoption;
      View viewBaseoption = rootView.findViewById(id);
      if (viewBaseoption == null) {
        break missingId;
      }
      ViewBaseoptionBinding binding_viewBaseoption = ViewBaseoptionBinding.bind(viewBaseoption);

      id = R.id.view_button;
      View viewButton = rootView.findViewById(id);
      if (viewButton == null) {
        break missingId;
      }
      ViewButtonBinding binding_viewButton = ViewButtonBinding.bind(viewButton);

      id = R.id.view_device;
      View viewDevice = rootView.findViewById(id);
      if (viewDevice == null) {
        break missingId;
      }
      ViewDeviceBinding binding_viewDevice = ViewDeviceBinding.bind(viewDevice);

      id = R.id.view_otaing;
      View viewOtaing = rootView.findViewById(id);
      if (viewOtaing == null) {
        break missingId;
      }
      ViewOtaingBinding binding_viewOtaing = ViewOtaingBinding.bind(viewOtaing);

      id = R.id.view_otalog;
      View viewOtalog = rootView.findViewById(id);
      if (viewOtalog == null) {
        break missingId;
      }
      ViewOtalogBinding binding_viewOtalog = ViewOtalogBinding.bind(viewOtalog);

      id = R.id.view_versionpath;
      View viewVersionpath = rootView.findViewById(id);
      if (viewVersionpath == null) {
        break missingId;
      }
      ViewVersionpathBinding binding_viewVersionpath = ViewVersionpathBinding.bind(viewVersionpath);

      return new ActOtauiBinding((ConstraintLayout) rootView, actOtauiBg, binding_tool,
          binding_viewBaseoption, binding_viewButton, binding_viewDevice, binding_viewOtaing,
          binding_viewOtalog, binding_viewVersionpath);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
