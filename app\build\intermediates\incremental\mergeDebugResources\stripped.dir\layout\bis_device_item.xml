<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="80dp"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="5dp"
    android:background="@color/white"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="126dp"
        >

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:textColor="@color/title_color_light"
            android:textSize="16sp"

            />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/status"
                android:layout_width="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_height="16dp"
                android:textColor="@color/title_color_light"
                android:textSize="13sp"
                />
            <TextView
                android:id="@+id/join_mark_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:textColor="@color/title_color_light"
                android:text="joining"/>
            <Button
                android:id="@+id/join_mark_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="13sp"
                android:text="@string/refresh"
                />

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="168dp"
        android:layout_height="match_parent"
        android:layout_marginLeft="-158dp"
        android:gravity="center_vertical|right"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/bis_icon_exit"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginRight="10dp"
            android:background="@drawable/tuichu"
            android:visibility="visible"/>

        <ImageView
            android:id="@+id/bis_icon_need_code"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginRight="10dp"
            android:background="@drawable/suoding"
            android:visibility="visible"/>

        <ImageView
            android:id="@+id/bis_icon_sound"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginRight="10dp"
            android:background="@drawable/sound_open"
            android:visibility="visible"/>

        <ImageView
            android:id="@+id/bis_btn_info"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="@drawable/info"
            android:visibility="visible"
            android:layout_marginRight="10dp"
            />
    </LinearLayout>

</LinearLayout>