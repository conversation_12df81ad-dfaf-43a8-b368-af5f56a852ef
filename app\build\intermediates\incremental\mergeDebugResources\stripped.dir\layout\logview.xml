<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#00000000"
    android:id="@+id/loginfo"
    android:visibility="gone">

    <ScrollView
        android:id="@+id/scr_policy"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="20dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:scrollbarAlwaysDrawVerticalTrack="true"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/logV"
                android:layout_width="match_parent"
                android:layout_height="550dp"
                android:scrollbarStyle="insideOverlay"
                android:scrollbars="vertical"
                android:lineSpacingExtra="2dp"
                android:background="@color/white"/>
            <Button
                android:id="@+id/done"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="10dp"
                android:background="@drawable/rectangle_button"
                android:text="OK"/>

        </LinearLayout>


    </ScrollView>


</LinearLayout>