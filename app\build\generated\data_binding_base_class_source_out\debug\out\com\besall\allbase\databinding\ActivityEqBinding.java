// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityEqBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnBandTotal1;

  @NonNull
  public final Button btnBandTotal10;

  @NonNull
  public final Button btnBandTotal2;

  @NonNull
  public final Button btnBandTotal3;

  @NonNull
  public final Button btnBandTotal4;

  @NonNull
  public final Button btnBandTotal5;

  @NonNull
  public final Button btnBandTotal6;

  @NonNull
  public final Button btnBandTotal7;

  @NonNull
  public final Button btnBandTotal8;

  @NonNull
  public final Button btnBandTotal9;

  @NonNull
  public final EditText editTextFreq0;

  @NonNull
  public final EditText editTextFreq1;

  @NonNull
  public final EditText editTextFreq2;

  @NonNull
  public final EditText editTextFreq3;

  @NonNull
  public final EditText editTextFreq4;

  @NonNull
  public final EditText editTextFreq5;

  @NonNull
  public final EditText editTextFreq6;

  @NonNull
  public final EditText editTextFreq7;

  @NonNull
  public final EditText editTextFreq8;

  @NonNull
  public final EditText editTextFreq9;

  @NonNull
  public final EditText editTextGain0;

  @NonNull
  public final EditText editTextGain1;

  @NonNull
  public final EditText editTextGain2;

  @NonNull
  public final EditText editTextGain3;

  @NonNull
  public final EditText editTextGain4;

  @NonNull
  public final EditText editTextGain5;

  @NonNull
  public final EditText editTextGain6;

  @NonNull
  public final EditText editTextGain7;

  @NonNull
  public final EditText editTextGain8;

  @NonNull
  public final EditText editTextGain9;

  @NonNull
  public final EditText editTextQ0;

  @NonNull
  public final EditText editTextQ1;

  @NonNull
  public final EditText editTextQ2;

  @NonNull
  public final EditText editTextQ3;

  @NonNull
  public final EditText editTextQ4;

  @NonNull
  public final EditText editTextQ5;

  @NonNull
  public final EditText editTextQ6;

  @NonNull
  public final EditText editTextQ7;

  @NonNull
  public final EditText editTextQ8;

  @NonNull
  public final EditText editTextQ9;

  @NonNull
  public final Spinner eqAllIndex;

  @NonNull
  public final SeekBar seekBar0;

  @NonNull
  public final SeekBar seekBar1;

  @NonNull
  public final SeekBar seekBar2;

  @NonNull
  public final SeekBar seekBar3;

  @NonNull
  public final SeekBar seekBar4;

  @NonNull
  public final SeekBar seekBar5;

  @NonNull
  public final SeekBar seekBar6;

  @NonNull
  public final SeekBar seekBar7;

  @NonNull
  public final SeekBar seekBar8;

  @NonNull
  public final SeekBar seekBar9;

  @NonNull
  public final ToolbarBinding tool;

  private ActivityEqBinding(@NonNull LinearLayout rootView, @NonNull Button btnBandTotal1,
      @NonNull Button btnBandTotal10, @NonNull Button btnBandTotal2, @NonNull Button btnBandTotal3,
      @NonNull Button btnBandTotal4, @NonNull Button btnBandTotal5, @NonNull Button btnBandTotal6,
      @NonNull Button btnBandTotal7, @NonNull Button btnBandTotal8, @NonNull Button btnBandTotal9,
      @NonNull EditText editTextFreq0, @NonNull EditText editTextFreq1,
      @NonNull EditText editTextFreq2, @NonNull EditText editTextFreq3,
      @NonNull EditText editTextFreq4, @NonNull EditText editTextFreq5,
      @NonNull EditText editTextFreq6, @NonNull EditText editTextFreq7,
      @NonNull EditText editTextFreq8, @NonNull EditText editTextFreq9,
      @NonNull EditText editTextGain0, @NonNull EditText editTextGain1,
      @NonNull EditText editTextGain2, @NonNull EditText editTextGain3,
      @NonNull EditText editTextGain4, @NonNull EditText editTextGain5,
      @NonNull EditText editTextGain6, @NonNull EditText editTextGain7,
      @NonNull EditText editTextGain8, @NonNull EditText editTextGain9,
      @NonNull EditText editTextQ0, @NonNull EditText editTextQ1, @NonNull EditText editTextQ2,
      @NonNull EditText editTextQ3, @NonNull EditText editTextQ4, @NonNull EditText editTextQ5,
      @NonNull EditText editTextQ6, @NonNull EditText editTextQ7, @NonNull EditText editTextQ8,
      @NonNull EditText editTextQ9, @NonNull Spinner eqAllIndex, @NonNull SeekBar seekBar0,
      @NonNull SeekBar seekBar1, @NonNull SeekBar seekBar2, @NonNull SeekBar seekBar3,
      @NonNull SeekBar seekBar4, @NonNull SeekBar seekBar5, @NonNull SeekBar seekBar6,
      @NonNull SeekBar seekBar7, @NonNull SeekBar seekBar8, @NonNull SeekBar seekBar9,
      @NonNull ToolbarBinding tool) {
    this.rootView = rootView;
    this.btnBandTotal1 = btnBandTotal1;
    this.btnBandTotal10 = btnBandTotal10;
    this.btnBandTotal2 = btnBandTotal2;
    this.btnBandTotal3 = btnBandTotal3;
    this.btnBandTotal4 = btnBandTotal4;
    this.btnBandTotal5 = btnBandTotal5;
    this.btnBandTotal6 = btnBandTotal6;
    this.btnBandTotal7 = btnBandTotal7;
    this.btnBandTotal8 = btnBandTotal8;
    this.btnBandTotal9 = btnBandTotal9;
    this.editTextFreq0 = editTextFreq0;
    this.editTextFreq1 = editTextFreq1;
    this.editTextFreq2 = editTextFreq2;
    this.editTextFreq3 = editTextFreq3;
    this.editTextFreq4 = editTextFreq4;
    this.editTextFreq5 = editTextFreq5;
    this.editTextFreq6 = editTextFreq6;
    this.editTextFreq7 = editTextFreq7;
    this.editTextFreq8 = editTextFreq8;
    this.editTextFreq9 = editTextFreq9;
    this.editTextGain0 = editTextGain0;
    this.editTextGain1 = editTextGain1;
    this.editTextGain2 = editTextGain2;
    this.editTextGain3 = editTextGain3;
    this.editTextGain4 = editTextGain4;
    this.editTextGain5 = editTextGain5;
    this.editTextGain6 = editTextGain6;
    this.editTextGain7 = editTextGain7;
    this.editTextGain8 = editTextGain8;
    this.editTextGain9 = editTextGain9;
    this.editTextQ0 = editTextQ0;
    this.editTextQ1 = editTextQ1;
    this.editTextQ2 = editTextQ2;
    this.editTextQ3 = editTextQ3;
    this.editTextQ4 = editTextQ4;
    this.editTextQ5 = editTextQ5;
    this.editTextQ6 = editTextQ6;
    this.editTextQ7 = editTextQ7;
    this.editTextQ8 = editTextQ8;
    this.editTextQ9 = editTextQ9;
    this.eqAllIndex = eqAllIndex;
    this.seekBar0 = seekBar0;
    this.seekBar1 = seekBar1;
    this.seekBar2 = seekBar2;
    this.seekBar3 = seekBar3;
    this.seekBar4 = seekBar4;
    this.seekBar5 = seekBar5;
    this.seekBar6 = seekBar6;
    this.seekBar7 = seekBar7;
    this.seekBar8 = seekBar8;
    this.seekBar9 = seekBar9;
    this.tool = tool;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityEqBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityEqBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_eq, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityEqBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnBandTotal1;
      Button btnBandTotal1 = rootView.findViewById(id);
      if (btnBandTotal1 == null) {
        break missingId;
      }

      id = R.id.btnBandTotal10;
      Button btnBandTotal10 = rootView.findViewById(id);
      if (btnBandTotal10 == null) {
        break missingId;
      }

      id = R.id.btnBandTotal2;
      Button btnBandTotal2 = rootView.findViewById(id);
      if (btnBandTotal2 == null) {
        break missingId;
      }

      id = R.id.btnBandTotal3;
      Button btnBandTotal3 = rootView.findViewById(id);
      if (btnBandTotal3 == null) {
        break missingId;
      }

      id = R.id.btnBandTotal4;
      Button btnBandTotal4 = rootView.findViewById(id);
      if (btnBandTotal4 == null) {
        break missingId;
      }

      id = R.id.btnBandTotal5;
      Button btnBandTotal5 = rootView.findViewById(id);
      if (btnBandTotal5 == null) {
        break missingId;
      }

      id = R.id.btnBandTotal6;
      Button btnBandTotal6 = rootView.findViewById(id);
      if (btnBandTotal6 == null) {
        break missingId;
      }

      id = R.id.btnBandTotal7;
      Button btnBandTotal7 = rootView.findViewById(id);
      if (btnBandTotal7 == null) {
        break missingId;
      }

      id = R.id.btnBandTotal8;
      Button btnBandTotal8 = rootView.findViewById(id);
      if (btnBandTotal8 == null) {
        break missingId;
      }

      id = R.id.btnBandTotal9;
      Button btnBandTotal9 = rootView.findViewById(id);
      if (btnBandTotal9 == null) {
        break missingId;
      }

      id = R.id.editTextFreq0;
      EditText editTextFreq0 = rootView.findViewById(id);
      if (editTextFreq0 == null) {
        break missingId;
      }

      id = R.id.editTextFreq1;
      EditText editTextFreq1 = rootView.findViewById(id);
      if (editTextFreq1 == null) {
        break missingId;
      }

      id = R.id.editTextFreq2;
      EditText editTextFreq2 = rootView.findViewById(id);
      if (editTextFreq2 == null) {
        break missingId;
      }

      id = R.id.editTextFreq3;
      EditText editTextFreq3 = rootView.findViewById(id);
      if (editTextFreq3 == null) {
        break missingId;
      }

      id = R.id.editTextFreq4;
      EditText editTextFreq4 = rootView.findViewById(id);
      if (editTextFreq4 == null) {
        break missingId;
      }

      id = R.id.editTextFreq5;
      EditText editTextFreq5 = rootView.findViewById(id);
      if (editTextFreq5 == null) {
        break missingId;
      }

      id = R.id.editTextFreq6;
      EditText editTextFreq6 = rootView.findViewById(id);
      if (editTextFreq6 == null) {
        break missingId;
      }

      id = R.id.editTextFreq7;
      EditText editTextFreq7 = rootView.findViewById(id);
      if (editTextFreq7 == null) {
        break missingId;
      }

      id = R.id.editTextFreq8;
      EditText editTextFreq8 = rootView.findViewById(id);
      if (editTextFreq8 == null) {
        break missingId;
      }

      id = R.id.editTextFreq9;
      EditText editTextFreq9 = rootView.findViewById(id);
      if (editTextFreq9 == null) {
        break missingId;
      }

      id = R.id.editTextGain0;
      EditText editTextGain0 = rootView.findViewById(id);
      if (editTextGain0 == null) {
        break missingId;
      }

      id = R.id.editTextGain1;
      EditText editTextGain1 = rootView.findViewById(id);
      if (editTextGain1 == null) {
        break missingId;
      }

      id = R.id.editTextGain2;
      EditText editTextGain2 = rootView.findViewById(id);
      if (editTextGain2 == null) {
        break missingId;
      }

      id = R.id.editTextGain3;
      EditText editTextGain3 = rootView.findViewById(id);
      if (editTextGain3 == null) {
        break missingId;
      }

      id = R.id.editTextGain4;
      EditText editTextGain4 = rootView.findViewById(id);
      if (editTextGain4 == null) {
        break missingId;
      }

      id = R.id.editTextGain5;
      EditText editTextGain5 = rootView.findViewById(id);
      if (editTextGain5 == null) {
        break missingId;
      }

      id = R.id.editTextGain6;
      EditText editTextGain6 = rootView.findViewById(id);
      if (editTextGain6 == null) {
        break missingId;
      }

      id = R.id.editTextGain7;
      EditText editTextGain7 = rootView.findViewById(id);
      if (editTextGain7 == null) {
        break missingId;
      }

      id = R.id.editTextGain8;
      EditText editTextGain8 = rootView.findViewById(id);
      if (editTextGain8 == null) {
        break missingId;
      }

      id = R.id.editTextGain9;
      EditText editTextGain9 = rootView.findViewById(id);
      if (editTextGain9 == null) {
        break missingId;
      }

      id = R.id.editTextQ0;
      EditText editTextQ0 = rootView.findViewById(id);
      if (editTextQ0 == null) {
        break missingId;
      }

      id = R.id.editTextQ1;
      EditText editTextQ1 = rootView.findViewById(id);
      if (editTextQ1 == null) {
        break missingId;
      }

      id = R.id.editTextQ2;
      EditText editTextQ2 = rootView.findViewById(id);
      if (editTextQ2 == null) {
        break missingId;
      }

      id = R.id.editTextQ3;
      EditText editTextQ3 = rootView.findViewById(id);
      if (editTextQ3 == null) {
        break missingId;
      }

      id = R.id.editTextQ4;
      EditText editTextQ4 = rootView.findViewById(id);
      if (editTextQ4 == null) {
        break missingId;
      }

      id = R.id.editTextQ5;
      EditText editTextQ5 = rootView.findViewById(id);
      if (editTextQ5 == null) {
        break missingId;
      }

      id = R.id.editTextQ6;
      EditText editTextQ6 = rootView.findViewById(id);
      if (editTextQ6 == null) {
        break missingId;
      }

      id = R.id.editTextQ7;
      EditText editTextQ7 = rootView.findViewById(id);
      if (editTextQ7 == null) {
        break missingId;
      }

      id = R.id.editTextQ8;
      EditText editTextQ8 = rootView.findViewById(id);
      if (editTextQ8 == null) {
        break missingId;
      }

      id = R.id.editTextQ9;
      EditText editTextQ9 = rootView.findViewById(id);
      if (editTextQ9 == null) {
        break missingId;
      }

      id = R.id.eq_all_index;
      Spinner eqAllIndex = rootView.findViewById(id);
      if (eqAllIndex == null) {
        break missingId;
      }

      id = R.id.seekBar0;
      SeekBar seekBar0 = rootView.findViewById(id);
      if (seekBar0 == null) {
        break missingId;
      }

      id = R.id.seekBar1;
      SeekBar seekBar1 = rootView.findViewById(id);
      if (seekBar1 == null) {
        break missingId;
      }

      id = R.id.seekBar2;
      SeekBar seekBar2 = rootView.findViewById(id);
      if (seekBar2 == null) {
        break missingId;
      }

      id = R.id.seekBar3;
      SeekBar seekBar3 = rootView.findViewById(id);
      if (seekBar3 == null) {
        break missingId;
      }

      id = R.id.seekBar4;
      SeekBar seekBar4 = rootView.findViewById(id);
      if (seekBar4 == null) {
        break missingId;
      }

      id = R.id.seekBar5;
      SeekBar seekBar5 = rootView.findViewById(id);
      if (seekBar5 == null) {
        break missingId;
      }

      id = R.id.seekBar6;
      SeekBar seekBar6 = rootView.findViewById(id);
      if (seekBar6 == null) {
        break missingId;
      }

      id = R.id.seekBar7;
      SeekBar seekBar7 = rootView.findViewById(id);
      if (seekBar7 == null) {
        break missingId;
      }

      id = R.id.seekBar8;
      SeekBar seekBar8 = rootView.findViewById(id);
      if (seekBar8 == null) {
        break missingId;
      }

      id = R.id.seekBar9;
      SeekBar seekBar9 = rootView.findViewById(id);
      if (seekBar9 == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      return new ActivityEqBinding((LinearLayout) rootView, btnBandTotal1, btnBandTotal10,
          btnBandTotal2, btnBandTotal3, btnBandTotal4, btnBandTotal5, btnBandTotal6, btnBandTotal7,
          btnBandTotal8, btnBandTotal9, editTextFreq0, editTextFreq1, editTextFreq2, editTextFreq3,
          editTextFreq4, editTextFreq5, editTextFreq6, editTextFreq7, editTextFreq8, editTextFreq9,
          editTextGain0, editTextGain1, editTextGain2, editTextGain3, editTextGain4, editTextGain5,
          editTextGain6, editTextGain7, editTextGain8, editTextGain9, editTextQ0, editTextQ1,
          editTextQ2, editTextQ3, editTextQ4, editTextQ5, editTextQ6, editTextQ7, editTextQ8,
          editTextQ9, eqAllIndex, seekBar0, seekBar1, seekBar2, seekBar3, seekBar4, seekBar5,
          seekBar6, seekBar7, seekBar8, seekBar9, binding_tool);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
