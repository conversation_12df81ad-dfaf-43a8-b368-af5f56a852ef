// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import com.github.mikephil.charting.charts.LineChart;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityRssiBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button connectSpp;

  @NonNull
  public final EditText interval;

  @NonNull
  public final TextView leftCallAgc;

  @NonNull
  public final TextView leftCallAgcMirror;

  @NonNull
  public final TextView leftCallMaxRssi;

  @NonNull
  public final TextView leftCallMaxRssiMirror;

  @NonNull
  public final TextView leftCallMinRssi;

  @NonNull
  public final TextView leftCallMinRssiMirror;

  @NonNull
  public final TextView leftCallRssi;

  @NonNull
  public final TextView leftCallRssiMirror;

  @NonNull
  public final TextView leftFAIDX;

  @NonNull
  public final TextView leftPacketLossRate;

  @NonNull
  public final TextView leftTwsAgc;

  @NonNull
  public final TextView leftTwsMaxRssi;

  @NonNull
  public final TextView leftTwsMinRssi;

  @NonNull
  public final TextView leftTwsRssi;

  @NonNull
  public final LogviewBinding loginfo;

  @NonNull
  public final TextView rightCallAgc;

  @NonNull
  public final TextView rightCallAgcMirror;

  @NonNull
  public final TextView rightCallMaxRssi;

  @NonNull
  public final TextView rightCallMaxRssiMirror;

  @NonNull
  public final TextView rightCallMinRssi;

  @NonNull
  public final TextView rightCallMinRssiMirror;

  @NonNull
  public final TextView rightCallRssi;

  @NonNull
  public final TextView rightCallRssiMirror;

  @NonNull
  public final TextView rightFAIDX;

  @NonNull
  public final TextView rightPacketLossRate;

  @NonNull
  public final TextView rightTwsAgc;

  @NonNull
  public final TextView rightTwsMaxRssi;

  @NonNull
  public final TextView rightTwsMinRssi;

  @NonNull
  public final TextView rightTwsRssi;

  @NonNull
  public final LineChart rssiChart;

  @NonNull
  public final Button rssiReadStart;

  @NonNull
  public final Button rssiReadStop;

  @NonNull
  public final Button sppStop;

  @NonNull
  public final ToolbarBinding tool;

  private ActivityRssiBinding(@NonNull LinearLayout rootView, @NonNull Button connectSpp,
      @NonNull EditText interval, @NonNull TextView leftCallAgc,
      @NonNull TextView leftCallAgcMirror, @NonNull TextView leftCallMaxRssi,
      @NonNull TextView leftCallMaxRssiMirror, @NonNull TextView leftCallMinRssi,
      @NonNull TextView leftCallMinRssiMirror, @NonNull TextView leftCallRssi,
      @NonNull TextView leftCallRssiMirror, @NonNull TextView leftFAIDX,
      @NonNull TextView leftPacketLossRate, @NonNull TextView leftTwsAgc,
      @NonNull TextView leftTwsMaxRssi, @NonNull TextView leftTwsMinRssi,
      @NonNull TextView leftTwsRssi, @NonNull LogviewBinding loginfo,
      @NonNull TextView rightCallAgc, @NonNull TextView rightCallAgcMirror,
      @NonNull TextView rightCallMaxRssi, @NonNull TextView rightCallMaxRssiMirror,
      @NonNull TextView rightCallMinRssi, @NonNull TextView rightCallMinRssiMirror,
      @NonNull TextView rightCallRssi, @NonNull TextView rightCallRssiMirror,
      @NonNull TextView rightFAIDX, @NonNull TextView rightPacketLossRate,
      @NonNull TextView rightTwsAgc, @NonNull TextView rightTwsMaxRssi,
      @NonNull TextView rightTwsMinRssi, @NonNull TextView rightTwsRssi,
      @NonNull LineChart rssiChart, @NonNull Button rssiReadStart, @NonNull Button rssiReadStop,
      @NonNull Button sppStop, @NonNull ToolbarBinding tool) {
    this.rootView = rootView;
    this.connectSpp = connectSpp;
    this.interval = interval;
    this.leftCallAgc = leftCallAgc;
    this.leftCallAgcMirror = leftCallAgcMirror;
    this.leftCallMaxRssi = leftCallMaxRssi;
    this.leftCallMaxRssiMirror = leftCallMaxRssiMirror;
    this.leftCallMinRssi = leftCallMinRssi;
    this.leftCallMinRssiMirror = leftCallMinRssiMirror;
    this.leftCallRssi = leftCallRssi;
    this.leftCallRssiMirror = leftCallRssiMirror;
    this.leftFAIDX = leftFAIDX;
    this.leftPacketLossRate = leftPacketLossRate;
    this.leftTwsAgc = leftTwsAgc;
    this.leftTwsMaxRssi = leftTwsMaxRssi;
    this.leftTwsMinRssi = leftTwsMinRssi;
    this.leftTwsRssi = leftTwsRssi;
    this.loginfo = loginfo;
    this.rightCallAgc = rightCallAgc;
    this.rightCallAgcMirror = rightCallAgcMirror;
    this.rightCallMaxRssi = rightCallMaxRssi;
    this.rightCallMaxRssiMirror = rightCallMaxRssiMirror;
    this.rightCallMinRssi = rightCallMinRssi;
    this.rightCallMinRssiMirror = rightCallMinRssiMirror;
    this.rightCallRssi = rightCallRssi;
    this.rightCallRssiMirror = rightCallRssiMirror;
    this.rightFAIDX = rightFAIDX;
    this.rightPacketLossRate = rightPacketLossRate;
    this.rightTwsAgc = rightTwsAgc;
    this.rightTwsMaxRssi = rightTwsMaxRssi;
    this.rightTwsMinRssi = rightTwsMinRssi;
    this.rightTwsRssi = rightTwsRssi;
    this.rssiChart = rssiChart;
    this.rssiReadStart = rssiReadStart;
    this.rssiReadStop = rssiReadStop;
    this.sppStop = sppStop;
    this.tool = tool;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityRssiBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityRssiBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_rssi, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityRssiBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.connect_spp;
      Button connectSpp = rootView.findViewById(id);
      if (connectSpp == null) {
        break missingId;
      }

      id = R.id.interval;
      EditText interval = rootView.findViewById(id);
      if (interval == null) {
        break missingId;
      }

      id = R.id.left_call_agc;
      TextView leftCallAgc = rootView.findViewById(id);
      if (leftCallAgc == null) {
        break missingId;
      }

      id = R.id.left_call_agc_mirror;
      TextView leftCallAgcMirror = rootView.findViewById(id);
      if (leftCallAgcMirror == null) {
        break missingId;
      }

      id = R.id.left_call_max_rssi;
      TextView leftCallMaxRssi = rootView.findViewById(id);
      if (leftCallMaxRssi == null) {
        break missingId;
      }

      id = R.id.left_call_max_rssi_mirror;
      TextView leftCallMaxRssiMirror = rootView.findViewById(id);
      if (leftCallMaxRssiMirror == null) {
        break missingId;
      }

      id = R.id.left_call_min_rssi;
      TextView leftCallMinRssi = rootView.findViewById(id);
      if (leftCallMinRssi == null) {
        break missingId;
      }

      id = R.id.left_call_min_rssi_mirror;
      TextView leftCallMinRssiMirror = rootView.findViewById(id);
      if (leftCallMinRssiMirror == null) {
        break missingId;
      }

      id = R.id.left_call_rssi;
      TextView leftCallRssi = rootView.findViewById(id);
      if (leftCallRssi == null) {
        break missingId;
      }

      id = R.id.left_call_rssi_mirror;
      TextView leftCallRssiMirror = rootView.findViewById(id);
      if (leftCallRssiMirror == null) {
        break missingId;
      }

      id = R.id.left_FA_IDX;
      TextView leftFAIDX = rootView.findViewById(id);
      if (leftFAIDX == null) {
        break missingId;
      }

      id = R.id.left_packet_loss_rate;
      TextView leftPacketLossRate = rootView.findViewById(id);
      if (leftPacketLossRate == null) {
        break missingId;
      }

      id = R.id.left_tws_agc;
      TextView leftTwsAgc = rootView.findViewById(id);
      if (leftTwsAgc == null) {
        break missingId;
      }

      id = R.id.left_tws_max_rssi;
      TextView leftTwsMaxRssi = rootView.findViewById(id);
      if (leftTwsMaxRssi == null) {
        break missingId;
      }

      id = R.id.left_tws_min_rssi;
      TextView leftTwsMinRssi = rootView.findViewById(id);
      if (leftTwsMinRssi == null) {
        break missingId;
      }

      id = R.id.left_tws_rssi;
      TextView leftTwsRssi = rootView.findViewById(id);
      if (leftTwsRssi == null) {
        break missingId;
      }

      id = R.id.loginfo;
      View loginfo = rootView.findViewById(id);
      if (loginfo == null) {
        break missingId;
      }
      LogviewBinding binding_loginfo = LogviewBinding.bind(loginfo);

      id = R.id.right_call_agc;
      TextView rightCallAgc = rootView.findViewById(id);
      if (rightCallAgc == null) {
        break missingId;
      }

      id = R.id.right_call_agc_mirror;
      TextView rightCallAgcMirror = rootView.findViewById(id);
      if (rightCallAgcMirror == null) {
        break missingId;
      }

      id = R.id.right_call_max_rssi;
      TextView rightCallMaxRssi = rootView.findViewById(id);
      if (rightCallMaxRssi == null) {
        break missingId;
      }

      id = R.id.right_call_max_rssi_mirror;
      TextView rightCallMaxRssiMirror = rootView.findViewById(id);
      if (rightCallMaxRssiMirror == null) {
        break missingId;
      }

      id = R.id.right_call_min_rssi;
      TextView rightCallMinRssi = rootView.findViewById(id);
      if (rightCallMinRssi == null) {
        break missingId;
      }

      id = R.id.right_call_min_rssi_mirror;
      TextView rightCallMinRssiMirror = rootView.findViewById(id);
      if (rightCallMinRssiMirror == null) {
        break missingId;
      }

      id = R.id.right_call_rssi;
      TextView rightCallRssi = rootView.findViewById(id);
      if (rightCallRssi == null) {
        break missingId;
      }

      id = R.id.right_call_rssi_mirror;
      TextView rightCallRssiMirror = rootView.findViewById(id);
      if (rightCallRssiMirror == null) {
        break missingId;
      }

      id = R.id.right_FA_IDX;
      TextView rightFAIDX = rootView.findViewById(id);
      if (rightFAIDX == null) {
        break missingId;
      }

      id = R.id.right_packet_loss_rate;
      TextView rightPacketLossRate = rootView.findViewById(id);
      if (rightPacketLossRate == null) {
        break missingId;
      }

      id = R.id.right_tws_agc;
      TextView rightTwsAgc = rootView.findViewById(id);
      if (rightTwsAgc == null) {
        break missingId;
      }

      id = R.id.right_tws_max_rssi;
      TextView rightTwsMaxRssi = rootView.findViewById(id);
      if (rightTwsMaxRssi == null) {
        break missingId;
      }

      id = R.id.right_tws_min_rssi;
      TextView rightTwsMinRssi = rootView.findViewById(id);
      if (rightTwsMinRssi == null) {
        break missingId;
      }

      id = R.id.right_tws_rssi;
      TextView rightTwsRssi = rootView.findViewById(id);
      if (rightTwsRssi == null) {
        break missingId;
      }

      id = R.id.rssiChart;
      LineChart rssiChart = rootView.findViewById(id);
      if (rssiChart == null) {
        break missingId;
      }

      id = R.id.rssi_read_start;
      Button rssiReadStart = rootView.findViewById(id);
      if (rssiReadStart == null) {
        break missingId;
      }

      id = R.id.rssi_read_stop;
      Button rssiReadStop = rootView.findViewById(id);
      if (rssiReadStop == null) {
        break missingId;
      }

      id = R.id.spp_stop;
      Button sppStop = rootView.findViewById(id);
      if (sppStop == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      return new ActivityRssiBinding((LinearLayout) rootView, connectSpp, interval, leftCallAgc,
          leftCallAgcMirror, leftCallMaxRssi, leftCallMaxRssiMirror, leftCallMinRssi,
          leftCallMinRssiMirror, leftCallRssi, leftCallRssiMirror, leftFAIDX, leftPacketLossRate,
          leftTwsAgc, leftTwsMaxRssi, leftTwsMinRssi, leftTwsRssi, binding_loginfo, rightCallAgc,
          rightCallAgcMirror, rightCallMaxRssi, rightCallMaxRssiMirror, rightCallMinRssi,
          rightCallMinRssiMirror, rightCallRssi, rightCallRssiMirror, rightFAIDX,
          rightPacketLossRate, rightTwsAgc, rightTwsMaxRssi, rightTwsMinRssi, rightTwsRssi,
          rssiChart, rssiReadStart, rssiReadStop, sppStop, binding_tool);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
