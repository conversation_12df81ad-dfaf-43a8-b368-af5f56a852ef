package com.besall.allbase.bluetooth.service.BleWifi;


import android.content.Context;
import android.util.Log;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.base.BesBaseService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.message.BaseMessage;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.common.utils.HttpUtils;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_SUCCESS;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_RECEIVE_FILE_PATH;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_RECEIVE_OTA_PATH;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_RECEIVE_WIFI_CONFIG;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_RECEIVE_WIFI_OPEN_WIFI_FAIL;
import static com.besall.allbase.bluetooth.service.BleWifi.BleWifiConstants.BES_BLE_WIFI_OPEN_WIFI_RSP;

import java.io.File;
import java.io.IOException;

public class BleWifiService extends BesBaseService {

    private BleWifiCMD bleWifiCMD;
    private String otaFilePath = "";
    private String otaHttpPath = "";

    public BleWifiService(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        super(serviceConfig, listener, context);
        bleWifiCMD = new BleWifiCMD();
        startConnect(serviceConfig);
    }

    @Override
    public void onStatusChanged(HmDevice device, int status, DeviceProtocol protocol) {
        super.onStatusChanged(device, status, protocol);
        if (status == BES_CONNECT_SUCCESS) {
            callBackStateChangedMessage(BES_CONNECT_SUCCESS, "");
        } else if (getDeviceConnectState(mConfig) != BesSdkConstants.BesConnectState.BES_CONNECT) {
            callBackErrorMessage(status);
        }
    }

    @Override
    public void onDataReceived(BaseMessage deviceMessage) {
        super.onDataReceived(deviceMessage);
        if (mConfig.getTotaConnect() && !totauccess) {
            return;
        }
        int result = bleWifiCMD.receiveData(mContext, (byte[]) deviceMessage.getMsgContent());
        if (result == BES_BLE_RECEIVE_OTA_PATH) {
            otaHttpPath = bleWifiCMD.getCurOtaPath();

            try {
                HttpUtils.sendPutRequest(otaHttpPath, otaFilePath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        } else if (result == BES_BLE_RECEIVE_FILE_PATH) {

        } else if (result == BES_BLE_WIFI_OPEN_WIFI_RSP) {
            callBackStateChangedMessage(result, "");
        } else if (result == BES_BLE_RECEIVE_WIFI_OPEN_WIFI_FAIL) {
            callBackStateChangedMessage(result, "");
        } else if (result == BES_BLE_RECEIVE_WIFI_CONFIG) {
            callBackStateChangedMessage(result, bleWifiCMD.getCurWifiInfo());
        }
    }

    public void changeDevice(BesServiceConfig serviceConfig) {
        startConnect(serviceConfig);
    }

    public void sendWifiData(String name, String psw) {
        if (sendData(bleWifiCMD.getWifiData(name, psw))) {
            ActivityUtils.showToast(R.string.wifiSendDataOK);
        } else  {
            ActivityUtils.showToast(R.string.wifiSendDataFail);
        }
    }

    public void startOtaConfig(String path) {
        otaFilePath = path;
        File file = new File(otaFilePath);
        if (!file.exists()) {
            addLog(TAG, "startOta Fail:file not exists");
            return;
        }
        addLog(TAG, "startOta OK");

        sendData(bleWifiCMD.getWorkPathCMD(2));
    }

    public void openWifi() {
        sendData(bleWifiCMD.getOpenWifiCMD());
    }

    private void addLog(String TAG, String str) {
        Log.i(TAG, str);
    }
}
