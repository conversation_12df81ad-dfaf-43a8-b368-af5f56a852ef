// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class OtafilepathItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button chooseFile;

  @NonNull
  public final TextView name;

  @NonNull
  public final TextView path;

  private OtafilepathItemBinding(@NonNull LinearLayout rootView, @NonNull Button chooseFile,
      @NonNull TextView name, @NonNull TextView path) {
    this.rootView = rootView;
    this.chooseFile = chooseFile;
    this.name = name;
    this.path = path;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static OtafilepathItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static OtafilepathItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.otafilepath_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static OtafilepathItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.choose_file;
      Button chooseFile = rootView.findViewById(id);
      if (chooseFile == null) {
        break missingId;
      }

      id = R.id.name;
      TextView name = rootView.findViewById(id);
      if (name == null) {
        break missingId;
      }

      id = R.id.path;
      TextView path = rootView.findViewById(id);
      if (path == null) {
        break missingId;
      }

      return new OtafilepathItemBinding((LinearLayout) rootView, chooseFile, name, path);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
