// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: transport.proto

package com.amazon.alexa.accessory.protocol;

public final class Transport {
  private Transport() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ConnectionDetailsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ConnectionDetails)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>bytes identifier = 1;</code>
     * @return The identifier.
     */
    com.google.protobuf.ByteString getIdentifier();
  }
  /**
   * Protobuf type {@code ConnectionDetails}
   */
  public static final class ConnectionDetails extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ConnectionDetails)
      ConnectionDetailsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ConnectionDetails.newBuilder() to construct.
    private ConnectionDetails(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ConnectionDetails() {
      identifier_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ConnectionDetails();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Transport.internal_static_ConnectionDetails_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Transport.internal_static_ConnectionDetails_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails.class, com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails.Builder.class);
    }

    public static final int IDENTIFIER_FIELD_NUMBER = 1;
    private com.google.protobuf.ByteString identifier_;
    /**
     * <code>bytes identifier = 1;</code>
     * @return The identifier.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIdentifier() {
      return identifier_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!identifier_.isEmpty()) {
        output.writeBytes(1, identifier_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!identifier_.isEmpty()) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, identifier_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails other = (com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails) obj;

      if (!getIdentifier()
          .equals(other.getIdentifier())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + IDENTIFIER_FIELD_NUMBER;
      hash = (53 * hash) + getIdentifier().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code ConnectionDetails}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ConnectionDetails)
        com.amazon.alexa.accessory.protocol.Transport.ConnectionDetailsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Transport.internal_static_ConnectionDetails_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Transport.internal_static_ConnectionDetails_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails.class, com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        identifier_ = com.google.protobuf.ByteString.EMPTY;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Transport.internal_static_ConnectionDetails_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails build() {
        com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails buildPartial() {
        com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails result = new com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails(this);
        result.identifier_ = identifier_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails other) {
        if (other == com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails.getDefaultInstance()) return this;
        if (other.getIdentifier() != com.google.protobuf.ByteString.EMPTY) {
          setIdentifier(other.getIdentifier());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                identifier_ = input.readBytes();

                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private com.google.protobuf.ByteString identifier_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>bytes identifier = 1;</code>
       * @return The identifier.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIdentifier() {
        return identifier_;
      }
      /**
       * <code>bytes identifier = 1;</code>
       * @param value The identifier to set.
       * @return This builder for chaining.
       */
      public Builder setIdentifier(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        identifier_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>bytes identifier = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIdentifier() {
        
        identifier_ = getDefaultInstance().getIdentifier();
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ConnectionDetails)
    }

    // @@protoc_insertion_point(class_scope:ConnectionDetails)
    private static final com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails();
    }

    public static com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ConnectionDetails>
        PARSER = new com.google.protobuf.AbstractParser<ConnectionDetails>() {
      @java.lang.Override
      public ConnectionDetails parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ConnectionDetails> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ConnectionDetails> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface UpgradeTransportOrBuilder extends
      // @@protoc_insertion_point(interface_extends:UpgradeTransport)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Transport transport = 1;</code>
     * @return The enum numeric value on the wire for transport.
     */
    int getTransportValue();
    /**
     * <code>.Transport transport = 1;</code>
     * @return The transport.
     */
    com.amazon.alexa.accessory.protocol.Common.Transport getTransport();
  }
  /**
   * Protobuf type {@code UpgradeTransport}
   */
  public static final class UpgradeTransport extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:UpgradeTransport)
      UpgradeTransportOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use UpgradeTransport.newBuilder() to construct.
    private UpgradeTransport(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private UpgradeTransport() {
      transport_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new UpgradeTransport();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Transport.internal_static_UpgradeTransport_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Transport.internal_static_UpgradeTransport_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport.class, com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport.Builder.class);
    }

    public static final int TRANSPORT_FIELD_NUMBER = 1;
    private int transport_;
    /**
     * <code>.Transport transport = 1;</code>
     * @return The enum numeric value on the wire for transport.
     */
    @java.lang.Override public int getTransportValue() {
      return transport_;
    }
    /**
     * <code>.Transport transport = 1;</code>
     * @return The transport.
     */
    @java.lang.Override public com.amazon.alexa.accessory.protocol.Common.Transport getTransport() {
      @SuppressWarnings("deprecation")
      com.amazon.alexa.accessory.protocol.Common.Transport result = com.amazon.alexa.accessory.protocol.Common.Transport.valueOf(transport_);
      return result == null ? com.amazon.alexa.accessory.protocol.Common.Transport.UNRECOGNIZED : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (transport_ != com.amazon.alexa.accessory.protocol.Common.Transport.BLUETOOTH_LOW_ENERGY.getNumber()) {
        output.writeEnum(1, transport_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (transport_ != com.amazon.alexa.accessory.protocol.Common.Transport.BLUETOOTH_LOW_ENERGY.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, transport_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport other = (com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport) obj;

      if (transport_ != other.transport_) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TRANSPORT_FIELD_NUMBER;
      hash = (53 * hash) + transport_;
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code UpgradeTransport}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:UpgradeTransport)
        com.amazon.alexa.accessory.protocol.Transport.UpgradeTransportOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Transport.internal_static_UpgradeTransport_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Transport.internal_static_UpgradeTransport_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport.class, com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        transport_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Transport.internal_static_UpgradeTransport_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport build() {
        com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport buildPartial() {
        com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport result = new com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport(this);
        result.transport_ = transport_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport other) {
        if (other == com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport.getDefaultInstance()) return this;
        if (other.transport_ != 0) {
          setTransportValue(other.getTransportValue());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                transport_ = input.readEnum();

                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private int transport_ = 0;
      /**
       * <code>.Transport transport = 1;</code>
       * @return The enum numeric value on the wire for transport.
       */
      @java.lang.Override public int getTransportValue() {
        return transport_;
      }
      /**
       * <code>.Transport transport = 1;</code>
       * @param value The enum numeric value on the wire for transport to set.
       * @return This builder for chaining.
       */
      public Builder setTransportValue(int value) {
        
        transport_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.Transport transport = 1;</code>
       * @return The transport.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Common.Transport getTransport() {
        @SuppressWarnings("deprecation")
        com.amazon.alexa.accessory.protocol.Common.Transport result = com.amazon.alexa.accessory.protocol.Common.Transport.valueOf(transport_);
        return result == null ? com.amazon.alexa.accessory.protocol.Common.Transport.UNRECOGNIZED : result;
      }
      /**
       * <code>.Transport transport = 1;</code>
       * @param value The transport to set.
       * @return This builder for chaining.
       */
      public Builder setTransport(com.amazon.alexa.accessory.protocol.Common.Transport value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        transport_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.Transport transport = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTransport() {
        
        transport_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:UpgradeTransport)
    }

    // @@protoc_insertion_point(class_scope:UpgradeTransport)
    private static final com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport();
    }

    public static com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<UpgradeTransport>
        PARSER = new com.google.protobuf.AbstractParser<UpgradeTransport>() {
      @java.lang.Override
      public UpgradeTransport parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<UpgradeTransport> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UpgradeTransport> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SwitchTransportOrBuilder extends
      // @@protoc_insertion_point(interface_extends:SwitchTransport)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Transport new_transport = 1;</code>
     * @return The enum numeric value on the wire for newTransport.
     */
    int getNewTransportValue();
    /**
     * <code>.Transport new_transport = 1;</code>
     * @return The newTransport.
     */
    com.amazon.alexa.accessory.protocol.Common.Transport getNewTransport();
  }
  /**
   * Protobuf type {@code SwitchTransport}
   */
  public static final class SwitchTransport extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:SwitchTransport)
      SwitchTransportOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SwitchTransport.newBuilder() to construct.
    private SwitchTransport(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SwitchTransport() {
      newTransport_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SwitchTransport();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Transport.internal_static_SwitchTransport_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Transport.internal_static_SwitchTransport_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Transport.SwitchTransport.class, com.amazon.alexa.accessory.protocol.Transport.SwitchTransport.Builder.class);
    }

    public static final int NEW_TRANSPORT_FIELD_NUMBER = 1;
    private int newTransport_;
    /**
     * <code>.Transport new_transport = 1;</code>
     * @return The enum numeric value on the wire for newTransport.
     */
    @java.lang.Override public int getNewTransportValue() {
      return newTransport_;
    }
    /**
     * <code>.Transport new_transport = 1;</code>
     * @return The newTransport.
     */
    @java.lang.Override public com.amazon.alexa.accessory.protocol.Common.Transport getNewTransport() {
      @SuppressWarnings("deprecation")
      com.amazon.alexa.accessory.protocol.Common.Transport result = com.amazon.alexa.accessory.protocol.Common.Transport.valueOf(newTransport_);
      return result == null ? com.amazon.alexa.accessory.protocol.Common.Transport.UNRECOGNIZED : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (newTransport_ != com.amazon.alexa.accessory.protocol.Common.Transport.BLUETOOTH_LOW_ENERGY.getNumber()) {
        output.writeEnum(1, newTransport_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (newTransport_ != com.amazon.alexa.accessory.protocol.Common.Transport.BLUETOOTH_LOW_ENERGY.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, newTransport_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Transport.SwitchTransport)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Transport.SwitchTransport other = (com.amazon.alexa.accessory.protocol.Transport.SwitchTransport) obj;

      if (newTransport_ != other.newTransport_) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + NEW_TRANSPORT_FIELD_NUMBER;
      hash = (53 * hash) + newTransport_;
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Transport.SwitchTransport parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.SwitchTransport parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.SwitchTransport parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.SwitchTransport parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.SwitchTransport parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.SwitchTransport parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.SwitchTransport parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.SwitchTransport parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.SwitchTransport parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.SwitchTransport parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.SwitchTransport parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Transport.SwitchTransport parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Transport.SwitchTransport prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code SwitchTransport}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:SwitchTransport)
        com.amazon.alexa.accessory.protocol.Transport.SwitchTransportOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Transport.internal_static_SwitchTransport_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Transport.internal_static_SwitchTransport_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Transport.SwitchTransport.class, com.amazon.alexa.accessory.protocol.Transport.SwitchTransport.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Transport.SwitchTransport.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        newTransport_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Transport.internal_static_SwitchTransport_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Transport.SwitchTransport getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Transport.SwitchTransport.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Transport.SwitchTransport build() {
        com.amazon.alexa.accessory.protocol.Transport.SwitchTransport result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Transport.SwitchTransport buildPartial() {
        com.amazon.alexa.accessory.protocol.Transport.SwitchTransport result = new com.amazon.alexa.accessory.protocol.Transport.SwitchTransport(this);
        result.newTransport_ = newTransport_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Transport.SwitchTransport) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Transport.SwitchTransport)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Transport.SwitchTransport other) {
        if (other == com.amazon.alexa.accessory.protocol.Transport.SwitchTransport.getDefaultInstance()) return this;
        if (other.newTransport_ != 0) {
          setNewTransportValue(other.getNewTransportValue());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                newTransport_ = input.readEnum();

                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private int newTransport_ = 0;
      /**
       * <code>.Transport new_transport = 1;</code>
       * @return The enum numeric value on the wire for newTransport.
       */
      @java.lang.Override public int getNewTransportValue() {
        return newTransport_;
      }
      /**
       * <code>.Transport new_transport = 1;</code>
       * @param value The enum numeric value on the wire for newTransport to set.
       * @return This builder for chaining.
       */
      public Builder setNewTransportValue(int value) {
        
        newTransport_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.Transport new_transport = 1;</code>
       * @return The newTransport.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Common.Transport getNewTransport() {
        @SuppressWarnings("deprecation")
        com.amazon.alexa.accessory.protocol.Common.Transport result = com.amazon.alexa.accessory.protocol.Common.Transport.valueOf(newTransport_);
        return result == null ? com.amazon.alexa.accessory.protocol.Common.Transport.UNRECOGNIZED : result;
      }
      /**
       * <code>.Transport new_transport = 1;</code>
       * @param value The newTransport to set.
       * @return This builder for chaining.
       */
      public Builder setNewTransport(com.amazon.alexa.accessory.protocol.Common.Transport value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        newTransport_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.Transport new_transport = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearNewTransport() {
        
        newTransport_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:SwitchTransport)
    }

    // @@protoc_insertion_point(class_scope:SwitchTransport)
    private static final com.amazon.alexa.accessory.protocol.Transport.SwitchTransport DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Transport.SwitchTransport();
    }

    public static com.amazon.alexa.accessory.protocol.Transport.SwitchTransport getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SwitchTransport>
        PARSER = new com.google.protobuf.AbstractParser<SwitchTransport>() {
      @java.lang.Override
      public SwitchTransport parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SwitchTransport> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SwitchTransport> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Transport.SwitchTransport getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ConnectionDetails_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ConnectionDetails_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_UpgradeTransport_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_UpgradeTransport_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_SwitchTransport_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_SwitchTransport_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\017transport.proto\032\014common.proto\"\'\n\021Conne" +
      "ctionDetails\022\022\n\nidentifier\030\001 \001(\014\"1\n\020Upgr" +
      "adeTransport\022\035\n\ttransport\030\001 \001(\0162\n.Transp" +
      "ort\"4\n\017SwitchTransport\022!\n\rnew_transport\030" +
      "\001 \001(\0162\n.TransportB0\n#com.amazon.alexa.ac" +
      "cessory.protocolH\003\240\001\001\242\002\003AACb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.amazon.alexa.accessory.protocol.Common.getDescriptor(),
        });
    internal_static_ConnectionDetails_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_ConnectionDetails_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ConnectionDetails_descriptor,
        new java.lang.String[] { "Identifier", });
    internal_static_UpgradeTransport_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_UpgradeTransport_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_UpgradeTransport_descriptor,
        new java.lang.String[] { "Transport", });
    internal_static_SwitchTransport_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_SwitchTransport_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_SwitchTransport_descriptor,
        new java.lang.String[] { "NewTransport", });
    com.amazon.alexa.accessory.protocol.Common.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
