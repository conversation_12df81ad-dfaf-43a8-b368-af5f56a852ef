// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActConnectBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button connectDevice;

  @NonNull
  public final EditText customerUuid;

  @NonNull
  public final TextView customerUuidTips;

  @NonNull
  public final TextView deviceAddress;

  @NonNull
  public final TextView deviceName;

  @NonNull
  public final Button editCutomercmd;

  @NonNull
  public final ImageView loading;

  @NonNull
  public final LogviewBinding loginfo;

  @NonNull
  public final Button pickDevice;

  @NonNull
  public final Button pickDeviceBle;

  @NonNull
  public final ToolbarBinding tool;

  private ActConnectBinding(@NonNull LinearLayout rootView, @NonNull Button connectDevice,
      @NonNull EditText customerUuid, @NonNull TextView customerUuidTips,
      @NonNull TextView deviceAddress, @NonNull TextView deviceName, @NonNull Button editCutomercmd,
      @NonNull ImageView loading, @NonNull LogviewBinding loginfo, @NonNull Button pickDevice,
      @NonNull Button pickDeviceBle, @NonNull ToolbarBinding tool) {
    this.rootView = rootView;
    this.connectDevice = connectDevice;
    this.customerUuid = customerUuid;
    this.customerUuidTips = customerUuidTips;
    this.deviceAddress = deviceAddress;
    this.deviceName = deviceName;
    this.editCutomercmd = editCutomercmd;
    this.loading = loading;
    this.loginfo = loginfo;
    this.pickDevice = pickDevice;
    this.pickDeviceBle = pickDeviceBle;
    this.tool = tool;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActConnectBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActConnectBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.act_connect, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActConnectBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.connect_device;
      Button connectDevice = rootView.findViewById(id);
      if (connectDevice == null) {
        break missingId;
      }

      id = R.id.customer_uuid;
      EditText customerUuid = rootView.findViewById(id);
      if (customerUuid == null) {
        break missingId;
      }

      id = R.id.customer_uuid_tips;
      TextView customerUuidTips = rootView.findViewById(id);
      if (customerUuidTips == null) {
        break missingId;
      }

      id = R.id.device_address;
      TextView deviceAddress = rootView.findViewById(id);
      if (deviceAddress == null) {
        break missingId;
      }

      id = R.id.device_name;
      TextView deviceName = rootView.findViewById(id);
      if (deviceName == null) {
        break missingId;
      }

      id = R.id.edit_cutomercmd;
      Button editCutomercmd = rootView.findViewById(id);
      if (editCutomercmd == null) {
        break missingId;
      }

      id = R.id.loading;
      ImageView loading = rootView.findViewById(id);
      if (loading == null) {
        break missingId;
      }

      id = R.id.loginfo;
      View loginfo = rootView.findViewById(id);
      if (loginfo == null) {
        break missingId;
      }
      LogviewBinding binding_loginfo = LogviewBinding.bind(loginfo);

      id = R.id.pick_device;
      Button pickDevice = rootView.findViewById(id);
      if (pickDevice == null) {
        break missingId;
      }

      id = R.id.pick_device_ble;
      Button pickDeviceBle = rootView.findViewById(id);
      if (pickDeviceBle == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      return new ActConnectBinding((LinearLayout) rootView, connectDevice, customerUuid,
          customerUuidTips, deviceAddress, deviceName, editCutomercmd, loading, binding_loginfo,
          pickDevice, pickDeviceBle, binding_tool);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
