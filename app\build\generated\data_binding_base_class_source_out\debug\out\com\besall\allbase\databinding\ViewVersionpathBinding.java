// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ListView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ViewVersionpathBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button chooseFile;

  @NonNull
  public final EditText deviceVersion;

  @NonNull
  public final ListView filePath;

  @NonNull
  public final Button otaFileTipsBtn;

  private ViewVersionpathBinding(@NonNull ConstraintLayout rootView, @NonNull Button chooseFile,
      @NonNull EditText deviceVersion, @NonNull ListView filePath, @NonNull Button otaFileTipsBtn) {
    this.rootView = rootView;
    this.chooseFile = chooseFile;
    this.deviceVersion = deviceVersion;
    this.filePath = filePath;
    this.otaFileTipsBtn = otaFileTipsBtn;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ViewVersionpathBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ViewVersionpathBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.view_versionpath, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ViewVersionpathBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.choose_file;
      Button chooseFile = rootView.findViewById(id);
      if (chooseFile == null) {
        break missingId;
      }

      id = R.id.device_version;
      EditText deviceVersion = rootView.findViewById(id);
      if (deviceVersion == null) {
        break missingId;
      }

      id = R.id.file_path;
      ListView filePath = rootView.findViewById(id);
      if (filePath == null) {
        break missingId;
      }

      id = R.id.ota_file_tips_btn;
      Button otaFileTipsBtn = rootView.findViewById(id);
      if (otaFileTipsBtn == null) {
        break missingId;
      }

      return new ViewVersionpathBinding((ConstraintLayout) rootView, chooseFile, deviceVersion,
          filePath, otaFileTipsBtn);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
