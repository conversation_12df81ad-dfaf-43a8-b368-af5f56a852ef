// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: notificationsClearIndicatorDirective.proto

package com.amazon.proto.avs.v20160207.notifications;

public final class ClearIndicatorDirective {
  private ClearIndicatorDirective() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ClearIndicatorDirectiveProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:notifications.ClearIndicatorDirectiveProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.notifications.ClearIndicatorDirectiveProto.Directive directive = 1;</code>
     * @return Whether the directive field is set.
     */
    boolean hasDirective();
    /**
     * <code>.notifications.ClearIndicatorDirectiveProto.Directive directive = 1;</code>
     * @return The directive.
     */
    com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive getDirective();
    /**
     * <code>.notifications.ClearIndicatorDirectiveProto.Directive directive = 1;</code>
     */
    com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.DirectiveOrBuilder getDirectiveOrBuilder();
  }
  /**
   * Protobuf type {@code notifications.ClearIndicatorDirectiveProto}
   */
  public static final class ClearIndicatorDirectiveProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:notifications.ClearIndicatorDirectiveProto)
      ClearIndicatorDirectiveProtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClearIndicatorDirectiveProto.newBuilder() to construct.
    private ClearIndicatorDirectiveProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClearIndicatorDirectiveProto() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClearIndicatorDirectiveProto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.internal_static_notifications_ClearIndicatorDirectiveProto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.internal_static_notifications_ClearIndicatorDirectiveProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.class, com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Builder.class);
    }

    public interface DirectiveOrBuilder extends
        // @@protoc_insertion_point(interface_extends:notifications.ClearIndicatorDirectiveProto.Directive)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>.notifications.ClearIndicatorDirectivePayloadProto payload = 2;</code>
       * @return Whether the payload field is set.
       */
      boolean hasPayload();
      /**
       * <code>.notifications.ClearIndicatorDirectivePayloadProto payload = 2;</code>
       * @return The payload.
       */
      com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProto getPayload();
      /**
       * <code>.notifications.ClearIndicatorDirectivePayloadProto payload = 2;</code>
       */
      com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProtoOrBuilder getPayloadOrBuilder();

      /**
       * <code>.header.DirectiveHeaderProto header = 1;</code>
       * @return Whether the header field is set.
       */
      boolean hasHeader();
      /**
       * <code>.header.DirectiveHeaderProto header = 1;</code>
       * @return The header.
       */
      com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto getHeader();
      /**
       * <code>.header.DirectiveHeaderProto header = 1;</code>
       */
      com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProtoOrBuilder getHeaderOrBuilder();
    }
    /**
     * Protobuf type {@code notifications.ClearIndicatorDirectiveProto.Directive}
     */
    public static final class Directive extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:notifications.ClearIndicatorDirectiveProto.Directive)
        DirectiveOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use Directive.newBuilder() to construct.
      private Directive(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private Directive() {
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new Directive();
      }

      @java.lang.Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.internal_static_notifications_ClearIndicatorDirectiveProto_Directive_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.internal_static_notifications_ClearIndicatorDirectiveProto_Directive_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive.class, com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive.Builder.class);
      }

      public static final int PAYLOAD_FIELD_NUMBER = 2;
      private com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProto payload_;
      /**
       * <code>.notifications.ClearIndicatorDirectivePayloadProto payload = 2;</code>
       * @return Whether the payload field is set.
       */
      @java.lang.Override
      public boolean hasPayload() {
        return payload_ != null;
      }
      /**
       * <code>.notifications.ClearIndicatorDirectivePayloadProto payload = 2;</code>
       * @return The payload.
       */
      @java.lang.Override
      public com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProto getPayload() {
        return payload_ == null ? com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProto.getDefaultInstance() : payload_;
      }
      /**
       * <code>.notifications.ClearIndicatorDirectivePayloadProto payload = 2;</code>
       */
      @java.lang.Override
      public com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProtoOrBuilder getPayloadOrBuilder() {
        return getPayload();
      }

      public static final int HEADER_FIELD_NUMBER = 1;
      private com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto header_;
      /**
       * <code>.header.DirectiveHeaderProto header = 1;</code>
       * @return Whether the header field is set.
       */
      @java.lang.Override
      public boolean hasHeader() {
        return header_ != null;
      }
      /**
       * <code>.header.DirectiveHeaderProto header = 1;</code>
       * @return The header.
       */
      @java.lang.Override
      public com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto getHeader() {
        return header_ == null ? com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.getDefaultInstance() : header_;
      }
      /**
       * <code>.header.DirectiveHeaderProto header = 1;</code>
       */
      @java.lang.Override
      public com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProtoOrBuilder getHeaderOrBuilder() {
        return getHeader();
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (header_ != null) {
          output.writeMessage(1, getHeader());
        }
        if (payload_ != null) {
          output.writeMessage(2, getPayload());
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (header_ != null) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, getHeader());
        }
        if (payload_ != null) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, getPayload());
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive)) {
          return super.equals(obj);
        }
        com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive other = (com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive) obj;

        if (hasPayload() != other.hasPayload()) return false;
        if (hasPayload()) {
          if (!getPayload()
              .equals(other.getPayload())) return false;
        }
        if (hasHeader() != other.hasHeader()) return false;
        if (hasHeader()) {
          if (!getHeader()
              .equals(other.getHeader())) return false;
        }
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        if (hasPayload()) {
          hash = (37 * hash) + PAYLOAD_FIELD_NUMBER;
          hash = (53 * hash) + getPayload().hashCode();
        }
        if (hasHeader()) {
          hash = (37 * hash) + HEADER_FIELD_NUMBER;
          hash = (53 * hash) + getHeader().hashCode();
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code notifications.ClearIndicatorDirectiveProto.Directive}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:notifications.ClearIndicatorDirectiveProto.Directive)
          com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.DirectiveOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.internal_static_notifications_ClearIndicatorDirectiveProto_Directive_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.internal_static_notifications_ClearIndicatorDirectiveProto_Directive_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive.class, com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive.Builder.class);
        }

        // Construct using com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          if (payloadBuilder_ == null) {
            payload_ = null;
          } else {
            payload_ = null;
            payloadBuilder_ = null;
          }
          if (headerBuilder_ == null) {
            header_ = null;
          } else {
            header_ = null;
            headerBuilder_ = null;
          }
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.internal_static_notifications_ClearIndicatorDirectiveProto_Directive_descriptor;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive getDefaultInstanceForType() {
          return com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive.getDefaultInstance();
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive build() {
          com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive buildPartial() {
          com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive result = new com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive(this);
          if (payloadBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = payloadBuilder_.build();
          }
          if (headerBuilder_ == null) {
            result.header_ = header_;
          } else {
            result.header_ = headerBuilder_.build();
          }
          onBuilt();
          return result;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive) {
            return mergeFrom((com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive other) {
          if (other == com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive.getDefaultInstance()) return this;
          if (other.hasPayload()) {
            mergePayload(other.getPayload());
          }
          if (other.hasHeader()) {
            mergeHeader(other.getHeader());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 10: {
                  input.readMessage(
                      getHeaderFieldBuilder().getBuilder(),
                      extensionRegistry);

                  break;
                } // case 10
                case 18: {
                  input.readMessage(
                      getPayloadFieldBuilder().getBuilder(),
                      extensionRegistry);

                  break;
                } // case 18
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }

        private com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProto payload_;
        private com.google.protobuf.SingleFieldBuilderV3<
            com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProto, com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProto.Builder, com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProtoOrBuilder> payloadBuilder_;
        /**
         * <code>.notifications.ClearIndicatorDirectivePayloadProto payload = 2;</code>
         * @return Whether the payload field is set.
         */
        public boolean hasPayload() {
          return payloadBuilder_ != null || payload_ != null;
        }
        /**
         * <code>.notifications.ClearIndicatorDirectivePayloadProto payload = 2;</code>
         * @return The payload.
         */
        public com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProto getPayload() {
          if (payloadBuilder_ == null) {
            return payload_ == null ? com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProto.getDefaultInstance() : payload_;
          } else {
            return payloadBuilder_.getMessage();
          }
        }
        /**
         * <code>.notifications.ClearIndicatorDirectivePayloadProto payload = 2;</code>
         */
        public Builder setPayload(com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProto value) {
          if (payloadBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            payload_ = value;
            onChanged();
          } else {
            payloadBuilder_.setMessage(value);
          }

          return this;
        }
        /**
         * <code>.notifications.ClearIndicatorDirectivePayloadProto payload = 2;</code>
         */
        public Builder setPayload(
            com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProto.Builder builderForValue) {
          if (payloadBuilder_ == null) {
            payload_ = builderForValue.build();
            onChanged();
          } else {
            payloadBuilder_.setMessage(builderForValue.build());
          }

          return this;
        }
        /**
         * <code>.notifications.ClearIndicatorDirectivePayloadProto payload = 2;</code>
         */
        public Builder mergePayload(com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProto value) {
          if (payloadBuilder_ == null) {
            if (payload_ != null) {
              payload_ =
                com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProto.newBuilder(payload_).mergeFrom(value).buildPartial();
            } else {
              payload_ = value;
            }
            onChanged();
          } else {
            payloadBuilder_.mergeFrom(value);
          }

          return this;
        }
        /**
         * <code>.notifications.ClearIndicatorDirectivePayloadProto payload = 2;</code>
         */
        public Builder clearPayload() {
          if (payloadBuilder_ == null) {
            payload_ = null;
            onChanged();
          } else {
            payload_ = null;
            payloadBuilder_ = null;
          }

          return this;
        }
        /**
         * <code>.notifications.ClearIndicatorDirectivePayloadProto payload = 2;</code>
         */
        public com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProto.Builder getPayloadBuilder() {
          
          onChanged();
          return getPayloadFieldBuilder().getBuilder();
        }
        /**
         * <code>.notifications.ClearIndicatorDirectivePayloadProto payload = 2;</code>
         */
        public com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProtoOrBuilder getPayloadOrBuilder() {
          if (payloadBuilder_ != null) {
            return payloadBuilder_.getMessageOrBuilder();
          } else {
            return payload_ == null ?
                com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProto.getDefaultInstance() : payload_;
          }
        }
        /**
         * <code>.notifications.ClearIndicatorDirectivePayloadProto payload = 2;</code>
         */
        private com.google.protobuf.SingleFieldBuilderV3<
            com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProto, com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProto.Builder, com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProtoOrBuilder> 
            getPayloadFieldBuilder() {
          if (payloadBuilder_ == null) {
            payloadBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
                com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProto, com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProto.Builder, com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.ClearIndicatorDirectivePayloadProtoOrBuilder>(
                    getPayload(),
                    getParentForChildren(),
                    isClean());
            payload_ = null;
          }
          return payloadBuilder_;
        }

        private com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto header_;
        private com.google.protobuf.SingleFieldBuilderV3<
            com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto, com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.Builder, com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProtoOrBuilder> headerBuilder_;
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         * @return Whether the header field is set.
         */
        public boolean hasHeader() {
          return headerBuilder_ != null || header_ != null;
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         * @return The header.
         */
        public com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto getHeader() {
          if (headerBuilder_ == null) {
            return header_ == null ? com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.getDefaultInstance() : header_;
          } else {
            return headerBuilder_.getMessage();
          }
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        public Builder setHeader(com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto value) {
          if (headerBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            header_ = value;
            onChanged();
          } else {
            headerBuilder_.setMessage(value);
          }

          return this;
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        public Builder setHeader(
            com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.Builder builderForValue) {
          if (headerBuilder_ == null) {
            header_ = builderForValue.build();
            onChanged();
          } else {
            headerBuilder_.setMessage(builderForValue.build());
          }

          return this;
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        public Builder mergeHeader(com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto value) {
          if (headerBuilder_ == null) {
            if (header_ != null) {
              header_ =
                com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.newBuilder(header_).mergeFrom(value).buildPartial();
            } else {
              header_ = value;
            }
            onChanged();
          } else {
            headerBuilder_.mergeFrom(value);
          }

          return this;
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        public Builder clearHeader() {
          if (headerBuilder_ == null) {
            header_ = null;
            onChanged();
          } else {
            header_ = null;
            headerBuilder_ = null;
          }

          return this;
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        public com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.Builder getHeaderBuilder() {
          
          onChanged();
          return getHeaderFieldBuilder().getBuilder();
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        public com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProtoOrBuilder getHeaderOrBuilder() {
          if (headerBuilder_ != null) {
            return headerBuilder_.getMessageOrBuilder();
          } else {
            return header_ == null ?
                com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.getDefaultInstance() : header_;
          }
        }
        /**
         * <code>.header.DirectiveHeaderProto header = 1;</code>
         */
        private com.google.protobuf.SingleFieldBuilderV3<
            com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto, com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.Builder, com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProtoOrBuilder> 
            getHeaderFieldBuilder() {
          if (headerBuilder_ == null) {
            headerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
                com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto, com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.Builder, com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProtoOrBuilder>(
                    getHeader(),
                    getParentForChildren(),
                    isClean());
            header_ = null;
          }
          return headerBuilder_;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:notifications.ClearIndicatorDirectiveProto.Directive)
      }

      // @@protoc_insertion_point(class_scope:notifications.ClearIndicatorDirectiveProto.Directive)
      private static final com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive();
      }

      public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Directive>
          PARSER = new com.google.protobuf.AbstractParser<Directive>() {
        @java.lang.Override
        public Directive parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Directive> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Directive> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public static final int DIRECTIVE_FIELD_NUMBER = 1;
    private com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive directive_;
    /**
     * <code>.notifications.ClearIndicatorDirectiveProto.Directive directive = 1;</code>
     * @return Whether the directive field is set.
     */
    @java.lang.Override
    public boolean hasDirective() {
      return directive_ != null;
    }
    /**
     * <code>.notifications.ClearIndicatorDirectiveProto.Directive directive = 1;</code>
     * @return The directive.
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive getDirective() {
      return directive_ == null ? com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive.getDefaultInstance() : directive_;
    }
    /**
     * <code>.notifications.ClearIndicatorDirectiveProto.Directive directive = 1;</code>
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.DirectiveOrBuilder getDirectiveOrBuilder() {
      return getDirective();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (directive_ != null) {
        output.writeMessage(1, getDirective());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (directive_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getDirective());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto)) {
        return super.equals(obj);
      }
      com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto other = (com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto) obj;

      if (hasDirective() != other.hasDirective()) return false;
      if (hasDirective()) {
        if (!getDirective()
            .equals(other.getDirective())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasDirective()) {
        hash = (37 * hash) + DIRECTIVE_FIELD_NUMBER;
        hash = (53 * hash) + getDirective().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code notifications.ClearIndicatorDirectiveProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:notifications.ClearIndicatorDirectiveProto)
        com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.internal_static_notifications_ClearIndicatorDirectiveProto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.internal_static_notifications_ClearIndicatorDirectiveProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.class, com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Builder.class);
      }

      // Construct using com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (directiveBuilder_ == null) {
          directive_ = null;
        } else {
          directive_ = null;
          directiveBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.internal_static_notifications_ClearIndicatorDirectiveProto_descriptor;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto getDefaultInstanceForType() {
        return com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto build() {
        com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto buildPartial() {
        com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto result = new com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto(this);
        if (directiveBuilder_ == null) {
          result.directive_ = directive_;
        } else {
          result.directive_ = directiveBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto) {
          return mergeFrom((com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto other) {
        if (other == com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.getDefaultInstance()) return this;
        if (other.hasDirective()) {
          mergeDirective(other.getDirective());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getDirectiveFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive directive_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive, com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive.Builder, com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.DirectiveOrBuilder> directiveBuilder_;
      /**
       * <code>.notifications.ClearIndicatorDirectiveProto.Directive directive = 1;</code>
       * @return Whether the directive field is set.
       */
      public boolean hasDirective() {
        return directiveBuilder_ != null || directive_ != null;
      }
      /**
       * <code>.notifications.ClearIndicatorDirectiveProto.Directive directive = 1;</code>
       * @return The directive.
       */
      public com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive getDirective() {
        if (directiveBuilder_ == null) {
          return directive_ == null ? com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive.getDefaultInstance() : directive_;
        } else {
          return directiveBuilder_.getMessage();
        }
      }
      /**
       * <code>.notifications.ClearIndicatorDirectiveProto.Directive directive = 1;</code>
       */
      public Builder setDirective(com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive value) {
        if (directiveBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          directive_ = value;
          onChanged();
        } else {
          directiveBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.notifications.ClearIndicatorDirectiveProto.Directive directive = 1;</code>
       */
      public Builder setDirective(
          com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive.Builder builderForValue) {
        if (directiveBuilder_ == null) {
          directive_ = builderForValue.build();
          onChanged();
        } else {
          directiveBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.notifications.ClearIndicatorDirectiveProto.Directive directive = 1;</code>
       */
      public Builder mergeDirective(com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive value) {
        if (directiveBuilder_ == null) {
          if (directive_ != null) {
            directive_ =
              com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive.newBuilder(directive_).mergeFrom(value).buildPartial();
          } else {
            directive_ = value;
          }
          onChanged();
        } else {
          directiveBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.notifications.ClearIndicatorDirectiveProto.Directive directive = 1;</code>
       */
      public Builder clearDirective() {
        if (directiveBuilder_ == null) {
          directive_ = null;
          onChanged();
        } else {
          directive_ = null;
          directiveBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.notifications.ClearIndicatorDirectiveProto.Directive directive = 1;</code>
       */
      public com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive.Builder getDirectiveBuilder() {
        
        onChanged();
        return getDirectiveFieldBuilder().getBuilder();
      }
      /**
       * <code>.notifications.ClearIndicatorDirectiveProto.Directive directive = 1;</code>
       */
      public com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.DirectiveOrBuilder getDirectiveOrBuilder() {
        if (directiveBuilder_ != null) {
          return directiveBuilder_.getMessageOrBuilder();
        } else {
          return directive_ == null ?
              com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive.getDefaultInstance() : directive_;
        }
      }
      /**
       * <code>.notifications.ClearIndicatorDirectiveProto.Directive directive = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive, com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive.Builder, com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.DirectiveOrBuilder> 
          getDirectiveFieldBuilder() {
        if (directiveBuilder_ == null) {
          directiveBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive, com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.Directive.Builder, com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto.DirectiveOrBuilder>(
                  getDirective(),
                  getParentForChildren(),
                  isClean());
          directive_ = null;
        }
        return directiveBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:notifications.ClearIndicatorDirectiveProto)
    }

    // @@protoc_insertion_point(class_scope:notifications.ClearIndicatorDirectiveProto)
    private static final com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto();
    }

    public static com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ClearIndicatorDirectiveProto>
        PARSER = new com.google.protobuf.AbstractParser<ClearIndicatorDirectiveProto>() {
      @java.lang.Override
      public ClearIndicatorDirectiveProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ClearIndicatorDirectiveProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClearIndicatorDirectiveProto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirective.ClearIndicatorDirectiveProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_notifications_ClearIndicatorDirectiveProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_notifications_ClearIndicatorDirectiveProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_notifications_ClearIndicatorDirectiveProto_Directive_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_notifications_ClearIndicatorDirectiveProto_Directive_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n*notificationsClearIndicatorDirective.p" +
      "roto\022\rnotifications\032\025directiveHeader.pro" +
      "to\0321notificationsClearIndicatorDirective" +
      "Payload.proto\"\350\001\n\034ClearIndicatorDirectiv" +
      "eProto\022H\n\tdirective\030\001 \001(\01325.notification" +
      "s.ClearIndicatorDirectiveProto.Directive" +
      "\032~\n\tDirective\022C\n\007payload\030\002 \001(\01322.notific" +
      "ations.ClearIndicatorDirectivePayloadPro" +
      "to\022,\n\006header\030\001 \001(\0132\034.header.DirectiveHea" +
      "derProtoBG\n,com.amazon.proto.avs.v201602" +
      "07.notificationsB\027ClearIndicatorDirectiv" +
      "eb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.amazon.proto.avs.v20160207.header.DirectiveHeader.getDescriptor(),
          com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.getDescriptor(),
        });
    internal_static_notifications_ClearIndicatorDirectiveProto_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_notifications_ClearIndicatorDirectiveProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_notifications_ClearIndicatorDirectiveProto_descriptor,
        new java.lang.String[] { "Directive", });
    internal_static_notifications_ClearIndicatorDirectiveProto_Directive_descriptor =
      internal_static_notifications_ClearIndicatorDirectiveProto_descriptor.getNestedTypes().get(0);
    internal_static_notifications_ClearIndicatorDirectiveProto_Directive_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_notifications_ClearIndicatorDirectiveProto_Directive_descriptor,
        new java.lang.String[] { "Payload", "Header", });
    com.amazon.proto.avs.v20160207.header.DirectiveHeader.getDescriptor();
    com.amazon.proto.avs.v20160207.notifications.ClearIndicatorDirectivePayload.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
