<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6"
    >

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:orientation="vertical"
        android:padding="2dp">

        <include
            android:id="@+id/tool"
            layout="@layout/toolbar"
            />

        <View
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:layout_marginTop="30dp"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:text="@string/current_device"
            android:textSize="20sp" />

        <TextView
            android:id="@+id/ble_name"
            android:layout_marginTop="10dp"
            android:layout_marginLeft="20dp"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:text="--" />

        <Button
            android:id="@+id/pick_device"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/ota_click"
            android:text="@string/pleaseSelectDevice"
            android:textAllCaps="false"
            android:textColor="@color/white"
            />

        <Button
            android:id="@+id/connect_device"
            android:layout_width="wrap_content"
            android:layout_height="83dp"
            android:layout_gravity="center"
            android:layout_marginTop="-10dp"
            android:background="@drawable/ota_click"
            android:text="@string/connect_device"
            android:textAllCaps="false"
            android:textColor="@color/white" />


        <Button
            android:id="@+id/send_data"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_gravity="center"
            android:background="@drawable/ota_click"
            android:text="@string/wifiSendData"
            android:textAllCaps="false"
            android:textColor="@color/white"
            />

        <Button
            android:id="@+id/stop_vibrate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_gravity="center"
            android:background="@drawable/ota_click"
            android:text="@string/stop_vibrate"
            android:textAllCaps="false"
            android:textColor="@color/white"
            />
    </LinearLayout>

</ScrollView>