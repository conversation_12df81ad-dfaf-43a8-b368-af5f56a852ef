<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6">

    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_marginTop="20dp"
        android:orientation="horizontal"
        android:background="@color/white"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:layout_marginLeft="20dp"
            android:text="Broadcast ID"
            android:textSize="18sp"
            android:textColor="@color/btnDisableColor"/>

        <TextView
            android:id="@+id/broadcast_id"
            android:layout_width="match_parent"
            android:layout_height="28dp"
            android:textAlignment="textEnd"
            android:layout_marginRight="20dp"
            android:text="0XAACD55"
            android:textSize="18sp"
            android:textColor="@color/btnDisableColor"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:orientation="horizontal"
        android:background="@color/white"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:layout_marginLeft="20dp"
            android:text="Number Of Channel"
            android:textSize="18sp"
            android:textColor="@color/btnDisableColor"/>

        <TextView
            android:id="@+id/number_of_channel"
            android:layout_width="match_parent"
            android:layout_height="28dp"
            android:textAlignment="textEnd"
            android:layout_marginRight="20dp"
            android:text="2(Left, Right)"
            android:textSize="18sp"
            android:textColor="@color/btnDisableColor"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:orientation="horizontal"
        android:background="@color/white"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:layout_marginLeft="20dp"
            android:text="Sampling Rate"
            android:textSize="18sp"
            android:textColor="@color/btnDisableColor"/>

        <TextView
            android:id="@+id/sampling_rate"
            android:layout_width="match_parent"
            android:layout_height="28dp"
            android:textAlignment="textEnd"
            android:layout_marginRight="20dp"
            android:text="2400Hz"
            android:textSize="18sp"
            android:textColor="@color/btnDisableColor"/>
    </LinearLayout>


    <Button
        android:id="@+id/leave_btn"
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_gravity="center"
        android:background="@drawable/ota_click"
        android:text="Leave"
        android:textAllCaps="false"
        android:textColor="@color/white"
        />
</LinearLayout>



