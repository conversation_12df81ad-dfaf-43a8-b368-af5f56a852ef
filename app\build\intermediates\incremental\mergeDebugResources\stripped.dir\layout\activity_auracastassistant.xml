<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6">

    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <ListView
        android:id="@+id/bis_devices"
        android:layout_marginTop="20dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="260dp"/>





    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="-100dp"
        android:orientation="horizontal"
        android:weightSum="1">
        <Button
            android:id="@+id/scan_complete"
            android:layout_width="0dp"
            android:layout_weight="0.5"
            android:layout_height="wrap_content"
            android:layout_marginTop="-10dp"
            android:background="@drawable/ota_click"
            android:text="@string/scan_complete"
            android:textAllCaps="false"
            android:textColor="@color/white"
            />

        <Button
            android:id="@+id/refresh"
            android:layout_width="0dp"
            android:layout_weight="0.5"
            android:layout_height="wrap_content"
            android:layout_marginTop="-10dp"
            android:background="@drawable/ota_click"
            android:text="@string/refresh"
            android:textAllCaps="false"
            android:textColor="@color/white"
            />

    </LinearLayout>

    <TextView
        android:id="@+id/scan_QR_result"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="-190dp"/>

    <Button
        android:id="@+id/scan_QR"
        android:layout_width="214dp"
        android:layout_height="68dp"
        android:layout_gravity="center"
        android:background="@drawable/ota_click"
        android:text="@string/scan_QR"
        android:textAllCaps="false"
        android:textColor="@color/white" />

</LinearLayout>



