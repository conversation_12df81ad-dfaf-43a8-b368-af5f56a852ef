<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProtobufLanguageSettings">
    <option name="autoConfigEnabled" value="false" />
    <option name="importPathEntries">
      <list>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/src/test/java" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/build/generated/ap_generated_sources/debug/out" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/build/generated/ap_generated_sources/debug/out" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/build/generated/res/resValues/debug" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/build/generated/source/buildConfig/debug" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/build/generated/source/proto/debug/java" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/src/main/res" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/src/main/java" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/src/main/assets" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/src/main/jni" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/build/generated/res/resValues/androidTest/debug" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/build/generated/source/buildConfig/androidTest/debug" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/build/generated/source/buildConfig/androidTest/debug" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/src/androidTest/java" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$USER_HOME$/Library/Caches/Google/AndroidStudio2022.1/protoeditor" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/src/main/proto/AlexaGadgetsProtobuf/common" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/src/main/proto/AlexaGadgetsProtobuf/Alerts/DeleteAlert" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/src/main/proto/AlexaGadgetsProtobuf/Alexa.Gadget.StateListener/StateUpdate" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/src/main/proto/AlexaGadgetsProtobuf/Notifications/ClearIndicator" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/src/main/proto/AlexaGadgetsProtobuf/Notifications/SetIndicator" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/src/main/proto/AlexaGadgetsProtobuf/Alexa.Gadget.MusicData/Tempo" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/src/main/proto/AlexaGadgetsProtobuf/Alexa.Gadget.SpeechData/Speechmarks" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/src/main/proto/AlexaGadgetsProtobuf/Alexa.Discovery/Discover.Response" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/src/main/proto/AlexaGadgetsProtobuf/Alexa.Discovery/Discover" />
        </ImportPathEntry>
        <ImportPathEntry>
          <option name="location" value="file://$PROJECT_DIR$/app/src/main/proto" />
        </ImportPathEntry>
      </list>
    </option>
    <option name="descriptorPath" value="google/protobuf/descriptor.proto" />
  </component>
</project>