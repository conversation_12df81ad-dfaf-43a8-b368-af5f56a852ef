// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import com.github.mikephil.charting.charts.LineChart;
import com.suke.widget.SwitchButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityCapsensorBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LineChart capsensorChart;

  @NonNull
  public final TextView capsensorData;

  @NonNull
  public final Button capsensorSendBtn;

  @NonNull
  public final EditText capsensorSendText;

  @NonNull
  public final SwitchButton capsensorShowTouchSwitch;

  @NonNull
  public final CheckBox cn1;

  @NonNull
  public final CheckBox cn2;

  @NonNull
  public final CheckBox cn3;

  @NonNull
  public final CheckBox cn4;

  @NonNull
  public final CheckBox cn5;

  @NonNull
  public final CheckBox cn6;

  @NonNull
  public final CheckBox cn7;

  @NonNull
  public final CheckBox cn8;

  @NonNull
  public final Button continues;

  @NonNull
  public final TextView earStateText;

  @NonNull
  public final EditText etDataMultiple;

  @NonNull
  public final LogviewBinding loginfo;

  @NonNull
  public final EditText saveName;

  @NonNull
  public final Button start;

  @NonNull
  public final Button stop;

  @NonNull
  public final ToolbarBinding tool;

  @NonNull
  public final TextView touchEventText;

  private ActivityCapsensorBinding(@NonNull LinearLayout rootView,
      @NonNull LineChart capsensorChart, @NonNull TextView capsensorData,
      @NonNull Button capsensorSendBtn, @NonNull EditText capsensorSendText,
      @NonNull SwitchButton capsensorShowTouchSwitch, @NonNull CheckBox cn1, @NonNull CheckBox cn2,
      @NonNull CheckBox cn3, @NonNull CheckBox cn4, @NonNull CheckBox cn5, @NonNull CheckBox cn6,
      @NonNull CheckBox cn7, @NonNull CheckBox cn8, @NonNull Button continues,
      @NonNull TextView earStateText, @NonNull EditText etDataMultiple,
      @NonNull LogviewBinding loginfo, @NonNull EditText saveName, @NonNull Button start,
      @NonNull Button stop, @NonNull ToolbarBinding tool, @NonNull TextView touchEventText) {
    this.rootView = rootView;
    this.capsensorChart = capsensorChart;
    this.capsensorData = capsensorData;
    this.capsensorSendBtn = capsensorSendBtn;
    this.capsensorSendText = capsensorSendText;
    this.capsensorShowTouchSwitch = capsensorShowTouchSwitch;
    this.cn1 = cn1;
    this.cn2 = cn2;
    this.cn3 = cn3;
    this.cn4 = cn4;
    this.cn5 = cn5;
    this.cn6 = cn6;
    this.cn7 = cn7;
    this.cn8 = cn8;
    this.continues = continues;
    this.earStateText = earStateText;
    this.etDataMultiple = etDataMultiple;
    this.loginfo = loginfo;
    this.saveName = saveName;
    this.start = start;
    this.stop = stop;
    this.tool = tool;
    this.touchEventText = touchEventText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCapsensorBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCapsensorBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_capsensor, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCapsensorBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.capsensor_chart;
      LineChart capsensorChart = rootView.findViewById(id);
      if (capsensorChart == null) {
        break missingId;
      }

      id = R.id.capsensor_data;
      TextView capsensorData = rootView.findViewById(id);
      if (capsensorData == null) {
        break missingId;
      }

      id = R.id.capsensor_send_btn;
      Button capsensorSendBtn = rootView.findViewById(id);
      if (capsensorSendBtn == null) {
        break missingId;
      }

      id = R.id.capsensor_send_text;
      EditText capsensorSendText = rootView.findViewById(id);
      if (capsensorSendText == null) {
        break missingId;
      }

      id = R.id.capsensor_show_touch_switch;
      SwitchButton capsensorShowTouchSwitch = rootView.findViewById(id);
      if (capsensorShowTouchSwitch == null) {
        break missingId;
      }

      id = R.id.cn1;
      CheckBox cn1 = rootView.findViewById(id);
      if (cn1 == null) {
        break missingId;
      }

      id = R.id.cn2;
      CheckBox cn2 = rootView.findViewById(id);
      if (cn2 == null) {
        break missingId;
      }

      id = R.id.cn3;
      CheckBox cn3 = rootView.findViewById(id);
      if (cn3 == null) {
        break missingId;
      }

      id = R.id.cn4;
      CheckBox cn4 = rootView.findViewById(id);
      if (cn4 == null) {
        break missingId;
      }

      id = R.id.cn5;
      CheckBox cn5 = rootView.findViewById(id);
      if (cn5 == null) {
        break missingId;
      }

      id = R.id.cn6;
      CheckBox cn6 = rootView.findViewById(id);
      if (cn6 == null) {
        break missingId;
      }

      id = R.id.cn7;
      CheckBox cn7 = rootView.findViewById(id);
      if (cn7 == null) {
        break missingId;
      }

      id = R.id.cn8;
      CheckBox cn8 = rootView.findViewById(id);
      if (cn8 == null) {
        break missingId;
      }

      id = R.id.continues;
      Button continues = rootView.findViewById(id);
      if (continues == null) {
        break missingId;
      }

      id = R.id.ear_state_text;
      TextView earStateText = rootView.findViewById(id);
      if (earStateText == null) {
        break missingId;
      }

      id = R.id.et_data_multiple;
      EditText etDataMultiple = rootView.findViewById(id);
      if (etDataMultiple == null) {
        break missingId;
      }

      id = R.id.loginfo;
      View loginfo = rootView.findViewById(id);
      if (loginfo == null) {
        break missingId;
      }
      LogviewBinding binding_loginfo = LogviewBinding.bind(loginfo);

      id = R.id.saveName;
      EditText saveName = rootView.findViewById(id);
      if (saveName == null) {
        break missingId;
      }

      id = R.id.start;
      Button start = rootView.findViewById(id);
      if (start == null) {
        break missingId;
      }

      id = R.id.stop;
      Button stop = rootView.findViewById(id);
      if (stop == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      id = R.id.touch_event_text;
      TextView touchEventText = rootView.findViewById(id);
      if (touchEventText == null) {
        break missingId;
      }

      return new ActivityCapsensorBinding((LinearLayout) rootView, capsensorChart, capsensorData,
          capsensorSendBtn, capsensorSendText, capsensorShowTouchSwitch, cn1, cn2, cn3, cn4, cn5,
          cn6, cn7, cn8, continues, earStateText, etDataMultiple, binding_loginfo, saveName, start,
          stop, binding_tool, touchEventText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
