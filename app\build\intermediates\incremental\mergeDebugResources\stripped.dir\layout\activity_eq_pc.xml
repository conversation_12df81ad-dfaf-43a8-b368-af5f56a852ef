<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:tc="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingLeft="20dp"
    android:paddingRight="20dp"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6"
    >

    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />
    <RadioGroup
        android:id="@+id/radiogroup_function"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:layout_marginTop="20dp"
        android:orientation="horizontal">
        <RadioButton
            android:id="@+id/iir_eq"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:textColor="@color/black"
            android:text="IIR_EQ">

        </RadioButton>
        <RadioButton
            android:id="@+id/drc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="DRC">

        </RadioButton>
        <RadioButton
            android:id="@+id/limiter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="LIMITER">

        </RadioButton>
    </RadioGroup>

    <TextView
        android:id="@+id/msgview"
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:textSize="12sp"
        android:gravity="center|left"
        android:textAlignment="center"
        android:background="@color/colorAccent"
        android:text="TOTA Connected"
        >
    </TextView>

    <LinearLayout
        android:id="@+id/iir_eq_view_title"
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:layout_marginTop="10dp"
        android:orientation="horizontal"
        android:weightSum="20">
        <TextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="9"
            android:text="Enable Filter type"
            android:textSize="13sp"
            android:textColor="@color/black"
            android:textAlignment="textStart"/>
        <TextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:text="Gain[dB]"
            android:textSize="11sp"
            android:textColor="@color/black"
            android:textAlignment="textStart"/>
        <TextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="5"
            android:text="Freq[Hz]"
            android:textSize="11sp"
            android:textColor="@color/black"
            android:textAlignment="textStart"/>
        <TextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:text="Q"
            android:textSize="11sp"
            android:textColor="@color/black"
            android:textAlignment="textStart"/>
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerview_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="80dp"
        android:paddingBottom="20dp"
        />

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="-80dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:paddingBottom="5dp"
                android:gravity="center"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/preview"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:textAllCaps="false"
                    android:text="Preview">

                </Button>

                <Button
                    android:id="@+id/eq_function_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="more">

                </Button>

                <Button
                    android:id="@+id/audition"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:textAllCaps="false"
                    android:text="Audition">
                </Button>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:layout_marginTop="10dp"
                android:orientation="horizontal"
                android:weightSum="20"
                >

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:layout_weight="4"
                    android:text="Left Gain"
                    android:textAlignment="center"
                    >

                </TextView>

                <EditText
                    android:id="@+id/gain_left_et"
                    android:layout_width="0dp"
                    android:layout_weight="5"
                    android:layout_height="match_parent"
                    android:inputType="numberDecimal|numberSigned"
                    android:text="0">

                </EditText>

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="dB"
                    android:textAlignment="center"
                    android:textSize="10sp"
                    >

                </TextView>

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="4"
                    android:gravity="center"
                    android:text="Right Gain"
                    android:textAlignment="center"
                    >

                </TextView>

                <EditText
                    android:id="@+id/gain_right_et"
                    android:layout_width="0dp"
                    android:layout_weight="5"
                    android:layout_height="match_parent"
                    android:inputType="numberDecimal|numberSigned"
                    android:text="0">

                </EditText>

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="dB"
                    android:textAlignment="center"
                    android:textSize="10sp"
                    >

                </TextView>


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:layout_marginTop="10dp"
                android:orientation="horizontal"
                android:gravity="center"
                >
                <Button
                    android:id="@+id/eq_open_btn"
                    android:layout_width="150dp"
                    android:layout_height="match_parent"
                    android:text="EQ ON/OFF(OFF)">

                </Button>

                <Button
                    android:id="@+id/eq_writeflash_btn"
                    android:layout_width="150dp"
                    android:layout_height="match_parent"
                    android:textAllCaps="false"
                    android:text="Write to Flash">
                </Button>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:layout_marginTop="10dp"
                android:orientation="horizontal"
                android:weightSum="20"
                >

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:layout_weight="3"
                    android:text="EQ MODEL"
                    android:textSize="12sp"
                    android:textAlignment="center"
                    >

                </TextView>

                <Spinner
                    android:id="@+id/eq_model_spinner"
                    android:layout_width="0dp"
                    android:layout_weight="7"
                    android:layout_height="match_parent"
                    android:background="@color/activityText"
                    >

                </Spinner>


                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="3"
                    android:gravity="center"
                    android:text="IIR TYPE"
                    android:textSize="12sp"
                    android:textAlignment="center"
                    >

                </TextView>

                <Spinner
                    android:id="@+id/iir_type_spinner"
                    android:layout_width="0dp"
                    android:layout_weight="7"
                    android:layout_height="match_parent"
                    android:background="@color/activityText"
                    >

                </Spinner>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:layout_marginTop="10dp"
                android:orientation="horizontal"
                android:gravity="center"
                >
                <Button
                    android:id="@+id/save_as"
                    android:layout_width="150dp"
                    android:layout_height="match_parent"
                    android:text="Save as">

                </Button>

                <Button
                    android:id="@+id/load"
                    android:layout_width="150dp"
                    android:layout_height="match_parent"
                    android:textAllCaps="false"
                    android:text="Load">
                </Button>

            </LinearLayout>



        </LinearLayout>




</LinearLayout>