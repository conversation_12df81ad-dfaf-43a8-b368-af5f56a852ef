// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: alexaGadgetSpeechDataSpeechmarksDirectivePayload.proto

package com.amazon.proto.avs.v20160207.alexaGadgetSpeechData;

public final class SpeechmarksDirectivePayload {
  private SpeechmarksDirectivePayload() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SpeechmarksDirectivePayloadProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
     */
    java.util.List<com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData> 
        getSpeechmarksDataList();
    /**
     * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
     */
    com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData getSpeechmarksData(int index);
    /**
     * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
     */
    int getSpeechmarksDataCount();
    /**
     * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
     */
    java.util.List<? extends com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksDataOrBuilder> 
        getSpeechmarksDataOrBuilderList();
    /**
     * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
     */
    com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksDataOrBuilder getSpeechmarksDataOrBuilder(
        int index);

    /**
     * <code>int32 playerOffsetInMilliSeconds = 1;</code>
     * @return The playerOffsetInMilliSeconds.
     */
    int getPlayerOffsetInMilliSeconds();
  }
  /**
   * Protobuf type {@code alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto}
   */
  public static final class SpeechmarksDirectivePayloadProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto)
      SpeechmarksDirectivePayloadProtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SpeechmarksDirectivePayloadProto.newBuilder() to construct.
    private SpeechmarksDirectivePayloadProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SpeechmarksDirectivePayloadProto() {
      speechmarksData_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SpeechmarksDirectivePayloadProto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.class, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.Builder.class);
    }

    public interface SpeechmarksDataOrBuilder extends
        // @@protoc_insertion_point(interface_extends:alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>int32 startOffsetInMilliSeconds = 3;</code>
       * @return The startOffsetInMilliSeconds.
       */
      int getStartOffsetInMilliSeconds();

      /**
       * <code>string type = 2;</code>
       * @return The type.
       */
      java.lang.String getType();
      /**
       * <code>string type = 2;</code>
       * @return The bytes for type.
       */
      com.google.protobuf.ByteString
          getTypeBytes();

      /**
       * <code>string value = 1;</code>
       * @return The value.
       */
      java.lang.String getValue();
      /**
       * <code>string value = 1;</code>
       * @return The bytes for value.
       */
      com.google.protobuf.ByteString
          getValueBytes();
    }
    /**
     * Protobuf type {@code alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData}
     */
    public static final class SpeechmarksData extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData)
        SpeechmarksDataOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use SpeechmarksData.newBuilder() to construct.
      private SpeechmarksData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private SpeechmarksData() {
        type_ = "";
        value_ = "";
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new SpeechmarksData();
      }

      @java.lang.Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_SpeechmarksData_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_SpeechmarksData_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData.class, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData.Builder.class);
      }

      public static final int STARTOFFSETINMILLISECONDS_FIELD_NUMBER = 3;
      private int startOffsetInMilliSeconds_;
      /**
       * <code>int32 startOffsetInMilliSeconds = 3;</code>
       * @return The startOffsetInMilliSeconds.
       */
      @java.lang.Override
      public int getStartOffsetInMilliSeconds() {
        return startOffsetInMilliSeconds_;
      }

      public static final int TYPE_FIELD_NUMBER = 2;
      private volatile java.lang.Object type_;
      /**
       * <code>string type = 2;</code>
       * @return The type.
       */
      @java.lang.Override
      public java.lang.String getType() {
        java.lang.Object ref = type_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          type_ = s;
          return s;
        }
      }
      /**
       * <code>string type = 2;</code>
       * @return The bytes for type.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getTypeBytes() {
        java.lang.Object ref = type_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          type_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int VALUE_FIELD_NUMBER = 1;
      private volatile java.lang.Object value_;
      /**
       * <code>string value = 1;</code>
       * @return The value.
       */
      @java.lang.Override
      public java.lang.String getValue() {
        java.lang.Object ref = value_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          value_ = s;
          return s;
        }
      }
      /**
       * <code>string value = 1;</code>
       * @return The bytes for value.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getValueBytes() {
        java.lang.Object ref = value_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          value_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(value_)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 1, value_);
        }
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(type_)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 2, type_);
        }
        if (startOffsetInMilliSeconds_ != 0) {
          output.writeInt32(3, startOffsetInMilliSeconds_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(value_)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, value_);
        }
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(type_)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, type_);
        }
        if (startOffsetInMilliSeconds_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(3, startOffsetInMilliSeconds_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData)) {
          return super.equals(obj);
        }
        com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData other = (com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData) obj;

        if (getStartOffsetInMilliSeconds()
            != other.getStartOffsetInMilliSeconds()) return false;
        if (!getType()
            .equals(other.getType())) return false;
        if (!getValue()
            .equals(other.getValue())) return false;
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + STARTOFFSETINMILLISECONDS_FIELD_NUMBER;
        hash = (53 * hash) + getStartOffsetInMilliSeconds();
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getType().hashCode();
        hash = (37 * hash) + VALUE_FIELD_NUMBER;
        hash = (53 * hash) + getValue().hashCode();
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData)
          com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksDataOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_SpeechmarksData_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_SpeechmarksData_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData.class, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData.Builder.class);
        }

        // Construct using com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          startOffsetInMilliSeconds_ = 0;

          type_ = "";

          value_ = "";

          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_SpeechmarksData_descriptor;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData getDefaultInstanceForType() {
          return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData.getDefaultInstance();
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData build() {
          com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData buildPartial() {
          com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData result = new com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData(this);
          result.startOffsetInMilliSeconds_ = startOffsetInMilliSeconds_;
          result.type_ = type_;
          result.value_ = value_;
          onBuilt();
          return result;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData) {
            return mergeFrom((com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData other) {
          if (other == com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData.getDefaultInstance()) return this;
          if (other.getStartOffsetInMilliSeconds() != 0) {
            setStartOffsetInMilliSeconds(other.getStartOffsetInMilliSeconds());
          }
          if (!other.getType().isEmpty()) {
            type_ = other.type_;
            onChanged();
          }
          if (!other.getValue().isEmpty()) {
            value_ = other.value_;
            onChanged();
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 10: {
                  value_ = input.readStringRequireUtf8();

                  break;
                } // case 10
                case 18: {
                  type_ = input.readStringRequireUtf8();

                  break;
                } // case 18
                case 24: {
                  startOffsetInMilliSeconds_ = input.readInt32();

                  break;
                } // case 24
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }

        private int startOffsetInMilliSeconds_ ;
        /**
         * <code>int32 startOffsetInMilliSeconds = 3;</code>
         * @return The startOffsetInMilliSeconds.
         */
        @java.lang.Override
        public int getStartOffsetInMilliSeconds() {
          return startOffsetInMilliSeconds_;
        }
        /**
         * <code>int32 startOffsetInMilliSeconds = 3;</code>
         * @param value The startOffsetInMilliSeconds to set.
         * @return This builder for chaining.
         */
        public Builder setStartOffsetInMilliSeconds(int value) {
          
          startOffsetInMilliSeconds_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>int32 startOffsetInMilliSeconds = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearStartOffsetInMilliSeconds() {
          
          startOffsetInMilliSeconds_ = 0;
          onChanged();
          return this;
        }

        private java.lang.Object type_ = "";
        /**
         * <code>string type = 2;</code>
         * @return The type.
         */
        public java.lang.String getType() {
          java.lang.Object ref = type_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            type_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>string type = 2;</code>
         * @return The bytes for type.
         */
        public com.google.protobuf.ByteString
            getTypeBytes() {
          java.lang.Object ref = type_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            type_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string type = 2;</code>
         * @param value The type to set.
         * @return This builder for chaining.
         */
        public Builder setType(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  
          type_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>string type = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearType() {
          
          type_ = getDefaultInstance().getType();
          onChanged();
          return this;
        }
        /**
         * <code>string type = 2;</code>
         * @param value The bytes for type to set.
         * @return This builder for chaining.
         */
        public Builder setTypeBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
          
          type_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object value_ = "";
        /**
         * <code>string value = 1;</code>
         * @return The value.
         */
        public java.lang.String getValue() {
          java.lang.Object ref = value_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            value_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>string value = 1;</code>
         * @return The bytes for value.
         */
        public com.google.protobuf.ByteString
            getValueBytes() {
          java.lang.Object ref = value_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            value_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string value = 1;</code>
         * @param value The value to set.
         * @return This builder for chaining.
         */
        public Builder setValue(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  
          value_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>string value = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearValue() {
          
          value_ = getDefaultInstance().getValue();
          onChanged();
          return this;
        }
        /**
         * <code>string value = 1;</code>
         * @param value The bytes for value to set.
         * @return This builder for chaining.
         */
        public Builder setValueBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
          
          value_ = value;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData)
      }

      // @@protoc_insertion_point(class_scope:alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData)
      private static final com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData();
      }

      public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<SpeechmarksData>
          PARSER = new com.google.protobuf.AbstractParser<SpeechmarksData>() {
        @java.lang.Override
        public SpeechmarksData parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<SpeechmarksData> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<SpeechmarksData> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public static final int SPEECHMARKSDATA_FIELD_NUMBER = 2;
    private java.util.List<com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData> speechmarksData_;
    /**
     * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData> getSpeechmarksDataList() {
      return speechmarksData_;
    }
    /**
     * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksDataOrBuilder> 
        getSpeechmarksDataOrBuilderList() {
      return speechmarksData_;
    }
    /**
     * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
     */
    @java.lang.Override
    public int getSpeechmarksDataCount() {
      return speechmarksData_.size();
    }
    /**
     * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData getSpeechmarksData(int index) {
      return speechmarksData_.get(index);
    }
    /**
     * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksDataOrBuilder getSpeechmarksDataOrBuilder(
        int index) {
      return speechmarksData_.get(index);
    }

    public static final int PLAYEROFFSETINMILLISECONDS_FIELD_NUMBER = 1;
    private int playerOffsetInMilliSeconds_;
    /**
     * <code>int32 playerOffsetInMilliSeconds = 1;</code>
     * @return The playerOffsetInMilliSeconds.
     */
    @java.lang.Override
    public int getPlayerOffsetInMilliSeconds() {
      return playerOffsetInMilliSeconds_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (playerOffsetInMilliSeconds_ != 0) {
        output.writeInt32(1, playerOffsetInMilliSeconds_);
      }
      for (int i = 0; i < speechmarksData_.size(); i++) {
        output.writeMessage(2, speechmarksData_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (playerOffsetInMilliSeconds_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, playerOffsetInMilliSeconds_);
      }
      for (int i = 0; i < speechmarksData_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, speechmarksData_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto)) {
        return super.equals(obj);
      }
      com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto other = (com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto) obj;

      if (!getSpeechmarksDataList()
          .equals(other.getSpeechmarksDataList())) return false;
      if (getPlayerOffsetInMilliSeconds()
          != other.getPlayerOffsetInMilliSeconds()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getSpeechmarksDataCount() > 0) {
        hash = (37 * hash) + SPEECHMARKSDATA_FIELD_NUMBER;
        hash = (53 * hash) + getSpeechmarksDataList().hashCode();
      }
      hash = (37 * hash) + PLAYEROFFSETINMILLISECONDS_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerOffsetInMilliSeconds();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto)
        com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.class, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.Builder.class);
      }

      // Construct using com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (speechmarksDataBuilder_ == null) {
          speechmarksData_ = java.util.Collections.emptyList();
        } else {
          speechmarksData_ = null;
          speechmarksDataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        playerOffsetInMilliSeconds_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_descriptor;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto getDefaultInstanceForType() {
        return com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto build() {
        com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto buildPartial() {
        com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto result = new com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto(this);
        int from_bitField0_ = bitField0_;
        if (speechmarksDataBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            speechmarksData_ = java.util.Collections.unmodifiableList(speechmarksData_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.speechmarksData_ = speechmarksData_;
        } else {
          result.speechmarksData_ = speechmarksDataBuilder_.build();
        }
        result.playerOffsetInMilliSeconds_ = playerOffsetInMilliSeconds_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto) {
          return mergeFrom((com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto other) {
        if (other == com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.getDefaultInstance()) return this;
        if (speechmarksDataBuilder_ == null) {
          if (!other.speechmarksData_.isEmpty()) {
            if (speechmarksData_.isEmpty()) {
              speechmarksData_ = other.speechmarksData_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureSpeechmarksDataIsMutable();
              speechmarksData_.addAll(other.speechmarksData_);
            }
            onChanged();
          }
        } else {
          if (!other.speechmarksData_.isEmpty()) {
            if (speechmarksDataBuilder_.isEmpty()) {
              speechmarksDataBuilder_.dispose();
              speechmarksDataBuilder_ = null;
              speechmarksData_ = other.speechmarksData_;
              bitField0_ = (bitField0_ & ~0x00000001);
              speechmarksDataBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getSpeechmarksDataFieldBuilder() : null;
            } else {
              speechmarksDataBuilder_.addAllMessages(other.speechmarksData_);
            }
          }
        }
        if (other.getPlayerOffsetInMilliSeconds() != 0) {
          setPlayerOffsetInMilliSeconds(other.getPlayerOffsetInMilliSeconds());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                playerOffsetInMilliSeconds_ = input.readInt32();

                break;
              } // case 8
              case 18: {
                com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData m =
                    input.readMessage(
                        com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData.parser(),
                        extensionRegistry);
                if (speechmarksDataBuilder_ == null) {
                  ensureSpeechmarksDataIsMutable();
                  speechmarksData_.add(m);
                } else {
                  speechmarksDataBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData> speechmarksData_ =
        java.util.Collections.emptyList();
      private void ensureSpeechmarksDataIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          speechmarksData_ = new java.util.ArrayList<com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData>(speechmarksData_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData.Builder, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksDataOrBuilder> speechmarksDataBuilder_;

      /**
       * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
       */
      public java.util.List<com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData> getSpeechmarksDataList() {
        if (speechmarksDataBuilder_ == null) {
          return java.util.Collections.unmodifiableList(speechmarksData_);
        } else {
          return speechmarksDataBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
       */
      public int getSpeechmarksDataCount() {
        if (speechmarksDataBuilder_ == null) {
          return speechmarksData_.size();
        } else {
          return speechmarksDataBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData getSpeechmarksData(int index) {
        if (speechmarksDataBuilder_ == null) {
          return speechmarksData_.get(index);
        } else {
          return speechmarksDataBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
       */
      public Builder setSpeechmarksData(
          int index, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData value) {
        if (speechmarksDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSpeechmarksDataIsMutable();
          speechmarksData_.set(index, value);
          onChanged();
        } else {
          speechmarksDataBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
       */
      public Builder setSpeechmarksData(
          int index, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData.Builder builderForValue) {
        if (speechmarksDataBuilder_ == null) {
          ensureSpeechmarksDataIsMutable();
          speechmarksData_.set(index, builderForValue.build());
          onChanged();
        } else {
          speechmarksDataBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
       */
      public Builder addSpeechmarksData(com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData value) {
        if (speechmarksDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSpeechmarksDataIsMutable();
          speechmarksData_.add(value);
          onChanged();
        } else {
          speechmarksDataBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
       */
      public Builder addSpeechmarksData(
          int index, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData value) {
        if (speechmarksDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSpeechmarksDataIsMutable();
          speechmarksData_.add(index, value);
          onChanged();
        } else {
          speechmarksDataBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
       */
      public Builder addSpeechmarksData(
          com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData.Builder builderForValue) {
        if (speechmarksDataBuilder_ == null) {
          ensureSpeechmarksDataIsMutable();
          speechmarksData_.add(builderForValue.build());
          onChanged();
        } else {
          speechmarksDataBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
       */
      public Builder addSpeechmarksData(
          int index, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData.Builder builderForValue) {
        if (speechmarksDataBuilder_ == null) {
          ensureSpeechmarksDataIsMutable();
          speechmarksData_.add(index, builderForValue.build());
          onChanged();
        } else {
          speechmarksDataBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
       */
      public Builder addAllSpeechmarksData(
          java.lang.Iterable<? extends com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData> values) {
        if (speechmarksDataBuilder_ == null) {
          ensureSpeechmarksDataIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, speechmarksData_);
          onChanged();
        } else {
          speechmarksDataBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
       */
      public Builder clearSpeechmarksData() {
        if (speechmarksDataBuilder_ == null) {
          speechmarksData_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          speechmarksDataBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
       */
      public Builder removeSpeechmarksData(int index) {
        if (speechmarksDataBuilder_ == null) {
          ensureSpeechmarksDataIsMutable();
          speechmarksData_.remove(index);
          onChanged();
        } else {
          speechmarksDataBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData.Builder getSpeechmarksDataBuilder(
          int index) {
        return getSpeechmarksDataFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksDataOrBuilder getSpeechmarksDataOrBuilder(
          int index) {
        if (speechmarksDataBuilder_ == null) {
          return speechmarksData_.get(index);  } else {
          return speechmarksDataBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
       */
      public java.util.List<? extends com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksDataOrBuilder> 
           getSpeechmarksDataOrBuilderList() {
        if (speechmarksDataBuilder_ != null) {
          return speechmarksDataBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(speechmarksData_);
        }
      }
      /**
       * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData.Builder addSpeechmarksDataBuilder() {
        return getSpeechmarksDataFieldBuilder().addBuilder(
            com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData.getDefaultInstance());
      }
      /**
       * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData.Builder addSpeechmarksDataBuilder(
          int index) {
        return getSpeechmarksDataFieldBuilder().addBuilder(
            index, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData.getDefaultInstance());
      }
      /**
       * <code>repeated .alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto.SpeechmarksData speechmarksData = 2;</code>
       */
      public java.util.List<com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData.Builder> 
           getSpeechmarksDataBuilderList() {
        return getSpeechmarksDataFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData.Builder, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksDataOrBuilder> 
          getSpeechmarksDataFieldBuilder() {
        if (speechmarksDataBuilder_ == null) {
          speechmarksDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksData.Builder, com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto.SpeechmarksDataOrBuilder>(
                  speechmarksData_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          speechmarksData_ = null;
        }
        return speechmarksDataBuilder_;
      }

      private int playerOffsetInMilliSeconds_ ;
      /**
       * <code>int32 playerOffsetInMilliSeconds = 1;</code>
       * @return The playerOffsetInMilliSeconds.
       */
      @java.lang.Override
      public int getPlayerOffsetInMilliSeconds() {
        return playerOffsetInMilliSeconds_;
      }
      /**
       * <code>int32 playerOffsetInMilliSeconds = 1;</code>
       * @param value The playerOffsetInMilliSeconds to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerOffsetInMilliSeconds(int value) {
        
        playerOffsetInMilliSeconds_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 playerOffsetInMilliSeconds = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerOffsetInMilliSeconds() {
        
        playerOffsetInMilliSeconds_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto)
    }

    // @@protoc_insertion_point(class_scope:alexaGadgetSpeechData.SpeechmarksDirectivePayloadProto)
    private static final com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto();
    }

    public static com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SpeechmarksDirectivePayloadProto>
        PARSER = new com.google.protobuf.AbstractParser<SpeechmarksDirectivePayloadProto>() {
      @java.lang.Override
      public SpeechmarksDirectivePayloadProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SpeechmarksDirectivePayloadProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SpeechmarksDirectivePayloadProto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alexaGadgetSpeechData.SpeechmarksDirectivePayload.SpeechmarksDirectivePayloadProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_SpeechmarksData_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_SpeechmarksData_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n6alexaGadgetSpeechDataSpeechmarksDirect" +
      "ivePayload.proto\022\025alexaGadgetSpeechData\"" +
      "\373\001\n SpeechmarksDirectivePayloadProto\022`\n\017" +
      "speechmarksData\030\002 \003(\0132G.alexaGadgetSpeec" +
      "hData.SpeechmarksDirectivePayloadProto.S" +
      "peechmarksData\022\"\n\032playerOffsetInMilliSec" +
      "onds\030\001 \001(\005\032Q\n\017SpeechmarksData\022!\n\031startOf" +
      "fsetInMilliSeconds\030\003 \001(\005\022\014\n\004type\030\002 \001(\t\022\r" +
      "\n\005value\030\001 \001(\tBS\n4com.amazon.proto.avs.v2" +
      "0160207.alexaGadgetSpeechDataB\033Speechmar" +
      "ksDirectivePayloadb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_descriptor,
        new java.lang.String[] { "SpeechmarksData", "PlayerOffsetInMilliSeconds", });
    internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_SpeechmarksData_descriptor =
      internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_descriptor.getNestedTypes().get(0);
    internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_SpeechmarksData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_alexaGadgetSpeechData_SpeechmarksDirectivePayloadProto_SpeechmarksData_descriptor,
        new java.lang.String[] { "StartOffsetInMilliSeconds", "Type", "Value", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
