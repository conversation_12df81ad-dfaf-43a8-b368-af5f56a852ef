
package com.besall.allbase.bluetooth.service.audiodump;


import android.content.Context;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.base.BesBaseService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.message.BaseMessage;
import com.bes.sdk.utils.DeviceProtocol;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;

public class AudioDumpService extends BesBaseService {

    public AudioDumpService(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        super(serviceConfig, listener, context);
        startConnect(serviceConfig);
    }

    @Override
    public void onStatusChanged(HmDevice device, int status, DeviceProtocol protocol) {
        super.onStatusChanged(device, status, protocol);
        if (status == BES_CONNECT_ERROR ) {
            callBackStateChangedMessage(BES_CONNECT_ERROR,"error");
        }
    }

    @Override
    public void onDataReceived(BaseMessage deviceMessage) {
        super.onDataReceived(deviceMessage);
//        Log.i(TAG, "onDataReceived: -----" + ArrayUtil.toHex((byte[]) deviceMessage.getMsgContent()));
        if (mConfig.getTotaConnect() && !totauccess) {
            return;
        }

        String result = AudioDumpCMD.receiveData((byte[]) deviceMessage.getMsgContent(), mContext);
        if(result.length() > 20) {
            callBackStateChangedMessage(AudioDumpConstants.AUDIO_DUMP_START, result);
        }
//        if (result.length() > 0) {
//            callBackStateChangedMessage(BesSdkConstants.TOTA_LOG_INFO, result);
//        }

    }

    public void setInsertData(byte singleByte) {
        AudioDumpCMD.setInsertData(singleByte);
    }

    public void audioDumpStart() {
        if (totauccess) {
            sendData(AudioDumpCMD.audioDumpStart(), 5000);
        } else {
            callBackErrorMessage(BesSdkConstants.BES_TOTA_ERROR);
        }
    }

    public void audioDumpStop() {
        if (totauccess) {
            sendData(AudioDumpCMD.audioDumpStop(), 5000);
        } else {
            callBackErrorMessage(BesSdkConstants.BES_TOTA_ERROR);
        }
    }

}
