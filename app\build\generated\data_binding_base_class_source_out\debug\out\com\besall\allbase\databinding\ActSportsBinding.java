// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import com.github.mikephil.charting.charts.BarChart;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActSportsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final RadioGroup dataPattern1;

  @NonNull
  public final LogviewBinding loginfo;

  @NonNull
  public final RadioButton pattern00;

  @NonNull
  public final RadioButton pattern01;

  @NonNull
  public final RadioButton pattern02;

  @NonNull
  public final Bar<PERSON>hart sportsChart;

  @NonNull
  public final Button sportsReadStart;

  @NonNull
  public final ToolbarBinding tool;

  private ActSportsBinding(@NonNull LinearLayout rootView, @NonNull RadioGroup dataPattern1,
      @NonNull LogviewBinding loginfo, @NonNull RadioButton pattern00,
      @NonNull RadioButton pattern01, @NonNull RadioButton pattern02, @NonNull BarChart sportsChart,
      @NonNull Button sportsReadStart, @NonNull ToolbarBinding tool) {
    this.rootView = rootView;
    this.dataPattern1 = dataPattern1;
    this.loginfo = loginfo;
    this.pattern00 = pattern00;
    this.pattern01 = pattern01;
    this.pattern02 = pattern02;
    this.sportsChart = sportsChart;
    this.sportsReadStart = sportsReadStart;
    this.tool = tool;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActSportsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActSportsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.act_sports, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActSportsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.data_pattern1;
      RadioGroup dataPattern1 = rootView.findViewById(id);
      if (dataPattern1 == null) {
        break missingId;
      }

      id = R.id.loginfo;
      View loginfo = rootView.findViewById(id);
      if (loginfo == null) {
        break missingId;
      }
      LogviewBinding binding_loginfo = LogviewBinding.bind(loginfo);

      id = R.id.pattern_00;
      RadioButton pattern00 = rootView.findViewById(id);
      if (pattern00 == null) {
        break missingId;
      }

      id = R.id.pattern_01;
      RadioButton pattern01 = rootView.findViewById(id);
      if (pattern01 == null) {
        break missingId;
      }

      id = R.id.pattern_02;
      RadioButton pattern02 = rootView.findViewById(id);
      if (pattern02 == null) {
        break missingId;
      }

      id = R.id.sportsChart;
      BarChart sportsChart = rootView.findViewById(id);
      if (sportsChart == null) {
        break missingId;
      }

      id = R.id.sports_read_start;
      Button sportsReadStart = rootView.findViewById(id);
      if (sportsReadStart == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      return new ActSportsBinding((LinearLayout) rootView, dataPattern1, binding_loginfo, pattern00,
          pattern01, pattern02, sportsChart, sportsReadStart, binding_tool);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
