{"logs": [{"outputFile": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-uz\\values-uz.xml", "map": [{"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\7ad5be15223e864e4511b3f7157ae4d3\\appcompat-1.2.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "288,393,488,588,670,770,887,972,1050,1141,1234,1329,1423,1517,1610,1705,1800,1891,1983,2067,2177,2283,2383,2491,2597,2699,2860,7546", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "388,483,583,665,765,882,967,1045,1136,1229,1324,1418,1512,1605,1700,1795,1886,1978,2062,2172,2278,2378,2486,2592,2694,2855,2954,7625"}}, {"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\3a6c3290bf94c2fdaee81cec79dba243\\core-1.5.0-rc01\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "90", "startColumns": "4", "startOffsets": "7630", "endColumns": "100", "endOffsets": "7726"}}, {"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\ec1a13a01684407bf169e0545c40db84\\navigation-ui-2.4.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,156", "endColumns": "100,121", "endOffsets": "151,273"}, "to": {"startLines": "86,87", "startColumns": "4,4", "startOffsets": "7240,7341", "endColumns": "100,121", "endOffsets": "7336,7458"}}, {"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\85d353c95dd38846b16194b262acf36b\\material-1.4.0-beta01\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,321,416,543,627,691,794,864,931,1040,1107,1166,1240,1303,1357,1472,1530,1592,1646,1721,1850,1940,2029,2140,2222,2304,2390,2457,2523,2596,2674,2760,2832,2909,2984,3055,3149,3228,3324,3418,3492,3568,3654,3707,3773,3858,3949,4011,4075,4138,4240,4331,4427,4519", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,94,126,83,63,102,69,66,108,66,58,73,62,53,114,57,61,53,74,128,89,88,110,81,81,85,66,65,72,77,85,71,76,74,70,93,78,95,93,73,75,85,52,65,84,90,61,63,62,101,90,95,91,82", "endOffsets": "233,316,411,538,622,686,789,859,926,1035,1102,1161,1235,1298,1352,1467,1525,1587,1641,1716,1845,1935,2024,2135,2217,2299,2385,2452,2518,2591,2669,2755,2827,2904,2979,3050,3144,3223,3319,3413,3487,3563,3649,3702,3768,3853,3944,4006,4070,4133,4235,4326,4422,4514,4597"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2959,3042,3137,3264,3348,3412,3515,3585,3652,3761,3828,3887,3961,4024,4078,4193,4251,4313,4367,4442,4571,4661,4750,4861,4943,5025,5111,5178,5244,5317,5395,5481,5553,5630,5705,5776,5870,5949,6045,6139,6213,6289,6375,6428,6494,6579,6670,6732,6796,6859,6961,7052,7148,7463", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,88", "endColumns": "12,82,94,126,83,63,102,69,66,108,66,58,73,62,53,114,57,61,53,74,128,89,88,110,81,81,85,66,65,72,77,85,71,76,74,70,93,78,95,93,73,75,85,52,65,84,90,61,63,62,101,90,95,91,82", "endOffsets": "283,3037,3132,3259,3343,3407,3510,3580,3647,3756,3823,3882,3956,4019,4073,4188,4246,4308,4362,4437,4566,4656,4745,4856,4938,5020,5106,5173,5239,5312,5390,5476,5548,5625,5700,5771,5865,5944,6040,6134,6208,6284,6370,6423,6489,6574,6665,6727,6791,6854,6956,7047,7143,7235,7541"}}]}]}