<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="view_otalog" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\view_otalog.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/view_otalog_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="66" endOffset="51"/></Target><Target id="@+id/ota_log_scrollview" view="ScrollView"><Expressions/><location startLine="8" startOffset="8" endLine="26" endOffset="20"/></Target><Target id="@+id/ota_log_text" view="TextView"><Expressions/><location startLine="14" startOffset="12" endLine="25" endOffset="22"/></Target><Target id="@+id/ota_log_scrollview_short" view="ScrollView"><Expressions/><location startLine="28" startOffset="8" endLine="47" endOffset="20"/></Target><Target id="@+id/ota_log_text_short" view="TextView"><Expressions/><location startLine="35" startOffset="12" endLine="46" endOffset="22"/></Target><Target id="@+id/ota_over_btn" view="Button"><Expressions/><location startLine="49" startOffset="8" endLine="61" endOffset="16"/></Target></Targets></Layout>