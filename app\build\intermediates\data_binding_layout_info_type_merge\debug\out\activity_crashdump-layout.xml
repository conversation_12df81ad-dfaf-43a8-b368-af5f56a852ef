<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_crashdump" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_crashdump.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_crashdump_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="174" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/activity_crashdump_0" include="toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="12" endOffset="9"/></Target><Target id="@+id/loginfo" tag="layout/activity_crashdump_0" include="logview"><Expressions/><location startLine="14" startOffset="4" endLine="17" endOffset="34"/></Target><Target id="@+id/log_read_start" view="Button"><Expressions/><location startLine="25" startOffset="8" endLine="38" endOffset="61"/></Target><Target id="@+id/log_progress" view="ProgressBar"><Expressions/><location startLine="40" startOffset="8" endLine="51" endOffset="13"/></Target><Target id="@+id/total_log_status" view="TextView"><Expressions/><location startLine="59" startOffset="12" endLine="69" endOffset="17"/></Target><Target id="@+id/total_log_progress" view="TextView"><Expressions/><location startLine="80" startOffset="12" endLine="89" endOffset="48"/></Target><Target id="@+id/tasks_view" view="com.besall.allbase.common.utils.CircleProgressView"><Expressions/><location startLine="106" startOffset="8" endLine="115" endOffset="49"/></Target><Target id="@+id/current_log_pro" view="TextView"><Expressions/><location startLine="125" startOffset="8" endLine="134" endOffset="36"/></Target><Target id="@+id/percent" view="ImageView"><Expressions/><location startLine="136" startOffset="8" endLine="141" endOffset="52"/></Target><Target id="@+id/current_log_status" view="TextView"><Expressions/><location startLine="145" startOffset="4" endLine="155" endOffset="9"/></Target><Target id="@+id/spp_stop" view="Button"><Expressions/><location startLine="161" startOffset="4" endLine="171" endOffset="36"/></Target></Targets></Layout>