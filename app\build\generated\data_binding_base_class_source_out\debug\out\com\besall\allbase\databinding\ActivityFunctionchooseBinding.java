// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityFunctionchooseBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button FuncOta;

  @NonNull
  public final Button FuncTools;

  @NonNull
  public final Button goToGoogleAssistant;

  @NonNull
  public final ToolbarBinding tool;

  @NonNull
  public final Button watchTools;

  private ActivityFunctionchooseBinding(@NonNull ConstraintLayout rootView, @NonNull Button FuncOta,
      @NonNull Button FuncTools, @NonNull Button goToGoogleAssistant, @NonNull ToolbarBinding tool,
      @NonNull Button watchTools) {
    this.rootView = rootView;
    this.FuncOta = FuncOta;
    this.FuncTools = FuncTools;
    this.goToGoogleAssistant = goToGoogleAssistant;
    this.tool = tool;
    this.watchTools = watchTools;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityFunctionchooseBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityFunctionchooseBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_functionchoose, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityFunctionchooseBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.Func_ota;
      Button FuncOta = rootView.findViewById(id);
      if (FuncOta == null) {
        break missingId;
      }

      id = R.id.Func_tools;
      Button FuncTools = rootView.findViewById(id);
      if (FuncTools == null) {
        break missingId;
      }

      id = R.id.go_to_google_assistant;
      Button goToGoogleAssistant = rootView.findViewById(id);
      if (goToGoogleAssistant == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      id = R.id.watch_tools;
      Button watchTools = rootView.findViewById(id);
      if (watchTools == null) {
        break missingId;
      }

      return new ActivityFunctionchooseBinding((ConstraintLayout) rootView, FuncOta, FuncTools,
          goToGoogleAssistant, binding_tool, watchTools);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
