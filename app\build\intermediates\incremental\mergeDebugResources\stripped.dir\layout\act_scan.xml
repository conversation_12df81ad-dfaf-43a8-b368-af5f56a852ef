<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6"
    >

    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />
    <TextView
        android:id="@+id/tips_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="20dp"
        android:layout_gravity="center"
        android:textColor="@color/ff097bbf"
        android:text="@string/scanTips">

    </TextView>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="30dp"
        android:layout_marginTop="20dp"
        >

        <ListView
            android:id="@+id/devices"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</LinearLayout>