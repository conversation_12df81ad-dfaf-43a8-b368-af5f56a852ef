// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityCheckcrcBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button factoryReset;

  @NonNull
  public final Button getCrc;

  @NonNull
  public final LogviewBinding loginfo;

  @NonNull
  public final TextView showCrc;

  @NonNull
  public final ToolbarBinding tool;

  private ActivityCheckcrcBinding(@NonNull LinearLayout rootView, @NonNull Button factoryReset,
      @NonNull Button getCrc, @NonNull LogviewBinding loginfo, @NonNull TextView showCrc,
      @NonNull ToolbarBinding tool) {
    this.rootView = rootView;
    this.factoryReset = factoryReset;
    this.getCrc = getCrc;
    this.loginfo = loginfo;
    this.showCrc = showCrc;
    this.tool = tool;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCheckcrcBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCheckcrcBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_checkcrc, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCheckcrcBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.factory_reset;
      Button factoryReset = rootView.findViewById(id);
      if (factoryReset == null) {
        break missingId;
      }

      id = R.id.get_crc;
      Button getCrc = rootView.findViewById(id);
      if (getCrc == null) {
        break missingId;
      }

      id = R.id.loginfo;
      View loginfo = rootView.findViewById(id);
      if (loginfo == null) {
        break missingId;
      }
      LogviewBinding binding_loginfo = LogviewBinding.bind(loginfo);

      id = R.id.show_crc;
      TextView showCrc = rootView.findViewById(id);
      if (showCrc == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      return new ActivityCheckcrcBinding((LinearLayout) rootView, factoryReset, getCrc,
          binding_loginfo, showCrc, binding_tool);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
