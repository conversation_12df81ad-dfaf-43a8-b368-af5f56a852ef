// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityOtafunctionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button bleOtaV1;

  @NonNull
  public final Button bleOtaV2;

  @NonNull
  public final Button bleTotaotaV1;

  @NonNull
  public final Button bleTotaotaV2;

  @NonNull
  public final Button sppOtaV1;

  @NonNull
  public final Button sppOtaV1Old;

  @NonNull
  public final Button sppOtaV2;

  @NonNull
  public final Button sppTotaotaV1;

  @NonNull
  public final Button sppTotaotaV2;

  @NonNull
  public final ToolbarBinding tool;

  @NonNull
  public final Button usbTotaOta;

  private ActivityOtafunctionBinding(@NonNull LinearLayout rootView, @NonNull Button bleOtaV1,
      @NonNull Button bleOtaV2, @NonNull Button bleTotaotaV1, @NonNull Button bleTotaotaV2,
      @NonNull Button sppOtaV1, @NonNull Button sppOtaV1Old, @NonNull Button sppOtaV2,
      @NonNull Button sppTotaotaV1, @NonNull Button sppTotaotaV2, @NonNull ToolbarBinding tool,
      @NonNull Button usbTotaOta) {
    this.rootView = rootView;
    this.bleOtaV1 = bleOtaV1;
    this.bleOtaV2 = bleOtaV2;
    this.bleTotaotaV1 = bleTotaotaV1;
    this.bleTotaotaV2 = bleTotaotaV2;
    this.sppOtaV1 = sppOtaV1;
    this.sppOtaV1Old = sppOtaV1Old;
    this.sppOtaV2 = sppOtaV2;
    this.sppTotaotaV1 = sppTotaotaV1;
    this.sppTotaotaV2 = sppTotaotaV2;
    this.tool = tool;
    this.usbTotaOta = usbTotaOta;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityOtafunctionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityOtafunctionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_otafunction, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityOtafunctionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ble_ota_v1;
      Button bleOtaV1 = rootView.findViewById(id);
      if (bleOtaV1 == null) {
        break missingId;
      }

      id = R.id.ble_ota_v2;
      Button bleOtaV2 = rootView.findViewById(id);
      if (bleOtaV2 == null) {
        break missingId;
      }

      id = R.id.ble_totaota_v1;
      Button bleTotaotaV1 = rootView.findViewById(id);
      if (bleTotaotaV1 == null) {
        break missingId;
      }

      id = R.id.ble_totaota_v2;
      Button bleTotaotaV2 = rootView.findViewById(id);
      if (bleTotaotaV2 == null) {
        break missingId;
      }

      id = R.id.spp_ota_v1;
      Button sppOtaV1 = rootView.findViewById(id);
      if (sppOtaV1 == null) {
        break missingId;
      }

      id = R.id.spp_ota_v1_old;
      Button sppOtaV1Old = rootView.findViewById(id);
      if (sppOtaV1Old == null) {
        break missingId;
      }

      id = R.id.spp_ota_v2;
      Button sppOtaV2 = rootView.findViewById(id);
      if (sppOtaV2 == null) {
        break missingId;
      }

      id = R.id.spp_totaota_v1;
      Button sppTotaotaV1 = rootView.findViewById(id);
      if (sppTotaotaV1 == null) {
        break missingId;
      }

      id = R.id.spp_totaota_v2;
      Button sppTotaotaV2 = rootView.findViewById(id);
      if (sppTotaotaV2 == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      id = R.id.usb_tota_ota;
      Button usbTotaOta = rootView.findViewById(id);
      if (usbTotaOta == null) {
        break missingId;
      }

      return new ActivityOtafunctionBinding((LinearLayout) rootView, bleOtaV1, bleOtaV2,
          bleTotaotaV1, bleTotaotaV2, sppOtaV1, sppOtaV1Old, sppOtaV2, sppTotaotaV1, sppTotaotaV2,
          binding_tool, usbTotaOta);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
