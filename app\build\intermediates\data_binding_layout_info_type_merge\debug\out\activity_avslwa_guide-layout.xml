<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_avslwa_guide" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_avslwa_guide.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_avslwa_guide_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="72" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/activity_avslwa_guide_0" include="toolbar"><Expressions/><location startLine="10" startOffset="4" endLine="13" endOffset="9"/></Target><Target id="@+id/loginfo" tag="layout/activity_avslwa_guide_0" include="logview"><Expressions/><location startLine="15" startOffset="4" endLine="18" endOffset="34"/></Target><Target id="@+id/avs_guide_next" view="Button"><Expressions/><location startLine="43" startOffset="4" endLine="56" endOffset="12"/></Target><Target id="@+id/jump_alexa_app_textview" view="TextView"><Expressions/><location startLine="59" startOffset="4" endLine="69" endOffset="9"/></Target></Targets></Layout>