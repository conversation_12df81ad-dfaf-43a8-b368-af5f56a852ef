<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="logview" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\logview.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout" rootNodeViewId="@+id/loginfo"><Targets><Target id="@+id/loginfo" tag="layout/logview_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="47" endOffset="14"/></Target><Target id="@+id/scr_policy" view="ScrollView"><Expressions/><location startLine="9" startOffset="4" endLine="44" endOffset="16"/></Target><Target id="@+id/logV" view="TextView"><Expressions/><location startLine="24" startOffset="12" endLine="31" endOffset="50"/></Target><Target id="@+id/done" view="Button"><Expressions/><location startLine="32" startOffset="12" endLine="39" endOffset="34"/></Target></Targets></Layout>