<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_main_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="161" endOffset="14"/></Target><Target id="@+id/entry_btn" view="Button"><Expressions/><location startLine="34" startOffset="8" endLine="41" endOffset="16"/></Target><Target id="@+id/version_text" view="TextView"><Expressions/><location startLine="58" startOffset="8" endLine="65" endOffset="18"/></Target><Target id="@+id/privacy_policy" view="Button"><Expressions/><location startLine="68" startOffset="8" endLine="77" endOffset="16"/></Target><Target id="@+id/privacy_link" view="Button"><Expressions/><location startLine="79" startOffset="8" endLine="86" endOffset="13"/></Target><Target id="@+id/agree_view" view="LinearLayout"><Expressions/><location startLine="91" startOffset="4" endLine="159" endOffset="18"/></Target><Target id="@+id/agreeTV" view="TextView"><Expressions/><location startLine="110" startOffset="12" endLine="116" endOffset="22"/></Target><Target id="@+id/disagree" view="Button"><Expressions/><location startLine="126" startOffset="12" endLine="135" endOffset="20"/></Target><Target id="@+id/agree" view="Button"><Expressions/><location startLine="143" startOffset="12" endLine="153" endOffset="20"/></Target></Targets></Layout>