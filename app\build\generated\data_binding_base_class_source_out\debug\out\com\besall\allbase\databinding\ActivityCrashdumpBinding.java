// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import com.besall.allbase.common.utils.CircleProgressView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityCrashdumpBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView currentLogPro;

  @NonNull
  public final TextView currentLogStatus;

  @NonNull
  public final ProgressBar logProgress;

  @NonNull
  public final Button logReadStart;

  @NonNull
  public final LogviewBinding loginfo;

  @NonNull
  public final ImageView percent;

  @NonNull
  public final Button sppStop;

  @NonNull
  public final CircleProgressView tasksView;

  @NonNull
  public final ToolbarBinding tool;

  @NonNull
  public final TextView totalLogProgress;

  @NonNull
  public final TextView totalLogStatus;

  private ActivityCrashdumpBinding(@NonNull LinearLayout rootView, @NonNull TextView currentLogPro,
      @NonNull TextView currentLogStatus, @NonNull ProgressBar logProgress,
      @NonNull Button logReadStart, @NonNull LogviewBinding loginfo, @NonNull ImageView percent,
      @NonNull Button sppStop, @NonNull CircleProgressView tasksView, @NonNull ToolbarBinding tool,
      @NonNull TextView totalLogProgress, @NonNull TextView totalLogStatus) {
    this.rootView = rootView;
    this.currentLogPro = currentLogPro;
    this.currentLogStatus = currentLogStatus;
    this.logProgress = logProgress;
    this.logReadStart = logReadStart;
    this.loginfo = loginfo;
    this.percent = percent;
    this.sppStop = sppStop;
    this.tasksView = tasksView;
    this.tool = tool;
    this.totalLogProgress = totalLogProgress;
    this.totalLogStatus = totalLogStatus;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCrashdumpBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCrashdumpBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_crashdump, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCrashdumpBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.current_log_pro;
      TextView currentLogPro = rootView.findViewById(id);
      if (currentLogPro == null) {
        break missingId;
      }

      id = R.id.current_log_status;
      TextView currentLogStatus = rootView.findViewById(id);
      if (currentLogStatus == null) {
        break missingId;
      }

      id = R.id.log_progress;
      ProgressBar logProgress = rootView.findViewById(id);
      if (logProgress == null) {
        break missingId;
      }

      id = R.id.log_read_start;
      Button logReadStart = rootView.findViewById(id);
      if (logReadStart == null) {
        break missingId;
      }

      id = R.id.loginfo;
      View loginfo = rootView.findViewById(id);
      if (loginfo == null) {
        break missingId;
      }
      LogviewBinding binding_loginfo = LogviewBinding.bind(loginfo);

      id = R.id.percent;
      ImageView percent = rootView.findViewById(id);
      if (percent == null) {
        break missingId;
      }

      id = R.id.spp_stop;
      Button sppStop = rootView.findViewById(id);
      if (sppStop == null) {
        break missingId;
      }

      id = R.id.tasks_view;
      CircleProgressView tasksView = rootView.findViewById(id);
      if (tasksView == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      id = R.id.total_log_progress;
      TextView totalLogProgress = rootView.findViewById(id);
      if (totalLogProgress == null) {
        break missingId;
      }

      id = R.id.total_log_status;
      TextView totalLogStatus = rootView.findViewById(id);
      if (totalLogStatus == null) {
        break missingId;
      }

      return new ActivityCrashdumpBinding((LinearLayout) rootView, currentLogPro, currentLogStatus,
          logProgress, logReadStart, binding_loginfo, percent, sppStop, tasksView, binding_tool,
          totalLogProgress, totalLogStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
