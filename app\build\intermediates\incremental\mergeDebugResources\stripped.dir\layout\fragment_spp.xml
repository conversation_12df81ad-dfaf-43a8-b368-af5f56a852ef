<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent" android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6"
    >
<!--<LinearLayout-->
<!--    android:layout_width="match_parent"-->
<!--    android:layout_height="match_parent"-->
<!--    android:orientation="vertical">-->
    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <include
        android:id="@+id/loginfo"
        layout="@layout/logview"
        android:visibility="gone"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:layout_gravity="center"
                android:orientation="horizontal"
                android:background="@color/white">

                <TextView
                    android:layout_width="119dp"
                    android:layout_height="22dp"
                    android:layout_gravity="center"
                    android:layout_marginLeft="20dp"
                    android:layout_weight="1"
                    android:textSize="16sp"
                    android:textColor="#FF2C4662"
                    android:checked="true"
                    android:text="Is With reponse"/>

                <com.suke.widget.SwitchButton
                    android:id="@+id/spp_config_is_with_reponse_switch"
                    android:layout_width="51dp"
                    android:layout_height="30dp"
                    android:layout_gravity="center"
                    android:layout_marginLeft="165dp"
                    android:layout_marginRight="20dp"/>


            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />
            <LinearLayout
                android:id="@+id/ble_config_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="horizontal"
                android:background="@color/white"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="22dp"
                    android:layout_gravity="center"
                    android:layout_marginLeft="20dp"
                    android:textSize="16sp"
                    android:textColor="#FF2C4662"
                    android:checked="true"
                    android:text="Is use the Specific BLE connection param"/>

                <com.suke.widget.SwitchButton
                    android:id="@+id/ble_config_is_use_specific_connection_param_switch"
                    android:layout_width="51dp"
                    android:layout_height="30dp"
                    android:layout_gravity="center"

                    android:layout_marginRight="20dp"/>


            </LinearLayout>
            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical">



                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="@color/white"
                    >

                    <TextView
                        android:id="@+id/title_config_value"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:layout_marginLeft="20dp"
                        android:textSize="20sp"
                        android:textColor="#ff2c4662"
                        android:text="@string/title_config_value"/>

                    <TextView
                        android:id="@+id/test_data_title"
                        android:layout_width="140dp"
                        android:layout_height="22dp"
                        android:layout_marginLeft="20dp"
                        android:layout_marginTop="15dp"
                        android:text="ThroughPut Data :"
                        android:textColor="#ff2c4662"
                        android:textSize="16sp"
                        />


                    <RadioGroup
                        android:id="@+id/data_pattern1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:background="@color/white">

                        <RadioButton

                            android:id="@+id/pattern_00"
                            android:layout_weight="0.5"
                            android:layout_width="100dp"
                            android:layout_height="42dp"
                            android:layout_marginLeft="20dp"
                            android:checked="true"
                            android:padding="7dp"
                            android:textSize="15dp"
                            android:textColor="#ffafb8c3"
                            android:text="Random"
                            android:background="@drawable/btn_shape2"
                            style="@style/CustomRadioButton"/>

                        <RadioButton
                            android:id="@+id/pattern_01"
                            android:layout_weight="0.5"
                            android:layout_width="100dp"
                            android:layout_height="42dp"
                            android:padding="7dp"
                            android:textSize="15dp"
                            android:textColor="#ffafb8c3"
                            android:text="0XF0"
                            android:background="@drawable/btn_shape2"
                            style="@style/CustomRadioButton"/>

                        <RadioButton
                            android:id="@+id/pattern_02"
                            android:layout_weight="0.5"
                            android:layout_marginRight="20dp"
                            android:layout_width="100dp"
                            android:layout_height="42dp"
                            android:textSize="15dp"
                            android:padding="7dp"
                            android:textColor="#ffafb8c3"
                            android:text="OXAA"
                            android:background="@drawable/btn_shape2"
                            style="@style/CustomRadioButton"/>

                    </RadioGroup>

                    <RadioGroup
                        android:id="@+id/data_pattern2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="10dp"
                        android:background="@color/white">

                        <RadioButton
                            android:id="@+id/pattern_03"
                            android:layout_below="@id/pattern_00"
                            android:layout_weight="0.5"
                            android:layout_width="100dp"
                            android:layout_height="42dp"
                            android:layout_marginLeft="20dp"
                            android:textSize="15dp"
                            android:padding="7dp"
                            android:textColor="#ffafb8c3"
                            android:text="0XFF"
                            android:background="@drawable/btn_shape2"
                            style="@style/CustomRadioButton"/>

                        <RadioButton
                            android:id="@+id/pattern_04"
                            android:layout_weight="0.5"
                            android:layout_width="100dp"
                            android:layout_height="42dp"
                            android:padding="7dp"
                            android:textSize="15dp"
                            android:textColor="#ffafb8c3"
                            android:text="0X00"
                            android:background="@drawable/btn_shape2"
                            style="@style/CustomRadioButton"/>

                        <RadioButton
                            android:id="@+id/pattern_05"
                            android:layout_weight="0.5"
                            android:layout_width="100dp"
                            android:layout_height="42dp"
                            android:padding="7dp"
                            android:layout_marginRight="20dp"
                            android:textSize="15dp"
                            android:textColor="#ffafb8c3"
                            android:text="0X0F"
                            android:background="@drawable/btn_shape2"
                            style="@style/CustomRadioButton"/>
                    </RadioGroup>


                    <RadioGroup
                        android:id="@+id/data_pattern3"
                        android:layout_width="match_parent"
                        android:layout_height="60dp"
                        android:orientation="horizontal"
                        android:layout_marginTop="10dp"
                        android:background="@color/white">

                        <RadioButton
                            android:id="@+id/pattern_06"
                            android:layout_weight="0.4"
                            android:layout_width="100dp"
                            android:layout_height="42dp"
                            android:layout_marginLeft="20dp"
                            android:textSize="15dp"
                            android:padding="7dp"
                            android:textColor="#ffafb8c3"
                            android:text="0X55"
                            android:background="@drawable/btn_shape2"
                            style="@style/CustomRadioButton"/>

                    </RadioGroup>
                </LinearLayout>

            </LinearLayout>


            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:orientation="horizontal"
                android:background="@color/white">

                <TextView
                    android:id="@+id/spp_config_lasting_time_in_second_in_ms"
                    android:layout_gravity="center"
                    android:text="Last time in second:"
                    android:layout_width="156dp"
                    android:layout_height="22dp"
                    android:layout_marginLeft="20dp"
                    android:textColor="#ff2c4662"
                    android:textSize="16sp"/>

                <Button
                    android:id="@+id/intminus"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_gravity="center"
                    android:layout_marginLeft="30dp"
                    android:background="@drawable/minus" />

                <EditText
                    android:id="@+id/ltis"
                    android:layout_width="66dp"
                    android:layout_height="36dp"
                    android:layout_gravity="center"
                    android:layout_marginLeft="20dp"
                    android:text="10"

                    android:background="@drawable/edittext"
                    android:inputType="number"
                    android:layout_weight="0.8"/>

                <Button
                    android:id="@+id/intplus"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_gravity="center"
                    android:layout_marginRight="20dp"
                    android:background="@drawable/plus"/>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:orientation="horizontal"
                android:background="@color/white">

                <TextView
                    android:id="@+id/spp_config_data_size_text"
                    android:layout_gravity="center"
                    android:text="Data Size (es):"
                    android:layout_width="156dp"
                    android:layout_height="22dp"
                    android:layout_marginLeft="20dp"
                    android:textColor="#ff2c4662"
                    android:textSize="16sp"/>

                <Button
                    android:id="@+id/dataminus"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_gravity="center"
                    android:layout_marginLeft="30dp"
                    android:background="@drawable/minus"/>

                <EditText
                    android:id="@+id/datasize"
                    android:layout_width="66dp"
                    android:layout_height="36dp"
                    android:layout_gravity="center"
                    android:layout_marginLeft="20dp"
                    android:text="970"

                    android:inputType="number"
                    android:background="@drawable/edittext"
                    android:layout_weight="0.8"/>

                <Button
                    android:id="@+id/dataplus"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_gravity="center"
                    android:layout_marginRight="20dp"
                    android:background="@drawable/plus" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="58dp"

                android:background="@color/white">

                <TextView
                    android:id="@+id/title_real_value"
                    android:layout_width="153dp"
                    android:layout_height="28dp"
                    android:layout_gravity="center"
                    android:layout_marginLeft="20dp"
                    android:textSize="20sp"
                    android:textColor="#ff2c4662"
                    android:text="@string/title_real_value"/>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <ProgressBar
                android:id="@+id/spp_loading"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:background="@drawable/progress_bg"
                android:indeterminate="true"
                android:padding="20dp"
                android:visibility="gone" />


            <!--    <RelativeLayout-->
            <!--        android:id="@+id/button_layout"-->
            <!--        android:layout_marginBottom="30dp"-->
            <!--        android:orientation="vertical"-->
            <!--        android:layout_width="wrap_content"-->
            <!--        android:layout_height="wrap_content">-->

            <!--        <Button-->
            <!--            android:id="@+id/spp_connect_state"-->
            <!--            android:layout_alignParentBottom="true"-->
            <!--            android:layout_width="match_parent"-->
            <!--            android:layout_height="wrap_content"-->
            <!--            android:text="SPP connnected"-->
            <!--            android:gravity="center"-->
            <!--            android:textColor="#ffffff"-->
            <!--            android:textAllCaps="false"-->
            <!--            />-->


            <!--    </RelativeLayout>-->



            <LinearLayout
                android:id="@+id/title_real_value_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/ble_connection_interval_value_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#FF2C4662"
                    android:textSize="13sp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="10dp"
                    android:text="Connection interval in ms : [ Min = 10ms,Max = 20ms ]"
                    android:visibility="gone"/>

                <TextView
                    android:id="@+id/spp_data_size_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#FF2C4662"
                    android:textSize="13sp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="10dp"
                    android:text="Data size  : [ len = 509 bytes ]"/>

                <TextView
                    android:id="@+id/spp_response_type_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#FF2C4662"
                    android:textSize="13sp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="5dp"
                    android:text="Response type  : [ no response ]"/>

                <TextView
                    android:id="@+id/spp_up_through_rate_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#FF2C4662"
                    android:textSize="13sp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="5dp"
                    android:text="Up-Throughput rate  : [ -- byte/second ]"/>


                <TextView
                    android:id="@+id/spp_down_through_rate_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#FF2C4662"
                    android:textSize="13sp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="5dp"
                    android:text="Down-Throughput rate  : [ -- byte/second ]"/>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginTop="10dp"
                android:layout_marginLeft="20dp"
                />
            <LinearLayout
                android:id="@+id/ble_interval"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@color/white"
                android:visibility="gone"
                >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="#FF2C4662"
                        android:textSize="13sp"
                        android:textStyle="bold"
                        android:layout_margin="5dp"
                        android:text="Min Connection interval in ms :"/>

                    <EditText
                        android:id="@+id/ble_config_min_connection_interval_in_ms_edit"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="bottom"
                        android:layout_marginRight="6dp"
                        android:inputType="number"
                        android:text="10"

                        android:textColor="#FF2C4662"
                        android:textSize="13sp"
                        />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="#FF2C4662"
                        android:textSize="13sp"
                        android:textStyle="bold"
                        android:layout_margin="5dp"
                        android:text="Max Connection interval in ms :"/>

                    <EditText
                        android:id="@+id/ble_config_max_connection_interval_in_ms_edit"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="bottom"
                        android:inputType="number"
                        android:layout_marginRight="6dp"
                        android:text="20"

                        android:textColor="#FF2C4662"
                        android:textSize="13sp"
                        />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dip"
                    android:background="#FFE1E6EB"
                    android:layout_marginTop="10dp"
                    android:layout_marginLeft="20dp"
                    />

            </LinearLayout>



            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <Button
                    android:id="@+id/spp_through_up_btn"
                    android:layout_weight="1"
                    android:layout_marginLeft="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Through Up Start"
                    android:gravity="center"
                    android:textColor="#ffffff"
                    android:textAllCaps="false"
                    android:background="@drawable/ota_button_bg"
                    />

                <Button
                    android:id="@+id/spp_through_down_btn"
                    android:layout_weight="1"
                    android:layout_marginRight="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Through Down Start"
                    android:gravity="center"
                    android:textColor="#ffffff"
                    android:textAllCaps="false"
                    android:background="@drawable/ota_button_bg"
                    />


            </LinearLayout>

        </LinearLayout>

    </ScrollView>
<!--</LinearLayout>-->


</LinearLayout>