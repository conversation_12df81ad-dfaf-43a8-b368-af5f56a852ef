<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="act_otaui" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\act_otaui.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/act_otaui_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="74" endOffset="51"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="15" startOffset="4" endLine="71" endOffset="18"/></Target><Target id="@+id/tool" tag="binding_1" include="toolbar"><Expressions/><location startLine="19" startOffset="8" endLine="22" endOffset="13"/></Target><Target tag="binding_2" view="LinearLayout"><Expressions/><location startLine="24" startOffset="8" endLine="68" endOffset="22"/></Target><Target id="@+id/view_otaing" tag="binding_2" include="view_otaing"><Expressions/><location startLine="29" startOffset="12" endLine="35" endOffset="17"/></Target><Target id="@+id/view_device" tag="binding_2" include="view_device"><Expressions/><location startLine="37" startOffset="12" endLine="41" endOffset="17"/></Target><Target id="@+id/view_baseoption" tag="binding_2" include="view_baseoption"><Expressions/><location startLine="43" startOffset="12" endLine="48" endOffset="17"/></Target><Target id="@+id/view_versionpath" tag="binding_2" include="view_versionpath"><Expressions/><location startLine="49" startOffset="12" endLine="54" endOffset="17"/></Target><Target id="@+id/view_button" tag="binding_2" include="view_button"><Expressions/><location startLine="56" startOffset="12" endLine="61" endOffset="17"/></Target><Target id="@+id/view_otalog" tag="binding_2" include="view_otalog"><Expressions/><location startLine="62" startOffset="12" endLine="67" endOffset="17"/></Target><Target id="@+id/act_otaui_bg" view="ImageView"><Expressions/><location startLine="7" startOffset="4" endLine="14" endOffset="15"/></Target></Targets></Layout>