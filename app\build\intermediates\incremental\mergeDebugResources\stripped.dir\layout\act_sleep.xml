<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:tc="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6"
    >
    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <include
        android:id="@+id/loginfo"
        layout="@layout/logview"
        android:visibility="gone"/>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:layout_marginTop="20dp">




        <Button
            android:id="@+id/sleep_read_start"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:layout_gravity="center"
            android:text="OP_TOTA_RING_sleep_INFO"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:background="@drawable/rectangle_button3"
            />

        <com.github.mikephil.charting.charts.BarChart
            android:id="@+id/sleepChart"
            android:layout_marginTop="50dp"
            android:layout_width="match_parent"
            android:layout_height="400dp"/>

    </LinearLayout>




</LinearLayout>