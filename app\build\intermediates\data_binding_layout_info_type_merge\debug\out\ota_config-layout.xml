<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="ota_config" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\ota_config.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/ota_config_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="410" endOffset="12"/></Target><Target id="@+id/clear_user_data" view="RadioGroup"><Expressions/><location startLine="27" startOffset="12" endLine="67" endOffset="24"/></Target><Target id="@+id/clear_user_data_yes" view="RadioButton"><Expressions/><location startLine="44" startOffset="16" endLine="49" endOffset="21"/></Target><Target id="@+id/clear_user_data_no" view="RadioButton"><Expressions/><location startLine="62" startOffset="16" endLine="66" endOffset="21"/></Target><Target id="@+id/update_bt_addr" view="RadioGroup"><Expressions/><location startLine="84" startOffset="12" endLine="123" endOffset="24"/></Target><Target id="@+id/update_bt_addr_yes" view="RadioButton"><Expressions/><location startLine="100" startOffset="16" endLine="104" endOffset="21"/></Target><Target id="@+id/update_bt_addr_no" view="RadioButton"><Expressions/><location startLine="117" startOffset="16" endLine="122" endOffset="21"/></Target><Target id="@+id/update_bt_addr_input" view="EditText"><Expressions/><location startLine="137" startOffset="16" endLine="149" endOffset="21"/></Target><Target id="@+id/update_bt_name" view="RadioGroup"><Expressions/><location startLine="168" startOffset="12" endLine="207" endOffset="24"/></Target><Target id="@+id/update_bt_name_yes" view="RadioButton"><Expressions/><location startLine="184" startOffset="16" endLine="188" endOffset="21"/></Target><Target id="@+id/update_bt_name_no" view="RadioButton"><Expressions/><location startLine="201" startOffset="16" endLine="206" endOffset="21"/></Target><Target id="@+id/update_bt_name_input" view="EditText"><Expressions/><location startLine="209" startOffset="12" endLine="221" endOffset="17"/></Target><Target id="@+id/update_ble_addr" view="RadioGroup"><Expressions/><location startLine="239" startOffset="12" endLine="278" endOffset="24"/></Target><Target id="@+id/update_ble_addr_yes" view="RadioButton"><Expressions/><location startLine="255" startOffset="16" endLine="259" endOffset="21"/></Target><Target id="@+id/update_ble_addr_no" view="RadioButton"><Expressions/><location startLine="272" startOffset="16" endLine="277" endOffset="21"/></Target><Target id="@+id/update_ble_addr_input" view="EditText"><Expressions/><location startLine="292" startOffset="16" endLine="304" endOffset="21"/></Target><Target id="@+id/update_ble_name" view="RadioGroup"><Expressions/><location startLine="323" startOffset="12" endLine="362" endOffset="24"/></Target><Target id="@+id/update_ble_name_yes" view="RadioButton"><Expressions/><location startLine="339" startOffset="16" endLine="343" endOffset="21"/></Target><Target id="@+id/update_ble_name_no" view="RadioButton"><Expressions/><location startLine="356" startOffset="16" endLine="361" endOffset="21"/></Target><Target id="@+id/update_ble_name_input" view="EditText"><Expressions/><location startLine="364" startOffset="12" endLine="376" endOffset="17"/></Target><Target id="@+id/cancel" view="Button"><Expressions/><location startLine="385" startOffset="12" endLine="394" endOffset="44"/></Target><Target id="@+id/ok" view="Button"><Expressions/><location startLine="396" startOffset="12" endLine="407" endOffset="17"/></Target></Targets></Layout>