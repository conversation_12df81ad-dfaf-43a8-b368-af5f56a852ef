<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:tc="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6"
    >
    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <include
        android:id="@+id/loginfo"
        layout="@layout/logview"
        android:visibility="gone"/>

    <TextView
        android:id="@+id/watch_avs_login_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:textAlignment="center"
        android:text="@string/already_logged_in"/>

    <Button
        android:id="@+id/watch_avs_login_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/ota_click"
        android:text="@string/login_with_amazon"
        android:textAllCaps="false"
        android:textColor="@color/white"
        />

    <TextView
        android:id="@+id/watch_avs_device_state_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:textAlignment="center"
        android:text=""/>

    <Button
        android:id="@+id/watch_avs_connect_device_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/ota_click"
        android:text="@string/pleaseSelectDevice"
        android:textAllCaps="false"
        android:textColor="@color/white"
        />
    <Button
        android:id="@+id/watch_avs_record_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_gravity="center"
        android:background="@drawable/ota_click"
        android:text="@string/ready_record"
        android:textAllCaps="false"
        android:textColor="@color/white"
        />


</LinearLayout>