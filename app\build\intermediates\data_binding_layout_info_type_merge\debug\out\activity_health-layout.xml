<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_health" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_health.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_health_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="86" endOffset="51"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="6" startOffset="4" endLine="84" endOffset="18"/></Target><Target id="@+id/tool" tag="binding_1" include="toolbar"><Expressions/><location startLine="11" startOffset="8" endLine="14" endOffset="13"/></Target><Target id="@+id/buttonView" view="LinearLayout"><Expressions/><location startLine="31" startOffset="12" endLine="36" endOffset="48"/></Target></Targets></Layout>