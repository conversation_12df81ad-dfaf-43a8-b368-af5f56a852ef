// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import com.suke.widget.SwitchButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAvslwaResizeBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button languageSelectBtn;

  @NonNull
  public final LogviewBinding loginfo;

  @NonNull
  public final SeekBar seekbarAlertVolume;

  @NonNull
  public final SeekBar seekbarLighting;

  @NonNull
  public final SeekBar seekbarSpeakerVolume;

  @NonNull
  public final SeekBar seekbarUxLed;

  @NonNull
  public final SwitchButton switchButtonDoNotDisturb;

  @NonNull
  public final SwitchButton switchButtonEndLisening;

  @NonNull
  public final SwitchButton switchButtonStartLisening;

  @NonNull
  public final TextView testtest;

  @NonNull
  public final ToolbarBinding tool;

  private ActivityAvslwaResizeBinding(@NonNull LinearLayout rootView,
      @NonNull Button languageSelectBtn, @NonNull LogviewBinding loginfo,
      @NonNull SeekBar seekbarAlertVolume, @NonNull SeekBar seekbarLighting,
      @NonNull SeekBar seekbarSpeakerVolume, @NonNull SeekBar seekbarUxLed,
      @NonNull SwitchButton switchButtonDoNotDisturb, @NonNull SwitchButton switchButtonEndLisening,
      @NonNull SwitchButton switchButtonStartLisening, @NonNull TextView testtest,
      @NonNull ToolbarBinding tool) {
    this.rootView = rootView;
    this.languageSelectBtn = languageSelectBtn;
    this.loginfo = loginfo;
    this.seekbarAlertVolume = seekbarAlertVolume;
    this.seekbarLighting = seekbarLighting;
    this.seekbarSpeakerVolume = seekbarSpeakerVolume;
    this.seekbarUxLed = seekbarUxLed;
    this.switchButtonDoNotDisturb = switchButtonDoNotDisturb;
    this.switchButtonEndLisening = switchButtonEndLisening;
    this.switchButtonStartLisening = switchButtonStartLisening;
    this.testtest = testtest;
    this.tool = tool;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAvslwaResizeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAvslwaResizeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_avslwa_resize, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAvslwaResizeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.language_select_btn;
      Button languageSelectBtn = rootView.findViewById(id);
      if (languageSelectBtn == null) {
        break missingId;
      }

      id = R.id.loginfo;
      View loginfo = rootView.findViewById(id);
      if (loginfo == null) {
        break missingId;
      }
      LogviewBinding binding_loginfo = LogviewBinding.bind(loginfo);

      id = R.id.seekbar_alert_volume;
      SeekBar seekbarAlertVolume = rootView.findViewById(id);
      if (seekbarAlertVolume == null) {
        break missingId;
      }

      id = R.id.seekbar_lighting;
      SeekBar seekbarLighting = rootView.findViewById(id);
      if (seekbarLighting == null) {
        break missingId;
      }

      id = R.id.seekbar_speaker_volume;
      SeekBar seekbarSpeakerVolume = rootView.findViewById(id);
      if (seekbarSpeakerVolume == null) {
        break missingId;
      }

      id = R.id.seekbar_ux_led;
      SeekBar seekbarUxLed = rootView.findViewById(id);
      if (seekbarUxLed == null) {
        break missingId;
      }

      id = R.id.switchButton_do_not_disturb;
      SwitchButton switchButtonDoNotDisturb = rootView.findViewById(id);
      if (switchButtonDoNotDisturb == null) {
        break missingId;
      }

      id = R.id.switchButton_end_lisening;
      SwitchButton switchButtonEndLisening = rootView.findViewById(id);
      if (switchButtonEndLisening == null) {
        break missingId;
      }

      id = R.id.switchButton_start_lisening;
      SwitchButton switchButtonStartLisening = rootView.findViewById(id);
      if (switchButtonStartLisening == null) {
        break missingId;
      }

      id = R.id.testtest;
      TextView testtest = rootView.findViewById(id);
      if (testtest == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      return new ActivityAvslwaResizeBinding((LinearLayout) rootView, languageSelectBtn,
          binding_loginfo, seekbarAlertVolume, seekbarLighting, seekbarSpeakerVolume, seekbarUxLed,
          switchButtonDoNotDisturb, switchButtonEndLisening, switchButtonStartLisening, testtest,
          binding_tool);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
