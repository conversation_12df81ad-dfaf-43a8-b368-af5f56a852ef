<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_eq_iireq" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\item_eq_iireq.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_eq_iireq_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="77" endOffset="14"/></Target><Target id="@+id/radioBtn" view="RadioButton"><Expressions/><location startLine="9" startOffset="4" endLine="17" endOffset="9"/></Target><Target id="@+id/enableFilterType" view="Spinner"><Expressions/><location startLine="19" startOffset="4" endLine="26" endOffset="9"/></Target><Target id="@+id/gain_et" view="EditText"><Expressions/><location startLine="28" startOffset="4" endLine="40" endOffset="14"/></Target><Target id="@+id/freq" view="EditText"><Expressions/><location startLine="42" startOffset="4" endLine="54" endOffset="14"/></Target><Target id="@+id/q" view="EditText"><Expressions/><location startLine="56" startOffset="4" endLine="67" endOffset="14"/></Target></Targets></Layout>