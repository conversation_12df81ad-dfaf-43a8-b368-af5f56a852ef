// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActAboutusBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView actOtauiBg;

  @NonNull
  public final Button agree;

  @NonNull
  public final TextView agreeTV;

  @NonNull
  public final LinearLayout agreeView;

  @NonNull
  public final Button disagree;

  @NonNull
  public final Button privacyPolicy;

  @NonNull
  public final ToolbarBinding tool;

  @NonNull
  public final TextView versionText;

  private ActAboutusBinding(@NonNull ConstraintLayout rootView, @NonNull ImageView actOtauiBg,
      @NonNull Button agree, @NonNull TextView agreeTV, @NonNull LinearLayout agreeView,
      @NonNull Button disagree, @NonNull Button privacyPolicy, @NonNull ToolbarBinding tool,
      @NonNull TextView versionText) {
    this.rootView = rootView;
    this.actOtauiBg = actOtauiBg;
    this.agree = agree;
    this.agreeTV = agreeTV;
    this.agreeView = agreeView;
    this.disagree = disagree;
    this.privacyPolicy = privacyPolicy;
    this.tool = tool;
    this.versionText = versionText;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActAboutusBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActAboutusBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.act_aboutus, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActAboutusBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.act_otaui_bg;
      ImageView actOtauiBg = rootView.findViewById(id);
      if (actOtauiBg == null) {
        break missingId;
      }

      id = R.id.agree;
      Button agree = rootView.findViewById(id);
      if (agree == null) {
        break missingId;
      }

      id = R.id.agreeTV;
      TextView agreeTV = rootView.findViewById(id);
      if (agreeTV == null) {
        break missingId;
      }

      id = R.id.agree_view;
      LinearLayout agreeView = rootView.findViewById(id);
      if (agreeView == null) {
        break missingId;
      }

      id = R.id.disagree;
      Button disagree = rootView.findViewById(id);
      if (disagree == null) {
        break missingId;
      }

      id = R.id.privacy_policy;
      Button privacyPolicy = rootView.findViewById(id);
      if (privacyPolicy == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      id = R.id.version_text;
      TextView versionText = rootView.findViewById(id);
      if (versionText == null) {
        break missingId;
      }

      return new ActAboutusBinding((ConstraintLayout) rootView, actOtauiBg, agree, agreeTV,
          agreeView, disagree, privacyPolicy, binding_tool, versionText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
