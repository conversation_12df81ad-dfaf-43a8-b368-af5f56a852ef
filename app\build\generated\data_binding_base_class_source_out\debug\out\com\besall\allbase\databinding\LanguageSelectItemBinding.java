// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LanguageSelectItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView language;

  @NonNull
  public final ImageView languageSelectImage;

  private LanguageSelectItemBinding(@NonNull LinearLayout rootView, @NonNull TextView language,
      @NonNull ImageView languageSelectImage) {
    this.rootView = rootView;
    this.language = language;
    this.languageSelectImage = languageSelectImage;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LanguageSelectItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LanguageSelectItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.language_select_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LanguageSelectItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.language;
      TextView language = rootView.findViewById(id);
      if (language == null) {
        break missingId;
      }

      id = R.id.language_select_image;
      ImageView languageSelectImage = rootView.findViewById(id);
      if (languageSelectImage == null) {
        break missingId;
      }

      return new LanguageSelectItemBinding((LinearLayout) rootView, language, languageSelectImage);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
