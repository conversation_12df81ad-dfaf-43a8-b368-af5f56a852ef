<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_makedial" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_makedial.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_makedial_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="170" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/activity_makedial_0" include="toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="12" endOffset="13"/></Target><Target tag="layout/activity_makedial_0" include="dial_show_home_bgview"><Expressions/><location startLine="24" startOffset="4" endLine="31" endOffset="9"/></Target><Target id="@+id/cur_dial_size" view="TextView"><Expressions/><location startLine="15" startOffset="4" endLine="22" endOffset="39"/></Target><Target id="@+id/photo_resolution" view="TextView"><Expressions/><location startLine="33" startOffset="4" endLine="39" endOffset="39"/></Target><Target id="@+id/dial_button" view="Button"><Expressions/><location startLine="40" startOffset="4" endLine="50" endOffset="9"/></Target><Target id="@+id/list_view" view="ListView"><Expressions/><location startLine="59" startOffset="8" endLine="71" endOffset="18"/></Target><Target id="@+id/dial_tab_background_image" view="ImageView"><Expressions/><location startLine="91" startOffset="16" endLine="95" endOffset="67"/></Target><Target id="@+id/dial_tab_background_text" view="TextView"><Expressions/><location startLine="97" startOffset="16" endLine="104" endOffset="59"/></Target><Target id="@+id/dial_tab_style_image" view="ImageView"><Expressions/><location startLine="114" startOffset="16" endLine="118" endOffset="62"/></Target><Target id="@+id/dial_tab_style_text" view="TextView"><Expressions/><location startLine="120" startOffset="16" endLine="127" endOffset="54"/></Target><Target id="@+id/dial_tab_color_image" view="ImageView"><Expressions/><location startLine="137" startOffset="16" endLine="141" endOffset="62"/></Target><Target id="@+id/dial_tab_color_text" view="TextView"><Expressions/><location startLine="143" startOffset="16" endLine="150" endOffset="54"/></Target></Targets></Layout>