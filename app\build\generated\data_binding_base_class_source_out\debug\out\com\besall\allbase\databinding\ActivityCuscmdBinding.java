// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityCuscmdBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button ImportCmd;

  @NonNull
  public final ImageView accountLoginDetete;

  @NonNull
  public final EditText cmdEd;

  @NonNull
  public final RadioGroup cmdHeader;

  @NonNull
  public final RadioGroup cmdNeedEfffectiveLength;

  @NonNull
  public final RadioButton cmdNeedEfffectiveLengthNo;

  @NonNull
  public final RadioButton cmdNeedEfffectiveLengthYes;

  @NonNull
  public final Button cmdSave;

  @NonNull
  public final RelativeLayout cmdTyping;

  @NonNull
  public final TextView curCmdInfo;

  @NonNull
  public final ImageButton dataSend;

  @NonNull
  public final Spinner datamode;

  @NonNull
  public final TextView filePath;

  @NonNull
  public final TextView loginAccountTextview;

  @NonNull
  public final LogviewBinding loginfo;

  @NonNull
  public final RadioButton nouseCmdHeader;

  @NonNull
  public final TextView receiveInfo;

  @NonNull
  public final Button sppStop;

  @NonNull
  public final ToolbarBinding tool;

  @NonNull
  public final RadioButton useCmdHeader;

  private ActivityCuscmdBinding(@NonNull LinearLayout rootView, @NonNull Button ImportCmd,
      @NonNull ImageView accountLoginDetete, @NonNull EditText cmdEd, @NonNull RadioGroup cmdHeader,
      @NonNull RadioGroup cmdNeedEfffectiveLength, @NonNull RadioButton cmdNeedEfffectiveLengthNo,
      @NonNull RadioButton cmdNeedEfffectiveLengthYes, @NonNull Button cmdSave,
      @NonNull RelativeLayout cmdTyping, @NonNull TextView curCmdInfo,
      @NonNull ImageButton dataSend, @NonNull Spinner datamode, @NonNull TextView filePath,
      @NonNull TextView loginAccountTextview, @NonNull LogviewBinding loginfo,
      @NonNull RadioButton nouseCmdHeader, @NonNull TextView receiveInfo, @NonNull Button sppStop,
      @NonNull ToolbarBinding tool, @NonNull RadioButton useCmdHeader) {
    this.rootView = rootView;
    this.ImportCmd = ImportCmd;
    this.accountLoginDetete = accountLoginDetete;
    this.cmdEd = cmdEd;
    this.cmdHeader = cmdHeader;
    this.cmdNeedEfffectiveLength = cmdNeedEfffectiveLength;
    this.cmdNeedEfffectiveLengthNo = cmdNeedEfffectiveLengthNo;
    this.cmdNeedEfffectiveLengthYes = cmdNeedEfffectiveLengthYes;
    this.cmdSave = cmdSave;
    this.cmdTyping = cmdTyping;
    this.curCmdInfo = curCmdInfo;
    this.dataSend = dataSend;
    this.datamode = datamode;
    this.filePath = filePath;
    this.loginAccountTextview = loginAccountTextview;
    this.loginfo = loginfo;
    this.nouseCmdHeader = nouseCmdHeader;
    this.receiveInfo = receiveInfo;
    this.sppStop = sppStop;
    this.tool = tool;
    this.useCmdHeader = useCmdHeader;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCuscmdBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCuscmdBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_cuscmd, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCuscmdBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ImportCmd;
      Button ImportCmd = rootView.findViewById(id);
      if (ImportCmd == null) {
        break missingId;
      }

      id = R.id.account_login_detete;
      ImageView accountLoginDetete = rootView.findViewById(id);
      if (accountLoginDetete == null) {
        break missingId;
      }

      id = R.id.cmd_ed;
      EditText cmdEd = rootView.findViewById(id);
      if (cmdEd == null) {
        break missingId;
      }

      id = R.id.cmd_header;
      RadioGroup cmdHeader = rootView.findViewById(id);
      if (cmdHeader == null) {
        break missingId;
      }

      id = R.id.cmd_need_efffective_length;
      RadioGroup cmdNeedEfffectiveLength = rootView.findViewById(id);
      if (cmdNeedEfffectiveLength == null) {
        break missingId;
      }

      id = R.id.cmd_need_efffective_length_no;
      RadioButton cmdNeedEfffectiveLengthNo = rootView.findViewById(id);
      if (cmdNeedEfffectiveLengthNo == null) {
        break missingId;
      }

      id = R.id.cmd_need_efffective_length_yes;
      RadioButton cmdNeedEfffectiveLengthYes = rootView.findViewById(id);
      if (cmdNeedEfffectiveLengthYes == null) {
        break missingId;
      }

      id = R.id.cmd_save;
      Button cmdSave = rootView.findViewById(id);
      if (cmdSave == null) {
        break missingId;
      }

      id = R.id.cmd_typing;
      RelativeLayout cmdTyping = rootView.findViewById(id);
      if (cmdTyping == null) {
        break missingId;
      }

      id = R.id.cur_cmd_info;
      TextView curCmdInfo = rootView.findViewById(id);
      if (curCmdInfo == null) {
        break missingId;
      }

      id = R.id.data_send;
      ImageButton dataSend = rootView.findViewById(id);
      if (dataSend == null) {
        break missingId;
      }

      id = R.id.datamode;
      Spinner datamode = rootView.findViewById(id);
      if (datamode == null) {
        break missingId;
      }

      id = R.id.file_path;
      TextView filePath = rootView.findViewById(id);
      if (filePath == null) {
        break missingId;
      }

      id = R.id.login_account_textview;
      TextView loginAccountTextview = rootView.findViewById(id);
      if (loginAccountTextview == null) {
        break missingId;
      }

      id = R.id.loginfo;
      View loginfo = rootView.findViewById(id);
      if (loginfo == null) {
        break missingId;
      }
      LogviewBinding binding_loginfo = LogviewBinding.bind(loginfo);

      id = R.id.nouse_cmd_header;
      RadioButton nouseCmdHeader = rootView.findViewById(id);
      if (nouseCmdHeader == null) {
        break missingId;
      }

      id = R.id.receive_info;
      TextView receiveInfo = rootView.findViewById(id);
      if (receiveInfo == null) {
        break missingId;
      }

      id = R.id.spp_stop;
      Button sppStop = rootView.findViewById(id);
      if (sppStop == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      id = R.id.use_cmd_header;
      RadioButton useCmdHeader = rootView.findViewById(id);
      if (useCmdHeader == null) {
        break missingId;
      }

      return new ActivityCuscmdBinding((LinearLayout) rootView, ImportCmd, accountLoginDetete,
          cmdEd, cmdHeader, cmdNeedEfffectiveLength, cmdNeedEfffectiveLengthNo,
          cmdNeedEfffectiveLengthYes, cmdSave, cmdTyping, curCmdInfo, dataSend, datamode, filePath,
          loginAccountTextview, binding_loginfo, nouseCmdHeader, receiveInfo, sppStop, binding_tool,
          useCmdHeader);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
