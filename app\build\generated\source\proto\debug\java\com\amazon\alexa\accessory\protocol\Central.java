// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: central.proto

package com.amazon.alexa.accessory.protocol;

public final class Central {
  private Central() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code Platform}
   */
  public enum Platform
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>UNDEFINED = 0;</code>
     */
    UNDEFINED(0),
    /**
     * <code>IOS = 1;</code>
     */
    IOS(1),
    /**
     * <code>ANDROID = 2;</code>
     */
    ANDROID(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>UNDEFINED = 0;</code>
     */
    public static final int UNDEFINED_VALUE = 0;
    /**
     * <code>IOS = 1;</code>
     */
    public static final int IOS_VALUE = 1;
    /**
     * <code>ANDROID = 2;</code>
     */
    public static final int ANDROID_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static Platform valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static Platform forNumber(int value) {
      switch (value) {
        case 0: return UNDEFINED;
        case 1: return IOS;
        case 2: return ANDROID;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<Platform>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        Platform> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<Platform>() {
            public Platform findValueByNumber(int number) {
              return Platform.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Central.getDescriptor().getEnumTypes().get(0);
    }

    private static final Platform[] VALUES = values();

    public static Platform valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private Platform(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:Platform)
  }

  public interface CentralInformationOrBuilder extends
      // @@protoc_insertion_point(interface_extends:CentralInformation)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string name = 1;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <code>string name = 1;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <code>.Platform platform = 2;</code>
     * @return The enum numeric value on the wire for platform.
     */
    int getPlatformValue();
    /**
     * <code>.Platform platform = 2;</code>
     * @return The platform.
     */
    com.amazon.alexa.accessory.protocol.Central.Platform getPlatform();
  }
  /**
   * Protobuf type {@code CentralInformation}
   */
  public static final class CentralInformation extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:CentralInformation)
      CentralInformationOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CentralInformation.newBuilder() to construct.
    private CentralInformation(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CentralInformation() {
      name_ = "";
      platform_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CentralInformation();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Central.internal_static_CentralInformation_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Central.internal_static_CentralInformation_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Central.CentralInformation.class, com.amazon.alexa.accessory.protocol.Central.CentralInformation.Builder.class);
    }

    public static final int NAME_FIELD_NUMBER = 1;
    private volatile java.lang.Object name_;
    /**
     * <code>string name = 1;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <code>string name = 1;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PLATFORM_FIELD_NUMBER = 2;
    private int platform_;
    /**
     * <code>.Platform platform = 2;</code>
     * @return The enum numeric value on the wire for platform.
     */
    @java.lang.Override public int getPlatformValue() {
      return platform_;
    }
    /**
     * <code>.Platform platform = 2;</code>
     * @return The platform.
     */
    @java.lang.Override public com.amazon.alexa.accessory.protocol.Central.Platform getPlatform() {
      @SuppressWarnings("deprecation")
      com.amazon.alexa.accessory.protocol.Central.Platform result = com.amazon.alexa.accessory.protocol.Central.Platform.valueOf(platform_);
      return result == null ? com.amazon.alexa.accessory.protocol.Central.Platform.UNRECOGNIZED : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, name_);
      }
      if (platform_ != com.amazon.alexa.accessory.protocol.Central.Platform.UNDEFINED.getNumber()) {
        output.writeEnum(2, platform_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, name_);
      }
      if (platform_ != com.amazon.alexa.accessory.protocol.Central.Platform.UNDEFINED.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, platform_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Central.CentralInformation)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Central.CentralInformation other = (com.amazon.alexa.accessory.protocol.Central.CentralInformation) obj;

      if (!getName()
          .equals(other.getName())) return false;
      if (platform_ != other.platform_) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (37 * hash) + PLATFORM_FIELD_NUMBER;
      hash = (53 * hash) + platform_;
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Central.CentralInformation parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Central.CentralInformation parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Central.CentralInformation parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Central.CentralInformation parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Central.CentralInformation parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Central.CentralInformation parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Central.CentralInformation parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Central.CentralInformation parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Central.CentralInformation parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Central.CentralInformation parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Central.CentralInformation parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Central.CentralInformation parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Central.CentralInformation prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code CentralInformation}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:CentralInformation)
        com.amazon.alexa.accessory.protocol.Central.CentralInformationOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Central.internal_static_CentralInformation_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Central.internal_static_CentralInformation_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Central.CentralInformation.class, com.amazon.alexa.accessory.protocol.Central.CentralInformation.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Central.CentralInformation.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        name_ = "";

        platform_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Central.internal_static_CentralInformation_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Central.CentralInformation getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Central.CentralInformation.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Central.CentralInformation build() {
        com.amazon.alexa.accessory.protocol.Central.CentralInformation result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Central.CentralInformation buildPartial() {
        com.amazon.alexa.accessory.protocol.Central.CentralInformation result = new com.amazon.alexa.accessory.protocol.Central.CentralInformation(this);
        result.name_ = name_;
        result.platform_ = platform_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Central.CentralInformation) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Central.CentralInformation)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Central.CentralInformation other) {
        if (other == com.amazon.alexa.accessory.protocol.Central.CentralInformation.getDefaultInstance()) return this;
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        if (other.platform_ != 0) {
          setPlatformValue(other.getPlatformValue());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                name_ = input.readStringRequireUtf8();

                break;
              } // case 10
              case 16: {
                platform_ = input.readEnum();

                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>string name = 1;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string name = 1;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string name = 1;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string name = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>string name = 1;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }

      private int platform_ = 0;
      /**
       * <code>.Platform platform = 2;</code>
       * @return The enum numeric value on the wire for platform.
       */
      @java.lang.Override public int getPlatformValue() {
        return platform_;
      }
      /**
       * <code>.Platform platform = 2;</code>
       * @param value The enum numeric value on the wire for platform to set.
       * @return This builder for chaining.
       */
      public Builder setPlatformValue(int value) {
        
        platform_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.Platform platform = 2;</code>
       * @return The platform.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Central.Platform getPlatform() {
        @SuppressWarnings("deprecation")
        com.amazon.alexa.accessory.protocol.Central.Platform result = com.amazon.alexa.accessory.protocol.Central.Platform.valueOf(platform_);
        return result == null ? com.amazon.alexa.accessory.protocol.Central.Platform.UNRECOGNIZED : result;
      }
      /**
       * <code>.Platform platform = 2;</code>
       * @param value The platform to set.
       * @return This builder for chaining.
       */
      public Builder setPlatform(com.amazon.alexa.accessory.protocol.Central.Platform value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        platform_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.Platform platform = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlatform() {
        
        platform_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:CentralInformation)
    }

    // @@protoc_insertion_point(class_scope:CentralInformation)
    private static final com.amazon.alexa.accessory.protocol.Central.CentralInformation DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Central.CentralInformation();
    }

    public static com.amazon.alexa.accessory.protocol.Central.CentralInformation getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CentralInformation>
        PARSER = new com.google.protobuf.AbstractParser<CentralInformation>() {
      @java.lang.Override
      public CentralInformation parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<CentralInformation> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CentralInformation> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Central.CentralInformation getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetCentralInformationOrBuilder extends
      // @@protoc_insertion_point(interface_extends:GetCentralInformation)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code GetCentralInformation}
   */
  public static final class GetCentralInformation extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:GetCentralInformation)
      GetCentralInformationOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetCentralInformation.newBuilder() to construct.
    private GetCentralInformation(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetCentralInformation() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetCentralInformation();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Central.internal_static_GetCentralInformation_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Central.internal_static_GetCentralInformation_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Central.GetCentralInformation.class, com.amazon.alexa.accessory.protocol.Central.GetCentralInformation.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Central.GetCentralInformation)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Central.GetCentralInformation other = (com.amazon.alexa.accessory.protocol.Central.GetCentralInformation) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Central.GetCentralInformation parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Central.GetCentralInformation parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Central.GetCentralInformation parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Central.GetCentralInformation parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Central.GetCentralInformation parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Central.GetCentralInformation parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Central.GetCentralInformation parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Central.GetCentralInformation parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Central.GetCentralInformation parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Central.GetCentralInformation parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Central.GetCentralInformation parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Central.GetCentralInformation parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Central.GetCentralInformation prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code GetCentralInformation}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:GetCentralInformation)
        com.amazon.alexa.accessory.protocol.Central.GetCentralInformationOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Central.internal_static_GetCentralInformation_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Central.internal_static_GetCentralInformation_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Central.GetCentralInformation.class, com.amazon.alexa.accessory.protocol.Central.GetCentralInformation.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Central.GetCentralInformation.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Central.internal_static_GetCentralInformation_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Central.GetCentralInformation getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Central.GetCentralInformation.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Central.GetCentralInformation build() {
        com.amazon.alexa.accessory.protocol.Central.GetCentralInformation result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Central.GetCentralInformation buildPartial() {
        com.amazon.alexa.accessory.protocol.Central.GetCentralInformation result = new com.amazon.alexa.accessory.protocol.Central.GetCentralInformation(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Central.GetCentralInformation) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Central.GetCentralInformation)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Central.GetCentralInformation other) {
        if (other == com.amazon.alexa.accessory.protocol.Central.GetCentralInformation.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:GetCentralInformation)
    }

    // @@protoc_insertion_point(class_scope:GetCentralInformation)
    private static final com.amazon.alexa.accessory.protocol.Central.GetCentralInformation DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Central.GetCentralInformation();
    }

    public static com.amazon.alexa.accessory.protocol.Central.GetCentralInformation getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GetCentralInformation>
        PARSER = new com.google.protobuf.AbstractParser<GetCentralInformation>() {
      @java.lang.Override
      public GetCentralInformation parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<GetCentralInformation> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetCentralInformation> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Central.GetCentralInformation getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_CentralInformation_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_CentralInformation_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_GetCentralInformation_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_GetCentralInformation_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rcentral.proto\"?\n\022CentralInformation\022\014\n" +
      "\004name\030\001 \001(\t\022\033\n\010platform\030\002 \001(\0162\t.Platform" +
      "\"\027\n\025GetCentralInformation*/\n\010Platform\022\r\n" +
      "\tUNDEFINED\020\000\022\007\n\003IOS\020\001\022\013\n\007ANDROID\020\002B0\n#co" +
      "m.amazon.alexa.accessory.protocolH\003\240\001\001\242\002" +
      "\003AACb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_CentralInformation_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_CentralInformation_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_CentralInformation_descriptor,
        new java.lang.String[] { "Name", "Platform", });
    internal_static_GetCentralInformation_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_GetCentralInformation_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_GetCentralInformation_descriptor,
        new java.lang.String[] { });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
