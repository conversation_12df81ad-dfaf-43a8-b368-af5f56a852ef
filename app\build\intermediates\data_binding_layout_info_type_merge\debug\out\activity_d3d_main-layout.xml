<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_d3d_main" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_d3d_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_d3d_main_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="119" endOffset="12"/></Target><Target id="@+id/classBt_btn" view="Button"><Expressions/><location startLine="12" startOffset="8" endLine="17" endOffset="37"/></Target><Target id="@+id/clear" view="Button"><Expressions/><location startLine="20" startOffset="8" endLine="27" endOffset="37"/></Target><Target id="@+id/sensor_start" view="Button"><Expressions/><location startLine="29" startOffset="8" endLine="36" endOffset="37"/></Target><Target id="@+id/sensor_stop" view="Button"><Expressions/><location startLine="38" startOffset="8" endLine="45" endOffset="37"/></Target><Target id="@+id/period_num" view="EditText"><Expressions/><location startLine="47" startOffset="8" endLine="53" endOffset="18"/></Target><Target id="@+id/sensor_config" view="Button"><Expressions/><location startLine="55" startOffset="8" endLine="62" endOffset="37"/></Target><Target id="@+id/play_file_name" view="TextView"><Expressions/><location startLine="64" startOffset="8" endLine="68" endOffset="31"/></Target><Target id="@+id/play_file" view="Button"><Expressions/><location startLine="74" startOffset="12" endLine="79" endOffset="57"/></Target><Target id="@+id/stop_file" view="Button"><Expressions/><location startLine="81" startOffset="12" endLine="86" endOffset="56"/></Target><Target id="@+id/select_file" view="Button"><Expressions/><location startLine="88" startOffset="12" endLine="92" endOffset="51"/></Target><Target id="@+id/sensor_info" view="TextView"><Expressions/><location startLine="107" startOffset="8" endLine="113" endOffset="18"/></Target></Targets></Layout>