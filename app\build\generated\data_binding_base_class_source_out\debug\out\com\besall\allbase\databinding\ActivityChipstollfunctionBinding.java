// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityChipstollfunctionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button EQ;

  @NonNull
  public final Button audioDump;

  @NonNull
  public final Button auracastAssistant;

  @NonNull
  public final Button avslwa;

  @NonNull
  public final Button bleWifi;

  @NonNull
  public final Button capsensor;

  @NonNull
  public final Button checkCrc;

  @NonNull
  public final Button commandSet;

  @NonNull
  public final Button crashDump;

  @NonNull
  public final Button customCommand;

  @NonNull
  public final Button logDump;

  @NonNull
  public final Button rssi;

  @NonNull
  public final Button rssiExtend;

  @NonNull
  public final Button smartvoice;

  @NonNull
  public final Button throughput;

  @NonNull
  public final ToolbarBinding tool;

  private ActivityChipstollfunctionBinding(@NonNull LinearLayout rootView, @NonNull Button EQ,
      @NonNull Button audioDump, @NonNull Button auracastAssistant, @NonNull Button avslwa,
      @NonNull Button bleWifi, @NonNull Button capsensor, @NonNull Button checkCrc,
      @NonNull Button commandSet, @NonNull Button crashDump, @NonNull Button customCommand,
      @NonNull Button logDump, @NonNull Button rssi, @NonNull Button rssiExtend,
      @NonNull Button smartvoice, @NonNull Button throughput, @NonNull ToolbarBinding tool) {
    this.rootView = rootView;
    this.EQ = EQ;
    this.audioDump = audioDump;
    this.auracastAssistant = auracastAssistant;
    this.avslwa = avslwa;
    this.bleWifi = bleWifi;
    this.capsensor = capsensor;
    this.checkCrc = checkCrc;
    this.commandSet = commandSet;
    this.crashDump = crashDump;
    this.customCommand = customCommand;
    this.logDump = logDump;
    this.rssi = rssi;
    this.rssiExtend = rssiExtend;
    this.smartvoice = smartvoice;
    this.throughput = throughput;
    this.tool = tool;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityChipstollfunctionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityChipstollfunctionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_chipstollfunction, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityChipstollfunctionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.EQ;
      Button EQ = rootView.findViewById(id);
      if (EQ == null) {
        break missingId;
      }

      id = R.id.audio_dump;
      Button audioDump = rootView.findViewById(id);
      if (audioDump == null) {
        break missingId;
      }

      id = R.id.auracast_assistant;
      Button auracastAssistant = rootView.findViewById(id);
      if (auracastAssistant == null) {
        break missingId;
      }

      id = R.id.avslwa;
      Button avslwa = rootView.findViewById(id);
      if (avslwa == null) {
        break missingId;
      }

      id = R.id.ble_wifi;
      Button bleWifi = rootView.findViewById(id);
      if (bleWifi == null) {
        break missingId;
      }

      id = R.id.capsensor;
      Button capsensor = rootView.findViewById(id);
      if (capsensor == null) {
        break missingId;
      }

      id = R.id.check_crc;
      Button checkCrc = rootView.findViewById(id);
      if (checkCrc == null) {
        break missingId;
      }

      id = R.id.command_set;
      Button commandSet = rootView.findViewById(id);
      if (commandSet == null) {
        break missingId;
      }

      id = R.id.crash_dump;
      Button crashDump = rootView.findViewById(id);
      if (crashDump == null) {
        break missingId;
      }

      id = R.id.custom_command;
      Button customCommand = rootView.findViewById(id);
      if (customCommand == null) {
        break missingId;
      }

      id = R.id.log_dump;
      Button logDump = rootView.findViewById(id);
      if (logDump == null) {
        break missingId;
      }

      id = R.id.rssi;
      Button rssi = rootView.findViewById(id);
      if (rssi == null) {
        break missingId;
      }

      id = R.id.rssi_extend;
      Button rssiExtend = rootView.findViewById(id);
      if (rssiExtend == null) {
        break missingId;
      }

      id = R.id.smartvoice;
      Button smartvoice = rootView.findViewById(id);
      if (smartvoice == null) {
        break missingId;
      }

      id = R.id.throughput;
      Button throughput = rootView.findViewById(id);
      if (throughput == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      return new ActivityChipstollfunctionBinding((LinearLayout) rootView, EQ, audioDump,
          auracastAssistant, avslwa, bleWifi, capsensor, checkCrc, commandSet, crashDump,
          customCommand, logDump, rssi, rssiExtend, smartvoice, throughput, binding_tool);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
