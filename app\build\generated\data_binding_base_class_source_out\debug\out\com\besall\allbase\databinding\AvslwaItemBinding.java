// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import com.besall.allbase.common.utils.SlideLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class AvslwaItemBinding implements ViewBinding {
  @NonNull
  private final SlideLayout rootView;

  @NonNull
  public final LinearLayout content;

  @NonNull
  public final TextView deleteButton;

  @NonNull
  public final TextView name;

  private AvslwaItemBinding(@NonNull SlideLayout rootView, @NonNull LinearLayout content,
      @NonNull TextView deleteButton, @NonNull TextView name) {
    this.rootView = rootView;
    this.content = content;
    this.deleteButton = deleteButton;
    this.name = name;
  }

  @Override
  @NonNull
  public SlideLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static AvslwaItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static AvslwaItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.avslwa_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static AvslwaItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.content;
      LinearLayout content = rootView.findViewById(id);
      if (content == null) {
        break missingId;
      }

      id = R.id.delete_button;
      TextView deleteButton = rootView.findViewById(id);
      if (deleteButton == null) {
        break missingId;
      }

      id = R.id.name;
      TextView name = rootView.findViewById(id);
      if (name == null) {
        break missingId;
      }

      return new AvslwaItemBinding((SlideLayout) rootView, content, deleteButton, name);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
