syntax = "proto3";

package alerts;

option java_package = "com.amazon.proto.avs.v20160207.alerts";

option java_outer_classname = "DeleteAlertDirective";

import "directiveHeader.proto";
import "alertsDeleteAlertDirectivePayload.proto";

message DeleteAlertDirectiveProto {
	Directive directive = 1;
	message Directive {
		alerts.DeleteAlertDirectivePayloadProto payload = 2;
		header.DirectiveHeaderProto header = 1;
	}
}
