<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@mipmap/bes_bg6"
    >
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        >
        <include
            android:id="@+id/tool"
            layout="@layout/toolbar"
            />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dip"
            android:background="#FFE1E6EB"
            android:layout_marginTop="34dp"
            />
        <Button
            android:id="@+id/Func_ota"
            android:layout_width="match_parent"
            android:layout_height="58dp"
            android:gravity="left|center_vertical"
            android:textSize="20dp"
            android:textColor="@color/ff2c4662"
            android:padding="20dp"
            android:background="@drawable/rectangle_longbtn"
            android:drawableLeft="@drawable/home_icon_ota"
            android:drawableRight="@drawable/home_icon_arrow"
            android:text="  OTA"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dip"
            android:layout_marginLeft="20dp"
            android:background="#FFE1E6EB"
            />

        <Button
            android:id="@+id/Func_tools"
            android:layout_width="match_parent"
            android:layout_height="58dp"
            android:layout_gravity="center"
            android:padding="20dp"
            android:gravity="left|center_vertical"
            android:background="@drawable/rectangle_longbtn"
            android:drawableLeft="@drawable/home_icon_chipstools"
            android:drawableRight="@drawable/home_icon_arrow"
            android:textSize="20dp"
            android:textColor="@color/ff2c4662"
            android:text="  Chips Tools"/>

        <View
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="0.5dip"
            android:layout_marginLeft="20dp"
            android:background="#FFE1E6EB"
            />
        <Button
            android:id="@+id/watch_tools"
            android:layout_width="match_parent"
            android:layout_height="58dp"
            android:layout_gravity="center"
            android:padding="20dp"
            android:gravity="left|center_vertical"
            android:background="@drawable/rectangle_longbtn"
            android:drawableLeft="@drawable/home_icon_chipstools"
            android:drawableRight="@drawable/home_icon_arrow"
            android:textSize="20dp"
            android:textColor="@color/ff2c4662"
            android:text="  WATCH Tools"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dip"
            android:background="#FFE1E6EB"
            />
        <Button
            android:id="@+id/go_to_google_assistant"
            android:layout_width="match_parent"
            android:layout_height="58dp"
            android:layout_gravity="center"
            android:padding="20dp"
            android:gravity="left|center_vertical"
            android:background="@drawable/rectangle_longbtn"
            android:drawableLeft="@drawable/home_icon_chipstools"
            android:drawableRight="@drawable/home_icon_arrow"
            android:textSize="20dp"
            android:textColor="@color/ff2c4662"
            android:text="  Google Assistant App"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dip"
            android:background="#FFE1E6EB"
            />


    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>