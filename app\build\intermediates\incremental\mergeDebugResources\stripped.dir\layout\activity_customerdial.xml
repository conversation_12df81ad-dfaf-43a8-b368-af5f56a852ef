<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@mipmap/bes_bg6"
    android:orientation="vertical">

    <include
        android:id="@+id/tool"
        layout="@layout/toolbar" />

    <include
        android:id="@+id/loginfo"
        layout="@layout/logview"
        android:visibility="gone" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="20dp">

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="fill_parent"
            android:orientation="vertical"
            android:padding="2dp">

            <TextView
                android:id="@+id/current_device"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:padding="5dp"
                android:textAlignment="center"
                android:textColor="@color/btnDisableColor"
                android:textSize="13sp" />

            <EditText
                android:id="@+id/address_text"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:textAlignment="center"/>
            <Button
                android:id="@+id/choose_device"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@drawable/ota_click"
                android:text="@string/change_device_tota_spp"
                android:textAllCaps="false"
                android:textColor="@color/white" />

            <ImageView
                android:id="@+id/show_dial_photo"
                android:layout_width="160dp"
                android:layout_height="190dp"
                android:layout_gravity="center"
                android:visibility="gone" />

            <Button
                android:id="@+id/choose_dial"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@drawable/ota_click"
                android:text="@string/choose_customer_dial"
                android:textAllCaps="false"
                android:textColor="@color/white" />

            <HorizontalScrollView
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:fadeScrollbars="false">

                <RadioGroup
                    android:id="@+id/radio_group_file_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/choose_online_dial" />

                    <RadioButton
                        android:id="@+id/radio_button_file_type_online"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:text="@string/choose_picture" />

                    <RadioButton
                        android:id="@+id/radio_button_file_type_picture"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:text="@string/choose_font_library" />

                    <RadioButton
                        android:id="@+id/radio_button_file_type_font"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:text="@string/choose_tp_firmware" />

                    <RadioButton
                        android:id="@+id/radio_button_file_type_tp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:text="@string/choose_heart_rate_firmware" />

                    <RadioButton
                        android:id="@+id/radio_button_file_type_heart_rate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:text="@string/choose_language_packet" />

                    <RadioButton
                        android:id="@+id/radio_button_file_type_language"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:text="ota boot" />

                    <RadioButton
                        android:id="@+id/radio_button_file_type_ota_boot"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </RadioGroup>


            </HorizontalScrollView>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/use_incremental_upgrade"
                    android:textAlignment="center"
                    android:textSize="16sp" />

                <com.suke.widget.SwitchButton
                    android:id="@+id/switchButton_use_diff_upgrade"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="5dp"
                    app:sb_show_indicator="false" />


            </LinearLayout>

            <TextView
                android:id="@+id/show_old_file_path"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:textAlignment="center"
                android:textColor="@color/btnDisableColor"
                android:textSize="13sp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/show_dial_dfu_path"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:textAlignment="center"
                android:textColor="@color/btnDisableColor"
                android:textSize="13sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal"
                android:weightSum="10">

                <Button
                    android:id="@+id/choose_old_file"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_weight="5"
                    android:background="@drawable/ota_click"
                    android:text="@string/choose_old_version_file"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    android:visibility="gone" />

                <Button
                    android:id="@+id/choose_dfu_file"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_weight="5"
                    android:background="@drawable/ota_click"
                    android:text="@string/choose_dfu_file"
                    android:textAllCaps="false"
                    android:textColor="@color/white" />

            </LinearLayout>

            <Button
                android:id="@+id/start_transfer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@drawable/ota_button_bg_press"
                android:enabled="false"
                android:text="@string/start_transfer_tips"
                android:textAllCaps="false"
                android:textColor="@color/white" />

            <ProgressBar
                android:id="@+id/transfer_progress"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:max="100"
                android:progress="0" />

            <TextView
                android:id="@+id/transfer_percent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="0"
                android:textAlignment="center"
                android:textColor="@color/title_color_light"
                android:textSize="18sp" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>