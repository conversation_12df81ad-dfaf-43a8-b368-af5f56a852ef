// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAvslwaLoginBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button loginWithAmazon;

  @NonNull
  public final LogviewBinding loginfo;

  @NonNull
  public final TextView profileInfo;

  @NonNull
  public final ToolbarBinding tool;

  private ActivityAvslwaLoginBinding(@NonNull LinearLayout rootView,
      @NonNull Button loginWithAmazon, @NonNull LogviewBinding loginfo,
      @NonNull TextView profileInfo, @NonNull ToolbarBinding tool) {
    this.rootView = rootView;
    this.loginWithAmazon = loginWithAmazon;
    this.loginfo = loginfo;
    this.profileInfo = profileInfo;
    this.tool = tool;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAvslwaLoginBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAvslwaLoginBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_avslwa_login, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAvslwaLoginBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.login_with_amazon;
      Button loginWithAmazon = rootView.findViewById(id);
      if (loginWithAmazon == null) {
        break missingId;
      }

      id = R.id.loginfo;
      View loginfo = rootView.findViewById(id);
      if (loginfo == null) {
        break missingId;
      }
      LogviewBinding binding_loginfo = LogviewBinding.bind(loginfo);

      id = R.id.profile_info;
      TextView profileInfo = rootView.findViewById(id);
      if (profileInfo == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      return new ActivityAvslwaLoginBinding((LinearLayout) rootView, loginWithAmazon,
          binding_loginfo, profileInfo, binding_tool);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
