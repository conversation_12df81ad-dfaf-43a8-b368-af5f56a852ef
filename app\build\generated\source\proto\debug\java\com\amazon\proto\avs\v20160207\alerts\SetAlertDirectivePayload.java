// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: alertsSetAlertDirectivePayload.proto

package com.amazon.proto.avs.v20160207.alerts;

public final class SetAlertDirectivePayload {
  private SetAlertDirectivePayload() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SetAlertDirectivePayloadProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:alerts.SetAlertDirectivePayloadProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated string assetPlayOrder = 5;</code>
     * @return A list containing the assetPlayOrder.
     */
    java.util.List<java.lang.String>
        getAssetPlayOrderList();
    /**
     * <code>repeated string assetPlayOrder = 5;</code>
     * @return The count of assetPlayOrder.
     */
    int getAssetPlayOrderCount();
    /**
     * <code>repeated string assetPlayOrder = 5;</code>
     * @param index The index of the element to return.
     * @return The assetPlayOrder at the given index.
     */
    java.lang.String getAssetPlayOrder(int index);
    /**
     * <code>repeated string assetPlayOrder = 5;</code>
     * @param index The index of the value to return.
     * @return The bytes of the assetPlayOrder at the given index.
     */
    com.google.protobuf.ByteString
        getAssetPlayOrderBytes(int index);

    /**
     * <code>int32 loopPauseInMilliSeconds = 8;</code>
     * @return The loopPauseInMilliSeconds.
     */
    int getLoopPauseInMilliSeconds();

    /**
     * <code>string scheduledTime = 3;</code>
     * @return The scheduledTime.
     */
    java.lang.String getScheduledTime();
    /**
     * <code>string scheduledTime = 3;</code>
     * @return The bytes for scheduledTime.
     */
    com.google.protobuf.ByteString
        getScheduledTimeBytes();

    /**
     * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
     */
    java.util.List<com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets> 
        getAssetsList();
    /**
     * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
     */
    com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets getAssets(int index);
    /**
     * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
     */
    int getAssetsCount();
    /**
     * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
     */
    java.util.List<? extends com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.AssetsOrBuilder> 
        getAssetsOrBuilderList();
    /**
     * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
     */
    com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.AssetsOrBuilder getAssetsOrBuilder(
        int index);

    /**
     * <code>int32 loopCount = 7;</code>
     * @return The loopCount.
     */
    int getLoopCount();

    /**
     * <code>string backgroundAlertAsset = 6;</code>
     * @return The backgroundAlertAsset.
     */
    java.lang.String getBackgroundAlertAsset();
    /**
     * <code>string backgroundAlertAsset = 6;</code>
     * @return The bytes for backgroundAlertAsset.
     */
    com.google.protobuf.ByteString
        getBackgroundAlertAssetBytes();

    /**
     * <code>string type = 2;</code>
     * @return The type.
     */
    java.lang.String getType();
    /**
     * <code>string type = 2;</code>
     * @return The bytes for type.
     */
    com.google.protobuf.ByteString
        getTypeBytes();

    /**
     * <code>string token = 1;</code>
     * @return The token.
     */
    java.lang.String getToken();
    /**
     * <code>string token = 1;</code>
     * @return The bytes for token.
     */
    com.google.protobuf.ByteString
        getTokenBytes();
  }
  /**
   * Protobuf type {@code alerts.SetAlertDirectivePayloadProto}
   */
  public static final class SetAlertDirectivePayloadProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:alerts.SetAlertDirectivePayloadProto)
      SetAlertDirectivePayloadProtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SetAlertDirectivePayloadProto.newBuilder() to construct.
    private SetAlertDirectivePayloadProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SetAlertDirectivePayloadProto() {
      assetPlayOrder_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      scheduledTime_ = "";
      assets_ = java.util.Collections.emptyList();
      backgroundAlertAsset_ = "";
      type_ = "";
      token_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SetAlertDirectivePayloadProto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.internal_static_alerts_SetAlertDirectivePayloadProto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.internal_static_alerts_SetAlertDirectivePayloadProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.class, com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Builder.class);
    }

    public interface AssetsOrBuilder extends
        // @@protoc_insertion_point(interface_extends:alerts.SetAlertDirectivePayloadProto.Assets)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>string assetId = 1;</code>
       * @return The assetId.
       */
      java.lang.String getAssetId();
      /**
       * <code>string assetId = 1;</code>
       * @return The bytes for assetId.
       */
      com.google.protobuf.ByteString
          getAssetIdBytes();

      /**
       * <code>string url = 2;</code>
       * @return The url.
       */
      java.lang.String getUrl();
      /**
       * <code>string url = 2;</code>
       * @return The bytes for url.
       */
      com.google.protobuf.ByteString
          getUrlBytes();
    }
    /**
     * Protobuf type {@code alerts.SetAlertDirectivePayloadProto.Assets}
     */
    public static final class Assets extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:alerts.SetAlertDirectivePayloadProto.Assets)
        AssetsOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use Assets.newBuilder() to construct.
      private Assets(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private Assets() {
        assetId_ = "";
        url_ = "";
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new Assets();
      }

      @java.lang.Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.internal_static_alerts_SetAlertDirectivePayloadProto_Assets_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.internal_static_alerts_SetAlertDirectivePayloadProto_Assets_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets.class, com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets.Builder.class);
      }

      public static final int ASSETID_FIELD_NUMBER = 1;
      private volatile java.lang.Object assetId_;
      /**
       * <code>string assetId = 1;</code>
       * @return The assetId.
       */
      @java.lang.Override
      public java.lang.String getAssetId() {
        java.lang.Object ref = assetId_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          assetId_ = s;
          return s;
        }
      }
      /**
       * <code>string assetId = 1;</code>
       * @return The bytes for assetId.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getAssetIdBytes() {
        java.lang.Object ref = assetId_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          assetId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int URL_FIELD_NUMBER = 2;
      private volatile java.lang.Object url_;
      /**
       * <code>string url = 2;</code>
       * @return The url.
       */
      @java.lang.Override
      public java.lang.String getUrl() {
        java.lang.Object ref = url_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          url_ = s;
          return s;
        }
      }
      /**
       * <code>string url = 2;</code>
       * @return The bytes for url.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getUrlBytes() {
        java.lang.Object ref = url_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          url_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(assetId_)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 1, assetId_);
        }
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(url_)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 2, url_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(assetId_)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, assetId_);
        }
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(url_)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, url_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets)) {
          return super.equals(obj);
        }
        com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets other = (com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets) obj;

        if (!getAssetId()
            .equals(other.getAssetId())) return false;
        if (!getUrl()
            .equals(other.getUrl())) return false;
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + ASSETID_FIELD_NUMBER;
        hash = (53 * hash) + getAssetId().hashCode();
        hash = (37 * hash) + URL_FIELD_NUMBER;
        hash = (53 * hash) + getUrl().hashCode();
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code alerts.SetAlertDirectivePayloadProto.Assets}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:alerts.SetAlertDirectivePayloadProto.Assets)
          com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.AssetsOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.internal_static_alerts_SetAlertDirectivePayloadProto_Assets_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.internal_static_alerts_SetAlertDirectivePayloadProto_Assets_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets.class, com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets.Builder.class);
        }

        // Construct using com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          assetId_ = "";

          url_ = "";

          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.internal_static_alerts_SetAlertDirectivePayloadProto_Assets_descriptor;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets getDefaultInstanceForType() {
          return com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets.getDefaultInstance();
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets build() {
          com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets buildPartial() {
          com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets result = new com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets(this);
          result.assetId_ = assetId_;
          result.url_ = url_;
          onBuilt();
          return result;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets) {
            return mergeFrom((com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets other) {
          if (other == com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets.getDefaultInstance()) return this;
          if (!other.getAssetId().isEmpty()) {
            assetId_ = other.assetId_;
            onChanged();
          }
          if (!other.getUrl().isEmpty()) {
            url_ = other.url_;
            onChanged();
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 10: {
                  assetId_ = input.readStringRequireUtf8();

                  break;
                } // case 10
                case 18: {
                  url_ = input.readStringRequireUtf8();

                  break;
                } // case 18
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }

        private java.lang.Object assetId_ = "";
        /**
         * <code>string assetId = 1;</code>
         * @return The assetId.
         */
        public java.lang.String getAssetId() {
          java.lang.Object ref = assetId_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            assetId_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>string assetId = 1;</code>
         * @return The bytes for assetId.
         */
        public com.google.protobuf.ByteString
            getAssetIdBytes() {
          java.lang.Object ref = assetId_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            assetId_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string assetId = 1;</code>
         * @param value The assetId to set.
         * @return This builder for chaining.
         */
        public Builder setAssetId(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  
          assetId_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>string assetId = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearAssetId() {
          
          assetId_ = getDefaultInstance().getAssetId();
          onChanged();
          return this;
        }
        /**
         * <code>string assetId = 1;</code>
         * @param value The bytes for assetId to set.
         * @return This builder for chaining.
         */
        public Builder setAssetIdBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
          
          assetId_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object url_ = "";
        /**
         * <code>string url = 2;</code>
         * @return The url.
         */
        public java.lang.String getUrl() {
          java.lang.Object ref = url_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            url_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>string url = 2;</code>
         * @return The bytes for url.
         */
        public com.google.protobuf.ByteString
            getUrlBytes() {
          java.lang.Object ref = url_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            url_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string url = 2;</code>
         * @param value The url to set.
         * @return This builder for chaining.
         */
        public Builder setUrl(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  
          url_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>string url = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearUrl() {
          
          url_ = getDefaultInstance().getUrl();
          onChanged();
          return this;
        }
        /**
         * <code>string url = 2;</code>
         * @param value The bytes for url to set.
         * @return This builder for chaining.
         */
        public Builder setUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
          
          url_ = value;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:alerts.SetAlertDirectivePayloadProto.Assets)
      }

      // @@protoc_insertion_point(class_scope:alerts.SetAlertDirectivePayloadProto.Assets)
      private static final com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets();
      }

      public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Assets>
          PARSER = new com.google.protobuf.AbstractParser<Assets>() {
        @java.lang.Override
        public Assets parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Assets> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Assets> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public static final int ASSETPLAYORDER_FIELD_NUMBER = 5;
    private com.google.protobuf.LazyStringList assetPlayOrder_;
    /**
     * <code>repeated string assetPlayOrder = 5;</code>
     * @return A list containing the assetPlayOrder.
     */
    public com.google.protobuf.ProtocolStringList
        getAssetPlayOrderList() {
      return assetPlayOrder_;
    }
    /**
     * <code>repeated string assetPlayOrder = 5;</code>
     * @return The count of assetPlayOrder.
     */
    public int getAssetPlayOrderCount() {
      return assetPlayOrder_.size();
    }
    /**
     * <code>repeated string assetPlayOrder = 5;</code>
     * @param index The index of the element to return.
     * @return The assetPlayOrder at the given index.
     */
    public java.lang.String getAssetPlayOrder(int index) {
      return assetPlayOrder_.get(index);
    }
    /**
     * <code>repeated string assetPlayOrder = 5;</code>
     * @param index The index of the value to return.
     * @return The bytes of the assetPlayOrder at the given index.
     */
    public com.google.protobuf.ByteString
        getAssetPlayOrderBytes(int index) {
      return assetPlayOrder_.getByteString(index);
    }

    public static final int LOOPPAUSEINMILLISECONDS_FIELD_NUMBER = 8;
    private int loopPauseInMilliSeconds_;
    /**
     * <code>int32 loopPauseInMilliSeconds = 8;</code>
     * @return The loopPauseInMilliSeconds.
     */
    @java.lang.Override
    public int getLoopPauseInMilliSeconds() {
      return loopPauseInMilliSeconds_;
    }

    public static final int SCHEDULEDTIME_FIELD_NUMBER = 3;
    private volatile java.lang.Object scheduledTime_;
    /**
     * <code>string scheduledTime = 3;</code>
     * @return The scheduledTime.
     */
    @java.lang.Override
    public java.lang.String getScheduledTime() {
      java.lang.Object ref = scheduledTime_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        scheduledTime_ = s;
        return s;
      }
    }
    /**
     * <code>string scheduledTime = 3;</code>
     * @return The bytes for scheduledTime.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getScheduledTimeBytes() {
      java.lang.Object ref = scheduledTime_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        scheduledTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ASSETS_FIELD_NUMBER = 4;
    private java.util.List<com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets> assets_;
    /**
     * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
     */
    @java.lang.Override
    public java.util.List<com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets> getAssetsList() {
      return assets_;
    }
    /**
     * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.AssetsOrBuilder> 
        getAssetsOrBuilderList() {
      return assets_;
    }
    /**
     * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
     */
    @java.lang.Override
    public int getAssetsCount() {
      return assets_.size();
    }
    /**
     * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets getAssets(int index) {
      return assets_.get(index);
    }
    /**
     * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.AssetsOrBuilder getAssetsOrBuilder(
        int index) {
      return assets_.get(index);
    }

    public static final int LOOPCOUNT_FIELD_NUMBER = 7;
    private int loopCount_;
    /**
     * <code>int32 loopCount = 7;</code>
     * @return The loopCount.
     */
    @java.lang.Override
    public int getLoopCount() {
      return loopCount_;
    }

    public static final int BACKGROUNDALERTASSET_FIELD_NUMBER = 6;
    private volatile java.lang.Object backgroundAlertAsset_;
    /**
     * <code>string backgroundAlertAsset = 6;</code>
     * @return The backgroundAlertAsset.
     */
    @java.lang.Override
    public java.lang.String getBackgroundAlertAsset() {
      java.lang.Object ref = backgroundAlertAsset_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        backgroundAlertAsset_ = s;
        return s;
      }
    }
    /**
     * <code>string backgroundAlertAsset = 6;</code>
     * @return The bytes for backgroundAlertAsset.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBackgroundAlertAssetBytes() {
      java.lang.Object ref = backgroundAlertAsset_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        backgroundAlertAsset_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private volatile java.lang.Object type_;
    /**
     * <code>string type = 2;</code>
     * @return The type.
     */
    @java.lang.Override
    public java.lang.String getType() {
      java.lang.Object ref = type_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        type_ = s;
        return s;
      }
    }
    /**
     * <code>string type = 2;</code>
     * @return The bytes for type.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTypeBytes() {
      java.lang.Object ref = type_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        type_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TOKEN_FIELD_NUMBER = 1;
    private volatile java.lang.Object token_;
    /**
     * <code>string token = 1;</code>
     * @return The token.
     */
    @java.lang.Override
    public java.lang.String getToken() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        token_ = s;
        return s;
      }
    }
    /**
     * <code>string token = 1;</code>
     * @return The bytes for token.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTokenBytes() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        token_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(token_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, token_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(type_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, type_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(scheduledTime_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, scheduledTime_);
      }
      for (int i = 0; i < assets_.size(); i++) {
        output.writeMessage(4, assets_.get(i));
      }
      for (int i = 0; i < assetPlayOrder_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, assetPlayOrder_.getRaw(i));
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(backgroundAlertAsset_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, backgroundAlertAsset_);
      }
      if (loopCount_ != 0) {
        output.writeInt32(7, loopCount_);
      }
      if (loopPauseInMilliSeconds_ != 0) {
        output.writeInt32(8, loopPauseInMilliSeconds_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(token_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, token_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(type_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, type_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(scheduledTime_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, scheduledTime_);
      }
      for (int i = 0; i < assets_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, assets_.get(i));
      }
      {
        int dataSize = 0;
        for (int i = 0; i < assetPlayOrder_.size(); i++) {
          dataSize += computeStringSizeNoTag(assetPlayOrder_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getAssetPlayOrderList().size();
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(backgroundAlertAsset_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, backgroundAlertAsset_);
      }
      if (loopCount_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, loopCount_);
      }
      if (loopPauseInMilliSeconds_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, loopPauseInMilliSeconds_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto)) {
        return super.equals(obj);
      }
      com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto other = (com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto) obj;

      if (!getAssetPlayOrderList()
          .equals(other.getAssetPlayOrderList())) return false;
      if (getLoopPauseInMilliSeconds()
          != other.getLoopPauseInMilliSeconds()) return false;
      if (!getScheduledTime()
          .equals(other.getScheduledTime())) return false;
      if (!getAssetsList()
          .equals(other.getAssetsList())) return false;
      if (getLoopCount()
          != other.getLoopCount()) return false;
      if (!getBackgroundAlertAsset()
          .equals(other.getBackgroundAlertAsset())) return false;
      if (!getType()
          .equals(other.getType())) return false;
      if (!getToken()
          .equals(other.getToken())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getAssetPlayOrderCount() > 0) {
        hash = (37 * hash) + ASSETPLAYORDER_FIELD_NUMBER;
        hash = (53 * hash) + getAssetPlayOrderList().hashCode();
      }
      hash = (37 * hash) + LOOPPAUSEINMILLISECONDS_FIELD_NUMBER;
      hash = (53 * hash) + getLoopPauseInMilliSeconds();
      hash = (37 * hash) + SCHEDULEDTIME_FIELD_NUMBER;
      hash = (53 * hash) + getScheduledTime().hashCode();
      if (getAssetsCount() > 0) {
        hash = (37 * hash) + ASSETS_FIELD_NUMBER;
        hash = (53 * hash) + getAssetsList().hashCode();
      }
      hash = (37 * hash) + LOOPCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getLoopCount();
      hash = (37 * hash) + BACKGROUNDALERTASSET_FIELD_NUMBER;
      hash = (53 * hash) + getBackgroundAlertAsset().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType().hashCode();
      hash = (37 * hash) + TOKEN_FIELD_NUMBER;
      hash = (53 * hash) + getToken().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code alerts.SetAlertDirectivePayloadProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:alerts.SetAlertDirectivePayloadProto)
        com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.internal_static_alerts_SetAlertDirectivePayloadProto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.internal_static_alerts_SetAlertDirectivePayloadProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.class, com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Builder.class);
      }

      // Construct using com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        assetPlayOrder_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        loopPauseInMilliSeconds_ = 0;

        scheduledTime_ = "";

        if (assetsBuilder_ == null) {
          assets_ = java.util.Collections.emptyList();
        } else {
          assets_ = null;
          assetsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        loopCount_ = 0;

        backgroundAlertAsset_ = "";

        type_ = "";

        token_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.internal_static_alerts_SetAlertDirectivePayloadProto_descriptor;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto getDefaultInstanceForType() {
        return com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto build() {
        com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto buildPartial() {
        com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto result = new com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          assetPlayOrder_ = assetPlayOrder_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.assetPlayOrder_ = assetPlayOrder_;
        result.loopPauseInMilliSeconds_ = loopPauseInMilliSeconds_;
        result.scheduledTime_ = scheduledTime_;
        if (assetsBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            assets_ = java.util.Collections.unmodifiableList(assets_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.assets_ = assets_;
        } else {
          result.assets_ = assetsBuilder_.build();
        }
        result.loopCount_ = loopCount_;
        result.backgroundAlertAsset_ = backgroundAlertAsset_;
        result.type_ = type_;
        result.token_ = token_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto) {
          return mergeFrom((com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto other) {
        if (other == com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.getDefaultInstance()) return this;
        if (!other.assetPlayOrder_.isEmpty()) {
          if (assetPlayOrder_.isEmpty()) {
            assetPlayOrder_ = other.assetPlayOrder_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureAssetPlayOrderIsMutable();
            assetPlayOrder_.addAll(other.assetPlayOrder_);
          }
          onChanged();
        }
        if (other.getLoopPauseInMilliSeconds() != 0) {
          setLoopPauseInMilliSeconds(other.getLoopPauseInMilliSeconds());
        }
        if (!other.getScheduledTime().isEmpty()) {
          scheduledTime_ = other.scheduledTime_;
          onChanged();
        }
        if (assetsBuilder_ == null) {
          if (!other.assets_.isEmpty()) {
            if (assets_.isEmpty()) {
              assets_ = other.assets_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureAssetsIsMutable();
              assets_.addAll(other.assets_);
            }
            onChanged();
          }
        } else {
          if (!other.assets_.isEmpty()) {
            if (assetsBuilder_.isEmpty()) {
              assetsBuilder_.dispose();
              assetsBuilder_ = null;
              assets_ = other.assets_;
              bitField0_ = (bitField0_ & ~0x00000002);
              assetsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getAssetsFieldBuilder() : null;
            } else {
              assetsBuilder_.addAllMessages(other.assets_);
            }
          }
        }
        if (other.getLoopCount() != 0) {
          setLoopCount(other.getLoopCount());
        }
        if (!other.getBackgroundAlertAsset().isEmpty()) {
          backgroundAlertAsset_ = other.backgroundAlertAsset_;
          onChanged();
        }
        if (!other.getType().isEmpty()) {
          type_ = other.type_;
          onChanged();
        }
        if (!other.getToken().isEmpty()) {
          token_ = other.token_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                token_ = input.readStringRequireUtf8();

                break;
              } // case 10
              case 18: {
                type_ = input.readStringRequireUtf8();

                break;
              } // case 18
              case 26: {
                scheduledTime_ = input.readStringRequireUtf8();

                break;
              } // case 26
              case 34: {
                com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets m =
                    input.readMessage(
                        com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets.parser(),
                        extensionRegistry);
                if (assetsBuilder_ == null) {
                  ensureAssetsIsMutable();
                  assets_.add(m);
                } else {
                  assetsBuilder_.addMessage(m);
                }
                break;
              } // case 34
              case 42: {
                java.lang.String s = input.readStringRequireUtf8();
                ensureAssetPlayOrderIsMutable();
                assetPlayOrder_.add(s);
                break;
              } // case 42
              case 50: {
                backgroundAlertAsset_ = input.readStringRequireUtf8();

                break;
              } // case 50
              case 56: {
                loopCount_ = input.readInt32();

                break;
              } // case 56
              case 64: {
                loopPauseInMilliSeconds_ = input.readInt32();

                break;
              } // case 64
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.LazyStringList assetPlayOrder_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureAssetPlayOrderIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          assetPlayOrder_ = new com.google.protobuf.LazyStringArrayList(assetPlayOrder_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated string assetPlayOrder = 5;</code>
       * @return A list containing the assetPlayOrder.
       */
      public com.google.protobuf.ProtocolStringList
          getAssetPlayOrderList() {
        return assetPlayOrder_.getUnmodifiableView();
      }
      /**
       * <code>repeated string assetPlayOrder = 5;</code>
       * @return The count of assetPlayOrder.
       */
      public int getAssetPlayOrderCount() {
        return assetPlayOrder_.size();
      }
      /**
       * <code>repeated string assetPlayOrder = 5;</code>
       * @param index The index of the element to return.
       * @return The assetPlayOrder at the given index.
       */
      public java.lang.String getAssetPlayOrder(int index) {
        return assetPlayOrder_.get(index);
      }
      /**
       * <code>repeated string assetPlayOrder = 5;</code>
       * @param index The index of the value to return.
       * @return The bytes of the assetPlayOrder at the given index.
       */
      public com.google.protobuf.ByteString
          getAssetPlayOrderBytes(int index) {
        return assetPlayOrder_.getByteString(index);
      }
      /**
       * <code>repeated string assetPlayOrder = 5;</code>
       * @param index The index to set the value at.
       * @param value The assetPlayOrder to set.
       * @return This builder for chaining.
       */
      public Builder setAssetPlayOrder(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureAssetPlayOrderIsMutable();
        assetPlayOrder_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string assetPlayOrder = 5;</code>
       * @param value The assetPlayOrder to add.
       * @return This builder for chaining.
       */
      public Builder addAssetPlayOrder(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureAssetPlayOrderIsMutable();
        assetPlayOrder_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string assetPlayOrder = 5;</code>
       * @param values The assetPlayOrder to add.
       * @return This builder for chaining.
       */
      public Builder addAllAssetPlayOrder(
          java.lang.Iterable<java.lang.String> values) {
        ensureAssetPlayOrderIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, assetPlayOrder_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string assetPlayOrder = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearAssetPlayOrder() {
        assetPlayOrder_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string assetPlayOrder = 5;</code>
       * @param value The bytes of the assetPlayOrder to add.
       * @return This builder for chaining.
       */
      public Builder addAssetPlayOrderBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        ensureAssetPlayOrderIsMutable();
        assetPlayOrder_.add(value);
        onChanged();
        return this;
      }

      private int loopPauseInMilliSeconds_ ;
      /**
       * <code>int32 loopPauseInMilliSeconds = 8;</code>
       * @return The loopPauseInMilliSeconds.
       */
      @java.lang.Override
      public int getLoopPauseInMilliSeconds() {
        return loopPauseInMilliSeconds_;
      }
      /**
       * <code>int32 loopPauseInMilliSeconds = 8;</code>
       * @param value The loopPauseInMilliSeconds to set.
       * @return This builder for chaining.
       */
      public Builder setLoopPauseInMilliSeconds(int value) {
        
        loopPauseInMilliSeconds_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 loopPauseInMilliSeconds = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearLoopPauseInMilliSeconds() {
        
        loopPauseInMilliSeconds_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object scheduledTime_ = "";
      /**
       * <code>string scheduledTime = 3;</code>
       * @return The scheduledTime.
       */
      public java.lang.String getScheduledTime() {
        java.lang.Object ref = scheduledTime_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          scheduledTime_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string scheduledTime = 3;</code>
       * @return The bytes for scheduledTime.
       */
      public com.google.protobuf.ByteString
          getScheduledTimeBytes() {
        java.lang.Object ref = scheduledTime_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          scheduledTime_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string scheduledTime = 3;</code>
       * @param value The scheduledTime to set.
       * @return This builder for chaining.
       */
      public Builder setScheduledTime(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        scheduledTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string scheduledTime = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearScheduledTime() {
        
        scheduledTime_ = getDefaultInstance().getScheduledTime();
        onChanged();
        return this;
      }
      /**
       * <code>string scheduledTime = 3;</code>
       * @param value The bytes for scheduledTime to set.
       * @return This builder for chaining.
       */
      public Builder setScheduledTimeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        scheduledTime_ = value;
        onChanged();
        return this;
      }

      private java.util.List<com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets> assets_ =
        java.util.Collections.emptyList();
      private void ensureAssetsIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          assets_ = new java.util.ArrayList<com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets>(assets_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets, com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets.Builder, com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.AssetsOrBuilder> assetsBuilder_;

      /**
       * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
       */
      public java.util.List<com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets> getAssetsList() {
        if (assetsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(assets_);
        } else {
          return assetsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
       */
      public int getAssetsCount() {
        if (assetsBuilder_ == null) {
          return assets_.size();
        } else {
          return assetsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
       */
      public com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets getAssets(int index) {
        if (assetsBuilder_ == null) {
          return assets_.get(index);
        } else {
          return assetsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
       */
      public Builder setAssets(
          int index, com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets value) {
        if (assetsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAssetsIsMutable();
          assets_.set(index, value);
          onChanged();
        } else {
          assetsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
       */
      public Builder setAssets(
          int index, com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets.Builder builderForValue) {
        if (assetsBuilder_ == null) {
          ensureAssetsIsMutable();
          assets_.set(index, builderForValue.build());
          onChanged();
        } else {
          assetsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
       */
      public Builder addAssets(com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets value) {
        if (assetsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAssetsIsMutable();
          assets_.add(value);
          onChanged();
        } else {
          assetsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
       */
      public Builder addAssets(
          int index, com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets value) {
        if (assetsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAssetsIsMutable();
          assets_.add(index, value);
          onChanged();
        } else {
          assetsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
       */
      public Builder addAssets(
          com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets.Builder builderForValue) {
        if (assetsBuilder_ == null) {
          ensureAssetsIsMutable();
          assets_.add(builderForValue.build());
          onChanged();
        } else {
          assetsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
       */
      public Builder addAssets(
          int index, com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets.Builder builderForValue) {
        if (assetsBuilder_ == null) {
          ensureAssetsIsMutable();
          assets_.add(index, builderForValue.build());
          onChanged();
        } else {
          assetsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
       */
      public Builder addAllAssets(
          java.lang.Iterable<? extends com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets> values) {
        if (assetsBuilder_ == null) {
          ensureAssetsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, assets_);
          onChanged();
        } else {
          assetsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
       */
      public Builder clearAssets() {
        if (assetsBuilder_ == null) {
          assets_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          assetsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
       */
      public Builder removeAssets(int index) {
        if (assetsBuilder_ == null) {
          ensureAssetsIsMutable();
          assets_.remove(index);
          onChanged();
        } else {
          assetsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
       */
      public com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets.Builder getAssetsBuilder(
          int index) {
        return getAssetsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
       */
      public com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.AssetsOrBuilder getAssetsOrBuilder(
          int index) {
        if (assetsBuilder_ == null) {
          return assets_.get(index);  } else {
          return assetsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
       */
      public java.util.List<? extends com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.AssetsOrBuilder> 
           getAssetsOrBuilderList() {
        if (assetsBuilder_ != null) {
          return assetsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(assets_);
        }
      }
      /**
       * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
       */
      public com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets.Builder addAssetsBuilder() {
        return getAssetsFieldBuilder().addBuilder(
            com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets.getDefaultInstance());
      }
      /**
       * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
       */
      public com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets.Builder addAssetsBuilder(
          int index) {
        return getAssetsFieldBuilder().addBuilder(
            index, com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets.getDefaultInstance());
      }
      /**
       * <code>repeated .alerts.SetAlertDirectivePayloadProto.Assets assets = 4;</code>
       */
      public java.util.List<com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets.Builder> 
           getAssetsBuilderList() {
        return getAssetsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets, com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets.Builder, com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.AssetsOrBuilder> 
          getAssetsFieldBuilder() {
        if (assetsBuilder_ == null) {
          assetsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets, com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.Assets.Builder, com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto.AssetsOrBuilder>(
                  assets_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          assets_ = null;
        }
        return assetsBuilder_;
      }

      private int loopCount_ ;
      /**
       * <code>int32 loopCount = 7;</code>
       * @return The loopCount.
       */
      @java.lang.Override
      public int getLoopCount() {
        return loopCount_;
      }
      /**
       * <code>int32 loopCount = 7;</code>
       * @param value The loopCount to set.
       * @return This builder for chaining.
       */
      public Builder setLoopCount(int value) {
        
        loopCount_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 loopCount = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearLoopCount() {
        
        loopCount_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object backgroundAlertAsset_ = "";
      /**
       * <code>string backgroundAlertAsset = 6;</code>
       * @return The backgroundAlertAsset.
       */
      public java.lang.String getBackgroundAlertAsset() {
        java.lang.Object ref = backgroundAlertAsset_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          backgroundAlertAsset_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string backgroundAlertAsset = 6;</code>
       * @return The bytes for backgroundAlertAsset.
       */
      public com.google.protobuf.ByteString
          getBackgroundAlertAssetBytes() {
        java.lang.Object ref = backgroundAlertAsset_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          backgroundAlertAsset_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string backgroundAlertAsset = 6;</code>
       * @param value The backgroundAlertAsset to set.
       * @return This builder for chaining.
       */
      public Builder setBackgroundAlertAsset(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        backgroundAlertAsset_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string backgroundAlertAsset = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearBackgroundAlertAsset() {
        
        backgroundAlertAsset_ = getDefaultInstance().getBackgroundAlertAsset();
        onChanged();
        return this;
      }
      /**
       * <code>string backgroundAlertAsset = 6;</code>
       * @param value The bytes for backgroundAlertAsset to set.
       * @return This builder for chaining.
       */
      public Builder setBackgroundAlertAssetBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        backgroundAlertAsset_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object type_ = "";
      /**
       * <code>string type = 2;</code>
       * @return The type.
       */
      public java.lang.String getType() {
        java.lang.Object ref = type_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          type_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string type = 2;</code>
       * @return The bytes for type.
       */
      public com.google.protobuf.ByteString
          getTypeBytes() {
        java.lang.Object ref = type_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          type_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string type = 2;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string type = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = getDefaultInstance().getType();
        onChanged();
        return this;
      }
      /**
       * <code>string type = 2;</code>
       * @param value The bytes for type to set.
       * @return This builder for chaining.
       */
      public Builder setTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        type_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object token_ = "";
      /**
       * <code>string token = 1;</code>
       * @return The token.
       */
      public java.lang.String getToken() {
        java.lang.Object ref = token_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          token_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string token = 1;</code>
       * @return The bytes for token.
       */
      public com.google.protobuf.ByteString
          getTokenBytes() {
        java.lang.Object ref = token_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string token = 1;</code>
       * @param value The token to set.
       * @return This builder for chaining.
       */
      public Builder setToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        token_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string token = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearToken() {
        
        token_ = getDefaultInstance().getToken();
        onChanged();
        return this;
      }
      /**
       * <code>string token = 1;</code>
       * @param value The bytes for token to set.
       * @return This builder for chaining.
       */
      public Builder setTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        token_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:alerts.SetAlertDirectivePayloadProto)
    }

    // @@protoc_insertion_point(class_scope:alerts.SetAlertDirectivePayloadProto)
    private static final com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto();
    }

    public static com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SetAlertDirectivePayloadProto>
        PARSER = new com.google.protobuf.AbstractParser<SetAlertDirectivePayloadProto>() {
      @java.lang.Override
      public SetAlertDirectivePayloadProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SetAlertDirectivePayloadProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SetAlertDirectivePayloadProto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alerts.SetAlertDirectivePayload.SetAlertDirectivePayloadProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_alerts_SetAlertDirectivePayloadProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_alerts_SetAlertDirectivePayloadProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_alerts_SetAlertDirectivePayloadProto_Assets_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_alerts_SetAlertDirectivePayloadProto_Assets_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n$alertsSetAlertDirectivePayload.proto\022\006" +
      "alerts\"\243\002\n\035SetAlertDirectivePayloadProto" +
      "\022\026\n\016assetPlayOrder\030\005 \003(\t\022\037\n\027loopPauseInM" +
      "illiSeconds\030\010 \001(\005\022\025\n\rscheduledTime\030\003 \001(\t" +
      "\022<\n\006assets\030\004 \003(\0132,.alerts.SetAlertDirect" +
      "ivePayloadProto.Assets\022\021\n\tloopCount\030\007 \001(" +
      "\005\022\034\n\024backgroundAlertAsset\030\006 \001(\t\022\014\n\004type\030" +
      "\002 \001(\t\022\r\n\005token\030\001 \001(\t\032&\n\006Assets\022\017\n\007assetI" +
      "d\030\001 \001(\t\022\013\n\003url\030\002 \001(\tBA\n%com.amazon.proto" +
      ".avs.v20160207.alertsB\030SetAlertDirective" +
      "Payloadb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_alerts_SetAlertDirectivePayloadProto_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_alerts_SetAlertDirectivePayloadProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_alerts_SetAlertDirectivePayloadProto_descriptor,
        new java.lang.String[] { "AssetPlayOrder", "LoopPauseInMilliSeconds", "ScheduledTime", "Assets", "LoopCount", "BackgroundAlertAsset", "Type", "Token", });
    internal_static_alerts_SetAlertDirectivePayloadProto_Assets_descriptor =
      internal_static_alerts_SetAlertDirectivePayloadProto_descriptor.getNestedTypes().get(0);
    internal_static_alerts_SetAlertDirectivePayloadProto_Assets_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_alerts_SetAlertDirectivePayloadProto_Assets_descriptor,
        new java.lang.String[] { "AssetId", "Url", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
