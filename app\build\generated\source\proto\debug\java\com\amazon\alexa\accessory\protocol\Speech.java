// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: speech.proto

package com.amazon.alexa.accessory.protocol;

public final class Speech {
  private Speech() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code AudioProfile}
   */
  public enum AudioProfile
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>CLOSE_TALK = 0;</code>
     */
    CLOSE_TALK(0),
    /**
     * <code>NEAR_FIELD = 1;</code>
     */
    NEAR_FIELD(1),
    /**
     * <code>FAR_FIELD = 2;</code>
     */
    FAR_FIELD(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>CLOSE_TALK = 0;</code>
     */
    public static final int CLOSE_TALK_VALUE = 0;
    /**
     * <code>NEAR_FIELD = 1;</code>
     */
    public static final int NEAR_FIELD_VALUE = 1;
    /**
     * <code>FAR_FIELD = 2;</code>
     */
    public static final int FAR_FIELD_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static AudioProfile valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static AudioProfile forNumber(int value) {
      switch (value) {
        case 0: return CLOSE_TALK;
        case 1: return NEAR_FIELD;
        case 2: return FAR_FIELD;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<AudioProfile>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        AudioProfile> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<AudioProfile>() {
            public AudioProfile findValueByNumber(int number) {
              return AudioProfile.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Speech.getDescriptor().getEnumTypes().get(0);
    }

    private static final AudioProfile[] VALUES = values();

    public static AudioProfile valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private AudioProfile(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:AudioProfile)
  }

  /**
   * Protobuf enum {@code AudioFormat}
   */
  public enum AudioFormat
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>PCM_L16_16KHZ_MONO = 0;</code>
     */
    PCM_L16_16KHZ_MONO(0),
    /**
     * <code>OPUS_16KHZ_32KBPS_CBR_0_20MS = 1;</code>
     */
    OPUS_16KHZ_32KBPS_CBR_0_20MS(1),
    /**
     * <code>OPUS_16KHZ_16KBPS_CBR_0_20MS = 2;</code>
     */
    OPUS_16KHZ_16KBPS_CBR_0_20MS(2),
    /**
     * <code>MSBC = 3;</code>
     */
    MSBC(3),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>PCM_L16_16KHZ_MONO = 0;</code>
     */
    public static final int PCM_L16_16KHZ_MONO_VALUE = 0;
    /**
     * <code>OPUS_16KHZ_32KBPS_CBR_0_20MS = 1;</code>
     */
    public static final int OPUS_16KHZ_32KBPS_CBR_0_20MS_VALUE = 1;
    /**
     * <code>OPUS_16KHZ_16KBPS_CBR_0_20MS = 2;</code>
     */
    public static final int OPUS_16KHZ_16KBPS_CBR_0_20MS_VALUE = 2;
    /**
     * <code>MSBC = 3;</code>
     */
    public static final int MSBC_VALUE = 3;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static AudioFormat valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static AudioFormat forNumber(int value) {
      switch (value) {
        case 0: return PCM_L16_16KHZ_MONO;
        case 1: return OPUS_16KHZ_32KBPS_CBR_0_20MS;
        case 2: return OPUS_16KHZ_16KBPS_CBR_0_20MS;
        case 3: return MSBC;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<AudioFormat>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        AudioFormat> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<AudioFormat>() {
            public AudioFormat findValueByNumber(int number) {
              return AudioFormat.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Speech.getDescriptor().getEnumTypes().get(1);
    }

    private static final AudioFormat[] VALUES = values();

    public static AudioFormat valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private AudioFormat(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:AudioFormat)
  }

  /**
   * Protobuf enum {@code AudioSource}
   */
  public enum AudioSource
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>STREAM = 0;</code>
     */
    STREAM(0),
    /**
     * <code>BLUETOOTH_SCO = 1;</code>
     */
    BLUETOOTH_SCO(1),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>STREAM = 0;</code>
     */
    public static final int STREAM_VALUE = 0;
    /**
     * <code>BLUETOOTH_SCO = 1;</code>
     */
    public static final int BLUETOOTH_SCO_VALUE = 1;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static AudioSource valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static AudioSource forNumber(int value) {
      switch (value) {
        case 0: return STREAM;
        case 1: return BLUETOOTH_SCO;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<AudioSource>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        AudioSource> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<AudioSource>() {
            public AudioSource findValueByNumber(int number) {
              return AudioSource.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Speech.getDescriptor().getEnumTypes().get(2);
    }

    private static final AudioSource[] VALUES = values();

    public static AudioSource valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private AudioSource(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:AudioSource)
  }

  /**
   * Protobuf enum {@code SpeechState}
   */
  public enum SpeechState
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>IDLE = 0;</code>
     */
    IDLE(0),
    /**
     * <code>LISTENING = 1;</code>
     */
    LISTENING(1),
    /**
     * <code>PROCESSING = 2;</code>
     */
    PROCESSING(2),
    /**
     * <code>SPEAKING = 3;</code>
     */
    SPEAKING(3),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>IDLE = 0;</code>
     */
    public static final int IDLE_VALUE = 0;
    /**
     * <code>LISTENING = 1;</code>
     */
    public static final int LISTENING_VALUE = 1;
    /**
     * <code>PROCESSING = 2;</code>
     */
    public static final int PROCESSING_VALUE = 2;
    /**
     * <code>SPEAKING = 3;</code>
     */
    public static final int SPEAKING_VALUE = 3;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static SpeechState valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static SpeechState forNumber(int value) {
      switch (value) {
        case 0: return IDLE;
        case 1: return LISTENING;
        case 2: return PROCESSING;
        case 3: return SPEAKING;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<SpeechState>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        SpeechState> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<SpeechState>() {
            public SpeechState findValueByNumber(int number) {
              return SpeechState.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Speech.getDescriptor().getEnumTypes().get(3);
    }

    private static final SpeechState[] VALUES = values();

    public static SpeechState valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private SpeechState(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:SpeechState)
  }

  public interface DialogOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Dialog)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    int getId();
  }
  /**
   * Protobuf type {@code Dialog}
   */
  public static final class Dialog extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Dialog)
      DialogOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Dialog.newBuilder() to construct.
    private Dialog(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Dialog() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Dialog();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Speech.internal_static_Dialog_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Speech.internal_static_Dialog_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Speech.Dialog.class, com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private int id_;
    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeUInt32(1, id_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, id_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Speech.Dialog)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Speech.Dialog other = (com.amazon.alexa.accessory.protocol.Speech.Dialog) obj;

      if (getId()
          != other.getId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Speech.Dialog parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.Dialog parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.Dialog parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.Dialog parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.Dialog parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.Dialog parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.Dialog parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.Dialog parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.Dialog parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.Dialog parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.Dialog parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.Dialog parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Speech.Dialog prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Dialog}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Dialog)
        com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_Dialog_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_Dialog_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Speech.Dialog.class, com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Speech.Dialog.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        id_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_Dialog_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.Dialog getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.Dialog build() {
        com.amazon.alexa.accessory.protocol.Speech.Dialog result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.Dialog buildPartial() {
        com.amazon.alexa.accessory.protocol.Speech.Dialog result = new com.amazon.alexa.accessory.protocol.Speech.Dialog(this);
        result.id_ = id_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Speech.Dialog) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Speech.Dialog)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Speech.Dialog other) {
        if (other == com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readUInt32();

                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private int id_ ;
      /**
       * <code>uint32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {
        
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        
        id_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Dialog)
    }

    // @@protoc_insertion_point(class_scope:Dialog)
    private static final com.amazon.alexa.accessory.protocol.Speech.Dialog DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Speech.Dialog();
    }

    public static com.amazon.alexa.accessory.protocol.Speech.Dialog getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Dialog>
        PARSER = new com.google.protobuf.AbstractParser<Dialog>() {
      @java.lang.Override
      public Dialog parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Dialog> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Dialog> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.Dialog getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SpeechSettingsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:SpeechSettings)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.AudioProfile audio_profile = 1;</code>
     * @return The enum numeric value on the wire for audioProfile.
     */
    int getAudioProfileValue();
    /**
     * <code>.AudioProfile audio_profile = 1;</code>
     * @return The audioProfile.
     */
    com.amazon.alexa.accessory.protocol.Speech.AudioProfile getAudioProfile();

    /**
     * <code>.AudioFormat audio_format = 2;</code>
     * @return The enum numeric value on the wire for audioFormat.
     */
    int getAudioFormatValue();
    /**
     * <code>.AudioFormat audio_format = 2;</code>
     * @return The audioFormat.
     */
    com.amazon.alexa.accessory.protocol.Speech.AudioFormat getAudioFormat();

    /**
     * <code>.AudioSource audio_source = 3;</code>
     * @return The enum numeric value on the wire for audioSource.
     */
    int getAudioSourceValue();
    /**
     * <code>.AudioSource audio_source = 3;</code>
     * @return The audioSource.
     */
    com.amazon.alexa.accessory.protocol.Speech.AudioSource getAudioSource();
  }
  /**
   * Protobuf type {@code SpeechSettings}
   */
  public static final class SpeechSettings extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:SpeechSettings)
      SpeechSettingsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SpeechSettings.newBuilder() to construct.
    private SpeechSettings(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SpeechSettings() {
      audioProfile_ = 0;
      audioFormat_ = 0;
      audioSource_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SpeechSettings();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Speech.internal_static_SpeechSettings_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Speech.internal_static_SpeechSettings_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.class, com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.Builder.class);
    }

    public static final int AUDIO_PROFILE_FIELD_NUMBER = 1;
    private int audioProfile_;
    /**
     * <code>.AudioProfile audio_profile = 1;</code>
     * @return The enum numeric value on the wire for audioProfile.
     */
    @java.lang.Override public int getAudioProfileValue() {
      return audioProfile_;
    }
    /**
     * <code>.AudioProfile audio_profile = 1;</code>
     * @return The audioProfile.
     */
    @java.lang.Override public com.amazon.alexa.accessory.protocol.Speech.AudioProfile getAudioProfile() {
      @SuppressWarnings("deprecation")
      com.amazon.alexa.accessory.protocol.Speech.AudioProfile result = com.amazon.alexa.accessory.protocol.Speech.AudioProfile.valueOf(audioProfile_);
      return result == null ? com.amazon.alexa.accessory.protocol.Speech.AudioProfile.UNRECOGNIZED : result;
    }

    public static final int AUDIO_FORMAT_FIELD_NUMBER = 2;
    private int audioFormat_;
    /**
     * <code>.AudioFormat audio_format = 2;</code>
     * @return The enum numeric value on the wire for audioFormat.
     */
    @java.lang.Override public int getAudioFormatValue() {
      return audioFormat_;
    }
    /**
     * <code>.AudioFormat audio_format = 2;</code>
     * @return The audioFormat.
     */
    @java.lang.Override public com.amazon.alexa.accessory.protocol.Speech.AudioFormat getAudioFormat() {
      @SuppressWarnings("deprecation")
      com.amazon.alexa.accessory.protocol.Speech.AudioFormat result = com.amazon.alexa.accessory.protocol.Speech.AudioFormat.valueOf(audioFormat_);
      return result == null ? com.amazon.alexa.accessory.protocol.Speech.AudioFormat.UNRECOGNIZED : result;
    }

    public static final int AUDIO_SOURCE_FIELD_NUMBER = 3;
    private int audioSource_;
    /**
     * <code>.AudioSource audio_source = 3;</code>
     * @return The enum numeric value on the wire for audioSource.
     */
    @java.lang.Override public int getAudioSourceValue() {
      return audioSource_;
    }
    /**
     * <code>.AudioSource audio_source = 3;</code>
     * @return The audioSource.
     */
    @java.lang.Override public com.amazon.alexa.accessory.protocol.Speech.AudioSource getAudioSource() {
      @SuppressWarnings("deprecation")
      com.amazon.alexa.accessory.protocol.Speech.AudioSource result = com.amazon.alexa.accessory.protocol.Speech.AudioSource.valueOf(audioSource_);
      return result == null ? com.amazon.alexa.accessory.protocol.Speech.AudioSource.UNRECOGNIZED : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (audioProfile_ != com.amazon.alexa.accessory.protocol.Speech.AudioProfile.CLOSE_TALK.getNumber()) {
        output.writeEnum(1, audioProfile_);
      }
      if (audioFormat_ != com.amazon.alexa.accessory.protocol.Speech.AudioFormat.PCM_L16_16KHZ_MONO.getNumber()) {
        output.writeEnum(2, audioFormat_);
      }
      if (audioSource_ != com.amazon.alexa.accessory.protocol.Speech.AudioSource.STREAM.getNumber()) {
        output.writeEnum(3, audioSource_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (audioProfile_ != com.amazon.alexa.accessory.protocol.Speech.AudioProfile.CLOSE_TALK.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, audioProfile_);
      }
      if (audioFormat_ != com.amazon.alexa.accessory.protocol.Speech.AudioFormat.PCM_L16_16KHZ_MONO.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, audioFormat_);
      }
      if (audioSource_ != com.amazon.alexa.accessory.protocol.Speech.AudioSource.STREAM.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(3, audioSource_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Speech.SpeechSettings)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Speech.SpeechSettings other = (com.amazon.alexa.accessory.protocol.Speech.SpeechSettings) obj;

      if (audioProfile_ != other.audioProfile_) return false;
      if (audioFormat_ != other.audioFormat_) return false;
      if (audioSource_ != other.audioSource_) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + AUDIO_PROFILE_FIELD_NUMBER;
      hash = (53 * hash) + audioProfile_;
      hash = (37 * hash) + AUDIO_FORMAT_FIELD_NUMBER;
      hash = (53 * hash) + audioFormat_;
      hash = (37 * hash) + AUDIO_SOURCE_FIELD_NUMBER;
      hash = (53 * hash) + audioSource_;
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Speech.SpeechSettings parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechSettings parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechSettings parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechSettings parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechSettings parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechSettings parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechSettings parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechSettings parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechSettings parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechSettings parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechSettings parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechSettings parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Speech.SpeechSettings prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code SpeechSettings}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:SpeechSettings)
        com.amazon.alexa.accessory.protocol.Speech.SpeechSettingsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_SpeechSettings_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_SpeechSettings_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.class, com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        audioProfile_ = 0;

        audioFormat_ = 0;

        audioSource_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_SpeechSettings_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.SpeechSettings getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.SpeechSettings build() {
        com.amazon.alexa.accessory.protocol.Speech.SpeechSettings result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.SpeechSettings buildPartial() {
        com.amazon.alexa.accessory.protocol.Speech.SpeechSettings result = new com.amazon.alexa.accessory.protocol.Speech.SpeechSettings(this);
        result.audioProfile_ = audioProfile_;
        result.audioFormat_ = audioFormat_;
        result.audioSource_ = audioSource_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Speech.SpeechSettings) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Speech.SpeechSettings)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Speech.SpeechSettings other) {
        if (other == com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.getDefaultInstance()) return this;
        if (other.audioProfile_ != 0) {
          setAudioProfileValue(other.getAudioProfileValue());
        }
        if (other.audioFormat_ != 0) {
          setAudioFormatValue(other.getAudioFormatValue());
        }
        if (other.audioSource_ != 0) {
          setAudioSourceValue(other.getAudioSourceValue());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                audioProfile_ = input.readEnum();

                break;
              } // case 8
              case 16: {
                audioFormat_ = input.readEnum();

                break;
              } // case 16
              case 24: {
                audioSource_ = input.readEnum();

                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private int audioProfile_ = 0;
      /**
       * <code>.AudioProfile audio_profile = 1;</code>
       * @return The enum numeric value on the wire for audioProfile.
       */
      @java.lang.Override public int getAudioProfileValue() {
        return audioProfile_;
      }
      /**
       * <code>.AudioProfile audio_profile = 1;</code>
       * @param value The enum numeric value on the wire for audioProfile to set.
       * @return This builder for chaining.
       */
      public Builder setAudioProfileValue(int value) {
        
        audioProfile_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.AudioProfile audio_profile = 1;</code>
       * @return The audioProfile.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.AudioProfile getAudioProfile() {
        @SuppressWarnings("deprecation")
        com.amazon.alexa.accessory.protocol.Speech.AudioProfile result = com.amazon.alexa.accessory.protocol.Speech.AudioProfile.valueOf(audioProfile_);
        return result == null ? com.amazon.alexa.accessory.protocol.Speech.AudioProfile.UNRECOGNIZED : result;
      }
      /**
       * <code>.AudioProfile audio_profile = 1;</code>
       * @param value The audioProfile to set.
       * @return This builder for chaining.
       */
      public Builder setAudioProfile(com.amazon.alexa.accessory.protocol.Speech.AudioProfile value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        audioProfile_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.AudioProfile audio_profile = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAudioProfile() {
        
        audioProfile_ = 0;
        onChanged();
        return this;
      }

      private int audioFormat_ = 0;
      /**
       * <code>.AudioFormat audio_format = 2;</code>
       * @return The enum numeric value on the wire for audioFormat.
       */
      @java.lang.Override public int getAudioFormatValue() {
        return audioFormat_;
      }
      /**
       * <code>.AudioFormat audio_format = 2;</code>
       * @param value The enum numeric value on the wire for audioFormat to set.
       * @return This builder for chaining.
       */
      public Builder setAudioFormatValue(int value) {
        
        audioFormat_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.AudioFormat audio_format = 2;</code>
       * @return The audioFormat.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.AudioFormat getAudioFormat() {
        @SuppressWarnings("deprecation")
        com.amazon.alexa.accessory.protocol.Speech.AudioFormat result = com.amazon.alexa.accessory.protocol.Speech.AudioFormat.valueOf(audioFormat_);
        return result == null ? com.amazon.alexa.accessory.protocol.Speech.AudioFormat.UNRECOGNIZED : result;
      }
      /**
       * <code>.AudioFormat audio_format = 2;</code>
       * @param value The audioFormat to set.
       * @return This builder for chaining.
       */
      public Builder setAudioFormat(com.amazon.alexa.accessory.protocol.Speech.AudioFormat value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        audioFormat_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.AudioFormat audio_format = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearAudioFormat() {
        
        audioFormat_ = 0;
        onChanged();
        return this;
      }

      private int audioSource_ = 0;
      /**
       * <code>.AudioSource audio_source = 3;</code>
       * @return The enum numeric value on the wire for audioSource.
       */
      @java.lang.Override public int getAudioSourceValue() {
        return audioSource_;
      }
      /**
       * <code>.AudioSource audio_source = 3;</code>
       * @param value The enum numeric value on the wire for audioSource to set.
       * @return This builder for chaining.
       */
      public Builder setAudioSourceValue(int value) {
        
        audioSource_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.AudioSource audio_source = 3;</code>
       * @return The audioSource.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.AudioSource getAudioSource() {
        @SuppressWarnings("deprecation")
        com.amazon.alexa.accessory.protocol.Speech.AudioSource result = com.amazon.alexa.accessory.protocol.Speech.AudioSource.valueOf(audioSource_);
        return result == null ? com.amazon.alexa.accessory.protocol.Speech.AudioSource.UNRECOGNIZED : result;
      }
      /**
       * <code>.AudioSource audio_source = 3;</code>
       * @param value The audioSource to set.
       * @return This builder for chaining.
       */
      public Builder setAudioSource(com.amazon.alexa.accessory.protocol.Speech.AudioSource value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        audioSource_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.AudioSource audio_source = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearAudioSource() {
        
        audioSource_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:SpeechSettings)
    }

    // @@protoc_insertion_point(class_scope:SpeechSettings)
    private static final com.amazon.alexa.accessory.protocol.Speech.SpeechSettings DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Speech.SpeechSettings();
    }

    public static com.amazon.alexa.accessory.protocol.Speech.SpeechSettings getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SpeechSettings>
        PARSER = new com.google.protobuf.AbstractParser<SpeechSettings>() {
      @java.lang.Override
      public SpeechSettings parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SpeechSettings> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SpeechSettings> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.SpeechSettings getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SpeechInitiatorOrBuilder extends
      // @@protoc_insertion_point(interface_extends:SpeechInitiator)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.SpeechInitiator.Type type = 1;</code>
     * @return The enum numeric value on the wire for type.
     */
    int getTypeValue();
    /**
     * <code>.SpeechInitiator.Type type = 1;</code>
     * @return The type.
     */
    com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.Type getType();

    /**
     * <code>.SpeechInitiator.WakeWord wake_word = 2;</code>
     * @return Whether the wakeWord field is set.
     */
    boolean hasWakeWord();
    /**
     * <code>.SpeechInitiator.WakeWord wake_word = 2;</code>
     * @return The wakeWord.
     */
    com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord getWakeWord();
    /**
     * <code>.SpeechInitiator.WakeWord wake_word = 2;</code>
     */
    com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWordOrBuilder getWakeWordOrBuilder();
  }
  /**
   * Protobuf type {@code SpeechInitiator}
   */
  public static final class SpeechInitiator extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:SpeechInitiator)
      SpeechInitiatorOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SpeechInitiator.newBuilder() to construct.
    private SpeechInitiator(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SpeechInitiator() {
      type_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SpeechInitiator();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Speech.internal_static_SpeechInitiator_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Speech.internal_static_SpeechInitiator_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.class, com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.Builder.class);
    }

    /**
     * Protobuf enum {@code SpeechInitiator.Type}
     */
    public enum Type
        implements com.google.protobuf.ProtocolMessageEnum {
      /**
       * <code>NONE = 0;</code>
       */
      NONE(0),
      /**
       * <code>PRESS_AND_HOLD = 1;</code>
       */
      PRESS_AND_HOLD(1),
      /**
       * <code>TAP = 3;</code>
       */
      TAP(3),
      /**
       * <code>WAKEWORD = 4;</code>
       */
      WAKEWORD(4),
      UNRECOGNIZED(-1),
      ;

      /**
       * <code>NONE = 0;</code>
       */
      public static final int NONE_VALUE = 0;
      /**
       * <code>PRESS_AND_HOLD = 1;</code>
       */
      public static final int PRESS_AND_HOLD_VALUE = 1;
      /**
       * <code>TAP = 3;</code>
       */
      public static final int TAP_VALUE = 3;
      /**
       * <code>WAKEWORD = 4;</code>
       */
      public static final int WAKEWORD_VALUE = 4;


      public final int getNumber() {
        if (this == UNRECOGNIZED) {
          throw new java.lang.IllegalArgumentException(
              "Can't get the number of an unknown enum value.");
        }
        return value;
      }

      /**
       * @param value The numeric wire value of the corresponding enum entry.
       * @return The enum associated with the given numeric wire value.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static Type valueOf(int value) {
        return forNumber(value);
      }

      /**
       * @param value The numeric wire value of the corresponding enum entry.
       * @return The enum associated with the given numeric wire value.
       */
      public static Type forNumber(int value) {
        switch (value) {
          case 0: return NONE;
          case 1: return PRESS_AND_HOLD;
          case 3: return TAP;
          case 4: return WAKEWORD;
          default: return null;
        }
      }

      public static com.google.protobuf.Internal.EnumLiteMap<Type>
          internalGetValueMap() {
        return internalValueMap;
      }
      private static final com.google.protobuf.Internal.EnumLiteMap<
          Type> internalValueMap =
            new com.google.protobuf.Internal.EnumLiteMap<Type>() {
              public Type findValueByNumber(int number) {
                return Type.forNumber(number);
              }
            };

      public final com.google.protobuf.Descriptors.EnumValueDescriptor
          getValueDescriptor() {
        if (this == UNRECOGNIZED) {
          throw new java.lang.IllegalStateException(
              "Can't get the descriptor of an unrecognized enum value.");
        }
        return getDescriptor().getValues().get(ordinal());
      }
      public final com.google.protobuf.Descriptors.EnumDescriptor
          getDescriptorForType() {
        return getDescriptor();
      }
      public static final com.google.protobuf.Descriptors.EnumDescriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.getDescriptor().getEnumTypes().get(0);
      }

      private static final Type[] VALUES = values();

      public static Type valueOf(
          com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
        if (desc.getType() != getDescriptor()) {
          throw new java.lang.IllegalArgumentException(
            "EnumValueDescriptor is not for this type.");
        }
        if (desc.getIndex() == -1) {
          return UNRECOGNIZED;
        }
        return VALUES[desc.getIndex()];
      }

      private final int value;

      private Type(int value) {
        this.value = value;
      }

      // @@protoc_insertion_point(enum_scope:SpeechInitiator.Type)
    }

    public interface WakeWordOrBuilder extends
        // @@protoc_insertion_point(interface_extends:SpeechInitiator.WakeWord)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>uint32 start_index_in_samples = 1;</code>
       * @return The startIndexInSamples.
       */
      int getStartIndexInSamples();

      /**
       * <code>uint32 end_index_in_samples = 2;</code>
       * @return The endIndexInSamples.
       */
      int getEndIndexInSamples();

      /**
       * <code>bool near_miss = 3;</code>
       * @return The nearMiss.
       */
      boolean getNearMiss();

      /**
       * <code>bytes metadata = 4;</code>
       * @return The metadata.
       */
      com.google.protobuf.ByteString getMetadata();
    }
    /**
     * Protobuf type {@code SpeechInitiator.WakeWord}
     */
    public static final class WakeWord extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:SpeechInitiator.WakeWord)
        WakeWordOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use WakeWord.newBuilder() to construct.
      private WakeWord(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private WakeWord() {
        metadata_ = com.google.protobuf.ByteString.EMPTY;
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new WakeWord();
      }

      @java.lang.Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_SpeechInitiator_WakeWord_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_SpeechInitiator_WakeWord_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord.class, com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord.Builder.class);
      }

      public static final int START_INDEX_IN_SAMPLES_FIELD_NUMBER = 1;
      private int startIndexInSamples_;
      /**
       * <code>uint32 start_index_in_samples = 1;</code>
       * @return The startIndexInSamples.
       */
      @java.lang.Override
      public int getStartIndexInSamples() {
        return startIndexInSamples_;
      }

      public static final int END_INDEX_IN_SAMPLES_FIELD_NUMBER = 2;
      private int endIndexInSamples_;
      /**
       * <code>uint32 end_index_in_samples = 2;</code>
       * @return The endIndexInSamples.
       */
      @java.lang.Override
      public int getEndIndexInSamples() {
        return endIndexInSamples_;
      }

      public static final int NEAR_MISS_FIELD_NUMBER = 3;
      private boolean nearMiss_;
      /**
       * <code>bool near_miss = 3;</code>
       * @return The nearMiss.
       */
      @java.lang.Override
      public boolean getNearMiss() {
        return nearMiss_;
      }

      public static final int METADATA_FIELD_NUMBER = 4;
      private com.google.protobuf.ByteString metadata_;
      /**
       * <code>bytes metadata = 4;</code>
       * @return The metadata.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMetadata() {
        return metadata_;
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (startIndexInSamples_ != 0) {
          output.writeUInt32(1, startIndexInSamples_);
        }
        if (endIndexInSamples_ != 0) {
          output.writeUInt32(2, endIndexInSamples_);
        }
        if (nearMiss_ != false) {
          output.writeBool(3, nearMiss_);
        }
        if (!metadata_.isEmpty()) {
          output.writeBytes(4, metadata_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (startIndexInSamples_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeUInt32Size(1, startIndexInSamples_);
        }
        if (endIndexInSamples_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeUInt32Size(2, endIndexInSamples_);
        }
        if (nearMiss_ != false) {
          size += com.google.protobuf.CodedOutputStream
            .computeBoolSize(3, nearMiss_);
        }
        if (!metadata_.isEmpty()) {
          size += com.google.protobuf.CodedOutputStream
            .computeBytesSize(4, metadata_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord)) {
          return super.equals(obj);
        }
        com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord other = (com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord) obj;

        if (getStartIndexInSamples()
            != other.getStartIndexInSamples()) return false;
        if (getEndIndexInSamples()
            != other.getEndIndexInSamples()) return false;
        if (getNearMiss()
            != other.getNearMiss()) return false;
        if (!getMetadata()
            .equals(other.getMetadata())) return false;
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + START_INDEX_IN_SAMPLES_FIELD_NUMBER;
        hash = (53 * hash) + getStartIndexInSamples();
        hash = (37 * hash) + END_INDEX_IN_SAMPLES_FIELD_NUMBER;
        hash = (53 * hash) + getEndIndexInSamples();
        hash = (37 * hash) + NEAR_MISS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getNearMiss());
        hash = (37 * hash) + METADATA_FIELD_NUMBER;
        hash = (53 * hash) + getMetadata().hashCode();
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code SpeechInitiator.WakeWord}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:SpeechInitiator.WakeWord)
          com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWordOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.amazon.alexa.accessory.protocol.Speech.internal_static_SpeechInitiator_WakeWord_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.amazon.alexa.accessory.protocol.Speech.internal_static_SpeechInitiator_WakeWord_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord.class, com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord.Builder.class);
        }

        // Construct using com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          startIndexInSamples_ = 0;

          endIndexInSamples_ = 0;

          nearMiss_ = false;

          metadata_ = com.google.protobuf.ByteString.EMPTY;

          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.amazon.alexa.accessory.protocol.Speech.internal_static_SpeechInitiator_WakeWord_descriptor;
        }

        @java.lang.Override
        public com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord getDefaultInstanceForType() {
          return com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord.getDefaultInstance();
        }

        @java.lang.Override
        public com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord build() {
          com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord buildPartial() {
          com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord result = new com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord(this);
          result.startIndexInSamples_ = startIndexInSamples_;
          result.endIndexInSamples_ = endIndexInSamples_;
          result.nearMiss_ = nearMiss_;
          result.metadata_ = metadata_;
          onBuilt();
          return result;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord) {
            return mergeFrom((com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord other) {
          if (other == com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord.getDefaultInstance()) return this;
          if (other.getStartIndexInSamples() != 0) {
            setStartIndexInSamples(other.getStartIndexInSamples());
          }
          if (other.getEndIndexInSamples() != 0) {
            setEndIndexInSamples(other.getEndIndexInSamples());
          }
          if (other.getNearMiss() != false) {
            setNearMiss(other.getNearMiss());
          }
          if (other.getMetadata() != com.google.protobuf.ByteString.EMPTY) {
            setMetadata(other.getMetadata());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 8: {
                  startIndexInSamples_ = input.readUInt32();

                  break;
                } // case 8
                case 16: {
                  endIndexInSamples_ = input.readUInt32();

                  break;
                } // case 16
                case 24: {
                  nearMiss_ = input.readBool();

                  break;
                } // case 24
                case 34: {
                  metadata_ = input.readBytes();

                  break;
                } // case 34
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }

        private int startIndexInSamples_ ;
        /**
         * <code>uint32 start_index_in_samples = 1;</code>
         * @return The startIndexInSamples.
         */
        @java.lang.Override
        public int getStartIndexInSamples() {
          return startIndexInSamples_;
        }
        /**
         * <code>uint32 start_index_in_samples = 1;</code>
         * @param value The startIndexInSamples to set.
         * @return This builder for chaining.
         */
        public Builder setStartIndexInSamples(int value) {
          
          startIndexInSamples_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>uint32 start_index_in_samples = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearStartIndexInSamples() {
          
          startIndexInSamples_ = 0;
          onChanged();
          return this;
        }

        private int endIndexInSamples_ ;
        /**
         * <code>uint32 end_index_in_samples = 2;</code>
         * @return The endIndexInSamples.
         */
        @java.lang.Override
        public int getEndIndexInSamples() {
          return endIndexInSamples_;
        }
        /**
         * <code>uint32 end_index_in_samples = 2;</code>
         * @param value The endIndexInSamples to set.
         * @return This builder for chaining.
         */
        public Builder setEndIndexInSamples(int value) {
          
          endIndexInSamples_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>uint32 end_index_in_samples = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearEndIndexInSamples() {
          
          endIndexInSamples_ = 0;
          onChanged();
          return this;
        }

        private boolean nearMiss_ ;
        /**
         * <code>bool near_miss = 3;</code>
         * @return The nearMiss.
         */
        @java.lang.Override
        public boolean getNearMiss() {
          return nearMiss_;
        }
        /**
         * <code>bool near_miss = 3;</code>
         * @param value The nearMiss to set.
         * @return This builder for chaining.
         */
        public Builder setNearMiss(boolean value) {
          
          nearMiss_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>bool near_miss = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearNearMiss() {
          
          nearMiss_ = false;
          onChanged();
          return this;
        }

        private com.google.protobuf.ByteString metadata_ = com.google.protobuf.ByteString.EMPTY;
        /**
         * <code>bytes metadata = 4;</code>
         * @return The metadata.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString getMetadata() {
          return metadata_;
        }
        /**
         * <code>bytes metadata = 4;</code>
         * @param value The metadata to set.
         * @return This builder for chaining.
         */
        public Builder setMetadata(com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  
          metadata_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>bytes metadata = 4;</code>
         * @return This builder for chaining.
         */
        public Builder clearMetadata() {
          
          metadata_ = getDefaultInstance().getMetadata();
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:SpeechInitiator.WakeWord)
      }

      // @@protoc_insertion_point(class_scope:SpeechInitiator.WakeWord)
      private static final com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord();
      }

      public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<WakeWord>
          PARSER = new com.google.protobuf.AbstractParser<WakeWord>() {
        @java.lang.Override
        public WakeWord parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<WakeWord> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<WakeWord> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <code>.SpeechInitiator.Type type = 1;</code>
     * @return The enum numeric value on the wire for type.
     */
    @java.lang.Override public int getTypeValue() {
      return type_;
    }
    /**
     * <code>.SpeechInitiator.Type type = 1;</code>
     * @return The type.
     */
    @java.lang.Override public com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.Type getType() {
      @SuppressWarnings("deprecation")
      com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.Type result = com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.Type.valueOf(type_);
      return result == null ? com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.Type.UNRECOGNIZED : result;
    }

    public static final int WAKE_WORD_FIELD_NUMBER = 2;
    private com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord wakeWord_;
    /**
     * <code>.SpeechInitiator.WakeWord wake_word = 2;</code>
     * @return Whether the wakeWord field is set.
     */
    @java.lang.Override
    public boolean hasWakeWord() {
      return wakeWord_ != null;
    }
    /**
     * <code>.SpeechInitiator.WakeWord wake_word = 2;</code>
     * @return The wakeWord.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord getWakeWord() {
      return wakeWord_ == null ? com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord.getDefaultInstance() : wakeWord_;
    }
    /**
     * <code>.SpeechInitiator.WakeWord wake_word = 2;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWordOrBuilder getWakeWordOrBuilder() {
      return getWakeWord();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.Type.NONE.getNumber()) {
        output.writeEnum(1, type_);
      }
      if (wakeWord_ != null) {
        output.writeMessage(2, getWakeWord());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.Type.NONE.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, type_);
      }
      if (wakeWord_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getWakeWord());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator other = (com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator) obj;

      if (type_ != other.type_) return false;
      if (hasWakeWord() != other.hasWakeWord()) return false;
      if (hasWakeWord()) {
        if (!getWakeWord()
            .equals(other.getWakeWord())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + type_;
      if (hasWakeWord()) {
        hash = (37 * hash) + WAKE_WORD_FIELD_NUMBER;
        hash = (53 * hash) + getWakeWord().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code SpeechInitiator}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:SpeechInitiator)
        com.amazon.alexa.accessory.protocol.Speech.SpeechInitiatorOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_SpeechInitiator_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_SpeechInitiator_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.class, com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;

        if (wakeWordBuilder_ == null) {
          wakeWord_ = null;
        } else {
          wakeWord_ = null;
          wakeWordBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_SpeechInitiator_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator build() {
        com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator buildPartial() {
        com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator result = new com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator(this);
        result.type_ = type_;
        if (wakeWordBuilder_ == null) {
          result.wakeWord_ = wakeWord_;
        } else {
          result.wakeWord_ = wakeWordBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator other) {
        if (other == com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.getDefaultInstance()) return this;
        if (other.type_ != 0) {
          setTypeValue(other.getTypeValue());
        }
        if (other.hasWakeWord()) {
          mergeWakeWord(other.getWakeWord());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                type_ = input.readEnum();

                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getWakeWordFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private int type_ = 0;
      /**
       * <code>.SpeechInitiator.Type type = 1;</code>
       * @return The enum numeric value on the wire for type.
       */
      @java.lang.Override public int getTypeValue() {
        return type_;
      }
      /**
       * <code>.SpeechInitiator.Type type = 1;</code>
       * @param value The enum numeric value on the wire for type to set.
       * @return This builder for chaining.
       */
      public Builder setTypeValue(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.SpeechInitiator.Type type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.Type getType() {
        @SuppressWarnings("deprecation")
        com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.Type result = com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.Type.valueOf(type_);
        return result == null ? com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.Type.UNRECOGNIZED : result;
      }
      /**
       * <code>.SpeechInitiator.Type type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.Type value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        type_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.SpeechInitiator.Type type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord wakeWord_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord, com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord.Builder, com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWordOrBuilder> wakeWordBuilder_;
      /**
       * <code>.SpeechInitiator.WakeWord wake_word = 2;</code>
       * @return Whether the wakeWord field is set.
       */
      public boolean hasWakeWord() {
        return wakeWordBuilder_ != null || wakeWord_ != null;
      }
      /**
       * <code>.SpeechInitiator.WakeWord wake_word = 2;</code>
       * @return The wakeWord.
       */
      public com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord getWakeWord() {
        if (wakeWordBuilder_ == null) {
          return wakeWord_ == null ? com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord.getDefaultInstance() : wakeWord_;
        } else {
          return wakeWordBuilder_.getMessage();
        }
      }
      /**
       * <code>.SpeechInitiator.WakeWord wake_word = 2;</code>
       */
      public Builder setWakeWord(com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord value) {
        if (wakeWordBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          wakeWord_ = value;
          onChanged();
        } else {
          wakeWordBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.SpeechInitiator.WakeWord wake_word = 2;</code>
       */
      public Builder setWakeWord(
          com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord.Builder builderForValue) {
        if (wakeWordBuilder_ == null) {
          wakeWord_ = builderForValue.build();
          onChanged();
        } else {
          wakeWordBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.SpeechInitiator.WakeWord wake_word = 2;</code>
       */
      public Builder mergeWakeWord(com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord value) {
        if (wakeWordBuilder_ == null) {
          if (wakeWord_ != null) {
            wakeWord_ =
              com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord.newBuilder(wakeWord_).mergeFrom(value).buildPartial();
          } else {
            wakeWord_ = value;
          }
          onChanged();
        } else {
          wakeWordBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.SpeechInitiator.WakeWord wake_word = 2;</code>
       */
      public Builder clearWakeWord() {
        if (wakeWordBuilder_ == null) {
          wakeWord_ = null;
          onChanged();
        } else {
          wakeWord_ = null;
          wakeWordBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.SpeechInitiator.WakeWord wake_word = 2;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord.Builder getWakeWordBuilder() {
        
        onChanged();
        return getWakeWordFieldBuilder().getBuilder();
      }
      /**
       * <code>.SpeechInitiator.WakeWord wake_word = 2;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWordOrBuilder getWakeWordOrBuilder() {
        if (wakeWordBuilder_ != null) {
          return wakeWordBuilder_.getMessageOrBuilder();
        } else {
          return wakeWord_ == null ?
              com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord.getDefaultInstance() : wakeWord_;
        }
      }
      /**
       * <code>.SpeechInitiator.WakeWord wake_word = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord, com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord.Builder, com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWordOrBuilder> 
          getWakeWordFieldBuilder() {
        if (wakeWordBuilder_ == null) {
          wakeWordBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord, com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWord.Builder, com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.WakeWordOrBuilder>(
                  getWakeWord(),
                  getParentForChildren(),
                  isClean());
          wakeWord_ = null;
        }
        return wakeWordBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:SpeechInitiator)
    }

    // @@protoc_insertion_point(class_scope:SpeechInitiator)
    private static final com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator();
    }

    public static com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SpeechInitiator>
        PARSER = new com.google.protobuf.AbstractParser<SpeechInitiator>() {
      @java.lang.Override
      public SpeechInitiator parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SpeechInitiator> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SpeechInitiator> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface StartSpeechOrBuilder extends
      // @@protoc_insertion_point(interface_extends:StartSpeech)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.SpeechSettings settings = 1;</code>
     * @return Whether the settings field is set.
     */
    boolean hasSettings();
    /**
     * <code>.SpeechSettings settings = 1;</code>
     * @return The settings.
     */
    com.amazon.alexa.accessory.protocol.Speech.SpeechSettings getSettings();
    /**
     * <code>.SpeechSettings settings = 1;</code>
     */
    com.amazon.alexa.accessory.protocol.Speech.SpeechSettingsOrBuilder getSettingsOrBuilder();

    /**
     * <code>.SpeechInitiator initiator = 2;</code>
     * @return Whether the initiator field is set.
     */
    boolean hasInitiator();
    /**
     * <code>.SpeechInitiator initiator = 2;</code>
     * @return The initiator.
     */
    com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator getInitiator();
    /**
     * <code>.SpeechInitiator initiator = 2;</code>
     */
    com.amazon.alexa.accessory.protocol.Speech.SpeechInitiatorOrBuilder getInitiatorOrBuilder();

    /**
     * <code>.Dialog dialog = 3;</code>
     * @return Whether the dialog field is set.
     */
    boolean hasDialog();
    /**
     * <code>.Dialog dialog = 3;</code>
     * @return The dialog.
     */
    com.amazon.alexa.accessory.protocol.Speech.Dialog getDialog();
    /**
     * <code>.Dialog dialog = 3;</code>
     */
    com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder getDialogOrBuilder();

    /**
     * <code>bool suppressEndpointEarcon = 4;</code>
     * @return The suppressEndpointEarcon.
     */
    boolean getSuppressEndpointEarcon();

    /**
     * <code>bool suppressStartEarcon = 5;</code>
     * @return The suppressStartEarcon.
     */
    boolean getSuppressStartEarcon();
  }
  /**
   * Protobuf type {@code StartSpeech}
   */
  public static final class StartSpeech extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:StartSpeech)
      StartSpeechOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use StartSpeech.newBuilder() to construct.
    private StartSpeech(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private StartSpeech() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new StartSpeech();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Speech.internal_static_StartSpeech_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Speech.internal_static_StartSpeech_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Speech.StartSpeech.class, com.amazon.alexa.accessory.protocol.Speech.StartSpeech.Builder.class);
    }

    public static final int SETTINGS_FIELD_NUMBER = 1;
    private com.amazon.alexa.accessory.protocol.Speech.SpeechSettings settings_;
    /**
     * <code>.SpeechSettings settings = 1;</code>
     * @return Whether the settings field is set.
     */
    @java.lang.Override
    public boolean hasSettings() {
      return settings_ != null;
    }
    /**
     * <code>.SpeechSettings settings = 1;</code>
     * @return The settings.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.SpeechSettings getSettings() {
      return settings_ == null ? com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.getDefaultInstance() : settings_;
    }
    /**
     * <code>.SpeechSettings settings = 1;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.SpeechSettingsOrBuilder getSettingsOrBuilder() {
      return getSettings();
    }

    public static final int INITIATOR_FIELD_NUMBER = 2;
    private com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator initiator_;
    /**
     * <code>.SpeechInitiator initiator = 2;</code>
     * @return Whether the initiator field is set.
     */
    @java.lang.Override
    public boolean hasInitiator() {
      return initiator_ != null;
    }
    /**
     * <code>.SpeechInitiator initiator = 2;</code>
     * @return The initiator.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator getInitiator() {
      return initiator_ == null ? com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.getDefaultInstance() : initiator_;
    }
    /**
     * <code>.SpeechInitiator initiator = 2;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.SpeechInitiatorOrBuilder getInitiatorOrBuilder() {
      return getInitiator();
    }

    public static final int DIALOG_FIELD_NUMBER = 3;
    private com.amazon.alexa.accessory.protocol.Speech.Dialog dialog_;
    /**
     * <code>.Dialog dialog = 3;</code>
     * @return Whether the dialog field is set.
     */
    @java.lang.Override
    public boolean hasDialog() {
      return dialog_ != null;
    }
    /**
     * <code>.Dialog dialog = 3;</code>
     * @return The dialog.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.Dialog getDialog() {
      return dialog_ == null ? com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance() : dialog_;
    }
    /**
     * <code>.Dialog dialog = 3;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder getDialogOrBuilder() {
      return getDialog();
    }

    public static final int SUPPRESSENDPOINTEARCON_FIELD_NUMBER = 4;
    private boolean suppressEndpointEarcon_;
    /**
     * <code>bool suppressEndpointEarcon = 4;</code>
     * @return The suppressEndpointEarcon.
     */
    @java.lang.Override
    public boolean getSuppressEndpointEarcon() {
      return suppressEndpointEarcon_;
    }

    public static final int SUPPRESSSTARTEARCON_FIELD_NUMBER = 5;
    private boolean suppressStartEarcon_;
    /**
     * <code>bool suppressStartEarcon = 5;</code>
     * @return The suppressStartEarcon.
     */
    @java.lang.Override
    public boolean getSuppressStartEarcon() {
      return suppressStartEarcon_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (settings_ != null) {
        output.writeMessage(1, getSettings());
      }
      if (initiator_ != null) {
        output.writeMessage(2, getInitiator());
      }
      if (dialog_ != null) {
        output.writeMessage(3, getDialog());
      }
      if (suppressEndpointEarcon_ != false) {
        output.writeBool(4, suppressEndpointEarcon_);
      }
      if (suppressStartEarcon_ != false) {
        output.writeBool(5, suppressStartEarcon_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (settings_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getSettings());
      }
      if (initiator_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getInitiator());
      }
      if (dialog_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getDialog());
      }
      if (suppressEndpointEarcon_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(4, suppressEndpointEarcon_);
      }
      if (suppressStartEarcon_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(5, suppressStartEarcon_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Speech.StartSpeech)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Speech.StartSpeech other = (com.amazon.alexa.accessory.protocol.Speech.StartSpeech) obj;

      if (hasSettings() != other.hasSettings()) return false;
      if (hasSettings()) {
        if (!getSettings()
            .equals(other.getSettings())) return false;
      }
      if (hasInitiator() != other.hasInitiator()) return false;
      if (hasInitiator()) {
        if (!getInitiator()
            .equals(other.getInitiator())) return false;
      }
      if (hasDialog() != other.hasDialog()) return false;
      if (hasDialog()) {
        if (!getDialog()
            .equals(other.getDialog())) return false;
      }
      if (getSuppressEndpointEarcon()
          != other.getSuppressEndpointEarcon()) return false;
      if (getSuppressStartEarcon()
          != other.getSuppressStartEarcon()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSettings()) {
        hash = (37 * hash) + SETTINGS_FIELD_NUMBER;
        hash = (53 * hash) + getSettings().hashCode();
      }
      if (hasInitiator()) {
        hash = (37 * hash) + INITIATOR_FIELD_NUMBER;
        hash = (53 * hash) + getInitiator().hashCode();
      }
      if (hasDialog()) {
        hash = (37 * hash) + DIALOG_FIELD_NUMBER;
        hash = (53 * hash) + getDialog().hashCode();
      }
      hash = (37 * hash) + SUPPRESSENDPOINTEARCON_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getSuppressEndpointEarcon());
      hash = (37 * hash) + SUPPRESSSTARTEARCON_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getSuppressStartEarcon());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Speech.StartSpeech parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StartSpeech parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StartSpeech parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StartSpeech parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StartSpeech parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StartSpeech parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StartSpeech parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StartSpeech parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StartSpeech parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StartSpeech parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StartSpeech parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StartSpeech parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Speech.StartSpeech prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code StartSpeech}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:StartSpeech)
        com.amazon.alexa.accessory.protocol.Speech.StartSpeechOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_StartSpeech_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_StartSpeech_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Speech.StartSpeech.class, com.amazon.alexa.accessory.protocol.Speech.StartSpeech.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Speech.StartSpeech.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (settingsBuilder_ == null) {
          settings_ = null;
        } else {
          settings_ = null;
          settingsBuilder_ = null;
        }
        if (initiatorBuilder_ == null) {
          initiator_ = null;
        } else {
          initiator_ = null;
          initiatorBuilder_ = null;
        }
        if (dialogBuilder_ == null) {
          dialog_ = null;
        } else {
          dialog_ = null;
          dialogBuilder_ = null;
        }
        suppressEndpointEarcon_ = false;

        suppressStartEarcon_ = false;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_StartSpeech_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.StartSpeech getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Speech.StartSpeech.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.StartSpeech build() {
        com.amazon.alexa.accessory.protocol.Speech.StartSpeech result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.StartSpeech buildPartial() {
        com.amazon.alexa.accessory.protocol.Speech.StartSpeech result = new com.amazon.alexa.accessory.protocol.Speech.StartSpeech(this);
        if (settingsBuilder_ == null) {
          result.settings_ = settings_;
        } else {
          result.settings_ = settingsBuilder_.build();
        }
        if (initiatorBuilder_ == null) {
          result.initiator_ = initiator_;
        } else {
          result.initiator_ = initiatorBuilder_.build();
        }
        if (dialogBuilder_ == null) {
          result.dialog_ = dialog_;
        } else {
          result.dialog_ = dialogBuilder_.build();
        }
        result.suppressEndpointEarcon_ = suppressEndpointEarcon_;
        result.suppressStartEarcon_ = suppressStartEarcon_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Speech.StartSpeech) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Speech.StartSpeech)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Speech.StartSpeech other) {
        if (other == com.amazon.alexa.accessory.protocol.Speech.StartSpeech.getDefaultInstance()) return this;
        if (other.hasSettings()) {
          mergeSettings(other.getSettings());
        }
        if (other.hasInitiator()) {
          mergeInitiator(other.getInitiator());
        }
        if (other.hasDialog()) {
          mergeDialog(other.getDialog());
        }
        if (other.getSuppressEndpointEarcon() != false) {
          setSuppressEndpointEarcon(other.getSuppressEndpointEarcon());
        }
        if (other.getSuppressStartEarcon() != false) {
          setSuppressStartEarcon(other.getSuppressStartEarcon());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getSettingsFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 10
              case 18: {
                input.readMessage(
                    getInitiatorFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 18
              case 26: {
                input.readMessage(
                    getDialogFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 26
              case 32: {
                suppressEndpointEarcon_ = input.readBool();

                break;
              } // case 32
              case 40: {
                suppressStartEarcon_ = input.readBool();

                break;
              } // case 40
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private com.amazon.alexa.accessory.protocol.Speech.SpeechSettings settings_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.SpeechSettings, com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.Builder, com.amazon.alexa.accessory.protocol.Speech.SpeechSettingsOrBuilder> settingsBuilder_;
      /**
       * <code>.SpeechSettings settings = 1;</code>
       * @return Whether the settings field is set.
       */
      public boolean hasSettings() {
        return settingsBuilder_ != null || settings_ != null;
      }
      /**
       * <code>.SpeechSettings settings = 1;</code>
       * @return The settings.
       */
      public com.amazon.alexa.accessory.protocol.Speech.SpeechSettings getSettings() {
        if (settingsBuilder_ == null) {
          return settings_ == null ? com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.getDefaultInstance() : settings_;
        } else {
          return settingsBuilder_.getMessage();
        }
      }
      /**
       * <code>.SpeechSettings settings = 1;</code>
       */
      public Builder setSettings(com.amazon.alexa.accessory.protocol.Speech.SpeechSettings value) {
        if (settingsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          settings_ = value;
          onChanged();
        } else {
          settingsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.SpeechSettings settings = 1;</code>
       */
      public Builder setSettings(
          com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.Builder builderForValue) {
        if (settingsBuilder_ == null) {
          settings_ = builderForValue.build();
          onChanged();
        } else {
          settingsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.SpeechSettings settings = 1;</code>
       */
      public Builder mergeSettings(com.amazon.alexa.accessory.protocol.Speech.SpeechSettings value) {
        if (settingsBuilder_ == null) {
          if (settings_ != null) {
            settings_ =
              com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.newBuilder(settings_).mergeFrom(value).buildPartial();
          } else {
            settings_ = value;
          }
          onChanged();
        } else {
          settingsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.SpeechSettings settings = 1;</code>
       */
      public Builder clearSettings() {
        if (settingsBuilder_ == null) {
          settings_ = null;
          onChanged();
        } else {
          settings_ = null;
          settingsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.SpeechSettings settings = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.Builder getSettingsBuilder() {
        
        onChanged();
        return getSettingsFieldBuilder().getBuilder();
      }
      /**
       * <code>.SpeechSettings settings = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.SpeechSettingsOrBuilder getSettingsOrBuilder() {
        if (settingsBuilder_ != null) {
          return settingsBuilder_.getMessageOrBuilder();
        } else {
          return settings_ == null ?
              com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.getDefaultInstance() : settings_;
        }
      }
      /**
       * <code>.SpeechSettings settings = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.SpeechSettings, com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.Builder, com.amazon.alexa.accessory.protocol.Speech.SpeechSettingsOrBuilder> 
          getSettingsFieldBuilder() {
        if (settingsBuilder_ == null) {
          settingsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Speech.SpeechSettings, com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.Builder, com.amazon.alexa.accessory.protocol.Speech.SpeechSettingsOrBuilder>(
                  getSettings(),
                  getParentForChildren(),
                  isClean());
          settings_ = null;
        }
        return settingsBuilder_;
      }

      private com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator initiator_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator, com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.Builder, com.amazon.alexa.accessory.protocol.Speech.SpeechInitiatorOrBuilder> initiatorBuilder_;
      /**
       * <code>.SpeechInitiator initiator = 2;</code>
       * @return Whether the initiator field is set.
       */
      public boolean hasInitiator() {
        return initiatorBuilder_ != null || initiator_ != null;
      }
      /**
       * <code>.SpeechInitiator initiator = 2;</code>
       * @return The initiator.
       */
      public com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator getInitiator() {
        if (initiatorBuilder_ == null) {
          return initiator_ == null ? com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.getDefaultInstance() : initiator_;
        } else {
          return initiatorBuilder_.getMessage();
        }
      }
      /**
       * <code>.SpeechInitiator initiator = 2;</code>
       */
      public Builder setInitiator(com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator value) {
        if (initiatorBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          initiator_ = value;
          onChanged();
        } else {
          initiatorBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.SpeechInitiator initiator = 2;</code>
       */
      public Builder setInitiator(
          com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.Builder builderForValue) {
        if (initiatorBuilder_ == null) {
          initiator_ = builderForValue.build();
          onChanged();
        } else {
          initiatorBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.SpeechInitiator initiator = 2;</code>
       */
      public Builder mergeInitiator(com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator value) {
        if (initiatorBuilder_ == null) {
          if (initiator_ != null) {
            initiator_ =
              com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.newBuilder(initiator_).mergeFrom(value).buildPartial();
          } else {
            initiator_ = value;
          }
          onChanged();
        } else {
          initiatorBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.SpeechInitiator initiator = 2;</code>
       */
      public Builder clearInitiator() {
        if (initiatorBuilder_ == null) {
          initiator_ = null;
          onChanged();
        } else {
          initiator_ = null;
          initiatorBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.SpeechInitiator initiator = 2;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.Builder getInitiatorBuilder() {
        
        onChanged();
        return getInitiatorFieldBuilder().getBuilder();
      }
      /**
       * <code>.SpeechInitiator initiator = 2;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.SpeechInitiatorOrBuilder getInitiatorOrBuilder() {
        if (initiatorBuilder_ != null) {
          return initiatorBuilder_.getMessageOrBuilder();
        } else {
          return initiator_ == null ?
              com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.getDefaultInstance() : initiator_;
        }
      }
      /**
       * <code>.SpeechInitiator initiator = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator, com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.Builder, com.amazon.alexa.accessory.protocol.Speech.SpeechInitiatorOrBuilder> 
          getInitiatorFieldBuilder() {
        if (initiatorBuilder_ == null) {
          initiatorBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator, com.amazon.alexa.accessory.protocol.Speech.SpeechInitiator.Builder, com.amazon.alexa.accessory.protocol.Speech.SpeechInitiatorOrBuilder>(
                  getInitiator(),
                  getParentForChildren(),
                  isClean());
          initiator_ = null;
        }
        return initiatorBuilder_;
      }

      private com.amazon.alexa.accessory.protocol.Speech.Dialog dialog_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.Dialog, com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder, com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder> dialogBuilder_;
      /**
       * <code>.Dialog dialog = 3;</code>
       * @return Whether the dialog field is set.
       */
      public boolean hasDialog() {
        return dialogBuilder_ != null || dialog_ != null;
      }
      /**
       * <code>.Dialog dialog = 3;</code>
       * @return The dialog.
       */
      public com.amazon.alexa.accessory.protocol.Speech.Dialog getDialog() {
        if (dialogBuilder_ == null) {
          return dialog_ == null ? com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance() : dialog_;
        } else {
          return dialogBuilder_.getMessage();
        }
      }
      /**
       * <code>.Dialog dialog = 3;</code>
       */
      public Builder setDialog(com.amazon.alexa.accessory.protocol.Speech.Dialog value) {
        if (dialogBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          dialog_ = value;
          onChanged();
        } else {
          dialogBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Dialog dialog = 3;</code>
       */
      public Builder setDialog(
          com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder builderForValue) {
        if (dialogBuilder_ == null) {
          dialog_ = builderForValue.build();
          onChanged();
        } else {
          dialogBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Dialog dialog = 3;</code>
       */
      public Builder mergeDialog(com.amazon.alexa.accessory.protocol.Speech.Dialog value) {
        if (dialogBuilder_ == null) {
          if (dialog_ != null) {
            dialog_ =
              com.amazon.alexa.accessory.protocol.Speech.Dialog.newBuilder(dialog_).mergeFrom(value).buildPartial();
          } else {
            dialog_ = value;
          }
          onChanged();
        } else {
          dialogBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Dialog dialog = 3;</code>
       */
      public Builder clearDialog() {
        if (dialogBuilder_ == null) {
          dialog_ = null;
          onChanged();
        } else {
          dialog_ = null;
          dialogBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Dialog dialog = 3;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder getDialogBuilder() {
        
        onChanged();
        return getDialogFieldBuilder().getBuilder();
      }
      /**
       * <code>.Dialog dialog = 3;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder getDialogOrBuilder() {
        if (dialogBuilder_ != null) {
          return dialogBuilder_.getMessageOrBuilder();
        } else {
          return dialog_ == null ?
              com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance() : dialog_;
        }
      }
      /**
       * <code>.Dialog dialog = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.Dialog, com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder, com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder> 
          getDialogFieldBuilder() {
        if (dialogBuilder_ == null) {
          dialogBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Speech.Dialog, com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder, com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder>(
                  getDialog(),
                  getParentForChildren(),
                  isClean());
          dialog_ = null;
        }
        return dialogBuilder_;
      }

      private boolean suppressEndpointEarcon_ ;
      /**
       * <code>bool suppressEndpointEarcon = 4;</code>
       * @return The suppressEndpointEarcon.
       */
      @java.lang.Override
      public boolean getSuppressEndpointEarcon() {
        return suppressEndpointEarcon_;
      }
      /**
       * <code>bool suppressEndpointEarcon = 4;</code>
       * @param value The suppressEndpointEarcon to set.
       * @return This builder for chaining.
       */
      public Builder setSuppressEndpointEarcon(boolean value) {
        
        suppressEndpointEarcon_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>bool suppressEndpointEarcon = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearSuppressEndpointEarcon() {
        
        suppressEndpointEarcon_ = false;
        onChanged();
        return this;
      }

      private boolean suppressStartEarcon_ ;
      /**
       * <code>bool suppressStartEarcon = 5;</code>
       * @return The suppressStartEarcon.
       */
      @java.lang.Override
      public boolean getSuppressStartEarcon() {
        return suppressStartEarcon_;
      }
      /**
       * <code>bool suppressStartEarcon = 5;</code>
       * @param value The suppressStartEarcon to set.
       * @return This builder for chaining.
       */
      public Builder setSuppressStartEarcon(boolean value) {
        
        suppressStartEarcon_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>bool suppressStartEarcon = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearSuppressStartEarcon() {
        
        suppressStartEarcon_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:StartSpeech)
    }

    // @@protoc_insertion_point(class_scope:StartSpeech)
    private static final com.amazon.alexa.accessory.protocol.Speech.StartSpeech DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Speech.StartSpeech();
    }

    public static com.amazon.alexa.accessory.protocol.Speech.StartSpeech getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<StartSpeech>
        PARSER = new com.google.protobuf.AbstractParser<StartSpeech>() {
      @java.lang.Override
      public StartSpeech parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<StartSpeech> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<StartSpeech> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.StartSpeech getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SpeechProviderOrBuilder extends
      // @@protoc_insertion_point(interface_extends:SpeechProvider)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.SpeechSettings speech_settings = 1;</code>
     * @return Whether the speechSettings field is set.
     */
    boolean hasSpeechSettings();
    /**
     * <code>.SpeechSettings speech_settings = 1;</code>
     * @return The speechSettings.
     */
    com.amazon.alexa.accessory.protocol.Speech.SpeechSettings getSpeechSettings();
    /**
     * <code>.SpeechSettings speech_settings = 1;</code>
     */
    com.amazon.alexa.accessory.protocol.Speech.SpeechSettingsOrBuilder getSpeechSettingsOrBuilder();

    /**
     * <code>.Dialog dialog = 2;</code>
     * @return Whether the dialog field is set.
     */
    boolean hasDialog();
    /**
     * <code>.Dialog dialog = 2;</code>
     * @return The dialog.
     */
    com.amazon.alexa.accessory.protocol.Speech.Dialog getDialog();
    /**
     * <code>.Dialog dialog = 2;</code>
     */
    com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder getDialogOrBuilder();
  }
  /**
   * Protobuf type {@code SpeechProvider}
   */
  public static final class SpeechProvider extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:SpeechProvider)
      SpeechProviderOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SpeechProvider.newBuilder() to construct.
    private SpeechProvider(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SpeechProvider() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SpeechProvider();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Speech.internal_static_SpeechProvider_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Speech.internal_static_SpeechProvider_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Speech.SpeechProvider.class, com.amazon.alexa.accessory.protocol.Speech.SpeechProvider.Builder.class);
    }

    public static final int SPEECH_SETTINGS_FIELD_NUMBER = 1;
    private com.amazon.alexa.accessory.protocol.Speech.SpeechSettings speechSettings_;
    /**
     * <code>.SpeechSettings speech_settings = 1;</code>
     * @return Whether the speechSettings field is set.
     */
    @java.lang.Override
    public boolean hasSpeechSettings() {
      return speechSettings_ != null;
    }
    /**
     * <code>.SpeechSettings speech_settings = 1;</code>
     * @return The speechSettings.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.SpeechSettings getSpeechSettings() {
      return speechSettings_ == null ? com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.getDefaultInstance() : speechSettings_;
    }
    /**
     * <code>.SpeechSettings speech_settings = 1;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.SpeechSettingsOrBuilder getSpeechSettingsOrBuilder() {
      return getSpeechSettings();
    }

    public static final int DIALOG_FIELD_NUMBER = 2;
    private com.amazon.alexa.accessory.protocol.Speech.Dialog dialog_;
    /**
     * <code>.Dialog dialog = 2;</code>
     * @return Whether the dialog field is set.
     */
    @java.lang.Override
    public boolean hasDialog() {
      return dialog_ != null;
    }
    /**
     * <code>.Dialog dialog = 2;</code>
     * @return The dialog.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.Dialog getDialog() {
      return dialog_ == null ? com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance() : dialog_;
    }
    /**
     * <code>.Dialog dialog = 2;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder getDialogOrBuilder() {
      return getDialog();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (speechSettings_ != null) {
        output.writeMessage(1, getSpeechSettings());
      }
      if (dialog_ != null) {
        output.writeMessage(2, getDialog());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (speechSettings_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getSpeechSettings());
      }
      if (dialog_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getDialog());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Speech.SpeechProvider)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Speech.SpeechProvider other = (com.amazon.alexa.accessory.protocol.Speech.SpeechProvider) obj;

      if (hasSpeechSettings() != other.hasSpeechSettings()) return false;
      if (hasSpeechSettings()) {
        if (!getSpeechSettings()
            .equals(other.getSpeechSettings())) return false;
      }
      if (hasDialog() != other.hasDialog()) return false;
      if (hasDialog()) {
        if (!getDialog()
            .equals(other.getDialog())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSpeechSettings()) {
        hash = (37 * hash) + SPEECH_SETTINGS_FIELD_NUMBER;
        hash = (53 * hash) + getSpeechSettings().hashCode();
      }
      if (hasDialog()) {
        hash = (37 * hash) + DIALOG_FIELD_NUMBER;
        hash = (53 * hash) + getDialog().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Speech.SpeechProvider parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechProvider parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechProvider parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechProvider parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechProvider parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechProvider parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechProvider parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechProvider parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechProvider parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechProvider parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechProvider parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.SpeechProvider parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Speech.SpeechProvider prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code SpeechProvider}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:SpeechProvider)
        com.amazon.alexa.accessory.protocol.Speech.SpeechProviderOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_SpeechProvider_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_SpeechProvider_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Speech.SpeechProvider.class, com.amazon.alexa.accessory.protocol.Speech.SpeechProvider.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Speech.SpeechProvider.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (speechSettingsBuilder_ == null) {
          speechSettings_ = null;
        } else {
          speechSettings_ = null;
          speechSettingsBuilder_ = null;
        }
        if (dialogBuilder_ == null) {
          dialog_ = null;
        } else {
          dialog_ = null;
          dialogBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_SpeechProvider_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.SpeechProvider getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Speech.SpeechProvider.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.SpeechProvider build() {
        com.amazon.alexa.accessory.protocol.Speech.SpeechProvider result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.SpeechProvider buildPartial() {
        com.amazon.alexa.accessory.protocol.Speech.SpeechProvider result = new com.amazon.alexa.accessory.protocol.Speech.SpeechProvider(this);
        if (speechSettingsBuilder_ == null) {
          result.speechSettings_ = speechSettings_;
        } else {
          result.speechSettings_ = speechSettingsBuilder_.build();
        }
        if (dialogBuilder_ == null) {
          result.dialog_ = dialog_;
        } else {
          result.dialog_ = dialogBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Speech.SpeechProvider) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Speech.SpeechProvider)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Speech.SpeechProvider other) {
        if (other == com.amazon.alexa.accessory.protocol.Speech.SpeechProvider.getDefaultInstance()) return this;
        if (other.hasSpeechSettings()) {
          mergeSpeechSettings(other.getSpeechSettings());
        }
        if (other.hasDialog()) {
          mergeDialog(other.getDialog());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getSpeechSettingsFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 10
              case 18: {
                input.readMessage(
                    getDialogFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private com.amazon.alexa.accessory.protocol.Speech.SpeechSettings speechSettings_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.SpeechSettings, com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.Builder, com.amazon.alexa.accessory.protocol.Speech.SpeechSettingsOrBuilder> speechSettingsBuilder_;
      /**
       * <code>.SpeechSettings speech_settings = 1;</code>
       * @return Whether the speechSettings field is set.
       */
      public boolean hasSpeechSettings() {
        return speechSettingsBuilder_ != null || speechSettings_ != null;
      }
      /**
       * <code>.SpeechSettings speech_settings = 1;</code>
       * @return The speechSettings.
       */
      public com.amazon.alexa.accessory.protocol.Speech.SpeechSettings getSpeechSettings() {
        if (speechSettingsBuilder_ == null) {
          return speechSettings_ == null ? com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.getDefaultInstance() : speechSettings_;
        } else {
          return speechSettingsBuilder_.getMessage();
        }
      }
      /**
       * <code>.SpeechSettings speech_settings = 1;</code>
       */
      public Builder setSpeechSettings(com.amazon.alexa.accessory.protocol.Speech.SpeechSettings value) {
        if (speechSettingsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          speechSettings_ = value;
          onChanged();
        } else {
          speechSettingsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.SpeechSettings speech_settings = 1;</code>
       */
      public Builder setSpeechSettings(
          com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.Builder builderForValue) {
        if (speechSettingsBuilder_ == null) {
          speechSettings_ = builderForValue.build();
          onChanged();
        } else {
          speechSettingsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.SpeechSettings speech_settings = 1;</code>
       */
      public Builder mergeSpeechSettings(com.amazon.alexa.accessory.protocol.Speech.SpeechSettings value) {
        if (speechSettingsBuilder_ == null) {
          if (speechSettings_ != null) {
            speechSettings_ =
              com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.newBuilder(speechSettings_).mergeFrom(value).buildPartial();
          } else {
            speechSettings_ = value;
          }
          onChanged();
        } else {
          speechSettingsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.SpeechSettings speech_settings = 1;</code>
       */
      public Builder clearSpeechSettings() {
        if (speechSettingsBuilder_ == null) {
          speechSettings_ = null;
          onChanged();
        } else {
          speechSettings_ = null;
          speechSettingsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.SpeechSettings speech_settings = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.Builder getSpeechSettingsBuilder() {
        
        onChanged();
        return getSpeechSettingsFieldBuilder().getBuilder();
      }
      /**
       * <code>.SpeechSettings speech_settings = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.SpeechSettingsOrBuilder getSpeechSettingsOrBuilder() {
        if (speechSettingsBuilder_ != null) {
          return speechSettingsBuilder_.getMessageOrBuilder();
        } else {
          return speechSettings_ == null ?
              com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.getDefaultInstance() : speechSettings_;
        }
      }
      /**
       * <code>.SpeechSettings speech_settings = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.SpeechSettings, com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.Builder, com.amazon.alexa.accessory.protocol.Speech.SpeechSettingsOrBuilder> 
          getSpeechSettingsFieldBuilder() {
        if (speechSettingsBuilder_ == null) {
          speechSettingsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Speech.SpeechSettings, com.amazon.alexa.accessory.protocol.Speech.SpeechSettings.Builder, com.amazon.alexa.accessory.protocol.Speech.SpeechSettingsOrBuilder>(
                  getSpeechSettings(),
                  getParentForChildren(),
                  isClean());
          speechSettings_ = null;
        }
        return speechSettingsBuilder_;
      }

      private com.amazon.alexa.accessory.protocol.Speech.Dialog dialog_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.Dialog, com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder, com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder> dialogBuilder_;
      /**
       * <code>.Dialog dialog = 2;</code>
       * @return Whether the dialog field is set.
       */
      public boolean hasDialog() {
        return dialogBuilder_ != null || dialog_ != null;
      }
      /**
       * <code>.Dialog dialog = 2;</code>
       * @return The dialog.
       */
      public com.amazon.alexa.accessory.protocol.Speech.Dialog getDialog() {
        if (dialogBuilder_ == null) {
          return dialog_ == null ? com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance() : dialog_;
        } else {
          return dialogBuilder_.getMessage();
        }
      }
      /**
       * <code>.Dialog dialog = 2;</code>
       */
      public Builder setDialog(com.amazon.alexa.accessory.protocol.Speech.Dialog value) {
        if (dialogBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          dialog_ = value;
          onChanged();
        } else {
          dialogBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Dialog dialog = 2;</code>
       */
      public Builder setDialog(
          com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder builderForValue) {
        if (dialogBuilder_ == null) {
          dialog_ = builderForValue.build();
          onChanged();
        } else {
          dialogBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Dialog dialog = 2;</code>
       */
      public Builder mergeDialog(com.amazon.alexa.accessory.protocol.Speech.Dialog value) {
        if (dialogBuilder_ == null) {
          if (dialog_ != null) {
            dialog_ =
              com.amazon.alexa.accessory.protocol.Speech.Dialog.newBuilder(dialog_).mergeFrom(value).buildPartial();
          } else {
            dialog_ = value;
          }
          onChanged();
        } else {
          dialogBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Dialog dialog = 2;</code>
       */
      public Builder clearDialog() {
        if (dialogBuilder_ == null) {
          dialog_ = null;
          onChanged();
        } else {
          dialog_ = null;
          dialogBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Dialog dialog = 2;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder getDialogBuilder() {
        
        onChanged();
        return getDialogFieldBuilder().getBuilder();
      }
      /**
       * <code>.Dialog dialog = 2;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder getDialogOrBuilder() {
        if (dialogBuilder_ != null) {
          return dialogBuilder_.getMessageOrBuilder();
        } else {
          return dialog_ == null ?
              com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance() : dialog_;
        }
      }
      /**
       * <code>.Dialog dialog = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.Dialog, com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder, com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder> 
          getDialogFieldBuilder() {
        if (dialogBuilder_ == null) {
          dialogBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Speech.Dialog, com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder, com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder>(
                  getDialog(),
                  getParentForChildren(),
                  isClean());
          dialog_ = null;
        }
        return dialogBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:SpeechProvider)
    }

    // @@protoc_insertion_point(class_scope:SpeechProvider)
    private static final com.amazon.alexa.accessory.protocol.Speech.SpeechProvider DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Speech.SpeechProvider();
    }

    public static com.amazon.alexa.accessory.protocol.Speech.SpeechProvider getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SpeechProvider>
        PARSER = new com.google.protobuf.AbstractParser<SpeechProvider>() {
      @java.lang.Override
      public SpeechProvider parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SpeechProvider> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SpeechProvider> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.SpeechProvider getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ProvideSpeechOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProvideSpeech)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Dialog dialog = 1;</code>
     * @return Whether the dialog field is set.
     */
    boolean hasDialog();
    /**
     * <code>.Dialog dialog = 1;</code>
     * @return The dialog.
     */
    com.amazon.alexa.accessory.protocol.Speech.Dialog getDialog();
    /**
     * <code>.Dialog dialog = 1;</code>
     */
    com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder getDialogOrBuilder();
  }
  /**
   * Protobuf type {@code ProvideSpeech}
   */
  public static final class ProvideSpeech extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProvideSpeech)
      ProvideSpeechOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ProvideSpeech.newBuilder() to construct.
    private ProvideSpeech(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ProvideSpeech() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ProvideSpeech();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Speech.internal_static_ProvideSpeech_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Speech.internal_static_ProvideSpeech_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech.class, com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech.Builder.class);
    }

    public static final int DIALOG_FIELD_NUMBER = 1;
    private com.amazon.alexa.accessory.protocol.Speech.Dialog dialog_;
    /**
     * <code>.Dialog dialog = 1;</code>
     * @return Whether the dialog field is set.
     */
    @java.lang.Override
    public boolean hasDialog() {
      return dialog_ != null;
    }
    /**
     * <code>.Dialog dialog = 1;</code>
     * @return The dialog.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.Dialog getDialog() {
      return dialog_ == null ? com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance() : dialog_;
    }
    /**
     * <code>.Dialog dialog = 1;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder getDialogOrBuilder() {
      return getDialog();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (dialog_ != null) {
        output.writeMessage(1, getDialog());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (dialog_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getDialog());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech other = (com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech) obj;

      if (hasDialog() != other.hasDialog()) return false;
      if (hasDialog()) {
        if (!getDialog()
            .equals(other.getDialog())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasDialog()) {
        hash = (37 * hash) + DIALOG_FIELD_NUMBER;
        hash = (53 * hash) + getDialog().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code ProvideSpeech}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProvideSpeech)
        com.amazon.alexa.accessory.protocol.Speech.ProvideSpeechOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_ProvideSpeech_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_ProvideSpeech_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech.class, com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (dialogBuilder_ == null) {
          dialog_ = null;
        } else {
          dialog_ = null;
          dialogBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_ProvideSpeech_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech build() {
        com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech buildPartial() {
        com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech result = new com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech(this);
        if (dialogBuilder_ == null) {
          result.dialog_ = dialog_;
        } else {
          result.dialog_ = dialogBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech other) {
        if (other == com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech.getDefaultInstance()) return this;
        if (other.hasDialog()) {
          mergeDialog(other.getDialog());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getDialogFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private com.amazon.alexa.accessory.protocol.Speech.Dialog dialog_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.Dialog, com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder, com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder> dialogBuilder_;
      /**
       * <code>.Dialog dialog = 1;</code>
       * @return Whether the dialog field is set.
       */
      public boolean hasDialog() {
        return dialogBuilder_ != null || dialog_ != null;
      }
      /**
       * <code>.Dialog dialog = 1;</code>
       * @return The dialog.
       */
      public com.amazon.alexa.accessory.protocol.Speech.Dialog getDialog() {
        if (dialogBuilder_ == null) {
          return dialog_ == null ? com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance() : dialog_;
        } else {
          return dialogBuilder_.getMessage();
        }
      }
      /**
       * <code>.Dialog dialog = 1;</code>
       */
      public Builder setDialog(com.amazon.alexa.accessory.protocol.Speech.Dialog value) {
        if (dialogBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          dialog_ = value;
          onChanged();
        } else {
          dialogBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Dialog dialog = 1;</code>
       */
      public Builder setDialog(
          com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder builderForValue) {
        if (dialogBuilder_ == null) {
          dialog_ = builderForValue.build();
          onChanged();
        } else {
          dialogBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Dialog dialog = 1;</code>
       */
      public Builder mergeDialog(com.amazon.alexa.accessory.protocol.Speech.Dialog value) {
        if (dialogBuilder_ == null) {
          if (dialog_ != null) {
            dialog_ =
              com.amazon.alexa.accessory.protocol.Speech.Dialog.newBuilder(dialog_).mergeFrom(value).buildPartial();
          } else {
            dialog_ = value;
          }
          onChanged();
        } else {
          dialogBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Dialog dialog = 1;</code>
       */
      public Builder clearDialog() {
        if (dialogBuilder_ == null) {
          dialog_ = null;
          onChanged();
        } else {
          dialog_ = null;
          dialogBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Dialog dialog = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder getDialogBuilder() {
        
        onChanged();
        return getDialogFieldBuilder().getBuilder();
      }
      /**
       * <code>.Dialog dialog = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder getDialogOrBuilder() {
        if (dialogBuilder_ != null) {
          return dialogBuilder_.getMessageOrBuilder();
        } else {
          return dialog_ == null ?
              com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance() : dialog_;
        }
      }
      /**
       * <code>.Dialog dialog = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.Dialog, com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder, com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder> 
          getDialogFieldBuilder() {
        if (dialogBuilder_ == null) {
          dialogBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Speech.Dialog, com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder, com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder>(
                  getDialog(),
                  getParentForChildren(),
                  isClean());
          dialog_ = null;
        }
        return dialogBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProvideSpeech)
    }

    // @@protoc_insertion_point(class_scope:ProvideSpeech)
    private static final com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech();
    }

    public static com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ProvideSpeech>
        PARSER = new com.google.protobuf.AbstractParser<ProvideSpeech>() {
      @java.lang.Override
      public ProvideSpeech parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ProvideSpeech> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ProvideSpeech> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface StopSpeechOrBuilder extends
      // @@protoc_insertion_point(interface_extends:StopSpeech)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.ErrorCode error_code = 1;</code>
     * @return The enum numeric value on the wire for errorCode.
     */
    int getErrorCodeValue();
    /**
     * <code>.ErrorCode error_code = 1;</code>
     * @return The errorCode.
     */
    com.amazon.alexa.accessory.protocol.Common.ErrorCode getErrorCode();

    /**
     * <code>.Dialog dialog = 2;</code>
     * @return Whether the dialog field is set.
     */
    boolean hasDialog();
    /**
     * <code>.Dialog dialog = 2;</code>
     * @return The dialog.
     */
    com.amazon.alexa.accessory.protocol.Speech.Dialog getDialog();
    /**
     * <code>.Dialog dialog = 2;</code>
     */
    com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder getDialogOrBuilder();
  }
  /**
   * Protobuf type {@code StopSpeech}
   */
  public static final class StopSpeech extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:StopSpeech)
      StopSpeechOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use StopSpeech.newBuilder() to construct.
    private StopSpeech(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private StopSpeech() {
      errorCode_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new StopSpeech();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Speech.internal_static_StopSpeech_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Speech.internal_static_StopSpeech_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Speech.StopSpeech.class, com.amazon.alexa.accessory.protocol.Speech.StopSpeech.Builder.class);
    }

    public static final int ERROR_CODE_FIELD_NUMBER = 1;
    private int errorCode_;
    /**
     * <code>.ErrorCode error_code = 1;</code>
     * @return The enum numeric value on the wire for errorCode.
     */
    @java.lang.Override public int getErrorCodeValue() {
      return errorCode_;
    }
    /**
     * <code>.ErrorCode error_code = 1;</code>
     * @return The errorCode.
     */
    @java.lang.Override public com.amazon.alexa.accessory.protocol.Common.ErrorCode getErrorCode() {
      @SuppressWarnings("deprecation")
      com.amazon.alexa.accessory.protocol.Common.ErrorCode result = com.amazon.alexa.accessory.protocol.Common.ErrorCode.valueOf(errorCode_);
      return result == null ? com.amazon.alexa.accessory.protocol.Common.ErrorCode.UNRECOGNIZED : result;
    }

    public static final int DIALOG_FIELD_NUMBER = 2;
    private com.amazon.alexa.accessory.protocol.Speech.Dialog dialog_;
    /**
     * <code>.Dialog dialog = 2;</code>
     * @return Whether the dialog field is set.
     */
    @java.lang.Override
    public boolean hasDialog() {
      return dialog_ != null;
    }
    /**
     * <code>.Dialog dialog = 2;</code>
     * @return The dialog.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.Dialog getDialog() {
      return dialog_ == null ? com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance() : dialog_;
    }
    /**
     * <code>.Dialog dialog = 2;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder getDialogOrBuilder() {
      return getDialog();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (errorCode_ != com.amazon.alexa.accessory.protocol.Common.ErrorCode.SUCCESS.getNumber()) {
        output.writeEnum(1, errorCode_);
      }
      if (dialog_ != null) {
        output.writeMessage(2, getDialog());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (errorCode_ != com.amazon.alexa.accessory.protocol.Common.ErrorCode.SUCCESS.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, errorCode_);
      }
      if (dialog_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getDialog());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Speech.StopSpeech)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Speech.StopSpeech other = (com.amazon.alexa.accessory.protocol.Speech.StopSpeech) obj;

      if (errorCode_ != other.errorCode_) return false;
      if (hasDialog() != other.hasDialog()) return false;
      if (hasDialog()) {
        if (!getDialog()
            .equals(other.getDialog())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ERROR_CODE_FIELD_NUMBER;
      hash = (53 * hash) + errorCode_;
      if (hasDialog()) {
        hash = (37 * hash) + DIALOG_FIELD_NUMBER;
        hash = (53 * hash) + getDialog().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Speech.StopSpeech parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StopSpeech parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StopSpeech parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StopSpeech parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StopSpeech parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StopSpeech parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StopSpeech parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StopSpeech parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StopSpeech parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StopSpeech parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StopSpeech parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.StopSpeech parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Speech.StopSpeech prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code StopSpeech}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:StopSpeech)
        com.amazon.alexa.accessory.protocol.Speech.StopSpeechOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_StopSpeech_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_StopSpeech_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Speech.StopSpeech.class, com.amazon.alexa.accessory.protocol.Speech.StopSpeech.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Speech.StopSpeech.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        errorCode_ = 0;

        if (dialogBuilder_ == null) {
          dialog_ = null;
        } else {
          dialog_ = null;
          dialogBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_StopSpeech_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.StopSpeech getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Speech.StopSpeech.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.StopSpeech build() {
        com.amazon.alexa.accessory.protocol.Speech.StopSpeech result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.StopSpeech buildPartial() {
        com.amazon.alexa.accessory.protocol.Speech.StopSpeech result = new com.amazon.alexa.accessory.protocol.Speech.StopSpeech(this);
        result.errorCode_ = errorCode_;
        if (dialogBuilder_ == null) {
          result.dialog_ = dialog_;
        } else {
          result.dialog_ = dialogBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Speech.StopSpeech) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Speech.StopSpeech)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Speech.StopSpeech other) {
        if (other == com.amazon.alexa.accessory.protocol.Speech.StopSpeech.getDefaultInstance()) return this;
        if (other.errorCode_ != 0) {
          setErrorCodeValue(other.getErrorCodeValue());
        }
        if (other.hasDialog()) {
          mergeDialog(other.getDialog());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                errorCode_ = input.readEnum();

                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getDialogFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private int errorCode_ = 0;
      /**
       * <code>.ErrorCode error_code = 1;</code>
       * @return The enum numeric value on the wire for errorCode.
       */
      @java.lang.Override public int getErrorCodeValue() {
        return errorCode_;
      }
      /**
       * <code>.ErrorCode error_code = 1;</code>
       * @param value The enum numeric value on the wire for errorCode to set.
       * @return This builder for chaining.
       */
      public Builder setErrorCodeValue(int value) {
        
        errorCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.ErrorCode error_code = 1;</code>
       * @return The errorCode.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Common.ErrorCode getErrorCode() {
        @SuppressWarnings("deprecation")
        com.amazon.alexa.accessory.protocol.Common.ErrorCode result = com.amazon.alexa.accessory.protocol.Common.ErrorCode.valueOf(errorCode_);
        return result == null ? com.amazon.alexa.accessory.protocol.Common.ErrorCode.UNRECOGNIZED : result;
      }
      /**
       * <code>.ErrorCode error_code = 1;</code>
       * @param value The errorCode to set.
       * @return This builder for chaining.
       */
      public Builder setErrorCode(com.amazon.alexa.accessory.protocol.Common.ErrorCode value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        errorCode_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.ErrorCode error_code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearErrorCode() {
        
        errorCode_ = 0;
        onChanged();
        return this;
      }

      private com.amazon.alexa.accessory.protocol.Speech.Dialog dialog_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.Dialog, com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder, com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder> dialogBuilder_;
      /**
       * <code>.Dialog dialog = 2;</code>
       * @return Whether the dialog field is set.
       */
      public boolean hasDialog() {
        return dialogBuilder_ != null || dialog_ != null;
      }
      /**
       * <code>.Dialog dialog = 2;</code>
       * @return The dialog.
       */
      public com.amazon.alexa.accessory.protocol.Speech.Dialog getDialog() {
        if (dialogBuilder_ == null) {
          return dialog_ == null ? com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance() : dialog_;
        } else {
          return dialogBuilder_.getMessage();
        }
      }
      /**
       * <code>.Dialog dialog = 2;</code>
       */
      public Builder setDialog(com.amazon.alexa.accessory.protocol.Speech.Dialog value) {
        if (dialogBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          dialog_ = value;
          onChanged();
        } else {
          dialogBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Dialog dialog = 2;</code>
       */
      public Builder setDialog(
          com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder builderForValue) {
        if (dialogBuilder_ == null) {
          dialog_ = builderForValue.build();
          onChanged();
        } else {
          dialogBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Dialog dialog = 2;</code>
       */
      public Builder mergeDialog(com.amazon.alexa.accessory.protocol.Speech.Dialog value) {
        if (dialogBuilder_ == null) {
          if (dialog_ != null) {
            dialog_ =
              com.amazon.alexa.accessory.protocol.Speech.Dialog.newBuilder(dialog_).mergeFrom(value).buildPartial();
          } else {
            dialog_ = value;
          }
          onChanged();
        } else {
          dialogBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Dialog dialog = 2;</code>
       */
      public Builder clearDialog() {
        if (dialogBuilder_ == null) {
          dialog_ = null;
          onChanged();
        } else {
          dialog_ = null;
          dialogBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Dialog dialog = 2;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder getDialogBuilder() {
        
        onChanged();
        return getDialogFieldBuilder().getBuilder();
      }
      /**
       * <code>.Dialog dialog = 2;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder getDialogOrBuilder() {
        if (dialogBuilder_ != null) {
          return dialogBuilder_.getMessageOrBuilder();
        } else {
          return dialog_ == null ?
              com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance() : dialog_;
        }
      }
      /**
       * <code>.Dialog dialog = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.Dialog, com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder, com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder> 
          getDialogFieldBuilder() {
        if (dialogBuilder_ == null) {
          dialogBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Speech.Dialog, com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder, com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder>(
                  getDialog(),
                  getParentForChildren(),
                  isClean());
          dialog_ = null;
        }
        return dialogBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:StopSpeech)
    }

    // @@protoc_insertion_point(class_scope:StopSpeech)
    private static final com.amazon.alexa.accessory.protocol.Speech.StopSpeech DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Speech.StopSpeech();
    }

    public static com.amazon.alexa.accessory.protocol.Speech.StopSpeech getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<StopSpeech>
        PARSER = new com.google.protobuf.AbstractParser<StopSpeech>() {
      @java.lang.Override
      public StopSpeech parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<StopSpeech> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<StopSpeech> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.StopSpeech getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface EndpointSpeechOrBuilder extends
      // @@protoc_insertion_point(interface_extends:EndpointSpeech)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Dialog dialog = 1;</code>
     * @return Whether the dialog field is set.
     */
    boolean hasDialog();
    /**
     * <code>.Dialog dialog = 1;</code>
     * @return The dialog.
     */
    com.amazon.alexa.accessory.protocol.Speech.Dialog getDialog();
    /**
     * <code>.Dialog dialog = 1;</code>
     */
    com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder getDialogOrBuilder();
  }
  /**
   * Protobuf type {@code EndpointSpeech}
   */
  public static final class EndpointSpeech extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:EndpointSpeech)
      EndpointSpeechOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use EndpointSpeech.newBuilder() to construct.
    private EndpointSpeech(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private EndpointSpeech() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new EndpointSpeech();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Speech.internal_static_EndpointSpeech_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Speech.internal_static_EndpointSpeech_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech.class, com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech.Builder.class);
    }

    public static final int DIALOG_FIELD_NUMBER = 1;
    private com.amazon.alexa.accessory.protocol.Speech.Dialog dialog_;
    /**
     * <code>.Dialog dialog = 1;</code>
     * @return Whether the dialog field is set.
     */
    @java.lang.Override
    public boolean hasDialog() {
      return dialog_ != null;
    }
    /**
     * <code>.Dialog dialog = 1;</code>
     * @return The dialog.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.Dialog getDialog() {
      return dialog_ == null ? com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance() : dialog_;
    }
    /**
     * <code>.Dialog dialog = 1;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder getDialogOrBuilder() {
      return getDialog();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (dialog_ != null) {
        output.writeMessage(1, getDialog());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (dialog_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getDialog());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech other = (com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech) obj;

      if (hasDialog() != other.hasDialog()) return false;
      if (hasDialog()) {
        if (!getDialog()
            .equals(other.getDialog())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasDialog()) {
        hash = (37 * hash) + DIALOG_FIELD_NUMBER;
        hash = (53 * hash) + getDialog().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code EndpointSpeech}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:EndpointSpeech)
        com.amazon.alexa.accessory.protocol.Speech.EndpointSpeechOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_EndpointSpeech_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_EndpointSpeech_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech.class, com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (dialogBuilder_ == null) {
          dialog_ = null;
        } else {
          dialog_ = null;
          dialogBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_EndpointSpeech_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech build() {
        com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech buildPartial() {
        com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech result = new com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech(this);
        if (dialogBuilder_ == null) {
          result.dialog_ = dialog_;
        } else {
          result.dialog_ = dialogBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech other) {
        if (other == com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech.getDefaultInstance()) return this;
        if (other.hasDialog()) {
          mergeDialog(other.getDialog());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getDialogFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private com.amazon.alexa.accessory.protocol.Speech.Dialog dialog_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.Dialog, com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder, com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder> dialogBuilder_;
      /**
       * <code>.Dialog dialog = 1;</code>
       * @return Whether the dialog field is set.
       */
      public boolean hasDialog() {
        return dialogBuilder_ != null || dialog_ != null;
      }
      /**
       * <code>.Dialog dialog = 1;</code>
       * @return The dialog.
       */
      public com.amazon.alexa.accessory.protocol.Speech.Dialog getDialog() {
        if (dialogBuilder_ == null) {
          return dialog_ == null ? com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance() : dialog_;
        } else {
          return dialogBuilder_.getMessage();
        }
      }
      /**
       * <code>.Dialog dialog = 1;</code>
       */
      public Builder setDialog(com.amazon.alexa.accessory.protocol.Speech.Dialog value) {
        if (dialogBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          dialog_ = value;
          onChanged();
        } else {
          dialogBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Dialog dialog = 1;</code>
       */
      public Builder setDialog(
          com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder builderForValue) {
        if (dialogBuilder_ == null) {
          dialog_ = builderForValue.build();
          onChanged();
        } else {
          dialogBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Dialog dialog = 1;</code>
       */
      public Builder mergeDialog(com.amazon.alexa.accessory.protocol.Speech.Dialog value) {
        if (dialogBuilder_ == null) {
          if (dialog_ != null) {
            dialog_ =
              com.amazon.alexa.accessory.protocol.Speech.Dialog.newBuilder(dialog_).mergeFrom(value).buildPartial();
          } else {
            dialog_ = value;
          }
          onChanged();
        } else {
          dialogBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Dialog dialog = 1;</code>
       */
      public Builder clearDialog() {
        if (dialogBuilder_ == null) {
          dialog_ = null;
          onChanged();
        } else {
          dialog_ = null;
          dialogBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Dialog dialog = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder getDialogBuilder() {
        
        onChanged();
        return getDialogFieldBuilder().getBuilder();
      }
      /**
       * <code>.Dialog dialog = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder getDialogOrBuilder() {
        if (dialogBuilder_ != null) {
          return dialogBuilder_.getMessageOrBuilder();
        } else {
          return dialog_ == null ?
              com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance() : dialog_;
        }
      }
      /**
       * <code>.Dialog dialog = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.Dialog, com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder, com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder> 
          getDialogFieldBuilder() {
        if (dialogBuilder_ == null) {
          dialogBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Speech.Dialog, com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder, com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder>(
                  getDialog(),
                  getParentForChildren(),
                  isClean());
          dialog_ = null;
        }
        return dialogBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:EndpointSpeech)
    }

    // @@protoc_insertion_point(class_scope:EndpointSpeech)
    private static final com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech();
    }

    public static com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<EndpointSpeech>
        PARSER = new com.google.protobuf.AbstractParser<EndpointSpeech>() {
      @java.lang.Override
      public EndpointSpeech parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<EndpointSpeech> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<EndpointSpeech> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface NotifySpeechStateOrBuilder extends
      // @@protoc_insertion_point(interface_extends:NotifySpeechState)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.SpeechState state = 1;</code>
     * @return The enum numeric value on the wire for state.
     */
    int getStateValue();
    /**
     * <code>.SpeechState state = 1;</code>
     * @return The state.
     */
    com.amazon.alexa.accessory.protocol.Speech.SpeechState getState();
  }
  /**
   * Protobuf type {@code NotifySpeechState}
   */
  public static final class NotifySpeechState extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:NotifySpeechState)
      NotifySpeechStateOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use NotifySpeechState.newBuilder() to construct.
    private NotifySpeechState(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private NotifySpeechState() {
      state_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new NotifySpeechState();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Speech.internal_static_NotifySpeechState_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Speech.internal_static_NotifySpeechState_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState.class, com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState.Builder.class);
    }

    public static final int STATE_FIELD_NUMBER = 1;
    private int state_;
    /**
     * <code>.SpeechState state = 1;</code>
     * @return The enum numeric value on the wire for state.
     */
    @java.lang.Override public int getStateValue() {
      return state_;
    }
    /**
     * <code>.SpeechState state = 1;</code>
     * @return The state.
     */
    @java.lang.Override public com.amazon.alexa.accessory.protocol.Speech.SpeechState getState() {
      @SuppressWarnings("deprecation")
      com.amazon.alexa.accessory.protocol.Speech.SpeechState result = com.amazon.alexa.accessory.protocol.Speech.SpeechState.valueOf(state_);
      return result == null ? com.amazon.alexa.accessory.protocol.Speech.SpeechState.UNRECOGNIZED : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (state_ != com.amazon.alexa.accessory.protocol.Speech.SpeechState.IDLE.getNumber()) {
        output.writeEnum(1, state_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (state_ != com.amazon.alexa.accessory.protocol.Speech.SpeechState.IDLE.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, state_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState other = (com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState) obj;

      if (state_ != other.state_) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + STATE_FIELD_NUMBER;
      hash = (53 * hash) + state_;
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code NotifySpeechState}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:NotifySpeechState)
        com.amazon.alexa.accessory.protocol.Speech.NotifySpeechStateOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_NotifySpeechState_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_NotifySpeechState_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState.class, com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        state_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Speech.internal_static_NotifySpeechState_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState build() {
        com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState buildPartial() {
        com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState result = new com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState(this);
        result.state_ = state_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState other) {
        if (other == com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState.getDefaultInstance()) return this;
        if (other.state_ != 0) {
          setStateValue(other.getStateValue());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                state_ = input.readEnum();

                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private int state_ = 0;
      /**
       * <code>.SpeechState state = 1;</code>
       * @return The enum numeric value on the wire for state.
       */
      @java.lang.Override public int getStateValue() {
        return state_;
      }
      /**
       * <code>.SpeechState state = 1;</code>
       * @param value The enum numeric value on the wire for state to set.
       * @return This builder for chaining.
       */
      public Builder setStateValue(int value) {
        
        state_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.SpeechState state = 1;</code>
       * @return The state.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.SpeechState getState() {
        @SuppressWarnings("deprecation")
        com.amazon.alexa.accessory.protocol.Speech.SpeechState result = com.amazon.alexa.accessory.protocol.Speech.SpeechState.valueOf(state_);
        return result == null ? com.amazon.alexa.accessory.protocol.Speech.SpeechState.UNRECOGNIZED : result;
      }
      /**
       * <code>.SpeechState state = 1;</code>
       * @param value The state to set.
       * @return This builder for chaining.
       */
      public Builder setState(com.amazon.alexa.accessory.protocol.Speech.SpeechState value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        state_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.SpeechState state = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearState() {
        
        state_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:NotifySpeechState)
    }

    // @@protoc_insertion_point(class_scope:NotifySpeechState)
    private static final com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState();
    }

    public static com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<NotifySpeechState>
        PARSER = new com.google.protobuf.AbstractParser<NotifySpeechState>() {
      @java.lang.Override
      public NotifySpeechState parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<NotifySpeechState> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<NotifySpeechState> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Dialog_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Dialog_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_SpeechSettings_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_SpeechSettings_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_SpeechInitiator_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_SpeechInitiator_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_SpeechInitiator_WakeWord_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_SpeechInitiator_WakeWord_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_StartSpeech_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_StartSpeech_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_SpeechProvider_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_SpeechProvider_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProvideSpeech_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProvideSpeech_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_StopSpeech_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_StopSpeech_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_EndpointSpeech_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_EndpointSpeech_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_NotifySpeechState_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_NotifySpeechState_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014speech.proto\032\014common.proto\"\024\n\006Dialog\022\n" +
      "\n\002id\030\001 \001(\r\"~\n\016SpeechSettings\022$\n\raudio_pr" +
      "ofile\030\001 \001(\0162\r.AudioProfile\022\"\n\014audio_form" +
      "at\030\002 \001(\0162\014.AudioFormat\022\"\n\014audio_source\030\003" +
      " \001(\0162\014.AudioSource\"\220\002\n\017SpeechInitiator\022#" +
      "\n\004type\030\001 \001(\0162\025.SpeechInitiator.Type\022,\n\tw" +
      "ake_word\030\002 \001(\0132\031.SpeechInitiator.WakeWor" +
      "d\032m\n\010WakeWord\022\036\n\026start_index_in_samples\030" +
      "\001 \001(\r\022\034\n\024end_index_in_samples\030\002 \001(\r\022\021\n\tn" +
      "ear_miss\030\003 \001(\010\022\020\n\010metadata\030\004 \001(\014\";\n\004Type" +
      "\022\010\n\004NONE\020\000\022\022\n\016PRESS_AND_HOLD\020\001\022\007\n\003TAP\020\003\022" +
      "\014\n\010WAKEWORD\020\004\"\253\001\n\013StartSpeech\022!\n\010setting" +
      "s\030\001 \001(\0132\017.SpeechSettings\022#\n\tinitiator\030\002 " +
      "\001(\0132\020.SpeechInitiator\022\027\n\006dialog\030\003 \001(\0132\007." +
      "Dialog\022\036\n\026suppressEndpointEarcon\030\004 \001(\010\022\033" +
      "\n\023suppressStartEarcon\030\005 \001(\010\"S\n\016SpeechPro" +
      "vider\022(\n\017speech_settings\030\001 \001(\0132\017.SpeechS" +
      "ettings\022\027\n\006dialog\030\002 \001(\0132\007.Dialog\"(\n\rProv" +
      "ideSpeech\022\027\n\006dialog\030\001 \001(\0132\007.Dialog\"E\n\nSt" +
      "opSpeech\022\036\n\nerror_code\030\001 \001(\0162\n.ErrorCode" +
      "\022\027\n\006dialog\030\002 \001(\0132\007.Dialog\")\n\016EndpointSpe" +
      "ech\022\027\n\006dialog\030\001 \001(\0132\007.Dialog\"0\n\021NotifySp" +
      "eechState\022\033\n\005state\030\001 \001(\0162\014.SpeechState*=" +
      "\n\014AudioProfile\022\016\n\nCLOSE_TALK\020\000\022\016\n\nNEAR_F" +
      "IELD\020\001\022\r\n\tFAR_FIELD\020\002*s\n\013AudioFormat\022\026\n\022" +
      "PCM_L16_16KHZ_MONO\020\000\022 \n\034OPUS_16KHZ_32KBP" +
      "S_CBR_0_20MS\020\001\022 \n\034OPUS_16KHZ_16KBPS_CBR_" +
      "0_20MS\020\002\022\010\n\004MSBC\020\003*,\n\013AudioSource\022\n\n\006STR" +
      "EAM\020\000\022\021\n\rBLUETOOTH_SCO\020\001*D\n\013SpeechState\022" +
      "\010\n\004IDLE\020\000\022\r\n\tLISTENING\020\001\022\016\n\nPROCESSING\020\002" +
      "\022\014\n\010SPEAKING\020\003B0\n#com.amazon.alexa.acces" +
      "sory.protocolH\003\240\001\001\242\002\003AACb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.amazon.alexa.accessory.protocol.Common.getDescriptor(),
        });
    internal_static_Dialog_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Dialog_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Dialog_descriptor,
        new java.lang.String[] { "Id", });
    internal_static_SpeechSettings_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_SpeechSettings_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_SpeechSettings_descriptor,
        new java.lang.String[] { "AudioProfile", "AudioFormat", "AudioSource", });
    internal_static_SpeechInitiator_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_SpeechInitiator_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_SpeechInitiator_descriptor,
        new java.lang.String[] { "Type", "WakeWord", });
    internal_static_SpeechInitiator_WakeWord_descriptor =
      internal_static_SpeechInitiator_descriptor.getNestedTypes().get(0);
    internal_static_SpeechInitiator_WakeWord_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_SpeechInitiator_WakeWord_descriptor,
        new java.lang.String[] { "StartIndexInSamples", "EndIndexInSamples", "NearMiss", "Metadata", });
    internal_static_StartSpeech_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_StartSpeech_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_StartSpeech_descriptor,
        new java.lang.String[] { "Settings", "Initiator", "Dialog", "SuppressEndpointEarcon", "SuppressStartEarcon", });
    internal_static_SpeechProvider_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_SpeechProvider_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_SpeechProvider_descriptor,
        new java.lang.String[] { "SpeechSettings", "Dialog", });
    internal_static_ProvideSpeech_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_ProvideSpeech_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProvideSpeech_descriptor,
        new java.lang.String[] { "Dialog", });
    internal_static_StopSpeech_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_StopSpeech_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_StopSpeech_descriptor,
        new java.lang.String[] { "ErrorCode", "Dialog", });
    internal_static_EndpointSpeech_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_EndpointSpeech_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_EndpointSpeech_descriptor,
        new java.lang.String[] { "Dialog", });
    internal_static_NotifySpeechState_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_NotifySpeechState_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_NotifySpeechState_descriptor,
        new java.lang.String[] { "State", });
    com.amazon.alexa.accessory.protocol.Common.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
