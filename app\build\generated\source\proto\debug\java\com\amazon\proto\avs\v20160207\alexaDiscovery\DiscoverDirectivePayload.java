// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: alexaDiscoveryDiscoverDirectivePayload.proto

package com.amazon.proto.avs.v20160207.alexaDiscovery;

public final class DiscoverDirectivePayload {
  private DiscoverDirectivePayload() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface DiscoverDirectivePayloadProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:alexaDiscovery.DiscoverDirectivePayloadProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.alexaDiscovery.DiscoverDirectivePayloadProto.Scope scope = 1;</code>
     * @return Whether the scope field is set.
     */
    boolean hasScope();
    /**
     * <code>.alexaDiscovery.DiscoverDirectivePayloadProto.Scope scope = 1;</code>
     * @return The scope.
     */
    com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope getScope();
    /**
     * <code>.alexaDiscovery.DiscoverDirectivePayloadProto.Scope scope = 1;</code>
     */
    com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.ScopeOrBuilder getScopeOrBuilder();
  }
  /**
   * Protobuf type {@code alexaDiscovery.DiscoverDirectivePayloadProto}
   */
  public static final class DiscoverDirectivePayloadProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:alexaDiscovery.DiscoverDirectivePayloadProto)
      DiscoverDirectivePayloadProtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DiscoverDirectivePayloadProto.newBuilder() to construct.
    private DiscoverDirectivePayloadProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DiscoverDirectivePayloadProto() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DiscoverDirectivePayloadProto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.class, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Builder.class);
    }

    public interface ScopeOrBuilder extends
        // @@protoc_insertion_point(interface_extends:alexaDiscovery.DiscoverDirectivePayloadProto.Scope)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>string type = 1;</code>
       * @return The type.
       */
      java.lang.String getType();
      /**
       * <code>string type = 1;</code>
       * @return The bytes for type.
       */
      com.google.protobuf.ByteString
          getTypeBytes();

      /**
       * <code>string token = 2;</code>
       * @return The token.
       */
      java.lang.String getToken();
      /**
       * <code>string token = 2;</code>
       * @return The bytes for token.
       */
      com.google.protobuf.ByteString
          getTokenBytes();
    }
    /**
     * Protobuf type {@code alexaDiscovery.DiscoverDirectivePayloadProto.Scope}
     */
    public static final class Scope extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:alexaDiscovery.DiscoverDirectivePayloadProto.Scope)
        ScopeOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use Scope.newBuilder() to construct.
      private Scope(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private Scope() {
        type_ = "";
        token_ = "";
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new Scope();
      }

      @java.lang.Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_Scope_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_Scope_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope.class, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope.Builder.class);
      }

      public static final int TYPE_FIELD_NUMBER = 1;
      private volatile java.lang.Object type_;
      /**
       * <code>string type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public java.lang.String getType() {
        java.lang.Object ref = type_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          type_ = s;
          return s;
        }
      }
      /**
       * <code>string type = 1;</code>
       * @return The bytes for type.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getTypeBytes() {
        java.lang.Object ref = type_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          type_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int TOKEN_FIELD_NUMBER = 2;
      private volatile java.lang.Object token_;
      /**
       * <code>string token = 2;</code>
       * @return The token.
       */
      @java.lang.Override
      public java.lang.String getToken() {
        java.lang.Object ref = token_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          token_ = s;
          return s;
        }
      }
      /**
       * <code>string token = 2;</code>
       * @return The bytes for token.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getTokenBytes() {
        java.lang.Object ref = token_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(type_)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 1, type_);
        }
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(token_)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 2, token_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(type_)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, type_);
        }
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(token_)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, token_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope)) {
          return super.equals(obj);
        }
        com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope other = (com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope) obj;

        if (!getType()
            .equals(other.getType())) return false;
        if (!getToken()
            .equals(other.getToken())) return false;
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getType().hashCode();
        hash = (37 * hash) + TOKEN_FIELD_NUMBER;
        hash = (53 * hash) + getToken().hashCode();
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code alexaDiscovery.DiscoverDirectivePayloadProto.Scope}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:alexaDiscovery.DiscoverDirectivePayloadProto.Scope)
          com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.ScopeOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_Scope_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_Scope_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope.class, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope.Builder.class);
        }

        // Construct using com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          type_ = "";

          token_ = "";

          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_Scope_descriptor;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope getDefaultInstanceForType() {
          return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope.getDefaultInstance();
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope build() {
          com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope buildPartial() {
          com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope result = new com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope(this);
          result.type_ = type_;
          result.token_ = token_;
          onBuilt();
          return result;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope) {
            return mergeFrom((com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope other) {
          if (other == com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope.getDefaultInstance()) return this;
          if (!other.getType().isEmpty()) {
            type_ = other.type_;
            onChanged();
          }
          if (!other.getToken().isEmpty()) {
            token_ = other.token_;
            onChanged();
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 10: {
                  type_ = input.readStringRequireUtf8();

                  break;
                } // case 10
                case 18: {
                  token_ = input.readStringRequireUtf8();

                  break;
                } // case 18
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }

        private java.lang.Object type_ = "";
        /**
         * <code>string type = 1;</code>
         * @return The type.
         */
        public java.lang.String getType() {
          java.lang.Object ref = type_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            type_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>string type = 1;</code>
         * @return The bytes for type.
         */
        public com.google.protobuf.ByteString
            getTypeBytes() {
          java.lang.Object ref = type_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            type_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string type = 1;</code>
         * @param value The type to set.
         * @return This builder for chaining.
         */
        public Builder setType(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  
          type_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>string type = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearType() {
          
          type_ = getDefaultInstance().getType();
          onChanged();
          return this;
        }
        /**
         * <code>string type = 1;</code>
         * @param value The bytes for type to set.
         * @return This builder for chaining.
         */
        public Builder setTypeBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
          
          type_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object token_ = "";
        /**
         * <code>string token = 2;</code>
         * @return The token.
         */
        public java.lang.String getToken() {
          java.lang.Object ref = token_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            token_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>string token = 2;</code>
         * @return The bytes for token.
         */
        public com.google.protobuf.ByteString
            getTokenBytes() {
          java.lang.Object ref = token_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            token_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string token = 2;</code>
         * @param value The token to set.
         * @return This builder for chaining.
         */
        public Builder setToken(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  
          token_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>string token = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearToken() {
          
          token_ = getDefaultInstance().getToken();
          onChanged();
          return this;
        }
        /**
         * <code>string token = 2;</code>
         * @param value The bytes for token to set.
         * @return This builder for chaining.
         */
        public Builder setTokenBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
          
          token_ = value;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:alexaDiscovery.DiscoverDirectivePayloadProto.Scope)
      }

      // @@protoc_insertion_point(class_scope:alexaDiscovery.DiscoverDirectivePayloadProto.Scope)
      private static final com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope();
      }

      public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Scope>
          PARSER = new com.google.protobuf.AbstractParser<Scope>() {
        @java.lang.Override
        public Scope parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Scope> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Scope> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public static final int SCOPE_FIELD_NUMBER = 1;
    private com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope scope_;
    /**
     * <code>.alexaDiscovery.DiscoverDirectivePayloadProto.Scope scope = 1;</code>
     * @return Whether the scope field is set.
     */
    @java.lang.Override
    public boolean hasScope() {
      return scope_ != null;
    }
    /**
     * <code>.alexaDiscovery.DiscoverDirectivePayloadProto.Scope scope = 1;</code>
     * @return The scope.
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope getScope() {
      return scope_ == null ? com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope.getDefaultInstance() : scope_;
    }
    /**
     * <code>.alexaDiscovery.DiscoverDirectivePayloadProto.Scope scope = 1;</code>
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.ScopeOrBuilder getScopeOrBuilder() {
      return getScope();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (scope_ != null) {
        output.writeMessage(1, getScope());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (scope_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getScope());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto)) {
        return super.equals(obj);
      }
      com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto other = (com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto) obj;

      if (hasScope() != other.hasScope()) return false;
      if (hasScope()) {
        if (!getScope()
            .equals(other.getScope())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasScope()) {
        hash = (37 * hash) + SCOPE_FIELD_NUMBER;
        hash = (53 * hash) + getScope().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code alexaDiscovery.DiscoverDirectivePayloadProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:alexaDiscovery.DiscoverDirectivePayloadProto)
        com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.class, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Builder.class);
      }

      // Construct using com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (scopeBuilder_ == null) {
          scope_ = null;
        } else {
          scope_ = null;
          scopeBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_descriptor;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto getDefaultInstanceForType() {
        return com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto build() {
        com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto buildPartial() {
        com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto result = new com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto(this);
        if (scopeBuilder_ == null) {
          result.scope_ = scope_;
        } else {
          result.scope_ = scopeBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto) {
          return mergeFrom((com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto other) {
        if (other == com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.getDefaultInstance()) return this;
        if (other.hasScope()) {
          mergeScope(other.getScope());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getScopeFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope scope_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope.Builder, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.ScopeOrBuilder> scopeBuilder_;
      /**
       * <code>.alexaDiscovery.DiscoverDirectivePayloadProto.Scope scope = 1;</code>
       * @return Whether the scope field is set.
       */
      public boolean hasScope() {
        return scopeBuilder_ != null || scope_ != null;
      }
      /**
       * <code>.alexaDiscovery.DiscoverDirectivePayloadProto.Scope scope = 1;</code>
       * @return The scope.
       */
      public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope getScope() {
        if (scopeBuilder_ == null) {
          return scope_ == null ? com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope.getDefaultInstance() : scope_;
        } else {
          return scopeBuilder_.getMessage();
        }
      }
      /**
       * <code>.alexaDiscovery.DiscoverDirectivePayloadProto.Scope scope = 1;</code>
       */
      public Builder setScope(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope value) {
        if (scopeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          scope_ = value;
          onChanged();
        } else {
          scopeBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.alexaDiscovery.DiscoverDirectivePayloadProto.Scope scope = 1;</code>
       */
      public Builder setScope(
          com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope.Builder builderForValue) {
        if (scopeBuilder_ == null) {
          scope_ = builderForValue.build();
          onChanged();
        } else {
          scopeBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.alexaDiscovery.DiscoverDirectivePayloadProto.Scope scope = 1;</code>
       */
      public Builder mergeScope(com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope value) {
        if (scopeBuilder_ == null) {
          if (scope_ != null) {
            scope_ =
              com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope.newBuilder(scope_).mergeFrom(value).buildPartial();
          } else {
            scope_ = value;
          }
          onChanged();
        } else {
          scopeBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.alexaDiscovery.DiscoverDirectivePayloadProto.Scope scope = 1;</code>
       */
      public Builder clearScope() {
        if (scopeBuilder_ == null) {
          scope_ = null;
          onChanged();
        } else {
          scope_ = null;
          scopeBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.alexaDiscovery.DiscoverDirectivePayloadProto.Scope scope = 1;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope.Builder getScopeBuilder() {
        
        onChanged();
        return getScopeFieldBuilder().getBuilder();
      }
      /**
       * <code>.alexaDiscovery.DiscoverDirectivePayloadProto.Scope scope = 1;</code>
       */
      public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.ScopeOrBuilder getScopeOrBuilder() {
        if (scopeBuilder_ != null) {
          return scopeBuilder_.getMessageOrBuilder();
        } else {
          return scope_ == null ?
              com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope.getDefaultInstance() : scope_;
        }
      }
      /**
       * <code>.alexaDiscovery.DiscoverDirectivePayloadProto.Scope scope = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope.Builder, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.ScopeOrBuilder> 
          getScopeFieldBuilder() {
        if (scopeBuilder_ == null) {
          scopeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.Scope.Builder, com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto.ScopeOrBuilder>(
                  getScope(),
                  getParentForChildren(),
                  isClean());
          scope_ = null;
        }
        return scopeBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:alexaDiscovery.DiscoverDirectivePayloadProto)
    }

    // @@protoc_insertion_point(class_scope:alexaDiscovery.DiscoverDirectivePayloadProto)
    private static final com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto();
    }

    public static com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DiscoverDirectivePayloadProto>
        PARSER = new com.google.protobuf.AbstractParser<DiscoverDirectivePayloadProto>() {
      @java.lang.Override
      public DiscoverDirectivePayloadProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<DiscoverDirectivePayloadProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DiscoverDirectivePayloadProto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.proto.avs.v20160207.alexaDiscovery.DiscoverDirectivePayload.DiscoverDirectivePayloadProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_Scope_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_Scope_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n,alexaDiscoveryDiscoverDirectivePayload" +
      ".proto\022\016alexaDiscovery\"\211\001\n\035DiscoverDirec" +
      "tivePayloadProto\022B\n\005scope\030\001 \001(\01323.alexaD" +
      "iscovery.DiscoverDirectivePayloadProto.S" +
      "cope\032$\n\005Scope\022\014\n\004type\030\001 \001(\t\022\r\n\005token\030\002 \001" +
      "(\tBI\n-com.amazon.proto.avs.v20160207.ale" +
      "xaDiscoveryB\030DiscoverDirectivePayloadb\006p" +
      "roto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_descriptor,
        new java.lang.String[] { "Scope", });
    internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_Scope_descriptor =
      internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_descriptor.getNestedTypes().get(0);
    internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_Scope_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_alexaDiscovery_DiscoverDirectivePayloadProto_Scope_descriptor,
        new java.lang.String[] { "Type", "Token", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
