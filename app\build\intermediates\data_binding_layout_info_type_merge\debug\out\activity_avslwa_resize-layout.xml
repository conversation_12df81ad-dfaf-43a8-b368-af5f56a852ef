<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_avslwa_resize" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_avslwa_resize.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_avslwa_resize_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="328" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/activity_avslwa_resize_0" include="toolbar"><Expressions/><location startLine="8" startOffset="4" endLine="11" endOffset="9"/></Target><Target id="@+id/loginfo" tag="layout/activity_avslwa_resize_0" include="logview"><Expressions/><location startLine="13" startOffset="4" endLine="16" endOffset="34"/></Target><Target id="@+id/testtest" view="TextView"><Expressions/><location startLine="30" startOffset="11" endLine="37" endOffset="40"/></Target><Target id="@+id/seekbar_lighting" view="SeekBar"><Expressions/><location startLine="53" startOffset="15" endLine="59" endOffset="20"/></Target><Target id="@+id/seekbar_speaker_volume" view="SeekBar"><Expressions/><location startLine="89" startOffset="15" endLine="95" endOffset="20"/></Target><Target id="@+id/seekbar_alert_volume" view="SeekBar"><Expressions/><location startLine="125" startOffset="15" endLine="131" endOffset="20"/></Target><Target id="@+id/seekbar_ux_led" view="SeekBar"><Expressions/><location startLine="161" startOffset="15" endLine="167" endOffset="20"/></Target><Target id="@+id/switchButton_start_lisening" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="207" startOffset="16" endLine="212" endOffset="50"/></Target><Target id="@+id/switchButton_end_lisening" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="241" startOffset="16" endLine="246" endOffset="50"/></Target><Target id="@+id/switchButton_do_not_disturb" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="272" startOffset="12" endLine="277" endOffset="46"/></Target><Target id="@+id/language_select_btn" view="Button"><Expressions/><location startLine="302" startOffset="15" endLine="316" endOffset="20"/></Target></Targets></Layout>