// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ViewOtalogBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ScrollView otaLogScrollview;

  @NonNull
  public final ScrollView otaLogScrollviewShort;

  @NonNull
  public final TextView otaLogText;

  @NonNull
  public final TextView otaLogTextShort;

  @NonNull
  public final Button otaOverBtn;

  private ViewOtalogBinding(@NonNull ConstraintLayout rootView,
      @NonNull ScrollView otaLogScrollview, @NonNull ScrollView otaLogScrollviewShort,
      @NonNull TextView otaLogText, @NonNull TextView otaLogTextShort, @NonNull Button otaOverBtn) {
    this.rootView = rootView;
    this.otaLogScrollview = otaLogScrollview;
    this.otaLogScrollviewShort = otaLogScrollviewShort;
    this.otaLogText = otaLogText;
    this.otaLogTextShort = otaLogTextShort;
    this.otaOverBtn = otaOverBtn;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ViewOtalogBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ViewOtalogBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.view_otalog, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ViewOtalogBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ota_log_scrollview;
      ScrollView otaLogScrollview = rootView.findViewById(id);
      if (otaLogScrollview == null) {
        break missingId;
      }

      id = R.id.ota_log_scrollview_short;
      ScrollView otaLogScrollviewShort = rootView.findViewById(id);
      if (otaLogScrollviewShort == null) {
        break missingId;
      }

      id = R.id.ota_log_text;
      TextView otaLogText = rootView.findViewById(id);
      if (otaLogText == null) {
        break missingId;
      }

      id = R.id.ota_log_text_short;
      TextView otaLogTextShort = rootView.findViewById(id);
      if (otaLogTextShort == null) {
        break missingId;
      }

      id = R.id.ota_over_btn;
      Button otaOverBtn = rootView.findViewById(id);
      if (otaOverBtn == null) {
        break missingId;
      }

      return new ViewOtalogBinding((ConstraintLayout) rootView, otaLogScrollview,
          otaLogScrollviewShort, otaLogText, otaLogTextShort, otaOverBtn);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
