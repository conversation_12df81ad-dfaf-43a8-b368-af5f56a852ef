// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: notificationsSetIndicatorDirectivePayload.proto

package com.amazon.proto.avs.v20160207.notifications;

public final class SetIndicatorDirectivePayload {
  private SetIndicatorDirectivePayload() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SetIndicatorDirectivePayloadProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:notifications.SetIndicatorDirectivePayloadProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>bool persistVisualIndicator = 1;</code>
     * @return The persistVisualIndicator.
     */
    boolean getPersistVisualIndicator();

    /**
     * <code>bool playAudioIndicator = 2;</code>
     * @return The playAudioIndicator.
     */
    boolean getPlayAudioIndicator();

    /**
     * <code>.notifications.SetIndicatorDirectivePayloadProto.Asset asset = 3;</code>
     * @return Whether the asset field is set.
     */
    boolean hasAsset();
    /**
     * <code>.notifications.SetIndicatorDirectivePayloadProto.Asset asset = 3;</code>
     * @return The asset.
     */
    com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset getAsset();
    /**
     * <code>.notifications.SetIndicatorDirectivePayloadProto.Asset asset = 3;</code>
     */
    com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.AssetOrBuilder getAssetOrBuilder();
  }
  /**
   * Protobuf type {@code notifications.SetIndicatorDirectivePayloadProto}
   */
  public static final class SetIndicatorDirectivePayloadProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:notifications.SetIndicatorDirectivePayloadProto)
      SetIndicatorDirectivePayloadProtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SetIndicatorDirectivePayloadProto.newBuilder() to construct.
    private SetIndicatorDirectivePayloadProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SetIndicatorDirectivePayloadProto() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SetIndicatorDirectivePayloadProto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.internal_static_notifications_SetIndicatorDirectivePayloadProto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.internal_static_notifications_SetIndicatorDirectivePayloadProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.class, com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Builder.class);
    }

    public interface AssetOrBuilder extends
        // @@protoc_insertion_point(interface_extends:notifications.SetIndicatorDirectivePayloadProto.Asset)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>string assetId = 1;</code>
       * @return The assetId.
       */
      java.lang.String getAssetId();
      /**
       * <code>string assetId = 1;</code>
       * @return The bytes for assetId.
       */
      com.google.protobuf.ByteString
          getAssetIdBytes();

      /**
       * <code>string url = 2;</code>
       * @return The url.
       */
      java.lang.String getUrl();
      /**
       * <code>string url = 2;</code>
       * @return The bytes for url.
       */
      com.google.protobuf.ByteString
          getUrlBytes();
    }
    /**
     * Protobuf type {@code notifications.SetIndicatorDirectivePayloadProto.Asset}
     */
    public static final class Asset extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:notifications.SetIndicatorDirectivePayloadProto.Asset)
        AssetOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use Asset.newBuilder() to construct.
      private Asset(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private Asset() {
        assetId_ = "";
        url_ = "";
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new Asset();
      }

      @java.lang.Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.internal_static_notifications_SetIndicatorDirectivePayloadProto_Asset_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.internal_static_notifications_SetIndicatorDirectivePayloadProto_Asset_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset.class, com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset.Builder.class);
      }

      public static final int ASSETID_FIELD_NUMBER = 1;
      private volatile java.lang.Object assetId_;
      /**
       * <code>string assetId = 1;</code>
       * @return The assetId.
       */
      @java.lang.Override
      public java.lang.String getAssetId() {
        java.lang.Object ref = assetId_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          assetId_ = s;
          return s;
        }
      }
      /**
       * <code>string assetId = 1;</code>
       * @return The bytes for assetId.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getAssetIdBytes() {
        java.lang.Object ref = assetId_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          assetId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int URL_FIELD_NUMBER = 2;
      private volatile java.lang.Object url_;
      /**
       * <code>string url = 2;</code>
       * @return The url.
       */
      @java.lang.Override
      public java.lang.String getUrl() {
        java.lang.Object ref = url_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          url_ = s;
          return s;
        }
      }
      /**
       * <code>string url = 2;</code>
       * @return The bytes for url.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getUrlBytes() {
        java.lang.Object ref = url_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          url_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(assetId_)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 1, assetId_);
        }
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(url_)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 2, url_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(assetId_)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, assetId_);
        }
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(url_)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, url_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset)) {
          return super.equals(obj);
        }
        com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset other = (com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset) obj;

        if (!getAssetId()
            .equals(other.getAssetId())) return false;
        if (!getUrl()
            .equals(other.getUrl())) return false;
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + ASSETID_FIELD_NUMBER;
        hash = (53 * hash) + getAssetId().hashCode();
        hash = (37 * hash) + URL_FIELD_NUMBER;
        hash = (53 * hash) + getUrl().hashCode();
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code notifications.SetIndicatorDirectivePayloadProto.Asset}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:notifications.SetIndicatorDirectivePayloadProto.Asset)
          com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.AssetOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.internal_static_notifications_SetIndicatorDirectivePayloadProto_Asset_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.internal_static_notifications_SetIndicatorDirectivePayloadProto_Asset_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset.class, com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset.Builder.class);
        }

        // Construct using com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          assetId_ = "";

          url_ = "";

          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.internal_static_notifications_SetIndicatorDirectivePayloadProto_Asset_descriptor;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset getDefaultInstanceForType() {
          return com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset.getDefaultInstance();
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset build() {
          com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset buildPartial() {
          com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset result = new com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset(this);
          result.assetId_ = assetId_;
          result.url_ = url_;
          onBuilt();
          return result;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset) {
            return mergeFrom((com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset other) {
          if (other == com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset.getDefaultInstance()) return this;
          if (!other.getAssetId().isEmpty()) {
            assetId_ = other.assetId_;
            onChanged();
          }
          if (!other.getUrl().isEmpty()) {
            url_ = other.url_;
            onChanged();
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 10: {
                  assetId_ = input.readStringRequireUtf8();

                  break;
                } // case 10
                case 18: {
                  url_ = input.readStringRequireUtf8();

                  break;
                } // case 18
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }

        private java.lang.Object assetId_ = "";
        /**
         * <code>string assetId = 1;</code>
         * @return The assetId.
         */
        public java.lang.String getAssetId() {
          java.lang.Object ref = assetId_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            assetId_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>string assetId = 1;</code>
         * @return The bytes for assetId.
         */
        public com.google.protobuf.ByteString
            getAssetIdBytes() {
          java.lang.Object ref = assetId_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            assetId_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string assetId = 1;</code>
         * @param value The assetId to set.
         * @return This builder for chaining.
         */
        public Builder setAssetId(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  
          assetId_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>string assetId = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearAssetId() {
          
          assetId_ = getDefaultInstance().getAssetId();
          onChanged();
          return this;
        }
        /**
         * <code>string assetId = 1;</code>
         * @param value The bytes for assetId to set.
         * @return This builder for chaining.
         */
        public Builder setAssetIdBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
          
          assetId_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object url_ = "";
        /**
         * <code>string url = 2;</code>
         * @return The url.
         */
        public java.lang.String getUrl() {
          java.lang.Object ref = url_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            url_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>string url = 2;</code>
         * @return The bytes for url.
         */
        public com.google.protobuf.ByteString
            getUrlBytes() {
          java.lang.Object ref = url_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            url_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string url = 2;</code>
         * @param value The url to set.
         * @return This builder for chaining.
         */
        public Builder setUrl(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  
          url_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>string url = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearUrl() {
          
          url_ = getDefaultInstance().getUrl();
          onChanged();
          return this;
        }
        /**
         * <code>string url = 2;</code>
         * @param value The bytes for url to set.
         * @return This builder for chaining.
         */
        public Builder setUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
          
          url_ = value;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:notifications.SetIndicatorDirectivePayloadProto.Asset)
      }

      // @@protoc_insertion_point(class_scope:notifications.SetIndicatorDirectivePayloadProto.Asset)
      private static final com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset();
      }

      public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Asset>
          PARSER = new com.google.protobuf.AbstractParser<Asset>() {
        @java.lang.Override
        public Asset parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Asset> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Asset> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public static final int PERSISTVISUALINDICATOR_FIELD_NUMBER = 1;
    private boolean persistVisualIndicator_;
    /**
     * <code>bool persistVisualIndicator = 1;</code>
     * @return The persistVisualIndicator.
     */
    @java.lang.Override
    public boolean getPersistVisualIndicator() {
      return persistVisualIndicator_;
    }

    public static final int PLAYAUDIOINDICATOR_FIELD_NUMBER = 2;
    private boolean playAudioIndicator_;
    /**
     * <code>bool playAudioIndicator = 2;</code>
     * @return The playAudioIndicator.
     */
    @java.lang.Override
    public boolean getPlayAudioIndicator() {
      return playAudioIndicator_;
    }

    public static final int ASSET_FIELD_NUMBER = 3;
    private com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset asset_;
    /**
     * <code>.notifications.SetIndicatorDirectivePayloadProto.Asset asset = 3;</code>
     * @return Whether the asset field is set.
     */
    @java.lang.Override
    public boolean hasAsset() {
      return asset_ != null;
    }
    /**
     * <code>.notifications.SetIndicatorDirectivePayloadProto.Asset asset = 3;</code>
     * @return The asset.
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset getAsset() {
      return asset_ == null ? com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset.getDefaultInstance() : asset_;
    }
    /**
     * <code>.notifications.SetIndicatorDirectivePayloadProto.Asset asset = 3;</code>
     */
    @java.lang.Override
    public com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.AssetOrBuilder getAssetOrBuilder() {
      return getAsset();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (persistVisualIndicator_ != false) {
        output.writeBool(1, persistVisualIndicator_);
      }
      if (playAudioIndicator_ != false) {
        output.writeBool(2, playAudioIndicator_);
      }
      if (asset_ != null) {
        output.writeMessage(3, getAsset());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (persistVisualIndicator_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, persistVisualIndicator_);
      }
      if (playAudioIndicator_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, playAudioIndicator_);
      }
      if (asset_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getAsset());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto)) {
        return super.equals(obj);
      }
      com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto other = (com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto) obj;

      if (getPersistVisualIndicator()
          != other.getPersistVisualIndicator()) return false;
      if (getPlayAudioIndicator()
          != other.getPlayAudioIndicator()) return false;
      if (hasAsset() != other.hasAsset()) return false;
      if (hasAsset()) {
        if (!getAsset()
            .equals(other.getAsset())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + PERSISTVISUALINDICATOR_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getPersistVisualIndicator());
      hash = (37 * hash) + PLAYAUDIOINDICATOR_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getPlayAudioIndicator());
      if (hasAsset()) {
        hash = (37 * hash) + ASSET_FIELD_NUMBER;
        hash = (53 * hash) + getAsset().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code notifications.SetIndicatorDirectivePayloadProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:notifications.SetIndicatorDirectivePayloadProto)
        com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.internal_static_notifications_SetIndicatorDirectivePayloadProto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.internal_static_notifications_SetIndicatorDirectivePayloadProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.class, com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Builder.class);
      }

      // Construct using com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        persistVisualIndicator_ = false;

        playAudioIndicator_ = false;

        if (assetBuilder_ == null) {
          asset_ = null;
        } else {
          asset_ = null;
          assetBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.internal_static_notifications_SetIndicatorDirectivePayloadProto_descriptor;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto getDefaultInstanceForType() {
        return com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto build() {
        com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto buildPartial() {
        com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto result = new com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto(this);
        result.persistVisualIndicator_ = persistVisualIndicator_;
        result.playAudioIndicator_ = playAudioIndicator_;
        if (assetBuilder_ == null) {
          result.asset_ = asset_;
        } else {
          result.asset_ = assetBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto) {
          return mergeFrom((com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto other) {
        if (other == com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.getDefaultInstance()) return this;
        if (other.getPersistVisualIndicator() != false) {
          setPersistVisualIndicator(other.getPersistVisualIndicator());
        }
        if (other.getPlayAudioIndicator() != false) {
          setPlayAudioIndicator(other.getPlayAudioIndicator());
        }
        if (other.hasAsset()) {
          mergeAsset(other.getAsset());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                persistVisualIndicator_ = input.readBool();

                break;
              } // case 8
              case 16: {
                playAudioIndicator_ = input.readBool();

                break;
              } // case 16
              case 26: {
                input.readMessage(
                    getAssetFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private boolean persistVisualIndicator_ ;
      /**
       * <code>bool persistVisualIndicator = 1;</code>
       * @return The persistVisualIndicator.
       */
      @java.lang.Override
      public boolean getPersistVisualIndicator() {
        return persistVisualIndicator_;
      }
      /**
       * <code>bool persistVisualIndicator = 1;</code>
       * @param value The persistVisualIndicator to set.
       * @return This builder for chaining.
       */
      public Builder setPersistVisualIndicator(boolean value) {
        
        persistVisualIndicator_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>bool persistVisualIndicator = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPersistVisualIndicator() {
        
        persistVisualIndicator_ = false;
        onChanged();
        return this;
      }

      private boolean playAudioIndicator_ ;
      /**
       * <code>bool playAudioIndicator = 2;</code>
       * @return The playAudioIndicator.
       */
      @java.lang.Override
      public boolean getPlayAudioIndicator() {
        return playAudioIndicator_;
      }
      /**
       * <code>bool playAudioIndicator = 2;</code>
       * @param value The playAudioIndicator to set.
       * @return This builder for chaining.
       */
      public Builder setPlayAudioIndicator(boolean value) {
        
        playAudioIndicator_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>bool playAudioIndicator = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayAudioIndicator() {
        
        playAudioIndicator_ = false;
        onChanged();
        return this;
      }

      private com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset asset_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset, com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset.Builder, com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.AssetOrBuilder> assetBuilder_;
      /**
       * <code>.notifications.SetIndicatorDirectivePayloadProto.Asset asset = 3;</code>
       * @return Whether the asset field is set.
       */
      public boolean hasAsset() {
        return assetBuilder_ != null || asset_ != null;
      }
      /**
       * <code>.notifications.SetIndicatorDirectivePayloadProto.Asset asset = 3;</code>
       * @return The asset.
       */
      public com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset getAsset() {
        if (assetBuilder_ == null) {
          return asset_ == null ? com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset.getDefaultInstance() : asset_;
        } else {
          return assetBuilder_.getMessage();
        }
      }
      /**
       * <code>.notifications.SetIndicatorDirectivePayloadProto.Asset asset = 3;</code>
       */
      public Builder setAsset(com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset value) {
        if (assetBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          asset_ = value;
          onChanged();
        } else {
          assetBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.notifications.SetIndicatorDirectivePayloadProto.Asset asset = 3;</code>
       */
      public Builder setAsset(
          com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset.Builder builderForValue) {
        if (assetBuilder_ == null) {
          asset_ = builderForValue.build();
          onChanged();
        } else {
          assetBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.notifications.SetIndicatorDirectivePayloadProto.Asset asset = 3;</code>
       */
      public Builder mergeAsset(com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset value) {
        if (assetBuilder_ == null) {
          if (asset_ != null) {
            asset_ =
              com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset.newBuilder(asset_).mergeFrom(value).buildPartial();
          } else {
            asset_ = value;
          }
          onChanged();
        } else {
          assetBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.notifications.SetIndicatorDirectivePayloadProto.Asset asset = 3;</code>
       */
      public Builder clearAsset() {
        if (assetBuilder_ == null) {
          asset_ = null;
          onChanged();
        } else {
          asset_ = null;
          assetBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.notifications.SetIndicatorDirectivePayloadProto.Asset asset = 3;</code>
       */
      public com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset.Builder getAssetBuilder() {
        
        onChanged();
        return getAssetFieldBuilder().getBuilder();
      }
      /**
       * <code>.notifications.SetIndicatorDirectivePayloadProto.Asset asset = 3;</code>
       */
      public com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.AssetOrBuilder getAssetOrBuilder() {
        if (assetBuilder_ != null) {
          return assetBuilder_.getMessageOrBuilder();
        } else {
          return asset_ == null ?
              com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset.getDefaultInstance() : asset_;
        }
      }
      /**
       * <code>.notifications.SetIndicatorDirectivePayloadProto.Asset asset = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset, com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset.Builder, com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.AssetOrBuilder> 
          getAssetFieldBuilder() {
        if (assetBuilder_ == null) {
          assetBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset, com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.Asset.Builder, com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto.AssetOrBuilder>(
                  getAsset(),
                  getParentForChildren(),
                  isClean());
          asset_ = null;
        }
        return assetBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:notifications.SetIndicatorDirectivePayloadProto)
    }

    // @@protoc_insertion_point(class_scope:notifications.SetIndicatorDirectivePayloadProto)
    private static final com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto();
    }

    public static com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SetIndicatorDirectivePayloadProto>
        PARSER = new com.google.protobuf.AbstractParser<SetIndicatorDirectivePayloadProto>() {
      @java.lang.Override
      public SetIndicatorDirectivePayloadProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SetIndicatorDirectivePayloadProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SetIndicatorDirectivePayloadProto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.proto.avs.v20160207.notifications.SetIndicatorDirectivePayload.SetIndicatorDirectivePayloadProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_notifications_SetIndicatorDirectivePayloadProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_notifications_SetIndicatorDirectivePayloadProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_notifications_SetIndicatorDirectivePayloadProto_Asset_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_notifications_SetIndicatorDirectivePayloadProto_Asset_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n/notificationsSetIndicatorDirectivePayl" +
      "oad.proto\022\rnotifications\"\315\001\n!SetIndicato" +
      "rDirectivePayloadProto\022\036\n\026persistVisualI" +
      "ndicator\030\001 \001(\010\022\032\n\022playAudioIndicator\030\002 \001" +
      "(\010\022E\n\005asset\030\003 \001(\01326.notifications.SetInd" +
      "icatorDirectivePayloadProto.Asset\032%\n\005Ass" +
      "et\022\017\n\007assetId\030\001 \001(\t\022\013\n\003url\030\002 \001(\tBL\n,com." +
      "amazon.proto.avs.v20160207.notifications" +
      "B\034SetIndicatorDirectivePayloadb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_notifications_SetIndicatorDirectivePayloadProto_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_notifications_SetIndicatorDirectivePayloadProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_notifications_SetIndicatorDirectivePayloadProto_descriptor,
        new java.lang.String[] { "PersistVisualIndicator", "PlayAudioIndicator", "Asset", });
    internal_static_notifications_SetIndicatorDirectivePayloadProto_Asset_descriptor =
      internal_static_notifications_SetIndicatorDirectivePayloadProto_descriptor.getNestedTypes().get(0);
    internal_static_notifications_SetIndicatorDirectivePayloadProto_Asset_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_notifications_SetIndicatorDirectivePayloadProto_Asset_descriptor,
        new java.lang.String[] { "AssetId", "Url", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
