<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_otafunction" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_otafunction.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_otafunction_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="260" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/activity_otafunction_0" include="toolbar"><Expressions/><location startLine="8" startOffset="4" endLine="11" endOffset="9"/></Target><Target id="@+id/usb_tota_ota" view="Button"><Expressions/><location startLine="30" startOffset="12" endLine="42" endOffset="17"/></Target><Target id="@+id/spp_ota_v1_old" view="Button"><Expressions/><location startLine="59" startOffset="12" endLine="71" endOffset="17"/></Target><Target id="@+id/spp_ota_v1" view="Button"><Expressions/><location startLine="79" startOffset="12" endLine="91" endOffset="17"/></Target><Target id="@+id/ble_ota_v1" view="Button"><Expressions/><location startLine="100" startOffset="12" endLine="112" endOffset="17"/></Target><Target id="@+id/spp_totaota_v1" view="Button"><Expressions/><location startLine="121" startOffset="12" endLine="133" endOffset="17"/></Target><Target id="@+id/ble_totaota_v1" view="Button"><Expressions/><location startLine="142" startOffset="12" endLine="154" endOffset="17"/></Target><Target id="@+id/spp_ota_v2" view="Button"><Expressions/><location startLine="169" startOffset="12" endLine="181" endOffset="17"/></Target><Target id="@+id/ble_ota_v2" view="Button"><Expressions/><location startLine="190" startOffset="12" endLine="202" endOffset="17"/></Target><Target id="@+id/spp_totaota_v2" view="Button"><Expressions/><location startLine="211" startOffset="12" endLine="223" endOffset="17"/></Target><Target id="@+id/ble_totaota_v2" view="Button"><Expressions/><location startLine="232" startOffset="12" endLine="244" endOffset="17"/></Target></Targets></Layout>