<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_findmy" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_findmy.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_findmy_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="88" endOffset="12"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="9" startOffset="4" endLine="86" endOffset="18"/></Target><Target id="@+id/tool" tag="binding_1" include="toolbar"><Expressions/><location startLine="15" startOffset="8" endLine="18" endOffset="13"/></Target><Target id="@+id/ble_name" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="38" endOffset="31"/></Target><Target id="@+id/pick_device" view="Button"><Expressions/><location startLine="40" startOffset="8" endLine="49" endOffset="13"/></Target><Target id="@+id/connect_device" view="Button"><Expressions/><location startLine="51" startOffset="8" endLine="60" endOffset="46"/></Target><Target id="@+id/send_data" view="Button"><Expressions/><location startLine="63" startOffset="8" endLine="73" endOffset="13"/></Target><Target id="@+id/stop_vibrate" view="Button"><Expressions/><location startLine="75" startOffset="8" endLine="85" endOffset="13"/></Target></Targets></Layout>