// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: directiveHeader.proto

package com.amazon.proto.avs.v20160207.header;

public final class DirectiveHeader {
  private DirectiveHeader() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface DirectiveHeaderProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:header.DirectiveHeaderProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string namespace = 1;</code>
     * @return The namespace.
     */
    java.lang.String getNamespace();
    /**
     * <code>string namespace = 1;</code>
     * @return The bytes for namespace.
     */
    com.google.protobuf.ByteString
        getNamespaceBytes();

    /**
     * <code>string name = 2;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <code>string messageId = 3;</code>
     * @return The messageId.
     */
    java.lang.String getMessageId();
    /**
     * <code>string messageId = 3;</code>
     * @return The bytes for messageId.
     */
    com.google.protobuf.ByteString
        getMessageIdBytes();

    /**
     * <code>string dialogRequestId = 4;</code>
     * @return The dialogRequestId.
     */
    java.lang.String getDialogRequestId();
    /**
     * <code>string dialogRequestId = 4;</code>
     * @return The bytes for dialogRequestId.
     */
    com.google.protobuf.ByteString
        getDialogRequestIdBytes();
  }
  /**
   * Protobuf type {@code header.DirectiveHeaderProto}
   */
  public static final class DirectiveHeaderProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:header.DirectiveHeaderProto)
      DirectiveHeaderProtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DirectiveHeaderProto.newBuilder() to construct.
    private DirectiveHeaderProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DirectiveHeaderProto() {
      namespace_ = "";
      name_ = "";
      messageId_ = "";
      dialogRequestId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DirectiveHeaderProto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.proto.avs.v20160207.header.DirectiveHeader.internal_static_header_DirectiveHeaderProto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.proto.avs.v20160207.header.DirectiveHeader.internal_static_header_DirectiveHeaderProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.class, com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.Builder.class);
    }

    public static final int NAMESPACE_FIELD_NUMBER = 1;
    private volatile java.lang.Object namespace_;
    /**
     * <code>string namespace = 1;</code>
     * @return The namespace.
     */
    @java.lang.Override
    public java.lang.String getNamespace() {
      java.lang.Object ref = namespace_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        namespace_ = s;
        return s;
      }
    }
    /**
     * <code>string namespace = 1;</code>
     * @return The bytes for namespace.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNamespaceBytes() {
      java.lang.Object ref = namespace_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        namespace_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int NAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object name_;
    /**
     * <code>string name = 2;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MESSAGEID_FIELD_NUMBER = 3;
    private volatile java.lang.Object messageId_;
    /**
     * <code>string messageId = 3;</code>
     * @return The messageId.
     */
    @java.lang.Override
    public java.lang.String getMessageId() {
      java.lang.Object ref = messageId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        messageId_ = s;
        return s;
      }
    }
    /**
     * <code>string messageId = 3;</code>
     * @return The bytes for messageId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMessageIdBytes() {
      java.lang.Object ref = messageId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        messageId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DIALOGREQUESTID_FIELD_NUMBER = 4;
    private volatile java.lang.Object dialogRequestId_;
    /**
     * <code>string dialogRequestId = 4;</code>
     * @return The dialogRequestId.
     */
    @java.lang.Override
    public java.lang.String getDialogRequestId() {
      java.lang.Object ref = dialogRequestId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        dialogRequestId_ = s;
        return s;
      }
    }
    /**
     * <code>string dialogRequestId = 4;</code>
     * @return The bytes for dialogRequestId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDialogRequestIdBytes() {
      java.lang.Object ref = dialogRequestId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        dialogRequestId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(namespace_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, namespace_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(messageId_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, messageId_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(dialogRequestId_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, dialogRequestId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(namespace_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, namespace_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(messageId_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, messageId_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(dialogRequestId_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, dialogRequestId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto)) {
        return super.equals(obj);
      }
      com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto other = (com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto) obj;

      if (!getNamespace()
          .equals(other.getNamespace())) return false;
      if (!getName()
          .equals(other.getName())) return false;
      if (!getMessageId()
          .equals(other.getMessageId())) return false;
      if (!getDialogRequestId()
          .equals(other.getDialogRequestId())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + NAMESPACE_FIELD_NUMBER;
      hash = (53 * hash) + getNamespace().hashCode();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (37 * hash) + MESSAGEID_FIELD_NUMBER;
      hash = (53 * hash) + getMessageId().hashCode();
      hash = (37 * hash) + DIALOGREQUESTID_FIELD_NUMBER;
      hash = (53 * hash) + getDialogRequestId().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code header.DirectiveHeaderProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:header.DirectiveHeaderProto)
        com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.proto.avs.v20160207.header.DirectiveHeader.internal_static_header_DirectiveHeaderProto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.proto.avs.v20160207.header.DirectiveHeader.internal_static_header_DirectiveHeaderProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.class, com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.Builder.class);
      }

      // Construct using com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        namespace_ = "";

        name_ = "";

        messageId_ = "";

        dialogRequestId_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.proto.avs.v20160207.header.DirectiveHeader.internal_static_header_DirectiveHeaderProto_descriptor;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto getDefaultInstanceForType() {
        return com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto build() {
        com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto buildPartial() {
        com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto result = new com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto(this);
        result.namespace_ = namespace_;
        result.name_ = name_;
        result.messageId_ = messageId_;
        result.dialogRequestId_ = dialogRequestId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto) {
          return mergeFrom((com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto other) {
        if (other == com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto.getDefaultInstance()) return this;
        if (!other.getNamespace().isEmpty()) {
          namespace_ = other.namespace_;
          onChanged();
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        if (!other.getMessageId().isEmpty()) {
          messageId_ = other.messageId_;
          onChanged();
        }
        if (!other.getDialogRequestId().isEmpty()) {
          dialogRequestId_ = other.dialogRequestId_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                namespace_ = input.readStringRequireUtf8();

                break;
              } // case 10
              case 18: {
                name_ = input.readStringRequireUtf8();

                break;
              } // case 18
              case 26: {
                messageId_ = input.readStringRequireUtf8();

                break;
              } // case 26
              case 34: {
                dialogRequestId_ = input.readStringRequireUtf8();

                break;
              } // case 34
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private java.lang.Object namespace_ = "";
      /**
       * <code>string namespace = 1;</code>
       * @return The namespace.
       */
      public java.lang.String getNamespace() {
        java.lang.Object ref = namespace_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          namespace_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string namespace = 1;</code>
       * @return The bytes for namespace.
       */
      public com.google.protobuf.ByteString
          getNamespaceBytes() {
        java.lang.Object ref = namespace_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          namespace_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string namespace = 1;</code>
       * @param value The namespace to set.
       * @return This builder for chaining.
       */
      public Builder setNamespace(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        namespace_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string namespace = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearNamespace() {
        
        namespace_ = getDefaultInstance().getNamespace();
        onChanged();
        return this;
      }
      /**
       * <code>string namespace = 1;</code>
       * @param value The bytes for namespace to set.
       * @return This builder for chaining.
       */
      public Builder setNamespaceBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        namespace_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>string name = 2;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object messageId_ = "";
      /**
       * <code>string messageId = 3;</code>
       * @return The messageId.
       */
      public java.lang.String getMessageId() {
        java.lang.Object ref = messageId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          messageId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string messageId = 3;</code>
       * @return The bytes for messageId.
       */
      public com.google.protobuf.ByteString
          getMessageIdBytes() {
        java.lang.Object ref = messageId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          messageId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string messageId = 3;</code>
       * @param value The messageId to set.
       * @return This builder for chaining.
       */
      public Builder setMessageId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        messageId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string messageId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMessageId() {
        
        messageId_ = getDefaultInstance().getMessageId();
        onChanged();
        return this;
      }
      /**
       * <code>string messageId = 3;</code>
       * @param value The bytes for messageId to set.
       * @return This builder for chaining.
       */
      public Builder setMessageIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        messageId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object dialogRequestId_ = "";
      /**
       * <code>string dialogRequestId = 4;</code>
       * @return The dialogRequestId.
       */
      public java.lang.String getDialogRequestId() {
        java.lang.Object ref = dialogRequestId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          dialogRequestId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string dialogRequestId = 4;</code>
       * @return The bytes for dialogRequestId.
       */
      public com.google.protobuf.ByteString
          getDialogRequestIdBytes() {
        java.lang.Object ref = dialogRequestId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          dialogRequestId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string dialogRequestId = 4;</code>
       * @param value The dialogRequestId to set.
       * @return This builder for chaining.
       */
      public Builder setDialogRequestId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        dialogRequestId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string dialogRequestId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearDialogRequestId() {
        
        dialogRequestId_ = getDefaultInstance().getDialogRequestId();
        onChanged();
        return this;
      }
      /**
       * <code>string dialogRequestId = 4;</code>
       * @param value The bytes for dialogRequestId to set.
       * @return This builder for chaining.
       */
      public Builder setDialogRequestIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        dialogRequestId_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:header.DirectiveHeaderProto)
    }

    // @@protoc_insertion_point(class_scope:header.DirectiveHeaderProto)
    private static final com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto();
    }

    public static com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DirectiveHeaderProto>
        PARSER = new com.google.protobuf.AbstractParser<DirectiveHeaderProto>() {
      @java.lang.Override
      public DirectiveHeaderProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<DirectiveHeaderProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DirectiveHeaderProto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.proto.avs.v20160207.header.DirectiveHeader.DirectiveHeaderProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_header_DirectiveHeaderProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_header_DirectiveHeaderProto_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\025directiveHeader.proto\022\006header\"c\n\024Direc" +
      "tiveHeaderProto\022\021\n\tnamespace\030\001 \001(\t\022\014\n\004na" +
      "me\030\002 \001(\t\022\021\n\tmessageId\030\003 \001(\t\022\027\n\017dialogReq" +
      "uestId\030\004 \001(\tB8\n%com.amazon.proto.avs.v20" +
      "160207.headerB\017DirectiveHeaderb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_header_DirectiveHeaderProto_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_header_DirectiveHeaderProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_header_DirectiveHeaderProto_descriptor,
        new java.lang.String[] { "Namespace", "Name", "MessageId", "DialogRequestId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
