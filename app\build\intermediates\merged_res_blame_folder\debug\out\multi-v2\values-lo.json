{"logs": [{"outputFile": "D:\\test_Travel\\BES_android\\BesAll_UI_android\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-lo\\values-lo.xml", "map": [{"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\7ad5be15223e864e4511b3f7157ae4d3\\appcompat-1.2.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "274,377,480,593,678,782,893,971,1048,1139,1232,1324,1418,1518,1611,1706,1802,1893,1984,2065,2172,2276,2374,2477,2581,2685,2842,7402", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "372,475,588,673,777,888,966,1043,1134,1227,1319,1413,1513,1606,1701,1797,1888,1979,2060,2167,2271,2369,2472,2576,2680,2837,2936,7479"}}, {"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\85d353c95dd38846b16194b262acf36b\\material-1.4.0-beta01\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,305,420,539,622,688,777,846,905,1000,1065,1123,1188,1249,1309,1415,1476,1536,1594,1665,1784,1870,1952,2065,2140,2216,2306,2373,2439,2508,2582,2661,2734,2811,2880,2950,3035,3110,3203,3296,3370,3439,3533,3585,3652,3736,3820,3882,3946,4009,4108,4200,4295,4387", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,80,114,118,82,65,88,68,58,94,64,57,64,60,59,105,60,59,57,70,118,85,81,112,74,75,89,66,65,68,73,78,72,76,68,69,84,74,92,92,73,68,93,51,66,83,83,61,63,62,98,91,94,91,78", "endOffsets": "219,300,415,534,617,683,772,841,900,995,1060,1118,1183,1244,1304,1410,1471,1531,1589,1660,1779,1865,1947,2060,2135,2211,2301,2368,2434,2503,2577,2656,2729,2806,2875,2945,3030,3105,3198,3291,3365,3434,3528,3580,3647,3731,3815,3877,3941,4004,4103,4195,4290,4382,4461"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2941,3022,3137,3256,3339,3405,3494,3563,3622,3717,3782,3840,3905,3966,4026,4132,4193,4253,4311,4382,4501,4587,4669,4782,4857,4933,5023,5090,5156,5225,5299,5378,5451,5528,5597,5667,5752,5827,5920,6013,6087,6156,6250,6302,6369,6453,6537,6599,6663,6726,6825,6917,7012,7323", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,88", "endColumns": "12,80,114,118,82,65,88,68,58,94,64,57,64,60,59,105,60,59,57,70,118,85,81,112,74,75,89,66,65,68,73,78,72,76,68,69,84,74,92,92,73,68,93,51,66,83,83,61,63,62,98,91,94,91,78", "endOffsets": "269,3017,3132,3251,3334,3400,3489,3558,3617,3712,3777,3835,3900,3961,4021,4127,4188,4248,4306,4377,4496,4582,4664,4777,4852,4928,5018,5085,5151,5220,5294,5373,5446,5523,5592,5662,5747,5822,5915,6008,6082,6151,6245,6297,6364,6448,6532,6594,6658,6721,6820,6912,7007,7099,7397"}}, {"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\ec1a13a01684407bf169e0545c40db84\\navigation-ui-2.4.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,109", "endOffsets": "159,269"}, "to": {"startLines": "86,87", "startColumns": "4,4", "startOffsets": "7104,7213", "endColumns": "108,109", "endOffsets": "7208,7318"}}, {"source": "E:\\Android_Studio_Data\\ .gradle\\caches\\transforms-2\\files-2.1\\3a6c3290bf94c2fdaee81cec79dba243\\core-1.5.0-rc01\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "90", "startColumns": "4", "startOffsets": "7484", "endColumns": "100", "endOffsets": "7580"}}]}]}