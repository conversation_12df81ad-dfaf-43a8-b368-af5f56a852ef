// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: system.proto

package com.amazon.alexa.accessory.protocol;

public final class System {
  private System() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ResetConnectionOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ResetConnection)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 timeout = 1;</code>
     * @return The timeout.
     */
    int getTimeout();

    /**
     * <code>bool force_disconnect = 2;</code>
     * @return The forceDisconnect.
     */
    boolean getForceDisconnect();

    /**
     * <code>.ResetConnection.ResetReason reset_reason = 3;</code>
     * @return The enum numeric value on the wire for resetReason.
     */
    int getResetReasonValue();
    /**
     * <code>.ResetConnection.ResetReason reset_reason = 3;</code>
     * @return The resetReason.
     */
    com.amazon.alexa.accessory.protocol.System.ResetConnection.ResetReason getResetReason();
  }
  /**
   * Protobuf type {@code ResetConnection}
   */
  public static final class ResetConnection extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ResetConnection)
      ResetConnectionOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResetConnection.newBuilder() to construct.
    private ResetConnection(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResetConnection() {
      resetReason_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResetConnection();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.System.internal_static_ResetConnection_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.System.internal_static_ResetConnection_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.System.ResetConnection.class, com.amazon.alexa.accessory.protocol.System.ResetConnection.Builder.class);
    }

    /**
     * Protobuf enum {@code ResetConnection.ResetReason}
     */
    public enum ResetReason
        implements com.google.protobuf.ProtocolMessageEnum {
      /**
       * <code>UNKNOWN = 0;</code>
       */
      UNKNOWN(0),
      /**
       * <code>AAP_REFUSED_MAX_CONNECTIONS_REACHED = 1;</code>
       */
      AAP_REFUSED_MAX_CONNECTIONS_REACHED(1),
      UNRECOGNIZED(-1),
      ;

      /**
       * <code>UNKNOWN = 0;</code>
       */
      public static final int UNKNOWN_VALUE = 0;
      /**
       * <code>AAP_REFUSED_MAX_CONNECTIONS_REACHED = 1;</code>
       */
      public static final int AAP_REFUSED_MAX_CONNECTIONS_REACHED_VALUE = 1;


      public final int getNumber() {
        if (this == UNRECOGNIZED) {
          throw new java.lang.IllegalArgumentException(
              "Can't get the number of an unknown enum value.");
        }
        return value;
      }

      /**
       * @param value The numeric wire value of the corresponding enum entry.
       * @return The enum associated with the given numeric wire value.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static ResetReason valueOf(int value) {
        return forNumber(value);
      }

      /**
       * @param value The numeric wire value of the corresponding enum entry.
       * @return The enum associated with the given numeric wire value.
       */
      public static ResetReason forNumber(int value) {
        switch (value) {
          case 0: return UNKNOWN;
          case 1: return AAP_REFUSED_MAX_CONNECTIONS_REACHED;
          default: return null;
        }
      }

      public static com.google.protobuf.Internal.EnumLiteMap<ResetReason>
          internalGetValueMap() {
        return internalValueMap;
      }
      private static final com.google.protobuf.Internal.EnumLiteMap<
          ResetReason> internalValueMap =
            new com.google.protobuf.Internal.EnumLiteMap<ResetReason>() {
              public ResetReason findValueByNumber(int number) {
                return ResetReason.forNumber(number);
              }
            };

      public final com.google.protobuf.Descriptors.EnumValueDescriptor
          getValueDescriptor() {
        if (this == UNRECOGNIZED) {
          throw new java.lang.IllegalStateException(
              "Can't get the descriptor of an unrecognized enum value.");
        }
        return getDescriptor().getValues().get(ordinal());
      }
      public final com.google.protobuf.Descriptors.EnumDescriptor
          getDescriptorForType() {
        return getDescriptor();
      }
      public static final com.google.protobuf.Descriptors.EnumDescriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.System.ResetConnection.getDescriptor().getEnumTypes().get(0);
      }

      private static final ResetReason[] VALUES = values();

      public static ResetReason valueOf(
          com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
        if (desc.getType() != getDescriptor()) {
          throw new java.lang.IllegalArgumentException(
            "EnumValueDescriptor is not for this type.");
        }
        if (desc.getIndex() == -1) {
          return UNRECOGNIZED;
        }
        return VALUES[desc.getIndex()];
      }

      private final int value;

      private ResetReason(int value) {
        this.value = value;
      }

      // @@protoc_insertion_point(enum_scope:ResetConnection.ResetReason)
    }

    public static final int TIMEOUT_FIELD_NUMBER = 1;
    private int timeout_;
    /**
     * <code>uint32 timeout = 1;</code>
     * @return The timeout.
     */
    @java.lang.Override
    public int getTimeout() {
      return timeout_;
    }

    public static final int FORCE_DISCONNECT_FIELD_NUMBER = 2;
    private boolean forceDisconnect_;
    /**
     * <code>bool force_disconnect = 2;</code>
     * @return The forceDisconnect.
     */
    @java.lang.Override
    public boolean getForceDisconnect() {
      return forceDisconnect_;
    }

    public static final int RESET_REASON_FIELD_NUMBER = 3;
    private int resetReason_;
    /**
     * <code>.ResetConnection.ResetReason reset_reason = 3;</code>
     * @return The enum numeric value on the wire for resetReason.
     */
    @java.lang.Override public int getResetReasonValue() {
      return resetReason_;
    }
    /**
     * <code>.ResetConnection.ResetReason reset_reason = 3;</code>
     * @return The resetReason.
     */
    @java.lang.Override public com.amazon.alexa.accessory.protocol.System.ResetConnection.ResetReason getResetReason() {
      @SuppressWarnings("deprecation")
      com.amazon.alexa.accessory.protocol.System.ResetConnection.ResetReason result = com.amazon.alexa.accessory.protocol.System.ResetConnection.ResetReason.valueOf(resetReason_);
      return result == null ? com.amazon.alexa.accessory.protocol.System.ResetConnection.ResetReason.UNRECOGNIZED : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (timeout_ != 0) {
        output.writeUInt32(1, timeout_);
      }
      if (forceDisconnect_ != false) {
        output.writeBool(2, forceDisconnect_);
      }
      if (resetReason_ != com.amazon.alexa.accessory.protocol.System.ResetConnection.ResetReason.UNKNOWN.getNumber()) {
        output.writeEnum(3, resetReason_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (timeout_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, timeout_);
      }
      if (forceDisconnect_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, forceDisconnect_);
      }
      if (resetReason_ != com.amazon.alexa.accessory.protocol.System.ResetConnection.ResetReason.UNKNOWN.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(3, resetReason_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.System.ResetConnection)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.System.ResetConnection other = (com.amazon.alexa.accessory.protocol.System.ResetConnection) obj;

      if (getTimeout()
          != other.getTimeout()) return false;
      if (getForceDisconnect()
          != other.getForceDisconnect()) return false;
      if (resetReason_ != other.resetReason_) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TIMEOUT_FIELD_NUMBER;
      hash = (53 * hash) + getTimeout();
      hash = (37 * hash) + FORCE_DISCONNECT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getForceDisconnect());
      hash = (37 * hash) + RESET_REASON_FIELD_NUMBER;
      hash = (53 * hash) + resetReason_;
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.System.ResetConnection parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.ResetConnection parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.ResetConnection parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.ResetConnection parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.ResetConnection parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.ResetConnection parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.ResetConnection parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.ResetConnection parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.ResetConnection parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.ResetConnection parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.ResetConnection parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.ResetConnection parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.System.ResetConnection prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code ResetConnection}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ResetConnection)
        com.amazon.alexa.accessory.protocol.System.ResetConnectionOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_ResetConnection_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_ResetConnection_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.System.ResetConnection.class, com.amazon.alexa.accessory.protocol.System.ResetConnection.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.System.ResetConnection.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        timeout_ = 0;

        forceDisconnect_ = false;

        resetReason_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_ResetConnection_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.ResetConnection getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.System.ResetConnection.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.ResetConnection build() {
        com.amazon.alexa.accessory.protocol.System.ResetConnection result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.ResetConnection buildPartial() {
        com.amazon.alexa.accessory.protocol.System.ResetConnection result = new com.amazon.alexa.accessory.protocol.System.ResetConnection(this);
        result.timeout_ = timeout_;
        result.forceDisconnect_ = forceDisconnect_;
        result.resetReason_ = resetReason_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.System.ResetConnection) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.System.ResetConnection)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.System.ResetConnection other) {
        if (other == com.amazon.alexa.accessory.protocol.System.ResetConnection.getDefaultInstance()) return this;
        if (other.getTimeout() != 0) {
          setTimeout(other.getTimeout());
        }
        if (other.getForceDisconnect() != false) {
          setForceDisconnect(other.getForceDisconnect());
        }
        if (other.resetReason_ != 0) {
          setResetReasonValue(other.getResetReasonValue());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                timeout_ = input.readUInt32();

                break;
              } // case 8
              case 16: {
                forceDisconnect_ = input.readBool();

                break;
              } // case 16
              case 24: {
                resetReason_ = input.readEnum();

                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private int timeout_ ;
      /**
       * <code>uint32 timeout = 1;</code>
       * @return The timeout.
       */
      @java.lang.Override
      public int getTimeout() {
        return timeout_;
      }
      /**
       * <code>uint32 timeout = 1;</code>
       * @param value The timeout to set.
       * @return This builder for chaining.
       */
      public Builder setTimeout(int value) {
        
        timeout_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 timeout = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimeout() {
        
        timeout_ = 0;
        onChanged();
        return this;
      }

      private boolean forceDisconnect_ ;
      /**
       * <code>bool force_disconnect = 2;</code>
       * @return The forceDisconnect.
       */
      @java.lang.Override
      public boolean getForceDisconnect() {
        return forceDisconnect_;
      }
      /**
       * <code>bool force_disconnect = 2;</code>
       * @param value The forceDisconnect to set.
       * @return This builder for chaining.
       */
      public Builder setForceDisconnect(boolean value) {
        
        forceDisconnect_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>bool force_disconnect = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearForceDisconnect() {
        
        forceDisconnect_ = false;
        onChanged();
        return this;
      }

      private int resetReason_ = 0;
      /**
       * <code>.ResetConnection.ResetReason reset_reason = 3;</code>
       * @return The enum numeric value on the wire for resetReason.
       */
      @java.lang.Override public int getResetReasonValue() {
        return resetReason_;
      }
      /**
       * <code>.ResetConnection.ResetReason reset_reason = 3;</code>
       * @param value The enum numeric value on the wire for resetReason to set.
       * @return This builder for chaining.
       */
      public Builder setResetReasonValue(int value) {
        
        resetReason_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.ResetConnection.ResetReason reset_reason = 3;</code>
       * @return The resetReason.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.ResetConnection.ResetReason getResetReason() {
        @SuppressWarnings("deprecation")
        com.amazon.alexa.accessory.protocol.System.ResetConnection.ResetReason result = com.amazon.alexa.accessory.protocol.System.ResetConnection.ResetReason.valueOf(resetReason_);
        return result == null ? com.amazon.alexa.accessory.protocol.System.ResetConnection.ResetReason.UNRECOGNIZED : result;
      }
      /**
       * <code>.ResetConnection.ResetReason reset_reason = 3;</code>
       * @param value The resetReason to set.
       * @return This builder for chaining.
       */
      public Builder setResetReason(com.amazon.alexa.accessory.protocol.System.ResetConnection.ResetReason value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        resetReason_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.ResetConnection.ResetReason reset_reason = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearResetReason() {
        
        resetReason_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ResetConnection)
    }

    // @@protoc_insertion_point(class_scope:ResetConnection)
    private static final com.amazon.alexa.accessory.protocol.System.ResetConnection DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.System.ResetConnection();
    }

    public static com.amazon.alexa.accessory.protocol.System.ResetConnection getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResetConnection>
        PARSER = new com.google.protobuf.AbstractParser<ResetConnection>() {
      @java.lang.Override
      public ResetConnection parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ResetConnection> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResetConnection> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.ResetConnection getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SynchronizeSettingsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:SynchronizeSettings)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 timestamp_hi = 1;</code>
     * @return The timestampHi.
     */
    int getTimestampHi();

    /**
     * <code>uint32 timestamp_lo = 2;</code>
     * @return The timestampLo.
     */
    int getTimestampLo();
  }
  /**
   * Protobuf type {@code SynchronizeSettings}
   */
  public static final class SynchronizeSettings extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:SynchronizeSettings)
      SynchronizeSettingsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SynchronizeSettings.newBuilder() to construct.
    private SynchronizeSettings(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SynchronizeSettings() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SynchronizeSettings();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.System.internal_static_SynchronizeSettings_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.System.internal_static_SynchronizeSettings_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.System.SynchronizeSettings.class, com.amazon.alexa.accessory.protocol.System.SynchronizeSettings.Builder.class);
    }

    public static final int TIMESTAMP_HI_FIELD_NUMBER = 1;
    private int timestampHi_;
    /**
     * <code>uint32 timestamp_hi = 1;</code>
     * @return The timestampHi.
     */
    @java.lang.Override
    public int getTimestampHi() {
      return timestampHi_;
    }

    public static final int TIMESTAMP_LO_FIELD_NUMBER = 2;
    private int timestampLo_;
    /**
     * <code>uint32 timestamp_lo = 2;</code>
     * @return The timestampLo.
     */
    @java.lang.Override
    public int getTimestampLo() {
      return timestampLo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (timestampHi_ != 0) {
        output.writeUInt32(1, timestampHi_);
      }
      if (timestampLo_ != 0) {
        output.writeUInt32(2, timestampLo_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (timestampHi_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, timestampHi_);
      }
      if (timestampLo_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, timestampLo_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.System.SynchronizeSettings)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.System.SynchronizeSettings other = (com.amazon.alexa.accessory.protocol.System.SynchronizeSettings) obj;

      if (getTimestampHi()
          != other.getTimestampHi()) return false;
      if (getTimestampLo()
          != other.getTimestampLo()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TIMESTAMP_HI_FIELD_NUMBER;
      hash = (53 * hash) + getTimestampHi();
      hash = (37 * hash) + TIMESTAMP_LO_FIELD_NUMBER;
      hash = (53 * hash) + getTimestampLo();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.System.SynchronizeSettings parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.SynchronizeSettings parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.SynchronizeSettings parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.SynchronizeSettings parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.SynchronizeSettings parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.SynchronizeSettings parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.SynchronizeSettings parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.SynchronizeSettings parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.SynchronizeSettings parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.SynchronizeSettings parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.SynchronizeSettings parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.SynchronizeSettings parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.System.SynchronizeSettings prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code SynchronizeSettings}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:SynchronizeSettings)
        com.amazon.alexa.accessory.protocol.System.SynchronizeSettingsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_SynchronizeSettings_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_SynchronizeSettings_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.System.SynchronizeSettings.class, com.amazon.alexa.accessory.protocol.System.SynchronizeSettings.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.System.SynchronizeSettings.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        timestampHi_ = 0;

        timestampLo_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_SynchronizeSettings_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.SynchronizeSettings getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.System.SynchronizeSettings.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.SynchronizeSettings build() {
        com.amazon.alexa.accessory.protocol.System.SynchronizeSettings result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.SynchronizeSettings buildPartial() {
        com.amazon.alexa.accessory.protocol.System.SynchronizeSettings result = new com.amazon.alexa.accessory.protocol.System.SynchronizeSettings(this);
        result.timestampHi_ = timestampHi_;
        result.timestampLo_ = timestampLo_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.System.SynchronizeSettings) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.System.SynchronizeSettings)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.System.SynchronizeSettings other) {
        if (other == com.amazon.alexa.accessory.protocol.System.SynchronizeSettings.getDefaultInstance()) return this;
        if (other.getTimestampHi() != 0) {
          setTimestampHi(other.getTimestampHi());
        }
        if (other.getTimestampLo() != 0) {
          setTimestampLo(other.getTimestampLo());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                timestampHi_ = input.readUInt32();

                break;
              } // case 8
              case 16: {
                timestampLo_ = input.readUInt32();

                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private int timestampHi_ ;
      /**
       * <code>uint32 timestamp_hi = 1;</code>
       * @return The timestampHi.
       */
      @java.lang.Override
      public int getTimestampHi() {
        return timestampHi_;
      }
      /**
       * <code>uint32 timestamp_hi = 1;</code>
       * @param value The timestampHi to set.
       * @return This builder for chaining.
       */
      public Builder setTimestampHi(int value) {
        
        timestampHi_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 timestamp_hi = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimestampHi() {
        
        timestampHi_ = 0;
        onChanged();
        return this;
      }

      private int timestampLo_ ;
      /**
       * <code>uint32 timestamp_lo = 2;</code>
       * @return The timestampLo.
       */
      @java.lang.Override
      public int getTimestampLo() {
        return timestampLo_;
      }
      /**
       * <code>uint32 timestamp_lo = 2;</code>
       * @param value The timestampLo to set.
       * @return This builder for chaining.
       */
      public Builder setTimestampLo(int value) {
        
        timestampLo_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 timestamp_lo = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimestampLo() {
        
        timestampLo_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:SynchronizeSettings)
    }

    // @@protoc_insertion_point(class_scope:SynchronizeSettings)
    private static final com.amazon.alexa.accessory.protocol.System.SynchronizeSettings DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.System.SynchronizeSettings();
    }

    public static com.amazon.alexa.accessory.protocol.System.SynchronizeSettings getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SynchronizeSettings>
        PARSER = new com.google.protobuf.AbstractParser<SynchronizeSettings>() {
      @java.lang.Override
      public SynchronizeSettings parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SynchronizeSettings> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SynchronizeSettings> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.SynchronizeSettings getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface KeepAliveOrBuilder extends
      // @@protoc_insertion_point(interface_extends:KeepAlive)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code KeepAlive}
   */
  public static final class KeepAlive extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:KeepAlive)
      KeepAliveOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use KeepAlive.newBuilder() to construct.
    private KeepAlive(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private KeepAlive() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new KeepAlive();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.System.internal_static_KeepAlive_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.System.internal_static_KeepAlive_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.System.KeepAlive.class, com.amazon.alexa.accessory.protocol.System.KeepAlive.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.System.KeepAlive)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.System.KeepAlive other = (com.amazon.alexa.accessory.protocol.System.KeepAlive) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.System.KeepAlive parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.KeepAlive parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.KeepAlive parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.KeepAlive parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.KeepAlive parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.KeepAlive parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.KeepAlive parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.KeepAlive parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.KeepAlive parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.KeepAlive parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.KeepAlive parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.KeepAlive parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.System.KeepAlive prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code KeepAlive}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:KeepAlive)
        com.amazon.alexa.accessory.protocol.System.KeepAliveOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_KeepAlive_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_KeepAlive_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.System.KeepAlive.class, com.amazon.alexa.accessory.protocol.System.KeepAlive.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.System.KeepAlive.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_KeepAlive_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.KeepAlive getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.System.KeepAlive.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.KeepAlive build() {
        com.amazon.alexa.accessory.protocol.System.KeepAlive result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.KeepAlive buildPartial() {
        com.amazon.alexa.accessory.protocol.System.KeepAlive result = new com.amazon.alexa.accessory.protocol.System.KeepAlive(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.System.KeepAlive) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.System.KeepAlive)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.System.KeepAlive other) {
        if (other == com.amazon.alexa.accessory.protocol.System.KeepAlive.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:KeepAlive)
    }

    // @@protoc_insertion_point(class_scope:KeepAlive)
    private static final com.amazon.alexa.accessory.protocol.System.KeepAlive DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.System.KeepAlive();
    }

    public static com.amazon.alexa.accessory.protocol.System.KeepAlive getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<KeepAlive>
        PARSER = new com.google.protobuf.AbstractParser<KeepAlive>() {
      @java.lang.Override
      public KeepAlive parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<KeepAlive> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<KeepAlive> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.KeepAlive getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RemoveDeviceOrBuilder extends
      // @@protoc_insertion_point(interface_extends:RemoveDevice)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code RemoveDevice}
   */
  public static final class RemoveDevice extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:RemoveDevice)
      RemoveDeviceOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RemoveDevice.newBuilder() to construct.
    private RemoveDevice(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RemoveDevice() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RemoveDevice();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.System.internal_static_RemoveDevice_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.System.internal_static_RemoveDevice_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.System.RemoveDevice.class, com.amazon.alexa.accessory.protocol.System.RemoveDevice.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.System.RemoveDevice)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.System.RemoveDevice other = (com.amazon.alexa.accessory.protocol.System.RemoveDevice) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.System.RemoveDevice parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.RemoveDevice parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.RemoveDevice parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.RemoveDevice parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.RemoveDevice parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.RemoveDevice parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.RemoveDevice parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.RemoveDevice parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.RemoveDevice parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.RemoveDevice parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.RemoveDevice parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.RemoveDevice parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.System.RemoveDevice prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code RemoveDevice}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:RemoveDevice)
        com.amazon.alexa.accessory.protocol.System.RemoveDeviceOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_RemoveDevice_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_RemoveDevice_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.System.RemoveDevice.class, com.amazon.alexa.accessory.protocol.System.RemoveDevice.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.System.RemoveDevice.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_RemoveDevice_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.RemoveDevice getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.System.RemoveDevice.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.RemoveDevice build() {
        com.amazon.alexa.accessory.protocol.System.RemoveDevice result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.RemoveDevice buildPartial() {
        com.amazon.alexa.accessory.protocol.System.RemoveDevice result = new com.amazon.alexa.accessory.protocol.System.RemoveDevice(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.System.RemoveDevice) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.System.RemoveDevice)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.System.RemoveDevice other) {
        if (other == com.amazon.alexa.accessory.protocol.System.RemoveDevice.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:RemoveDevice)
    }

    // @@protoc_insertion_point(class_scope:RemoveDevice)
    private static final com.amazon.alexa.accessory.protocol.System.RemoveDevice DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.System.RemoveDevice();
    }

    public static com.amazon.alexa.accessory.protocol.System.RemoveDevice getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RemoveDevice>
        PARSER = new com.google.protobuf.AbstractParser<RemoveDevice>() {
      @java.lang.Override
      public RemoveDevice parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<RemoveDevice> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RemoveDevice> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.RemoveDevice getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface LocaleOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Locale)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string name = 1;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <code>string name = 1;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();
  }
  /**
   * Protobuf type {@code Locale}
   */
  public static final class Locale extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Locale)
      LocaleOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Locale.newBuilder() to construct.
    private Locale(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Locale() {
      name_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Locale();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.System.internal_static_Locale_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.System.internal_static_Locale_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.System.Locale.class, com.amazon.alexa.accessory.protocol.System.Locale.Builder.class);
    }

    public static final int NAME_FIELD_NUMBER = 1;
    private volatile java.lang.Object name_;
    /**
     * <code>string name = 1;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <code>string name = 1;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, name_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, name_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.System.Locale)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.System.Locale other = (com.amazon.alexa.accessory.protocol.System.Locale) obj;

      if (!getName()
          .equals(other.getName())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.System.Locale parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locale parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locale parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locale parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locale parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locale parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locale parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locale parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locale parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locale parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locale parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locale parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.System.Locale prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Locale}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Locale)
        com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_Locale_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_Locale_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.System.Locale.class, com.amazon.alexa.accessory.protocol.System.Locale.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.System.Locale.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        name_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_Locale_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.Locale getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.System.Locale.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.Locale build() {
        com.amazon.alexa.accessory.protocol.System.Locale result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.Locale buildPartial() {
        com.amazon.alexa.accessory.protocol.System.Locale result = new com.amazon.alexa.accessory.protocol.System.Locale(this);
        result.name_ = name_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.System.Locale) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.System.Locale)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.System.Locale other) {
        if (other == com.amazon.alexa.accessory.protocol.System.Locale.getDefaultInstance()) return this;
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                name_ = input.readStringRequireUtf8();

                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>string name = 1;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string name = 1;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string name = 1;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string name = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>string name = 1;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Locale)
    }

    // @@protoc_insertion_point(class_scope:Locale)
    private static final com.amazon.alexa.accessory.protocol.System.Locale DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.System.Locale();
    }

    public static com.amazon.alexa.accessory.protocol.System.Locale getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Locale>
        PARSER = new com.google.protobuf.AbstractParser<Locale>() {
      @java.lang.Override
      public Locale parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Locale> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Locale> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.Locale getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface LocalesOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Locales)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .Locale supported_locales = 1;</code>
     */
    java.util.List<com.amazon.alexa.accessory.protocol.System.Locale> 
        getSupportedLocalesList();
    /**
     * <code>repeated .Locale supported_locales = 1;</code>
     */
    com.amazon.alexa.accessory.protocol.System.Locale getSupportedLocales(int index);
    /**
     * <code>repeated .Locale supported_locales = 1;</code>
     */
    int getSupportedLocalesCount();
    /**
     * <code>repeated .Locale supported_locales = 1;</code>
     */
    java.util.List<? extends com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder> 
        getSupportedLocalesOrBuilderList();
    /**
     * <code>repeated .Locale supported_locales = 1;</code>
     */
    com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder getSupportedLocalesOrBuilder(
        int index);

    /**
     * <code>.Locale current_locale = 2;</code>
     * @return Whether the currentLocale field is set.
     */
    boolean hasCurrentLocale();
    /**
     * <code>.Locale current_locale = 2;</code>
     * @return The currentLocale.
     */
    com.amazon.alexa.accessory.protocol.System.Locale getCurrentLocale();
    /**
     * <code>.Locale current_locale = 2;</code>
     */
    com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder getCurrentLocaleOrBuilder();
  }
  /**
   * Protobuf type {@code Locales}
   */
  public static final class Locales extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Locales)
      LocalesOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Locales.newBuilder() to construct.
    private Locales(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Locales() {
      supportedLocales_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Locales();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.System.internal_static_Locales_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.System.internal_static_Locales_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.System.Locales.class, com.amazon.alexa.accessory.protocol.System.Locales.Builder.class);
    }

    public static final int SUPPORTED_LOCALES_FIELD_NUMBER = 1;
    private java.util.List<com.amazon.alexa.accessory.protocol.System.Locale> supportedLocales_;
    /**
     * <code>repeated .Locale supported_locales = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.amazon.alexa.accessory.protocol.System.Locale> getSupportedLocalesList() {
      return supportedLocales_;
    }
    /**
     * <code>repeated .Locale supported_locales = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder> 
        getSupportedLocalesOrBuilderList() {
      return supportedLocales_;
    }
    /**
     * <code>repeated .Locale supported_locales = 1;</code>
     */
    @java.lang.Override
    public int getSupportedLocalesCount() {
      return supportedLocales_.size();
    }
    /**
     * <code>repeated .Locale supported_locales = 1;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.Locale getSupportedLocales(int index) {
      return supportedLocales_.get(index);
    }
    /**
     * <code>repeated .Locale supported_locales = 1;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder getSupportedLocalesOrBuilder(
        int index) {
      return supportedLocales_.get(index);
    }

    public static final int CURRENT_LOCALE_FIELD_NUMBER = 2;
    private com.amazon.alexa.accessory.protocol.System.Locale currentLocale_;
    /**
     * <code>.Locale current_locale = 2;</code>
     * @return Whether the currentLocale field is set.
     */
    @java.lang.Override
    public boolean hasCurrentLocale() {
      return currentLocale_ != null;
    }
    /**
     * <code>.Locale current_locale = 2;</code>
     * @return The currentLocale.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.Locale getCurrentLocale() {
      return currentLocale_ == null ? com.amazon.alexa.accessory.protocol.System.Locale.getDefaultInstance() : currentLocale_;
    }
    /**
     * <code>.Locale current_locale = 2;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder getCurrentLocaleOrBuilder() {
      return getCurrentLocale();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < supportedLocales_.size(); i++) {
        output.writeMessage(1, supportedLocales_.get(i));
      }
      if (currentLocale_ != null) {
        output.writeMessage(2, getCurrentLocale());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < supportedLocales_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, supportedLocales_.get(i));
      }
      if (currentLocale_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCurrentLocale());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.System.Locales)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.System.Locales other = (com.amazon.alexa.accessory.protocol.System.Locales) obj;

      if (!getSupportedLocalesList()
          .equals(other.getSupportedLocalesList())) return false;
      if (hasCurrentLocale() != other.hasCurrentLocale()) return false;
      if (hasCurrentLocale()) {
        if (!getCurrentLocale()
            .equals(other.getCurrentLocale())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getSupportedLocalesCount() > 0) {
        hash = (37 * hash) + SUPPORTED_LOCALES_FIELD_NUMBER;
        hash = (53 * hash) + getSupportedLocalesList().hashCode();
      }
      if (hasCurrentLocale()) {
        hash = (37 * hash) + CURRENT_LOCALE_FIELD_NUMBER;
        hash = (53 * hash) + getCurrentLocale().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.System.Locales parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locales parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locales parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locales parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locales parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locales parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locales parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locales parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locales parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locales parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locales parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.Locales parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.System.Locales prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Locales}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Locales)
        com.amazon.alexa.accessory.protocol.System.LocalesOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_Locales_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_Locales_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.System.Locales.class, com.amazon.alexa.accessory.protocol.System.Locales.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.System.Locales.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (supportedLocalesBuilder_ == null) {
          supportedLocales_ = java.util.Collections.emptyList();
        } else {
          supportedLocales_ = null;
          supportedLocalesBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        if (currentLocaleBuilder_ == null) {
          currentLocale_ = null;
        } else {
          currentLocale_ = null;
          currentLocaleBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_Locales_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.Locales getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.System.Locales.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.Locales build() {
        com.amazon.alexa.accessory.protocol.System.Locales result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.Locales buildPartial() {
        com.amazon.alexa.accessory.protocol.System.Locales result = new com.amazon.alexa.accessory.protocol.System.Locales(this);
        int from_bitField0_ = bitField0_;
        if (supportedLocalesBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            supportedLocales_ = java.util.Collections.unmodifiableList(supportedLocales_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.supportedLocales_ = supportedLocales_;
        } else {
          result.supportedLocales_ = supportedLocalesBuilder_.build();
        }
        if (currentLocaleBuilder_ == null) {
          result.currentLocale_ = currentLocale_;
        } else {
          result.currentLocale_ = currentLocaleBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.System.Locales) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.System.Locales)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.System.Locales other) {
        if (other == com.amazon.alexa.accessory.protocol.System.Locales.getDefaultInstance()) return this;
        if (supportedLocalesBuilder_ == null) {
          if (!other.supportedLocales_.isEmpty()) {
            if (supportedLocales_.isEmpty()) {
              supportedLocales_ = other.supportedLocales_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureSupportedLocalesIsMutable();
              supportedLocales_.addAll(other.supportedLocales_);
            }
            onChanged();
          }
        } else {
          if (!other.supportedLocales_.isEmpty()) {
            if (supportedLocalesBuilder_.isEmpty()) {
              supportedLocalesBuilder_.dispose();
              supportedLocalesBuilder_ = null;
              supportedLocales_ = other.supportedLocales_;
              bitField0_ = (bitField0_ & ~0x00000001);
              supportedLocalesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getSupportedLocalesFieldBuilder() : null;
            } else {
              supportedLocalesBuilder_.addAllMessages(other.supportedLocales_);
            }
          }
        }
        if (other.hasCurrentLocale()) {
          mergeCurrentLocale(other.getCurrentLocale());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                com.amazon.alexa.accessory.protocol.System.Locale m =
                    input.readMessage(
                        com.amazon.alexa.accessory.protocol.System.Locale.parser(),
                        extensionRegistry);
                if (supportedLocalesBuilder_ == null) {
                  ensureSupportedLocalesIsMutable();
                  supportedLocales_.add(m);
                } else {
                  supportedLocalesBuilder_.addMessage(m);
                }
                break;
              } // case 10
              case 18: {
                input.readMessage(
                    getCurrentLocaleFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<com.amazon.alexa.accessory.protocol.System.Locale> supportedLocales_ =
        java.util.Collections.emptyList();
      private void ensureSupportedLocalesIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          supportedLocales_ = new java.util.ArrayList<com.amazon.alexa.accessory.protocol.System.Locale>(supportedLocales_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.Locale, com.amazon.alexa.accessory.protocol.System.Locale.Builder, com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder> supportedLocalesBuilder_;

      /**
       * <code>repeated .Locale supported_locales = 1;</code>
       */
      public java.util.List<com.amazon.alexa.accessory.protocol.System.Locale> getSupportedLocalesList() {
        if (supportedLocalesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(supportedLocales_);
        } else {
          return supportedLocalesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .Locale supported_locales = 1;</code>
       */
      public int getSupportedLocalesCount() {
        if (supportedLocalesBuilder_ == null) {
          return supportedLocales_.size();
        } else {
          return supportedLocalesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .Locale supported_locales = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.System.Locale getSupportedLocales(int index) {
        if (supportedLocalesBuilder_ == null) {
          return supportedLocales_.get(index);
        } else {
          return supportedLocalesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .Locale supported_locales = 1;</code>
       */
      public Builder setSupportedLocales(
          int index, com.amazon.alexa.accessory.protocol.System.Locale value) {
        if (supportedLocalesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSupportedLocalesIsMutable();
          supportedLocales_.set(index, value);
          onChanged();
        } else {
          supportedLocalesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Locale supported_locales = 1;</code>
       */
      public Builder setSupportedLocales(
          int index, com.amazon.alexa.accessory.protocol.System.Locale.Builder builderForValue) {
        if (supportedLocalesBuilder_ == null) {
          ensureSupportedLocalesIsMutable();
          supportedLocales_.set(index, builderForValue.build());
          onChanged();
        } else {
          supportedLocalesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Locale supported_locales = 1;</code>
       */
      public Builder addSupportedLocales(com.amazon.alexa.accessory.protocol.System.Locale value) {
        if (supportedLocalesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSupportedLocalesIsMutable();
          supportedLocales_.add(value);
          onChanged();
        } else {
          supportedLocalesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .Locale supported_locales = 1;</code>
       */
      public Builder addSupportedLocales(
          int index, com.amazon.alexa.accessory.protocol.System.Locale value) {
        if (supportedLocalesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSupportedLocalesIsMutable();
          supportedLocales_.add(index, value);
          onChanged();
        } else {
          supportedLocalesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Locale supported_locales = 1;</code>
       */
      public Builder addSupportedLocales(
          com.amazon.alexa.accessory.protocol.System.Locale.Builder builderForValue) {
        if (supportedLocalesBuilder_ == null) {
          ensureSupportedLocalesIsMutable();
          supportedLocales_.add(builderForValue.build());
          onChanged();
        } else {
          supportedLocalesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Locale supported_locales = 1;</code>
       */
      public Builder addSupportedLocales(
          int index, com.amazon.alexa.accessory.protocol.System.Locale.Builder builderForValue) {
        if (supportedLocalesBuilder_ == null) {
          ensureSupportedLocalesIsMutable();
          supportedLocales_.add(index, builderForValue.build());
          onChanged();
        } else {
          supportedLocalesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Locale supported_locales = 1;</code>
       */
      public Builder addAllSupportedLocales(
          java.lang.Iterable<? extends com.amazon.alexa.accessory.protocol.System.Locale> values) {
        if (supportedLocalesBuilder_ == null) {
          ensureSupportedLocalesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, supportedLocales_);
          onChanged();
        } else {
          supportedLocalesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .Locale supported_locales = 1;</code>
       */
      public Builder clearSupportedLocales() {
        if (supportedLocalesBuilder_ == null) {
          supportedLocales_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          supportedLocalesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .Locale supported_locales = 1;</code>
       */
      public Builder removeSupportedLocales(int index) {
        if (supportedLocalesBuilder_ == null) {
          ensureSupportedLocalesIsMutable();
          supportedLocales_.remove(index);
          onChanged();
        } else {
          supportedLocalesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .Locale supported_locales = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.System.Locale.Builder getSupportedLocalesBuilder(
          int index) {
        return getSupportedLocalesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .Locale supported_locales = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder getSupportedLocalesOrBuilder(
          int index) {
        if (supportedLocalesBuilder_ == null) {
          return supportedLocales_.get(index);  } else {
          return supportedLocalesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .Locale supported_locales = 1;</code>
       */
      public java.util.List<? extends com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder> 
           getSupportedLocalesOrBuilderList() {
        if (supportedLocalesBuilder_ != null) {
          return supportedLocalesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(supportedLocales_);
        }
      }
      /**
       * <code>repeated .Locale supported_locales = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.System.Locale.Builder addSupportedLocalesBuilder() {
        return getSupportedLocalesFieldBuilder().addBuilder(
            com.amazon.alexa.accessory.protocol.System.Locale.getDefaultInstance());
      }
      /**
       * <code>repeated .Locale supported_locales = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.System.Locale.Builder addSupportedLocalesBuilder(
          int index) {
        return getSupportedLocalesFieldBuilder().addBuilder(
            index, com.amazon.alexa.accessory.protocol.System.Locale.getDefaultInstance());
      }
      /**
       * <code>repeated .Locale supported_locales = 1;</code>
       */
      public java.util.List<com.amazon.alexa.accessory.protocol.System.Locale.Builder> 
           getSupportedLocalesBuilderList() {
        return getSupportedLocalesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.Locale, com.amazon.alexa.accessory.protocol.System.Locale.Builder, com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder> 
          getSupportedLocalesFieldBuilder() {
        if (supportedLocalesBuilder_ == null) {
          supportedLocalesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.System.Locale, com.amazon.alexa.accessory.protocol.System.Locale.Builder, com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder>(
                  supportedLocales_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          supportedLocales_ = null;
        }
        return supportedLocalesBuilder_;
      }

      private com.amazon.alexa.accessory.protocol.System.Locale currentLocale_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.Locale, com.amazon.alexa.accessory.protocol.System.Locale.Builder, com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder> currentLocaleBuilder_;
      /**
       * <code>.Locale current_locale = 2;</code>
       * @return Whether the currentLocale field is set.
       */
      public boolean hasCurrentLocale() {
        return currentLocaleBuilder_ != null || currentLocale_ != null;
      }
      /**
       * <code>.Locale current_locale = 2;</code>
       * @return The currentLocale.
       */
      public com.amazon.alexa.accessory.protocol.System.Locale getCurrentLocale() {
        if (currentLocaleBuilder_ == null) {
          return currentLocale_ == null ? com.amazon.alexa.accessory.protocol.System.Locale.getDefaultInstance() : currentLocale_;
        } else {
          return currentLocaleBuilder_.getMessage();
        }
      }
      /**
       * <code>.Locale current_locale = 2;</code>
       */
      public Builder setCurrentLocale(com.amazon.alexa.accessory.protocol.System.Locale value) {
        if (currentLocaleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          currentLocale_ = value;
          onChanged();
        } else {
          currentLocaleBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Locale current_locale = 2;</code>
       */
      public Builder setCurrentLocale(
          com.amazon.alexa.accessory.protocol.System.Locale.Builder builderForValue) {
        if (currentLocaleBuilder_ == null) {
          currentLocale_ = builderForValue.build();
          onChanged();
        } else {
          currentLocaleBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Locale current_locale = 2;</code>
       */
      public Builder mergeCurrentLocale(com.amazon.alexa.accessory.protocol.System.Locale value) {
        if (currentLocaleBuilder_ == null) {
          if (currentLocale_ != null) {
            currentLocale_ =
              com.amazon.alexa.accessory.protocol.System.Locale.newBuilder(currentLocale_).mergeFrom(value).buildPartial();
          } else {
            currentLocale_ = value;
          }
          onChanged();
        } else {
          currentLocaleBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Locale current_locale = 2;</code>
       */
      public Builder clearCurrentLocale() {
        if (currentLocaleBuilder_ == null) {
          currentLocale_ = null;
          onChanged();
        } else {
          currentLocale_ = null;
          currentLocaleBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Locale current_locale = 2;</code>
       */
      public com.amazon.alexa.accessory.protocol.System.Locale.Builder getCurrentLocaleBuilder() {
        
        onChanged();
        return getCurrentLocaleFieldBuilder().getBuilder();
      }
      /**
       * <code>.Locale current_locale = 2;</code>
       */
      public com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder getCurrentLocaleOrBuilder() {
        if (currentLocaleBuilder_ != null) {
          return currentLocaleBuilder_.getMessageOrBuilder();
        } else {
          return currentLocale_ == null ?
              com.amazon.alexa.accessory.protocol.System.Locale.getDefaultInstance() : currentLocale_;
        }
      }
      /**
       * <code>.Locale current_locale = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.Locale, com.amazon.alexa.accessory.protocol.System.Locale.Builder, com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder> 
          getCurrentLocaleFieldBuilder() {
        if (currentLocaleBuilder_ == null) {
          currentLocaleBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.System.Locale, com.amazon.alexa.accessory.protocol.System.Locale.Builder, com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder>(
                  getCurrentLocale(),
                  getParentForChildren(),
                  isClean());
          currentLocale_ = null;
        }
        return currentLocaleBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Locales)
    }

    // @@protoc_insertion_point(class_scope:Locales)
    private static final com.amazon.alexa.accessory.protocol.System.Locales DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.System.Locales();
    }

    public static com.amazon.alexa.accessory.protocol.System.Locales getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Locales>
        PARSER = new com.google.protobuf.AbstractParser<Locales>() {
      @java.lang.Override
      public Locales parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Locales> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Locales> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.Locales getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetLocalesOrBuilder extends
      // @@protoc_insertion_point(interface_extends:GetLocales)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code GetLocales}
   */
  public static final class GetLocales extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:GetLocales)
      GetLocalesOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetLocales.newBuilder() to construct.
    private GetLocales(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetLocales() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetLocales();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.System.internal_static_GetLocales_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.System.internal_static_GetLocales_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.System.GetLocales.class, com.amazon.alexa.accessory.protocol.System.GetLocales.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.System.GetLocales)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.System.GetLocales other = (com.amazon.alexa.accessory.protocol.System.GetLocales) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.System.GetLocales parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.GetLocales parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.GetLocales parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.GetLocales parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.GetLocales parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.GetLocales parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.GetLocales parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.GetLocales parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.GetLocales parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.GetLocales parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.GetLocales parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.GetLocales parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.System.GetLocales prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code GetLocales}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:GetLocales)
        com.amazon.alexa.accessory.protocol.System.GetLocalesOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_GetLocales_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_GetLocales_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.System.GetLocales.class, com.amazon.alexa.accessory.protocol.System.GetLocales.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.System.GetLocales.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_GetLocales_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.GetLocales getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.System.GetLocales.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.GetLocales build() {
        com.amazon.alexa.accessory.protocol.System.GetLocales result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.GetLocales buildPartial() {
        com.amazon.alexa.accessory.protocol.System.GetLocales result = new com.amazon.alexa.accessory.protocol.System.GetLocales(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.System.GetLocales) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.System.GetLocales)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.System.GetLocales other) {
        if (other == com.amazon.alexa.accessory.protocol.System.GetLocales.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:GetLocales)
    }

    // @@protoc_insertion_point(class_scope:GetLocales)
    private static final com.amazon.alexa.accessory.protocol.System.GetLocales DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.System.GetLocales();
    }

    public static com.amazon.alexa.accessory.protocol.System.GetLocales getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GetLocales>
        PARSER = new com.google.protobuf.AbstractParser<GetLocales>() {
      @java.lang.Override
      public GetLocales parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<GetLocales> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetLocales> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.GetLocales getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SetLocaleOrBuilder extends
      // @@protoc_insertion_point(interface_extends:SetLocale)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Locale locale = 1;</code>
     * @return Whether the locale field is set.
     */
    boolean hasLocale();
    /**
     * <code>.Locale locale = 1;</code>
     * @return The locale.
     */
    com.amazon.alexa.accessory.protocol.System.Locale getLocale();
    /**
     * <code>.Locale locale = 1;</code>
     */
    com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder getLocaleOrBuilder();
  }
  /**
   * Protobuf type {@code SetLocale}
   */
  public static final class SetLocale extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:SetLocale)
      SetLocaleOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SetLocale.newBuilder() to construct.
    private SetLocale(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SetLocale() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SetLocale();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.System.internal_static_SetLocale_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.System.internal_static_SetLocale_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.System.SetLocale.class, com.amazon.alexa.accessory.protocol.System.SetLocale.Builder.class);
    }

    public static final int LOCALE_FIELD_NUMBER = 1;
    private com.amazon.alexa.accessory.protocol.System.Locale locale_;
    /**
     * <code>.Locale locale = 1;</code>
     * @return Whether the locale field is set.
     */
    @java.lang.Override
    public boolean hasLocale() {
      return locale_ != null;
    }
    /**
     * <code>.Locale locale = 1;</code>
     * @return The locale.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.Locale getLocale() {
      return locale_ == null ? com.amazon.alexa.accessory.protocol.System.Locale.getDefaultInstance() : locale_;
    }
    /**
     * <code>.Locale locale = 1;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder getLocaleOrBuilder() {
      return getLocale();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (locale_ != null) {
        output.writeMessage(1, getLocale());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (locale_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getLocale());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.System.SetLocale)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.System.SetLocale other = (com.amazon.alexa.accessory.protocol.System.SetLocale) obj;

      if (hasLocale() != other.hasLocale()) return false;
      if (hasLocale()) {
        if (!getLocale()
            .equals(other.getLocale())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasLocale()) {
        hash = (37 * hash) + LOCALE_FIELD_NUMBER;
        hash = (53 * hash) + getLocale().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.System.SetLocale parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.SetLocale parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.SetLocale parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.SetLocale parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.SetLocale parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.SetLocale parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.SetLocale parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.SetLocale parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.SetLocale parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.SetLocale parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.SetLocale parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.SetLocale parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.System.SetLocale prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code SetLocale}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:SetLocale)
        com.amazon.alexa.accessory.protocol.System.SetLocaleOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_SetLocale_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_SetLocale_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.System.SetLocale.class, com.amazon.alexa.accessory.protocol.System.SetLocale.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.System.SetLocale.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (localeBuilder_ == null) {
          locale_ = null;
        } else {
          locale_ = null;
          localeBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_SetLocale_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.SetLocale getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.System.SetLocale.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.SetLocale build() {
        com.amazon.alexa.accessory.protocol.System.SetLocale result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.SetLocale buildPartial() {
        com.amazon.alexa.accessory.protocol.System.SetLocale result = new com.amazon.alexa.accessory.protocol.System.SetLocale(this);
        if (localeBuilder_ == null) {
          result.locale_ = locale_;
        } else {
          result.locale_ = localeBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.System.SetLocale) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.System.SetLocale)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.System.SetLocale other) {
        if (other == com.amazon.alexa.accessory.protocol.System.SetLocale.getDefaultInstance()) return this;
        if (other.hasLocale()) {
          mergeLocale(other.getLocale());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getLocaleFieldBuilder().getBuilder(),
                    extensionRegistry);

                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private com.amazon.alexa.accessory.protocol.System.Locale locale_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.Locale, com.amazon.alexa.accessory.protocol.System.Locale.Builder, com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder> localeBuilder_;
      /**
       * <code>.Locale locale = 1;</code>
       * @return Whether the locale field is set.
       */
      public boolean hasLocale() {
        return localeBuilder_ != null || locale_ != null;
      }
      /**
       * <code>.Locale locale = 1;</code>
       * @return The locale.
       */
      public com.amazon.alexa.accessory.protocol.System.Locale getLocale() {
        if (localeBuilder_ == null) {
          return locale_ == null ? com.amazon.alexa.accessory.protocol.System.Locale.getDefaultInstance() : locale_;
        } else {
          return localeBuilder_.getMessage();
        }
      }
      /**
       * <code>.Locale locale = 1;</code>
       */
      public Builder setLocale(com.amazon.alexa.accessory.protocol.System.Locale value) {
        if (localeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          locale_ = value;
          onChanged();
        } else {
          localeBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Locale locale = 1;</code>
       */
      public Builder setLocale(
          com.amazon.alexa.accessory.protocol.System.Locale.Builder builderForValue) {
        if (localeBuilder_ == null) {
          locale_ = builderForValue.build();
          onChanged();
        } else {
          localeBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Locale locale = 1;</code>
       */
      public Builder mergeLocale(com.amazon.alexa.accessory.protocol.System.Locale value) {
        if (localeBuilder_ == null) {
          if (locale_ != null) {
            locale_ =
              com.amazon.alexa.accessory.protocol.System.Locale.newBuilder(locale_).mergeFrom(value).buildPartial();
          } else {
            locale_ = value;
          }
          onChanged();
        } else {
          localeBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Locale locale = 1;</code>
       */
      public Builder clearLocale() {
        if (localeBuilder_ == null) {
          locale_ = null;
          onChanged();
        } else {
          locale_ = null;
          localeBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Locale locale = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.System.Locale.Builder getLocaleBuilder() {
        
        onChanged();
        return getLocaleFieldBuilder().getBuilder();
      }
      /**
       * <code>.Locale locale = 1;</code>
       */
      public com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder getLocaleOrBuilder() {
        if (localeBuilder_ != null) {
          return localeBuilder_.getMessageOrBuilder();
        } else {
          return locale_ == null ?
              com.amazon.alexa.accessory.protocol.System.Locale.getDefaultInstance() : locale_;
        }
      }
      /**
       * <code>.Locale locale = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.Locale, com.amazon.alexa.accessory.protocol.System.Locale.Builder, com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder> 
          getLocaleFieldBuilder() {
        if (localeBuilder_ == null) {
          localeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.System.Locale, com.amazon.alexa.accessory.protocol.System.Locale.Builder, com.amazon.alexa.accessory.protocol.System.LocaleOrBuilder>(
                  getLocale(),
                  getParentForChildren(),
                  isClean());
          locale_ = null;
        }
        return localeBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:SetLocale)
    }

    // @@protoc_insertion_point(class_scope:SetLocale)
    private static final com.amazon.alexa.accessory.protocol.System.SetLocale DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.System.SetLocale();
    }

    public static com.amazon.alexa.accessory.protocol.System.SetLocale getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SetLocale>
        PARSER = new com.google.protobuf.AbstractParser<SetLocale>() {
      @java.lang.Override
      public SetLocale parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SetLocale> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SetLocale> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.SetLocale getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface LaunchAppOrBuilder extends
      // @@protoc_insertion_point(interface_extends:LaunchApp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string app_id = 1;</code>
     * @return The appId.
     */
    java.lang.String getAppId();
    /**
     * <code>string app_id = 1;</code>
     * @return The bytes for appId.
     */
    com.google.protobuf.ByteString
        getAppIdBytes();
  }
  /**
   * Protobuf type {@code LaunchApp}
   */
  public static final class LaunchApp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:LaunchApp)
      LaunchAppOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use LaunchApp.newBuilder() to construct.
    private LaunchApp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private LaunchApp() {
      appId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new LaunchApp();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.System.internal_static_LaunchApp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.System.internal_static_LaunchApp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.System.LaunchApp.class, com.amazon.alexa.accessory.protocol.System.LaunchApp.Builder.class);
    }

    public static final int APP_ID_FIELD_NUMBER = 1;
    private volatile java.lang.Object appId_;
    /**
     * <code>string app_id = 1;</code>
     * @return The appId.
     */
    @java.lang.Override
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appId_ = s;
        return s;
      }
    }
    /**
     * <code>string app_id = 1;</code>
     * @return The bytes for appId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(appId_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, appId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(appId_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, appId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.System.LaunchApp)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.System.LaunchApp other = (com.amazon.alexa.accessory.protocol.System.LaunchApp) obj;

      if (!getAppId()
          .equals(other.getAppId())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + APP_ID_FIELD_NUMBER;
      hash = (53 * hash) + getAppId().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.System.LaunchApp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.LaunchApp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.LaunchApp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.LaunchApp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.LaunchApp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.System.LaunchApp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.LaunchApp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.LaunchApp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.LaunchApp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.LaunchApp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.System.LaunchApp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.System.LaunchApp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.System.LaunchApp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code LaunchApp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:LaunchApp)
        com.amazon.alexa.accessory.protocol.System.LaunchAppOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_LaunchApp_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_LaunchApp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.System.LaunchApp.class, com.amazon.alexa.accessory.protocol.System.LaunchApp.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.System.LaunchApp.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        appId_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.System.internal_static_LaunchApp_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.LaunchApp getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.System.LaunchApp.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.LaunchApp build() {
        com.amazon.alexa.accessory.protocol.System.LaunchApp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.LaunchApp buildPartial() {
        com.amazon.alexa.accessory.protocol.System.LaunchApp result = new com.amazon.alexa.accessory.protocol.System.LaunchApp(this);
        result.appId_ = appId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.System.LaunchApp) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.System.LaunchApp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.System.LaunchApp other) {
        if (other == com.amazon.alexa.accessory.protocol.System.LaunchApp.getDefaultInstance()) return this;
        if (!other.getAppId().isEmpty()) {
          appId_ = other.appId_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                appId_ = input.readStringRequireUtf8();

                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      private java.lang.Object appId_ = "";
      /**
       * <code>string app_id = 1;</code>
       * @return The appId.
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string app_id = 1;</code>
       * @return The bytes for appId.
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string app_id = 1;</code>
       * @param value The appId to set.
       * @return This builder for chaining.
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string app_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAppId() {
        
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <code>string app_id = 1;</code>
       * @param value The bytes for appId to set.
       * @return This builder for chaining.
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        appId_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:LaunchApp)
    }

    // @@protoc_insertion_point(class_scope:LaunchApp)
    private static final com.amazon.alexa.accessory.protocol.System.LaunchApp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.System.LaunchApp();
    }

    public static com.amazon.alexa.accessory.protocol.System.LaunchApp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<LaunchApp>
        PARSER = new com.google.protobuf.AbstractParser<LaunchApp>() {
      @java.lang.Override
      public LaunchApp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<LaunchApp> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<LaunchApp> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.LaunchApp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ResetConnection_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ResetConnection_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_SynchronizeSettings_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_SynchronizeSettings_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_KeepAlive_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_KeepAlive_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_RemoveDevice_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_RemoveDevice_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Locale_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Locale_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Locales_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Locales_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_GetLocales_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_GetLocales_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_SetLocale_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_SetLocale_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_LaunchApp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_LaunchApp_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014system.proto\"\265\001\n\017ResetConnection\022\017\n\007ti" +
      "meout\030\001 \001(\r\022\030\n\020force_disconnect\030\002 \001(\010\0222\n" +
      "\014reset_reason\030\003 \001(\0162\034.ResetConnection.Re" +
      "setReason\"C\n\013ResetReason\022\013\n\007UNKNOWN\020\000\022\'\n" +
      "#AAP_REFUSED_MAX_CONNECTIONS_REACHED\020\001\"A" +
      "\n\023SynchronizeSettings\022\024\n\014timestamp_hi\030\001 " +
      "\001(\r\022\024\n\014timestamp_lo\030\002 \001(\r\"\013\n\tKeepAlive\"\016" +
      "\n\014RemoveDevice\"\026\n\006Locale\022\014\n\004name\030\001 \001(\t\"N" +
      "\n\007Locales\022\"\n\021supported_locales\030\001 \003(\0132\007.L" +
      "ocale\022\037\n\016current_locale\030\002 \001(\0132\007.Locale\"\014" +
      "\n\nGetLocales\"$\n\tSetLocale\022\027\n\006locale\030\001 \001(" +
      "\0132\007.Locale\"\033\n\tLaunchApp\022\016\n\006app_id\030\001 \001(\tB" +
      "0\n#com.amazon.alexa.accessory.protocolH\003" +
      "\240\001\001\242\002\003AACb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_ResetConnection_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_ResetConnection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ResetConnection_descriptor,
        new java.lang.String[] { "Timeout", "ForceDisconnect", "ResetReason", });
    internal_static_SynchronizeSettings_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_SynchronizeSettings_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_SynchronizeSettings_descriptor,
        new java.lang.String[] { "TimestampHi", "TimestampLo", });
    internal_static_KeepAlive_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_KeepAlive_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_KeepAlive_descriptor,
        new java.lang.String[] { });
    internal_static_RemoveDevice_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_RemoveDevice_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_RemoveDevice_descriptor,
        new java.lang.String[] { });
    internal_static_Locale_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Locale_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Locale_descriptor,
        new java.lang.String[] { "Name", });
    internal_static_Locales_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Locales_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Locales_descriptor,
        new java.lang.String[] { "SupportedLocales", "CurrentLocale", });
    internal_static_GetLocales_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_GetLocales_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_GetLocales_descriptor,
        new java.lang.String[] { });
    internal_static_SetLocale_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_SetLocale_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_SetLocale_descriptor,
        new java.lang.String[] { "Locale", });
    internal_static_LaunchApp_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_LaunchApp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_LaunchApp_descriptor,
        new java.lang.String[] { "AppId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
