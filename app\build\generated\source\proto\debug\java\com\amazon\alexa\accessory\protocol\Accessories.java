// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: accessories.proto

package com.amazon.alexa.accessory.protocol;

public final class Accessories {
  private Accessories() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code Command}
   */
  public enum Command
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>NONE = 0;</code>
     */
    NONE(0),
    /**
     * <code>RESET_CONNECTION = 51;</code>
     */
    RESET_CONNECTION(51),
    /**
     * <code>SYNCHRONIZE_SETTINGS = 50;</code>
     */
    SYNCHRONIZE_SETTINGS(50),
    /**
     * <code>KEEP_ALIVE = 55;</code>
     */
    KEEP_ALIVE(55),
    /**
     * <code>REMOVE_DEVICE = 56;</code>
     */
    REMOVE_DEVICE(56),
    /**
     * <code>GET_LOCALES = 57;</code>
     */
    GET_LOCALES(57),
    /**
     * <code>SET_LOCALE = 58;</code>
     */
    SET_LOCALE(58),
    /**
     * <code>LAUNCH_APP = 59;</code>
     */
    LAUNCH_APP(59),
    /**
     * <code>UPGRADE_TRANSPORT = 30;</code>
     */
    UPGRADE_TRANSPORT(30),
    /**
     * <code>SWITCH_TRANSPORT = 31;</code>
     */
    SWITCH_TRANSPORT(31),
    /**
     * <code>START_SPEECH = 11;</code>
     */
    START_SPEECH(11),
    /**
     * <code>PROVIDE_SPEECH = 10;</code>
     */
    PROVIDE_SPEECH(10),
    /**
     * <code>STOP_SPEECH = 12;</code>
     */
    STOP_SPEECH(12),
    /**
     * <code>ENDPOINT_SPEECH = 13;</code>
     */
    ENDPOINT_SPEECH(13),
    /**
     * <code>NOTIFY_SPEECH_STATE = 14;</code>
     */
    NOTIFY_SPEECH_STATE(14),
    /**
     * <code>FORWARD_AT_COMMAND = 40;</code>
     */
    FORWARD_AT_COMMAND(40),
    /**
     * <code>INCOMING_CALL = 41;</code>
     */
    INCOMING_CALL(41),
    /**
     * <code>GET_CENTRAL_INFORMATION = 103;</code>
     */
    GET_CENTRAL_INFORMATION(103),
    /**
     * <code>GET_DEVICE_INFORMATION = 20;</code>
     */
    GET_DEVICE_INFORMATION(20),
    /**
     * <code>GET_DEVICE_CONFIGURATION = 21;</code>
     */
    GET_DEVICE_CONFIGURATION(21),
    /**
     * <code>OVERRIDE_ASSISTANT = 22;</code>
     */
    OVERRIDE_ASSISTANT(22),
    /**
     * <code>START_SETUP = 23;</code>
     */
    START_SETUP(23),
    /**
     * <code>COMPLETE_SETUP = 24;</code>
     */
    COMPLETE_SETUP(24),
    /**
     * <code>NOTIFY_DEVICE_CONFIGURATION = 25;</code>
     */
    NOTIFY_DEVICE_CONFIGURATION(25),
    /**
     * <code>UPDATE_DEVICE_INFORMATION = 26;</code>
     */
    UPDATE_DEVICE_INFORMATION(26),
    /**
     * <code>NOTIFY_DEVICE_INFORMATION = 27;</code>
     */
    NOTIFY_DEVICE_INFORMATION(27),
    /**
     * <code>GET_DEVICE_FEATURES = 28;</code>
     */
    GET_DEVICE_FEATURES(28),
    /**
     * <code>ISSUE_MEDIA_CONTROL = 60;</code>
     */
    ISSUE_MEDIA_CONTROL(60),
    /**
     * <code>GET_STATE = 100;</code>
     */
    GET_STATE(100),
    /**
     * <code>SET_STATE = 101;</code>
     */
    SET_STATE(101),
    /**
     * <code>SYNCHRONIZE_STATE = 102;</code>
     */
    SYNCHRONIZE_STATE(102),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>NONE = 0;</code>
     */
    public static final int NONE_VALUE = 0;
    /**
     * <code>RESET_CONNECTION = 51;</code>
     */
    public static final int RESET_CONNECTION_VALUE = 51;
    /**
     * <code>SYNCHRONIZE_SETTINGS = 50;</code>
     */
    public static final int SYNCHRONIZE_SETTINGS_VALUE = 50;
    /**
     * <code>KEEP_ALIVE = 55;</code>
     */
    public static final int KEEP_ALIVE_VALUE = 55;
    /**
     * <code>REMOVE_DEVICE = 56;</code>
     */
    public static final int REMOVE_DEVICE_VALUE = 56;
    /**
     * <code>GET_LOCALES = 57;</code>
     */
    public static final int GET_LOCALES_VALUE = 57;
    /**
     * <code>SET_LOCALE = 58;</code>
     */
    public static final int SET_LOCALE_VALUE = 58;
    /**
     * <code>LAUNCH_APP = 59;</code>
     */
    public static final int LAUNCH_APP_VALUE = 59;
    /**
     * <code>UPGRADE_TRANSPORT = 30;</code>
     */
    public static final int UPGRADE_TRANSPORT_VALUE = 30;
    /**
     * <code>SWITCH_TRANSPORT = 31;</code>
     */
    public static final int SWITCH_TRANSPORT_VALUE = 31;
    /**
     * <code>START_SPEECH = 11;</code>
     */
    public static final int START_SPEECH_VALUE = 11;
    /**
     * <code>PROVIDE_SPEECH = 10;</code>
     */
    public static final int PROVIDE_SPEECH_VALUE = 10;
    /**
     * <code>STOP_SPEECH = 12;</code>
     */
    public static final int STOP_SPEECH_VALUE = 12;
    /**
     * <code>ENDPOINT_SPEECH = 13;</code>
     */
    public static final int ENDPOINT_SPEECH_VALUE = 13;
    /**
     * <code>NOTIFY_SPEECH_STATE = 14;</code>
     */
    public static final int NOTIFY_SPEECH_STATE_VALUE = 14;
    /**
     * <code>FORWARD_AT_COMMAND = 40;</code>
     */
    public static final int FORWARD_AT_COMMAND_VALUE = 40;
    /**
     * <code>INCOMING_CALL = 41;</code>
     */
    public static final int INCOMING_CALL_VALUE = 41;
    /**
     * <code>GET_CENTRAL_INFORMATION = 103;</code>
     */
    public static final int GET_CENTRAL_INFORMATION_VALUE = 103;
    /**
     * <code>GET_DEVICE_INFORMATION = 20;</code>
     */
    public static final int GET_DEVICE_INFORMATION_VALUE = 20;
    /**
     * <code>GET_DEVICE_CONFIGURATION = 21;</code>
     */
    public static final int GET_DEVICE_CONFIGURATION_VALUE = 21;
    /**
     * <code>OVERRIDE_ASSISTANT = 22;</code>
     */
    public static final int OVERRIDE_ASSISTANT_VALUE = 22;
    /**
     * <code>START_SETUP = 23;</code>
     */
    public static final int START_SETUP_VALUE = 23;
    /**
     * <code>COMPLETE_SETUP = 24;</code>
     */
    public static final int COMPLETE_SETUP_VALUE = 24;
    /**
     * <code>NOTIFY_DEVICE_CONFIGURATION = 25;</code>
     */
    public static final int NOTIFY_DEVICE_CONFIGURATION_VALUE = 25;
    /**
     * <code>UPDATE_DEVICE_INFORMATION = 26;</code>
     */
    public static final int UPDATE_DEVICE_INFORMATION_VALUE = 26;
    /**
     * <code>NOTIFY_DEVICE_INFORMATION = 27;</code>
     */
    public static final int NOTIFY_DEVICE_INFORMATION_VALUE = 27;
    /**
     * <code>GET_DEVICE_FEATURES = 28;</code>
     */
    public static final int GET_DEVICE_FEATURES_VALUE = 28;
    /**
     * <code>ISSUE_MEDIA_CONTROL = 60;</code>
     */
    public static final int ISSUE_MEDIA_CONTROL_VALUE = 60;
    /**
     * <code>GET_STATE = 100;</code>
     */
    public static final int GET_STATE_VALUE = 100;
    /**
     * <code>SET_STATE = 101;</code>
     */
    public static final int SET_STATE_VALUE = 101;
    /**
     * <code>SYNCHRONIZE_STATE = 102;</code>
     */
    public static final int SYNCHRONIZE_STATE_VALUE = 102;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static Command valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static Command forNumber(int value) {
      switch (value) {
        case 0: return NONE;
        case 51: return RESET_CONNECTION;
        case 50: return SYNCHRONIZE_SETTINGS;
        case 55: return KEEP_ALIVE;
        case 56: return REMOVE_DEVICE;
        case 57: return GET_LOCALES;
        case 58: return SET_LOCALE;
        case 59: return LAUNCH_APP;
        case 30: return UPGRADE_TRANSPORT;
        case 31: return SWITCH_TRANSPORT;
        case 11: return START_SPEECH;
        case 10: return PROVIDE_SPEECH;
        case 12: return STOP_SPEECH;
        case 13: return ENDPOINT_SPEECH;
        case 14: return NOTIFY_SPEECH_STATE;
        case 40: return FORWARD_AT_COMMAND;
        case 41: return INCOMING_CALL;
        case 103: return GET_CENTRAL_INFORMATION;
        case 20: return GET_DEVICE_INFORMATION;
        case 21: return GET_DEVICE_CONFIGURATION;
        case 22: return OVERRIDE_ASSISTANT;
        case 23: return START_SETUP;
        case 24: return COMPLETE_SETUP;
        case 25: return NOTIFY_DEVICE_CONFIGURATION;
        case 26: return UPDATE_DEVICE_INFORMATION;
        case 27: return NOTIFY_DEVICE_INFORMATION;
        case 28: return GET_DEVICE_FEATURES;
        case 60: return ISSUE_MEDIA_CONTROL;
        case 100: return GET_STATE;
        case 101: return SET_STATE;
        case 102: return SYNCHRONIZE_STATE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<Command>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        Command> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<Command>() {
            public Command findValueByNumber(int number) {
              return Command.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Accessories.getDescriptor().getEnumTypes().get(0);
    }

    private static final Command[] VALUES = values();

    public static Command valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private Command(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:Command)
  }

  public interface ResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Response)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.ErrorCode error_code = 1;</code>
     * @return The enum numeric value on the wire for errorCode.
     */
    int getErrorCodeValue();
    /**
     * <code>.ErrorCode error_code = 1;</code>
     * @return The errorCode.
     */
    com.amazon.alexa.accessory.protocol.Common.ErrorCode getErrorCode();

    /**
     * <code>.Locales locales = 21;</code>
     * @return Whether the locales field is set.
     */
    boolean hasLocales();
    /**
     * <code>.Locales locales = 21;</code>
     * @return The locales.
     */
    com.amazon.alexa.accessory.protocol.System.Locales getLocales();
    /**
     * <code>.Locales locales = 21;</code>
     */
    com.amazon.alexa.accessory.protocol.System.LocalesOrBuilder getLocalesOrBuilder();

    /**
     * <code>.ConnectionDetails connection_details = 8;</code>
     * @return Whether the connectionDetails field is set.
     */
    boolean hasConnectionDetails();
    /**
     * <code>.ConnectionDetails connection_details = 8;</code>
     * @return The connectionDetails.
     */
    com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails getConnectionDetails();
    /**
     * <code>.ConnectionDetails connection_details = 8;</code>
     */
    com.amazon.alexa.accessory.protocol.Transport.ConnectionDetailsOrBuilder getConnectionDetailsOrBuilder();

    /**
     * <code>.Dialog dialog = 14;</code>
     * @return Whether the dialog field is set.
     */
    boolean hasDialog();
    /**
     * <code>.Dialog dialog = 14;</code>
     * @return The dialog.
     */
    com.amazon.alexa.accessory.protocol.Speech.Dialog getDialog();
    /**
     * <code>.Dialog dialog = 14;</code>
     */
    com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder getDialogOrBuilder();

    /**
     * <code>.SpeechProvider speech_provider = 15;</code>
     * @return Whether the speechProvider field is set.
     */
    boolean hasSpeechProvider();
    /**
     * <code>.SpeechProvider speech_provider = 15;</code>
     * @return The speechProvider.
     */
    com.amazon.alexa.accessory.protocol.Speech.SpeechProvider getSpeechProvider();
    /**
     * <code>.SpeechProvider speech_provider = 15;</code>
     */
    com.amazon.alexa.accessory.protocol.Speech.SpeechProviderOrBuilder getSpeechProviderOrBuilder();

    /**
     * <code>.CentralInformation central_information = 13;</code>
     * @return Whether the centralInformation field is set.
     */
    boolean hasCentralInformation();
    /**
     * <code>.CentralInformation central_information = 13;</code>
     * @return The centralInformation.
     */
    com.amazon.alexa.accessory.protocol.Central.CentralInformation getCentralInformation();
    /**
     * <code>.CentralInformation central_information = 13;</code>
     */
    com.amazon.alexa.accessory.protocol.Central.CentralInformationOrBuilder getCentralInformationOrBuilder();

    /**
     * <code>.DeviceInformation device_information = 3;</code>
     * @return Whether the deviceInformation field is set.
     */
    boolean hasDeviceInformation();
    /**
     * <code>.DeviceInformation device_information = 3;</code>
     * @return The deviceInformation.
     */
    com.amazon.alexa.accessory.protocol.Device.DeviceInformation getDeviceInformation();
    /**
     * <code>.DeviceInformation device_information = 3;</code>
     */
    com.amazon.alexa.accessory.protocol.Device.DeviceInformationOrBuilder getDeviceInformationOrBuilder();

    /**
     * <code>.DeviceConfiguration device_configuration = 10;</code>
     * @return Whether the deviceConfiguration field is set.
     */
    boolean hasDeviceConfiguration();
    /**
     * <code>.DeviceConfiguration device_configuration = 10;</code>
     * @return The deviceConfiguration.
     */
    com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration getDeviceConfiguration();
    /**
     * <code>.DeviceConfiguration device_configuration = 10;</code>
     */
    com.amazon.alexa.accessory.protocol.Device.DeviceConfigurationOrBuilder getDeviceConfigurationOrBuilder();

    /**
     * <code>.DeviceFeatures device_features = 28;</code>
     * @return Whether the deviceFeatures field is set.
     */
    boolean hasDeviceFeatures();
    /**
     * <code>.DeviceFeatures device_features = 28;</code>
     * @return The deviceFeatures.
     */
    com.amazon.alexa.accessory.protocol.Device.DeviceFeatures getDeviceFeatures();
    /**
     * <code>.DeviceFeatures device_features = 28;</code>
     */
    com.amazon.alexa.accessory.protocol.Device.DeviceFeaturesOrBuilder getDeviceFeaturesOrBuilder();

    /**
     * <code>.State state = 7;</code>
     * @return Whether the state field is set.
     */
    boolean hasState();
    /**
     * <code>.State state = 7;</code>
     * @return The state.
     */
    com.amazon.alexa.accessory.protocol.StateOuterClass.State getState();
    /**
     * <code>.State state = 7;</code>
     */
    com.amazon.alexa.accessory.protocol.StateOuterClass.StateOrBuilder getStateOrBuilder();

    public com.amazon.alexa.accessory.protocol.Accessories.Response.PayloadCase getPayloadCase();
  }
  /**
   * Protobuf type {@code Response}
   */
  public static final class Response extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Response)
      ResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Response.newBuilder() to construct.
    private Response(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Response() {
      errorCode_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Response();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Accessories.internal_static_Response_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Accessories.internal_static_Response_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Accessories.Response.class, com.amazon.alexa.accessory.protocol.Accessories.Response.Builder.class);
    }

    private int payloadCase_ = 0;
    private java.lang.Object payload_;
    public enum PayloadCase
        implements com.google.protobuf.Internal.EnumLite,
            com.google.protobuf.AbstractMessage.InternalOneOfEnum {
      LOCALES(21),
      CONNECTION_DETAILS(8),
      DIALOG(14),
      SPEECH_PROVIDER(15),
      CENTRAL_INFORMATION(13),
      DEVICE_INFORMATION(3),
      DEVICE_CONFIGURATION(10),
      DEVICE_FEATURES(28),
      STATE(7),
      PAYLOAD_NOT_SET(0);
      private final int value;
      private PayloadCase(int value) {
        this.value = value;
      }
      /**
       * @param value The number of the enum to look for.
       * @return The enum associated with the given number.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static PayloadCase valueOf(int value) {
        return forNumber(value);
      }

      public static PayloadCase forNumber(int value) {
        switch (value) {
          case 21: return LOCALES;
          case 8: return CONNECTION_DETAILS;
          case 14: return DIALOG;
          case 15: return SPEECH_PROVIDER;
          case 13: return CENTRAL_INFORMATION;
          case 3: return DEVICE_INFORMATION;
          case 10: return DEVICE_CONFIGURATION;
          case 28: return DEVICE_FEATURES;
          case 7: return STATE;
          case 0: return PAYLOAD_NOT_SET;
          default: return null;
        }
      }
      public int getNumber() {
        return this.value;
      }
    };

    public PayloadCase
    getPayloadCase() {
      return PayloadCase.forNumber(
          payloadCase_);
    }

    public static final int ERROR_CODE_FIELD_NUMBER = 1;
    private int errorCode_;
    /**
     * <code>.ErrorCode error_code = 1;</code>
     * @return The enum numeric value on the wire for errorCode.
     */
    @java.lang.Override public int getErrorCodeValue() {
      return errorCode_;
    }
    /**
     * <code>.ErrorCode error_code = 1;</code>
     * @return The errorCode.
     */
    @java.lang.Override public com.amazon.alexa.accessory.protocol.Common.ErrorCode getErrorCode() {
      @SuppressWarnings("deprecation")
      com.amazon.alexa.accessory.protocol.Common.ErrorCode result = com.amazon.alexa.accessory.protocol.Common.ErrorCode.valueOf(errorCode_);
      return result == null ? com.amazon.alexa.accessory.protocol.Common.ErrorCode.UNRECOGNIZED : result;
    }

    public static final int LOCALES_FIELD_NUMBER = 21;
    /**
     * <code>.Locales locales = 21;</code>
     * @return Whether the locales field is set.
     */
    @java.lang.Override
    public boolean hasLocales() {
      return payloadCase_ == 21;
    }
    /**
     * <code>.Locales locales = 21;</code>
     * @return The locales.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.Locales getLocales() {
      if (payloadCase_ == 21) {
         return (com.amazon.alexa.accessory.protocol.System.Locales) payload_;
      }
      return com.amazon.alexa.accessory.protocol.System.Locales.getDefaultInstance();
    }
    /**
     * <code>.Locales locales = 21;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.LocalesOrBuilder getLocalesOrBuilder() {
      if (payloadCase_ == 21) {
         return (com.amazon.alexa.accessory.protocol.System.Locales) payload_;
      }
      return com.amazon.alexa.accessory.protocol.System.Locales.getDefaultInstance();
    }

    public static final int CONNECTION_DETAILS_FIELD_NUMBER = 8;
    /**
     * <code>.ConnectionDetails connection_details = 8;</code>
     * @return Whether the connectionDetails field is set.
     */
    @java.lang.Override
    public boolean hasConnectionDetails() {
      return payloadCase_ == 8;
    }
    /**
     * <code>.ConnectionDetails connection_details = 8;</code>
     * @return The connectionDetails.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails getConnectionDetails() {
      if (payloadCase_ == 8) {
         return (com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails.getDefaultInstance();
    }
    /**
     * <code>.ConnectionDetails connection_details = 8;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Transport.ConnectionDetailsOrBuilder getConnectionDetailsOrBuilder() {
      if (payloadCase_ == 8) {
         return (com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails.getDefaultInstance();
    }

    public static final int DIALOG_FIELD_NUMBER = 14;
    /**
     * <code>.Dialog dialog = 14;</code>
     * @return Whether the dialog field is set.
     */
    @java.lang.Override
    public boolean hasDialog() {
      return payloadCase_ == 14;
    }
    /**
     * <code>.Dialog dialog = 14;</code>
     * @return The dialog.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.Dialog getDialog() {
      if (payloadCase_ == 14) {
         return (com.amazon.alexa.accessory.protocol.Speech.Dialog) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance();
    }
    /**
     * <code>.Dialog dialog = 14;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder getDialogOrBuilder() {
      if (payloadCase_ == 14) {
         return (com.amazon.alexa.accessory.protocol.Speech.Dialog) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance();
    }

    public static final int SPEECH_PROVIDER_FIELD_NUMBER = 15;
    /**
     * <code>.SpeechProvider speech_provider = 15;</code>
     * @return Whether the speechProvider field is set.
     */
    @java.lang.Override
    public boolean hasSpeechProvider() {
      return payloadCase_ == 15;
    }
    /**
     * <code>.SpeechProvider speech_provider = 15;</code>
     * @return The speechProvider.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.SpeechProvider getSpeechProvider() {
      if (payloadCase_ == 15) {
         return (com.amazon.alexa.accessory.protocol.Speech.SpeechProvider) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Speech.SpeechProvider.getDefaultInstance();
    }
    /**
     * <code>.SpeechProvider speech_provider = 15;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.SpeechProviderOrBuilder getSpeechProviderOrBuilder() {
      if (payloadCase_ == 15) {
         return (com.amazon.alexa.accessory.protocol.Speech.SpeechProvider) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Speech.SpeechProvider.getDefaultInstance();
    }

    public static final int CENTRAL_INFORMATION_FIELD_NUMBER = 13;
    /**
     * <code>.CentralInformation central_information = 13;</code>
     * @return Whether the centralInformation field is set.
     */
    @java.lang.Override
    public boolean hasCentralInformation() {
      return payloadCase_ == 13;
    }
    /**
     * <code>.CentralInformation central_information = 13;</code>
     * @return The centralInformation.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Central.CentralInformation getCentralInformation() {
      if (payloadCase_ == 13) {
         return (com.amazon.alexa.accessory.protocol.Central.CentralInformation) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Central.CentralInformation.getDefaultInstance();
    }
    /**
     * <code>.CentralInformation central_information = 13;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Central.CentralInformationOrBuilder getCentralInformationOrBuilder() {
      if (payloadCase_ == 13) {
         return (com.amazon.alexa.accessory.protocol.Central.CentralInformation) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Central.CentralInformation.getDefaultInstance();
    }

    public static final int DEVICE_INFORMATION_FIELD_NUMBER = 3;
    /**
     * <code>.DeviceInformation device_information = 3;</code>
     * @return Whether the deviceInformation field is set.
     */
    @java.lang.Override
    public boolean hasDeviceInformation() {
      return payloadCase_ == 3;
    }
    /**
     * <code>.DeviceInformation device_information = 3;</code>
     * @return The deviceInformation.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.DeviceInformation getDeviceInformation() {
      if (payloadCase_ == 3) {
         return (com.amazon.alexa.accessory.protocol.Device.DeviceInformation) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.DeviceInformation.getDefaultInstance();
    }
    /**
     * <code>.DeviceInformation device_information = 3;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.DeviceInformationOrBuilder getDeviceInformationOrBuilder() {
      if (payloadCase_ == 3) {
         return (com.amazon.alexa.accessory.protocol.Device.DeviceInformation) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.DeviceInformation.getDefaultInstance();
    }

    public static final int DEVICE_CONFIGURATION_FIELD_NUMBER = 10;
    /**
     * <code>.DeviceConfiguration device_configuration = 10;</code>
     * @return Whether the deviceConfiguration field is set.
     */
    @java.lang.Override
    public boolean hasDeviceConfiguration() {
      return payloadCase_ == 10;
    }
    /**
     * <code>.DeviceConfiguration device_configuration = 10;</code>
     * @return The deviceConfiguration.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration getDeviceConfiguration() {
      if (payloadCase_ == 10) {
         return (com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.getDefaultInstance();
    }
    /**
     * <code>.DeviceConfiguration device_configuration = 10;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.DeviceConfigurationOrBuilder getDeviceConfigurationOrBuilder() {
      if (payloadCase_ == 10) {
         return (com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.getDefaultInstance();
    }

    public static final int DEVICE_FEATURES_FIELD_NUMBER = 28;
    /**
     * <code>.DeviceFeatures device_features = 28;</code>
     * @return Whether the deviceFeatures field is set.
     */
    @java.lang.Override
    public boolean hasDeviceFeatures() {
      return payloadCase_ == 28;
    }
    /**
     * <code>.DeviceFeatures device_features = 28;</code>
     * @return The deviceFeatures.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.DeviceFeatures getDeviceFeatures() {
      if (payloadCase_ == 28) {
         return (com.amazon.alexa.accessory.protocol.Device.DeviceFeatures) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.DeviceFeatures.getDefaultInstance();
    }
    /**
     * <code>.DeviceFeatures device_features = 28;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.DeviceFeaturesOrBuilder getDeviceFeaturesOrBuilder() {
      if (payloadCase_ == 28) {
         return (com.amazon.alexa.accessory.protocol.Device.DeviceFeatures) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.DeviceFeatures.getDefaultInstance();
    }

    public static final int STATE_FIELD_NUMBER = 7;
    /**
     * <code>.State state = 7;</code>
     * @return Whether the state field is set.
     */
    @java.lang.Override
    public boolean hasState() {
      return payloadCase_ == 7;
    }
    /**
     * <code>.State state = 7;</code>
     * @return The state.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.StateOuterClass.State getState() {
      if (payloadCase_ == 7) {
         return (com.amazon.alexa.accessory.protocol.StateOuterClass.State) payload_;
      }
      return com.amazon.alexa.accessory.protocol.StateOuterClass.State.getDefaultInstance();
    }
    /**
     * <code>.State state = 7;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.StateOuterClass.StateOrBuilder getStateOrBuilder() {
      if (payloadCase_ == 7) {
         return (com.amazon.alexa.accessory.protocol.StateOuterClass.State) payload_;
      }
      return com.amazon.alexa.accessory.protocol.StateOuterClass.State.getDefaultInstance();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (errorCode_ != com.amazon.alexa.accessory.protocol.Common.ErrorCode.SUCCESS.getNumber()) {
        output.writeEnum(1, errorCode_);
      }
      if (payloadCase_ == 3) {
        output.writeMessage(3, (com.amazon.alexa.accessory.protocol.Device.DeviceInformation) payload_);
      }
      if (payloadCase_ == 7) {
        output.writeMessage(7, (com.amazon.alexa.accessory.protocol.StateOuterClass.State) payload_);
      }
      if (payloadCase_ == 8) {
        output.writeMessage(8, (com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails) payload_);
      }
      if (payloadCase_ == 10) {
        output.writeMessage(10, (com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration) payload_);
      }
      if (payloadCase_ == 13) {
        output.writeMessage(13, (com.amazon.alexa.accessory.protocol.Central.CentralInformation) payload_);
      }
      if (payloadCase_ == 14) {
        output.writeMessage(14, (com.amazon.alexa.accessory.protocol.Speech.Dialog) payload_);
      }
      if (payloadCase_ == 15) {
        output.writeMessage(15, (com.amazon.alexa.accessory.protocol.Speech.SpeechProvider) payload_);
      }
      if (payloadCase_ == 21) {
        output.writeMessage(21, (com.amazon.alexa.accessory.protocol.System.Locales) payload_);
      }
      if (payloadCase_ == 28) {
        output.writeMessage(28, (com.amazon.alexa.accessory.protocol.Device.DeviceFeatures) payload_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (errorCode_ != com.amazon.alexa.accessory.protocol.Common.ErrorCode.SUCCESS.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, errorCode_);
      }
      if (payloadCase_ == 3) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, (com.amazon.alexa.accessory.protocol.Device.DeviceInformation) payload_);
      }
      if (payloadCase_ == 7) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, (com.amazon.alexa.accessory.protocol.StateOuterClass.State) payload_);
      }
      if (payloadCase_ == 8) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(8, (com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails) payload_);
      }
      if (payloadCase_ == 10) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(10, (com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration) payload_);
      }
      if (payloadCase_ == 13) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(13, (com.amazon.alexa.accessory.protocol.Central.CentralInformation) payload_);
      }
      if (payloadCase_ == 14) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(14, (com.amazon.alexa.accessory.protocol.Speech.Dialog) payload_);
      }
      if (payloadCase_ == 15) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(15, (com.amazon.alexa.accessory.protocol.Speech.SpeechProvider) payload_);
      }
      if (payloadCase_ == 21) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(21, (com.amazon.alexa.accessory.protocol.System.Locales) payload_);
      }
      if (payloadCase_ == 28) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(28, (com.amazon.alexa.accessory.protocol.Device.DeviceFeatures) payload_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Accessories.Response)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Accessories.Response other = (com.amazon.alexa.accessory.protocol.Accessories.Response) obj;

      if (errorCode_ != other.errorCode_) return false;
      if (!getPayloadCase().equals(other.getPayloadCase())) return false;
      switch (payloadCase_) {
        case 21:
          if (!getLocales()
              .equals(other.getLocales())) return false;
          break;
        case 8:
          if (!getConnectionDetails()
              .equals(other.getConnectionDetails())) return false;
          break;
        case 14:
          if (!getDialog()
              .equals(other.getDialog())) return false;
          break;
        case 15:
          if (!getSpeechProvider()
              .equals(other.getSpeechProvider())) return false;
          break;
        case 13:
          if (!getCentralInformation()
              .equals(other.getCentralInformation())) return false;
          break;
        case 3:
          if (!getDeviceInformation()
              .equals(other.getDeviceInformation())) return false;
          break;
        case 10:
          if (!getDeviceConfiguration()
              .equals(other.getDeviceConfiguration())) return false;
          break;
        case 28:
          if (!getDeviceFeatures()
              .equals(other.getDeviceFeatures())) return false;
          break;
        case 7:
          if (!getState()
              .equals(other.getState())) return false;
          break;
        case 0:
        default:
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ERROR_CODE_FIELD_NUMBER;
      hash = (53 * hash) + errorCode_;
      switch (payloadCase_) {
        case 21:
          hash = (37 * hash) + LOCALES_FIELD_NUMBER;
          hash = (53 * hash) + getLocales().hashCode();
          break;
        case 8:
          hash = (37 * hash) + CONNECTION_DETAILS_FIELD_NUMBER;
          hash = (53 * hash) + getConnectionDetails().hashCode();
          break;
        case 14:
          hash = (37 * hash) + DIALOG_FIELD_NUMBER;
          hash = (53 * hash) + getDialog().hashCode();
          break;
        case 15:
          hash = (37 * hash) + SPEECH_PROVIDER_FIELD_NUMBER;
          hash = (53 * hash) + getSpeechProvider().hashCode();
          break;
        case 13:
          hash = (37 * hash) + CENTRAL_INFORMATION_FIELD_NUMBER;
          hash = (53 * hash) + getCentralInformation().hashCode();
          break;
        case 3:
          hash = (37 * hash) + DEVICE_INFORMATION_FIELD_NUMBER;
          hash = (53 * hash) + getDeviceInformation().hashCode();
          break;
        case 10:
          hash = (37 * hash) + DEVICE_CONFIGURATION_FIELD_NUMBER;
          hash = (53 * hash) + getDeviceConfiguration().hashCode();
          break;
        case 28:
          hash = (37 * hash) + DEVICE_FEATURES_FIELD_NUMBER;
          hash = (53 * hash) + getDeviceFeatures().hashCode();
          break;
        case 7:
          hash = (37 * hash) + STATE_FIELD_NUMBER;
          hash = (53 * hash) + getState().hashCode();
          break;
        case 0:
        default:
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Accessories.Response parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.Response parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.Response parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.Response parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.Response parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.Response parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.Response parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.Response parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.Response parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.Response parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.Response parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.Response parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Accessories.Response prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Response}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Response)
        com.amazon.alexa.accessory.protocol.Accessories.ResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Accessories.internal_static_Response_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Accessories.internal_static_Response_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Accessories.Response.class, com.amazon.alexa.accessory.protocol.Accessories.Response.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Accessories.Response.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        errorCode_ = 0;

        if (localesBuilder_ != null) {
          localesBuilder_.clear();
        }
        if (connectionDetailsBuilder_ != null) {
          connectionDetailsBuilder_.clear();
        }
        if (dialogBuilder_ != null) {
          dialogBuilder_.clear();
        }
        if (speechProviderBuilder_ != null) {
          speechProviderBuilder_.clear();
        }
        if (centralInformationBuilder_ != null) {
          centralInformationBuilder_.clear();
        }
        if (deviceInformationBuilder_ != null) {
          deviceInformationBuilder_.clear();
        }
        if (deviceConfigurationBuilder_ != null) {
          deviceConfigurationBuilder_.clear();
        }
        if (deviceFeaturesBuilder_ != null) {
          deviceFeaturesBuilder_.clear();
        }
        if (stateBuilder_ != null) {
          stateBuilder_.clear();
        }
        payloadCase_ = 0;
        payload_ = null;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Accessories.internal_static_Response_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Accessories.Response getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Accessories.Response.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Accessories.Response build() {
        com.amazon.alexa.accessory.protocol.Accessories.Response result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Accessories.Response buildPartial() {
        com.amazon.alexa.accessory.protocol.Accessories.Response result = new com.amazon.alexa.accessory.protocol.Accessories.Response(this);
        result.errorCode_ = errorCode_;
        if (payloadCase_ == 21) {
          if (localesBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = localesBuilder_.build();
          }
        }
        if (payloadCase_ == 8) {
          if (connectionDetailsBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = connectionDetailsBuilder_.build();
          }
        }
        if (payloadCase_ == 14) {
          if (dialogBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = dialogBuilder_.build();
          }
        }
        if (payloadCase_ == 15) {
          if (speechProviderBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = speechProviderBuilder_.build();
          }
        }
        if (payloadCase_ == 13) {
          if (centralInformationBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = centralInformationBuilder_.build();
          }
        }
        if (payloadCase_ == 3) {
          if (deviceInformationBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = deviceInformationBuilder_.build();
          }
        }
        if (payloadCase_ == 10) {
          if (deviceConfigurationBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = deviceConfigurationBuilder_.build();
          }
        }
        if (payloadCase_ == 28) {
          if (deviceFeaturesBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = deviceFeaturesBuilder_.build();
          }
        }
        if (payloadCase_ == 7) {
          if (stateBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = stateBuilder_.build();
          }
        }
        result.payloadCase_ = payloadCase_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Accessories.Response) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Accessories.Response)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Accessories.Response other) {
        if (other == com.amazon.alexa.accessory.protocol.Accessories.Response.getDefaultInstance()) return this;
        if (other.errorCode_ != 0) {
          setErrorCodeValue(other.getErrorCodeValue());
        }
        switch (other.getPayloadCase()) {
          case LOCALES: {
            mergeLocales(other.getLocales());
            break;
          }
          case CONNECTION_DETAILS: {
            mergeConnectionDetails(other.getConnectionDetails());
            break;
          }
          case DIALOG: {
            mergeDialog(other.getDialog());
            break;
          }
          case SPEECH_PROVIDER: {
            mergeSpeechProvider(other.getSpeechProvider());
            break;
          }
          case CENTRAL_INFORMATION: {
            mergeCentralInformation(other.getCentralInformation());
            break;
          }
          case DEVICE_INFORMATION: {
            mergeDeviceInformation(other.getDeviceInformation());
            break;
          }
          case DEVICE_CONFIGURATION: {
            mergeDeviceConfiguration(other.getDeviceConfiguration());
            break;
          }
          case DEVICE_FEATURES: {
            mergeDeviceFeatures(other.getDeviceFeatures());
            break;
          }
          case STATE: {
            mergeState(other.getState());
            break;
          }
          case PAYLOAD_NOT_SET: {
            break;
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                errorCode_ = input.readEnum();

                break;
              } // case 8
              case 26: {
                input.readMessage(
                    getDeviceInformationFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 3;
                break;
              } // case 26
              case 58: {
                input.readMessage(
                    getStateFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 7;
                break;
              } // case 58
              case 66: {
                input.readMessage(
                    getConnectionDetailsFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 8;
                break;
              } // case 66
              case 82: {
                input.readMessage(
                    getDeviceConfigurationFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 10;
                break;
              } // case 82
              case 106: {
                input.readMessage(
                    getCentralInformationFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 13;
                break;
              } // case 106
              case 114: {
                input.readMessage(
                    getDialogFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 14;
                break;
              } // case 114
              case 122: {
                input.readMessage(
                    getSpeechProviderFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 15;
                break;
              } // case 122
              case 170: {
                input.readMessage(
                    getLocalesFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 21;
                break;
              } // case 170
              case 226: {
                input.readMessage(
                    getDeviceFeaturesFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 28;
                break;
              } // case 226
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int payloadCase_ = 0;
      private java.lang.Object payload_;
      public PayloadCase
          getPayloadCase() {
        return PayloadCase.forNumber(
            payloadCase_);
      }

      public Builder clearPayload() {
        payloadCase_ = 0;
        payload_ = null;
        onChanged();
        return this;
      }


      private int errorCode_ = 0;
      /**
       * <code>.ErrorCode error_code = 1;</code>
       * @return The enum numeric value on the wire for errorCode.
       */
      @java.lang.Override public int getErrorCodeValue() {
        return errorCode_;
      }
      /**
       * <code>.ErrorCode error_code = 1;</code>
       * @param value The enum numeric value on the wire for errorCode to set.
       * @return This builder for chaining.
       */
      public Builder setErrorCodeValue(int value) {
        
        errorCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.ErrorCode error_code = 1;</code>
       * @return The errorCode.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Common.ErrorCode getErrorCode() {
        @SuppressWarnings("deprecation")
        com.amazon.alexa.accessory.protocol.Common.ErrorCode result = com.amazon.alexa.accessory.protocol.Common.ErrorCode.valueOf(errorCode_);
        return result == null ? com.amazon.alexa.accessory.protocol.Common.ErrorCode.UNRECOGNIZED : result;
      }
      /**
       * <code>.ErrorCode error_code = 1;</code>
       * @param value The errorCode to set.
       * @return This builder for chaining.
       */
      public Builder setErrorCode(com.amazon.alexa.accessory.protocol.Common.ErrorCode value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        errorCode_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.ErrorCode error_code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearErrorCode() {
        
        errorCode_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.Locales, com.amazon.alexa.accessory.protocol.System.Locales.Builder, com.amazon.alexa.accessory.protocol.System.LocalesOrBuilder> localesBuilder_;
      /**
       * <code>.Locales locales = 21;</code>
       * @return Whether the locales field is set.
       */
      @java.lang.Override
      public boolean hasLocales() {
        return payloadCase_ == 21;
      }
      /**
       * <code>.Locales locales = 21;</code>
       * @return The locales.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.Locales getLocales() {
        if (localesBuilder_ == null) {
          if (payloadCase_ == 21) {
            return (com.amazon.alexa.accessory.protocol.System.Locales) payload_;
          }
          return com.amazon.alexa.accessory.protocol.System.Locales.getDefaultInstance();
        } else {
          if (payloadCase_ == 21) {
            return localesBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.System.Locales.getDefaultInstance();
        }
      }
      /**
       * <code>.Locales locales = 21;</code>
       */
      public Builder setLocales(com.amazon.alexa.accessory.protocol.System.Locales value) {
        if (localesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          localesBuilder_.setMessage(value);
        }
        payloadCase_ = 21;
        return this;
      }
      /**
       * <code>.Locales locales = 21;</code>
       */
      public Builder setLocales(
          com.amazon.alexa.accessory.protocol.System.Locales.Builder builderForValue) {
        if (localesBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          localesBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 21;
        return this;
      }
      /**
       * <code>.Locales locales = 21;</code>
       */
      public Builder mergeLocales(com.amazon.alexa.accessory.protocol.System.Locales value) {
        if (localesBuilder_ == null) {
          if (payloadCase_ == 21 &&
              payload_ != com.amazon.alexa.accessory.protocol.System.Locales.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.System.Locales.newBuilder((com.amazon.alexa.accessory.protocol.System.Locales) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 21) {
            localesBuilder_.mergeFrom(value);
          } else {
            localesBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 21;
        return this;
      }
      /**
       * <code>.Locales locales = 21;</code>
       */
      public Builder clearLocales() {
        if (localesBuilder_ == null) {
          if (payloadCase_ == 21) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 21) {
            payloadCase_ = 0;
            payload_ = null;
          }
          localesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.Locales locales = 21;</code>
       */
      public com.amazon.alexa.accessory.protocol.System.Locales.Builder getLocalesBuilder() {
        return getLocalesFieldBuilder().getBuilder();
      }
      /**
       * <code>.Locales locales = 21;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.LocalesOrBuilder getLocalesOrBuilder() {
        if ((payloadCase_ == 21) && (localesBuilder_ != null)) {
          return localesBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 21) {
            return (com.amazon.alexa.accessory.protocol.System.Locales) payload_;
          }
          return com.amazon.alexa.accessory.protocol.System.Locales.getDefaultInstance();
        }
      }
      /**
       * <code>.Locales locales = 21;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.Locales, com.amazon.alexa.accessory.protocol.System.Locales.Builder, com.amazon.alexa.accessory.protocol.System.LocalesOrBuilder> 
          getLocalesFieldBuilder() {
        if (localesBuilder_ == null) {
          if (!(payloadCase_ == 21)) {
            payload_ = com.amazon.alexa.accessory.protocol.System.Locales.getDefaultInstance();
          }
          localesBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.System.Locales, com.amazon.alexa.accessory.protocol.System.Locales.Builder, com.amazon.alexa.accessory.protocol.System.LocalesOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.System.Locales) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 21;
        onChanged();;
        return localesBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails, com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails.Builder, com.amazon.alexa.accessory.protocol.Transport.ConnectionDetailsOrBuilder> connectionDetailsBuilder_;
      /**
       * <code>.ConnectionDetails connection_details = 8;</code>
       * @return Whether the connectionDetails field is set.
       */
      @java.lang.Override
      public boolean hasConnectionDetails() {
        return payloadCase_ == 8;
      }
      /**
       * <code>.ConnectionDetails connection_details = 8;</code>
       * @return The connectionDetails.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails getConnectionDetails() {
        if (connectionDetailsBuilder_ == null) {
          if (payloadCase_ == 8) {
            return (com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails.getDefaultInstance();
        } else {
          if (payloadCase_ == 8) {
            return connectionDetailsBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails.getDefaultInstance();
        }
      }
      /**
       * <code>.ConnectionDetails connection_details = 8;</code>
       */
      public Builder setConnectionDetails(com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails value) {
        if (connectionDetailsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          connectionDetailsBuilder_.setMessage(value);
        }
        payloadCase_ = 8;
        return this;
      }
      /**
       * <code>.ConnectionDetails connection_details = 8;</code>
       */
      public Builder setConnectionDetails(
          com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails.Builder builderForValue) {
        if (connectionDetailsBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          connectionDetailsBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 8;
        return this;
      }
      /**
       * <code>.ConnectionDetails connection_details = 8;</code>
       */
      public Builder mergeConnectionDetails(com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails value) {
        if (connectionDetailsBuilder_ == null) {
          if (payloadCase_ == 8 &&
              payload_ != com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails.newBuilder((com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 8) {
            connectionDetailsBuilder_.mergeFrom(value);
          } else {
            connectionDetailsBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 8;
        return this;
      }
      /**
       * <code>.ConnectionDetails connection_details = 8;</code>
       */
      public Builder clearConnectionDetails() {
        if (connectionDetailsBuilder_ == null) {
          if (payloadCase_ == 8) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 8) {
            payloadCase_ = 0;
            payload_ = null;
          }
          connectionDetailsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.ConnectionDetails connection_details = 8;</code>
       */
      public com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails.Builder getConnectionDetailsBuilder() {
        return getConnectionDetailsFieldBuilder().getBuilder();
      }
      /**
       * <code>.ConnectionDetails connection_details = 8;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Transport.ConnectionDetailsOrBuilder getConnectionDetailsOrBuilder() {
        if ((payloadCase_ == 8) && (connectionDetailsBuilder_ != null)) {
          return connectionDetailsBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 8) {
            return (com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails.getDefaultInstance();
        }
      }
      /**
       * <code>.ConnectionDetails connection_details = 8;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails, com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails.Builder, com.amazon.alexa.accessory.protocol.Transport.ConnectionDetailsOrBuilder> 
          getConnectionDetailsFieldBuilder() {
        if (connectionDetailsBuilder_ == null) {
          if (!(payloadCase_ == 8)) {
            payload_ = com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails.getDefaultInstance();
          }
          connectionDetailsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails, com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails.Builder, com.amazon.alexa.accessory.protocol.Transport.ConnectionDetailsOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Transport.ConnectionDetails) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 8;
        onChanged();;
        return connectionDetailsBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.Dialog, com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder, com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder> dialogBuilder_;
      /**
       * <code>.Dialog dialog = 14;</code>
       * @return Whether the dialog field is set.
       */
      @java.lang.Override
      public boolean hasDialog() {
        return payloadCase_ == 14;
      }
      /**
       * <code>.Dialog dialog = 14;</code>
       * @return The dialog.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.Dialog getDialog() {
        if (dialogBuilder_ == null) {
          if (payloadCase_ == 14) {
            return (com.amazon.alexa.accessory.protocol.Speech.Dialog) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance();
        } else {
          if (payloadCase_ == 14) {
            return dialogBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance();
        }
      }
      /**
       * <code>.Dialog dialog = 14;</code>
       */
      public Builder setDialog(com.amazon.alexa.accessory.protocol.Speech.Dialog value) {
        if (dialogBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          dialogBuilder_.setMessage(value);
        }
        payloadCase_ = 14;
        return this;
      }
      /**
       * <code>.Dialog dialog = 14;</code>
       */
      public Builder setDialog(
          com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder builderForValue) {
        if (dialogBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          dialogBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 14;
        return this;
      }
      /**
       * <code>.Dialog dialog = 14;</code>
       */
      public Builder mergeDialog(com.amazon.alexa.accessory.protocol.Speech.Dialog value) {
        if (dialogBuilder_ == null) {
          if (payloadCase_ == 14 &&
              payload_ != com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Speech.Dialog.newBuilder((com.amazon.alexa.accessory.protocol.Speech.Dialog) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 14) {
            dialogBuilder_.mergeFrom(value);
          } else {
            dialogBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 14;
        return this;
      }
      /**
       * <code>.Dialog dialog = 14;</code>
       */
      public Builder clearDialog() {
        if (dialogBuilder_ == null) {
          if (payloadCase_ == 14) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 14) {
            payloadCase_ = 0;
            payload_ = null;
          }
          dialogBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.Dialog dialog = 14;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder getDialogBuilder() {
        return getDialogFieldBuilder().getBuilder();
      }
      /**
       * <code>.Dialog dialog = 14;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder getDialogOrBuilder() {
        if ((payloadCase_ == 14) && (dialogBuilder_ != null)) {
          return dialogBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 14) {
            return (com.amazon.alexa.accessory.protocol.Speech.Dialog) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance();
        }
      }
      /**
       * <code>.Dialog dialog = 14;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.Dialog, com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder, com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder> 
          getDialogFieldBuilder() {
        if (dialogBuilder_ == null) {
          if (!(payloadCase_ == 14)) {
            payload_ = com.amazon.alexa.accessory.protocol.Speech.Dialog.getDefaultInstance();
          }
          dialogBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Speech.Dialog, com.amazon.alexa.accessory.protocol.Speech.Dialog.Builder, com.amazon.alexa.accessory.protocol.Speech.DialogOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Speech.Dialog) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 14;
        onChanged();;
        return dialogBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.SpeechProvider, com.amazon.alexa.accessory.protocol.Speech.SpeechProvider.Builder, com.amazon.alexa.accessory.protocol.Speech.SpeechProviderOrBuilder> speechProviderBuilder_;
      /**
       * <code>.SpeechProvider speech_provider = 15;</code>
       * @return Whether the speechProvider field is set.
       */
      @java.lang.Override
      public boolean hasSpeechProvider() {
        return payloadCase_ == 15;
      }
      /**
       * <code>.SpeechProvider speech_provider = 15;</code>
       * @return The speechProvider.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.SpeechProvider getSpeechProvider() {
        if (speechProviderBuilder_ == null) {
          if (payloadCase_ == 15) {
            return (com.amazon.alexa.accessory.protocol.Speech.SpeechProvider) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Speech.SpeechProvider.getDefaultInstance();
        } else {
          if (payloadCase_ == 15) {
            return speechProviderBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Speech.SpeechProvider.getDefaultInstance();
        }
      }
      /**
       * <code>.SpeechProvider speech_provider = 15;</code>
       */
      public Builder setSpeechProvider(com.amazon.alexa.accessory.protocol.Speech.SpeechProvider value) {
        if (speechProviderBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          speechProviderBuilder_.setMessage(value);
        }
        payloadCase_ = 15;
        return this;
      }
      /**
       * <code>.SpeechProvider speech_provider = 15;</code>
       */
      public Builder setSpeechProvider(
          com.amazon.alexa.accessory.protocol.Speech.SpeechProvider.Builder builderForValue) {
        if (speechProviderBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          speechProviderBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 15;
        return this;
      }
      /**
       * <code>.SpeechProvider speech_provider = 15;</code>
       */
      public Builder mergeSpeechProvider(com.amazon.alexa.accessory.protocol.Speech.SpeechProvider value) {
        if (speechProviderBuilder_ == null) {
          if (payloadCase_ == 15 &&
              payload_ != com.amazon.alexa.accessory.protocol.Speech.SpeechProvider.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Speech.SpeechProvider.newBuilder((com.amazon.alexa.accessory.protocol.Speech.SpeechProvider) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 15) {
            speechProviderBuilder_.mergeFrom(value);
          } else {
            speechProviderBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 15;
        return this;
      }
      /**
       * <code>.SpeechProvider speech_provider = 15;</code>
       */
      public Builder clearSpeechProvider() {
        if (speechProviderBuilder_ == null) {
          if (payloadCase_ == 15) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 15) {
            payloadCase_ = 0;
            payload_ = null;
          }
          speechProviderBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.SpeechProvider speech_provider = 15;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.SpeechProvider.Builder getSpeechProviderBuilder() {
        return getSpeechProviderFieldBuilder().getBuilder();
      }
      /**
       * <code>.SpeechProvider speech_provider = 15;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.SpeechProviderOrBuilder getSpeechProviderOrBuilder() {
        if ((payloadCase_ == 15) && (speechProviderBuilder_ != null)) {
          return speechProviderBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 15) {
            return (com.amazon.alexa.accessory.protocol.Speech.SpeechProvider) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Speech.SpeechProvider.getDefaultInstance();
        }
      }
      /**
       * <code>.SpeechProvider speech_provider = 15;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.SpeechProvider, com.amazon.alexa.accessory.protocol.Speech.SpeechProvider.Builder, com.amazon.alexa.accessory.protocol.Speech.SpeechProviderOrBuilder> 
          getSpeechProviderFieldBuilder() {
        if (speechProviderBuilder_ == null) {
          if (!(payloadCase_ == 15)) {
            payload_ = com.amazon.alexa.accessory.protocol.Speech.SpeechProvider.getDefaultInstance();
          }
          speechProviderBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Speech.SpeechProvider, com.amazon.alexa.accessory.protocol.Speech.SpeechProvider.Builder, com.amazon.alexa.accessory.protocol.Speech.SpeechProviderOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Speech.SpeechProvider) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 15;
        onChanged();;
        return speechProviderBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Central.CentralInformation, com.amazon.alexa.accessory.protocol.Central.CentralInformation.Builder, com.amazon.alexa.accessory.protocol.Central.CentralInformationOrBuilder> centralInformationBuilder_;
      /**
       * <code>.CentralInformation central_information = 13;</code>
       * @return Whether the centralInformation field is set.
       */
      @java.lang.Override
      public boolean hasCentralInformation() {
        return payloadCase_ == 13;
      }
      /**
       * <code>.CentralInformation central_information = 13;</code>
       * @return The centralInformation.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Central.CentralInformation getCentralInformation() {
        if (centralInformationBuilder_ == null) {
          if (payloadCase_ == 13) {
            return (com.amazon.alexa.accessory.protocol.Central.CentralInformation) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Central.CentralInformation.getDefaultInstance();
        } else {
          if (payloadCase_ == 13) {
            return centralInformationBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Central.CentralInformation.getDefaultInstance();
        }
      }
      /**
       * <code>.CentralInformation central_information = 13;</code>
       */
      public Builder setCentralInformation(com.amazon.alexa.accessory.protocol.Central.CentralInformation value) {
        if (centralInformationBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          centralInformationBuilder_.setMessage(value);
        }
        payloadCase_ = 13;
        return this;
      }
      /**
       * <code>.CentralInformation central_information = 13;</code>
       */
      public Builder setCentralInformation(
          com.amazon.alexa.accessory.protocol.Central.CentralInformation.Builder builderForValue) {
        if (centralInformationBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          centralInformationBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 13;
        return this;
      }
      /**
       * <code>.CentralInformation central_information = 13;</code>
       */
      public Builder mergeCentralInformation(com.amazon.alexa.accessory.protocol.Central.CentralInformation value) {
        if (centralInformationBuilder_ == null) {
          if (payloadCase_ == 13 &&
              payload_ != com.amazon.alexa.accessory.protocol.Central.CentralInformation.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Central.CentralInformation.newBuilder((com.amazon.alexa.accessory.protocol.Central.CentralInformation) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 13) {
            centralInformationBuilder_.mergeFrom(value);
          } else {
            centralInformationBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 13;
        return this;
      }
      /**
       * <code>.CentralInformation central_information = 13;</code>
       */
      public Builder clearCentralInformation() {
        if (centralInformationBuilder_ == null) {
          if (payloadCase_ == 13) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 13) {
            payloadCase_ = 0;
            payload_ = null;
          }
          centralInformationBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.CentralInformation central_information = 13;</code>
       */
      public com.amazon.alexa.accessory.protocol.Central.CentralInformation.Builder getCentralInformationBuilder() {
        return getCentralInformationFieldBuilder().getBuilder();
      }
      /**
       * <code>.CentralInformation central_information = 13;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Central.CentralInformationOrBuilder getCentralInformationOrBuilder() {
        if ((payloadCase_ == 13) && (centralInformationBuilder_ != null)) {
          return centralInformationBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 13) {
            return (com.amazon.alexa.accessory.protocol.Central.CentralInformation) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Central.CentralInformation.getDefaultInstance();
        }
      }
      /**
       * <code>.CentralInformation central_information = 13;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Central.CentralInformation, com.amazon.alexa.accessory.protocol.Central.CentralInformation.Builder, com.amazon.alexa.accessory.protocol.Central.CentralInformationOrBuilder> 
          getCentralInformationFieldBuilder() {
        if (centralInformationBuilder_ == null) {
          if (!(payloadCase_ == 13)) {
            payload_ = com.amazon.alexa.accessory.protocol.Central.CentralInformation.getDefaultInstance();
          }
          centralInformationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Central.CentralInformation, com.amazon.alexa.accessory.protocol.Central.CentralInformation.Builder, com.amazon.alexa.accessory.protocol.Central.CentralInformationOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Central.CentralInformation) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 13;
        onChanged();;
        return centralInformationBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.DeviceInformation, com.amazon.alexa.accessory.protocol.Device.DeviceInformation.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceInformationOrBuilder> deviceInformationBuilder_;
      /**
       * <code>.DeviceInformation device_information = 3;</code>
       * @return Whether the deviceInformation field is set.
       */
      @java.lang.Override
      public boolean hasDeviceInformation() {
        return payloadCase_ == 3;
      }
      /**
       * <code>.DeviceInformation device_information = 3;</code>
       * @return The deviceInformation.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceInformation getDeviceInformation() {
        if (deviceInformationBuilder_ == null) {
          if (payloadCase_ == 3) {
            return (com.amazon.alexa.accessory.protocol.Device.DeviceInformation) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.DeviceInformation.getDefaultInstance();
        } else {
          if (payloadCase_ == 3) {
            return deviceInformationBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Device.DeviceInformation.getDefaultInstance();
        }
      }
      /**
       * <code>.DeviceInformation device_information = 3;</code>
       */
      public Builder setDeviceInformation(com.amazon.alexa.accessory.protocol.Device.DeviceInformation value) {
        if (deviceInformationBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          deviceInformationBuilder_.setMessage(value);
        }
        payloadCase_ = 3;
        return this;
      }
      /**
       * <code>.DeviceInformation device_information = 3;</code>
       */
      public Builder setDeviceInformation(
          com.amazon.alexa.accessory.protocol.Device.DeviceInformation.Builder builderForValue) {
        if (deviceInformationBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          deviceInformationBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 3;
        return this;
      }
      /**
       * <code>.DeviceInformation device_information = 3;</code>
       */
      public Builder mergeDeviceInformation(com.amazon.alexa.accessory.protocol.Device.DeviceInformation value) {
        if (deviceInformationBuilder_ == null) {
          if (payloadCase_ == 3 &&
              payload_ != com.amazon.alexa.accessory.protocol.Device.DeviceInformation.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.DeviceInformation.newBuilder((com.amazon.alexa.accessory.protocol.Device.DeviceInformation) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 3) {
            deviceInformationBuilder_.mergeFrom(value);
          } else {
            deviceInformationBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 3;
        return this;
      }
      /**
       * <code>.DeviceInformation device_information = 3;</code>
       */
      public Builder clearDeviceInformation() {
        if (deviceInformationBuilder_ == null) {
          if (payloadCase_ == 3) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 3) {
            payloadCase_ = 0;
            payload_ = null;
          }
          deviceInformationBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.DeviceInformation device_information = 3;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.DeviceInformation.Builder getDeviceInformationBuilder() {
        return getDeviceInformationFieldBuilder().getBuilder();
      }
      /**
       * <code>.DeviceInformation device_information = 3;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceInformationOrBuilder getDeviceInformationOrBuilder() {
        if ((payloadCase_ == 3) && (deviceInformationBuilder_ != null)) {
          return deviceInformationBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 3) {
            return (com.amazon.alexa.accessory.protocol.Device.DeviceInformation) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.DeviceInformation.getDefaultInstance();
        }
      }
      /**
       * <code>.DeviceInformation device_information = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.DeviceInformation, com.amazon.alexa.accessory.protocol.Device.DeviceInformation.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceInformationOrBuilder> 
          getDeviceInformationFieldBuilder() {
        if (deviceInformationBuilder_ == null) {
          if (!(payloadCase_ == 3)) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.DeviceInformation.getDefaultInstance();
          }
          deviceInformationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Device.DeviceInformation, com.amazon.alexa.accessory.protocol.Device.DeviceInformation.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceInformationOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Device.DeviceInformation) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 3;
        onChanged();;
        return deviceInformationBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration, com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceConfigurationOrBuilder> deviceConfigurationBuilder_;
      /**
       * <code>.DeviceConfiguration device_configuration = 10;</code>
       * @return Whether the deviceConfiguration field is set.
       */
      @java.lang.Override
      public boolean hasDeviceConfiguration() {
        return payloadCase_ == 10;
      }
      /**
       * <code>.DeviceConfiguration device_configuration = 10;</code>
       * @return The deviceConfiguration.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration getDeviceConfiguration() {
        if (deviceConfigurationBuilder_ == null) {
          if (payloadCase_ == 10) {
            return (com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.getDefaultInstance();
        } else {
          if (payloadCase_ == 10) {
            return deviceConfigurationBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.getDefaultInstance();
        }
      }
      /**
       * <code>.DeviceConfiguration device_configuration = 10;</code>
       */
      public Builder setDeviceConfiguration(com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration value) {
        if (deviceConfigurationBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          deviceConfigurationBuilder_.setMessage(value);
        }
        payloadCase_ = 10;
        return this;
      }
      /**
       * <code>.DeviceConfiguration device_configuration = 10;</code>
       */
      public Builder setDeviceConfiguration(
          com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.Builder builderForValue) {
        if (deviceConfigurationBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          deviceConfigurationBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 10;
        return this;
      }
      /**
       * <code>.DeviceConfiguration device_configuration = 10;</code>
       */
      public Builder mergeDeviceConfiguration(com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration value) {
        if (deviceConfigurationBuilder_ == null) {
          if (payloadCase_ == 10 &&
              payload_ != com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.newBuilder((com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 10) {
            deviceConfigurationBuilder_.mergeFrom(value);
          } else {
            deviceConfigurationBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 10;
        return this;
      }
      /**
       * <code>.DeviceConfiguration device_configuration = 10;</code>
       */
      public Builder clearDeviceConfiguration() {
        if (deviceConfigurationBuilder_ == null) {
          if (payloadCase_ == 10) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 10) {
            payloadCase_ = 0;
            payload_ = null;
          }
          deviceConfigurationBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.DeviceConfiguration device_configuration = 10;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.Builder getDeviceConfigurationBuilder() {
        return getDeviceConfigurationFieldBuilder().getBuilder();
      }
      /**
       * <code>.DeviceConfiguration device_configuration = 10;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceConfigurationOrBuilder getDeviceConfigurationOrBuilder() {
        if ((payloadCase_ == 10) && (deviceConfigurationBuilder_ != null)) {
          return deviceConfigurationBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 10) {
            return (com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.getDefaultInstance();
        }
      }
      /**
       * <code>.DeviceConfiguration device_configuration = 10;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration, com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceConfigurationOrBuilder> 
          getDeviceConfigurationFieldBuilder() {
        if (deviceConfigurationBuilder_ == null) {
          if (!(payloadCase_ == 10)) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.getDefaultInstance();
          }
          deviceConfigurationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration, com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceConfigurationOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Device.DeviceConfiguration) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 10;
        onChanged();;
        return deviceConfigurationBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.DeviceFeatures, com.amazon.alexa.accessory.protocol.Device.DeviceFeatures.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceFeaturesOrBuilder> deviceFeaturesBuilder_;
      /**
       * <code>.DeviceFeatures device_features = 28;</code>
       * @return Whether the deviceFeatures field is set.
       */
      @java.lang.Override
      public boolean hasDeviceFeatures() {
        return payloadCase_ == 28;
      }
      /**
       * <code>.DeviceFeatures device_features = 28;</code>
       * @return The deviceFeatures.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceFeatures getDeviceFeatures() {
        if (deviceFeaturesBuilder_ == null) {
          if (payloadCase_ == 28) {
            return (com.amazon.alexa.accessory.protocol.Device.DeviceFeatures) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.DeviceFeatures.getDefaultInstance();
        } else {
          if (payloadCase_ == 28) {
            return deviceFeaturesBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Device.DeviceFeatures.getDefaultInstance();
        }
      }
      /**
       * <code>.DeviceFeatures device_features = 28;</code>
       */
      public Builder setDeviceFeatures(com.amazon.alexa.accessory.protocol.Device.DeviceFeatures value) {
        if (deviceFeaturesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          deviceFeaturesBuilder_.setMessage(value);
        }
        payloadCase_ = 28;
        return this;
      }
      /**
       * <code>.DeviceFeatures device_features = 28;</code>
       */
      public Builder setDeviceFeatures(
          com.amazon.alexa.accessory.protocol.Device.DeviceFeatures.Builder builderForValue) {
        if (deviceFeaturesBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          deviceFeaturesBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 28;
        return this;
      }
      /**
       * <code>.DeviceFeatures device_features = 28;</code>
       */
      public Builder mergeDeviceFeatures(com.amazon.alexa.accessory.protocol.Device.DeviceFeatures value) {
        if (deviceFeaturesBuilder_ == null) {
          if (payloadCase_ == 28 &&
              payload_ != com.amazon.alexa.accessory.protocol.Device.DeviceFeatures.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.DeviceFeatures.newBuilder((com.amazon.alexa.accessory.protocol.Device.DeviceFeatures) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 28) {
            deviceFeaturesBuilder_.mergeFrom(value);
          } else {
            deviceFeaturesBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 28;
        return this;
      }
      /**
       * <code>.DeviceFeatures device_features = 28;</code>
       */
      public Builder clearDeviceFeatures() {
        if (deviceFeaturesBuilder_ == null) {
          if (payloadCase_ == 28) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 28) {
            payloadCase_ = 0;
            payload_ = null;
          }
          deviceFeaturesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.DeviceFeatures device_features = 28;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.DeviceFeatures.Builder getDeviceFeaturesBuilder() {
        return getDeviceFeaturesFieldBuilder().getBuilder();
      }
      /**
       * <code>.DeviceFeatures device_features = 28;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.DeviceFeaturesOrBuilder getDeviceFeaturesOrBuilder() {
        if ((payloadCase_ == 28) && (deviceFeaturesBuilder_ != null)) {
          return deviceFeaturesBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 28) {
            return (com.amazon.alexa.accessory.protocol.Device.DeviceFeatures) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.DeviceFeatures.getDefaultInstance();
        }
      }
      /**
       * <code>.DeviceFeatures device_features = 28;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.DeviceFeatures, com.amazon.alexa.accessory.protocol.Device.DeviceFeatures.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceFeaturesOrBuilder> 
          getDeviceFeaturesFieldBuilder() {
        if (deviceFeaturesBuilder_ == null) {
          if (!(payloadCase_ == 28)) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.DeviceFeatures.getDefaultInstance();
          }
          deviceFeaturesBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Device.DeviceFeatures, com.amazon.alexa.accessory.protocol.Device.DeviceFeatures.Builder, com.amazon.alexa.accessory.protocol.Device.DeviceFeaturesOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Device.DeviceFeatures) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 28;
        onChanged();;
        return deviceFeaturesBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.StateOuterClass.State, com.amazon.alexa.accessory.protocol.StateOuterClass.State.Builder, com.amazon.alexa.accessory.protocol.StateOuterClass.StateOrBuilder> stateBuilder_;
      /**
       * <code>.State state = 7;</code>
       * @return Whether the state field is set.
       */
      @java.lang.Override
      public boolean hasState() {
        return payloadCase_ == 7;
      }
      /**
       * <code>.State state = 7;</code>
       * @return The state.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.StateOuterClass.State getState() {
        if (stateBuilder_ == null) {
          if (payloadCase_ == 7) {
            return (com.amazon.alexa.accessory.protocol.StateOuterClass.State) payload_;
          }
          return com.amazon.alexa.accessory.protocol.StateOuterClass.State.getDefaultInstance();
        } else {
          if (payloadCase_ == 7) {
            return stateBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.StateOuterClass.State.getDefaultInstance();
        }
      }
      /**
       * <code>.State state = 7;</code>
       */
      public Builder setState(com.amazon.alexa.accessory.protocol.StateOuterClass.State value) {
        if (stateBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          stateBuilder_.setMessage(value);
        }
        payloadCase_ = 7;
        return this;
      }
      /**
       * <code>.State state = 7;</code>
       */
      public Builder setState(
          com.amazon.alexa.accessory.protocol.StateOuterClass.State.Builder builderForValue) {
        if (stateBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          stateBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 7;
        return this;
      }
      /**
       * <code>.State state = 7;</code>
       */
      public Builder mergeState(com.amazon.alexa.accessory.protocol.StateOuterClass.State value) {
        if (stateBuilder_ == null) {
          if (payloadCase_ == 7 &&
              payload_ != com.amazon.alexa.accessory.protocol.StateOuterClass.State.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.StateOuterClass.State.newBuilder((com.amazon.alexa.accessory.protocol.StateOuterClass.State) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 7) {
            stateBuilder_.mergeFrom(value);
          } else {
            stateBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 7;
        return this;
      }
      /**
       * <code>.State state = 7;</code>
       */
      public Builder clearState() {
        if (stateBuilder_ == null) {
          if (payloadCase_ == 7) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 7) {
            payloadCase_ = 0;
            payload_ = null;
          }
          stateBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.State state = 7;</code>
       */
      public com.amazon.alexa.accessory.protocol.StateOuterClass.State.Builder getStateBuilder() {
        return getStateFieldBuilder().getBuilder();
      }
      /**
       * <code>.State state = 7;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.StateOuterClass.StateOrBuilder getStateOrBuilder() {
        if ((payloadCase_ == 7) && (stateBuilder_ != null)) {
          return stateBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 7) {
            return (com.amazon.alexa.accessory.protocol.StateOuterClass.State) payload_;
          }
          return com.amazon.alexa.accessory.protocol.StateOuterClass.State.getDefaultInstance();
        }
      }
      /**
       * <code>.State state = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.StateOuterClass.State, com.amazon.alexa.accessory.protocol.StateOuterClass.State.Builder, com.amazon.alexa.accessory.protocol.StateOuterClass.StateOrBuilder> 
          getStateFieldBuilder() {
        if (stateBuilder_ == null) {
          if (!(payloadCase_ == 7)) {
            payload_ = com.amazon.alexa.accessory.protocol.StateOuterClass.State.getDefaultInstance();
          }
          stateBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.StateOuterClass.State, com.amazon.alexa.accessory.protocol.StateOuterClass.State.Builder, com.amazon.alexa.accessory.protocol.StateOuterClass.StateOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.StateOuterClass.State) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 7;
        onChanged();;
        return stateBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Response)
    }

    // @@protoc_insertion_point(class_scope:Response)
    private static final com.amazon.alexa.accessory.protocol.Accessories.Response DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Accessories.Response();
    }

    public static com.amazon.alexa.accessory.protocol.Accessories.Response getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Response>
        PARSER = new com.google.protobuf.AbstractParser<Response>() {
      @java.lang.Override
      public Response parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Response> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Response> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Accessories.Response getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ControlEnvelopeOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ControlEnvelope)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Command command = 1;</code>
     * @return The enum numeric value on the wire for command.
     */
    int getCommandValue();
    /**
     * <code>.Command command = 1;</code>
     * @return The command.
     */
    com.amazon.alexa.accessory.protocol.Accessories.Command getCommand();

    /**
     * <code>.Response response = 9;</code>
     * @return Whether the response field is set.
     */
    boolean hasResponse();
    /**
     * <code>.Response response = 9;</code>
     * @return The response.
     */
    com.amazon.alexa.accessory.protocol.Accessories.Response getResponse();
    /**
     * <code>.Response response = 9;</code>
     */
    com.amazon.alexa.accessory.protocol.Accessories.ResponseOrBuilder getResponseOrBuilder();

    /**
     * <code>.ResetConnection reset_connection = 51;</code>
     * @return Whether the resetConnection field is set.
     */
    boolean hasResetConnection();
    /**
     * <code>.ResetConnection reset_connection = 51;</code>
     * @return The resetConnection.
     */
    com.amazon.alexa.accessory.protocol.System.ResetConnection getResetConnection();
    /**
     * <code>.ResetConnection reset_connection = 51;</code>
     */
    com.amazon.alexa.accessory.protocol.System.ResetConnectionOrBuilder getResetConnectionOrBuilder();

    /**
     * <code>.SynchronizeSettings synchronize_settings = 50;</code>
     * @return Whether the synchronizeSettings field is set.
     */
    boolean hasSynchronizeSettings();
    /**
     * <code>.SynchronizeSettings synchronize_settings = 50;</code>
     * @return The synchronizeSettings.
     */
    com.amazon.alexa.accessory.protocol.System.SynchronizeSettings getSynchronizeSettings();
    /**
     * <code>.SynchronizeSettings synchronize_settings = 50;</code>
     */
    com.amazon.alexa.accessory.protocol.System.SynchronizeSettingsOrBuilder getSynchronizeSettingsOrBuilder();

    /**
     * <code>.KeepAlive keep_alive = 55;</code>
     * @return Whether the keepAlive field is set.
     */
    boolean hasKeepAlive();
    /**
     * <code>.KeepAlive keep_alive = 55;</code>
     * @return The keepAlive.
     */
    com.amazon.alexa.accessory.protocol.System.KeepAlive getKeepAlive();
    /**
     * <code>.KeepAlive keep_alive = 55;</code>
     */
    com.amazon.alexa.accessory.protocol.System.KeepAliveOrBuilder getKeepAliveOrBuilder();

    /**
     * <code>.RemoveDevice remove_device = 56;</code>
     * @return Whether the removeDevice field is set.
     */
    boolean hasRemoveDevice();
    /**
     * <code>.RemoveDevice remove_device = 56;</code>
     * @return The removeDevice.
     */
    com.amazon.alexa.accessory.protocol.System.RemoveDevice getRemoveDevice();
    /**
     * <code>.RemoveDevice remove_device = 56;</code>
     */
    com.amazon.alexa.accessory.protocol.System.RemoveDeviceOrBuilder getRemoveDeviceOrBuilder();

    /**
     * <code>.GetLocales get_locales = 57;</code>
     * @return Whether the getLocales field is set.
     */
    boolean hasGetLocales();
    /**
     * <code>.GetLocales get_locales = 57;</code>
     * @return The getLocales.
     */
    com.amazon.alexa.accessory.protocol.System.GetLocales getGetLocales();
    /**
     * <code>.GetLocales get_locales = 57;</code>
     */
    com.amazon.alexa.accessory.protocol.System.GetLocalesOrBuilder getGetLocalesOrBuilder();

    /**
     * <code>.SetLocale set_locale = 58;</code>
     * @return Whether the setLocale field is set.
     */
    boolean hasSetLocale();
    /**
     * <code>.SetLocale set_locale = 58;</code>
     * @return The setLocale.
     */
    com.amazon.alexa.accessory.protocol.System.SetLocale getSetLocale();
    /**
     * <code>.SetLocale set_locale = 58;</code>
     */
    com.amazon.alexa.accessory.protocol.System.SetLocaleOrBuilder getSetLocaleOrBuilder();

    /**
     * <code>.LaunchApp launch_app = 59;</code>
     * @return Whether the launchApp field is set.
     */
    boolean hasLaunchApp();
    /**
     * <code>.LaunchApp launch_app = 59;</code>
     * @return The launchApp.
     */
    com.amazon.alexa.accessory.protocol.System.LaunchApp getLaunchApp();
    /**
     * <code>.LaunchApp launch_app = 59;</code>
     */
    com.amazon.alexa.accessory.protocol.System.LaunchAppOrBuilder getLaunchAppOrBuilder();

    /**
     * <code>.UpgradeTransport upgrade_transport = 30;</code>
     * @return Whether the upgradeTransport field is set.
     */
    boolean hasUpgradeTransport();
    /**
     * <code>.UpgradeTransport upgrade_transport = 30;</code>
     * @return The upgradeTransport.
     */
    com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport getUpgradeTransport();
    /**
     * <code>.UpgradeTransport upgrade_transport = 30;</code>
     */
    com.amazon.alexa.accessory.protocol.Transport.UpgradeTransportOrBuilder getUpgradeTransportOrBuilder();

    /**
     * <code>.SwitchTransport switch_transport = 31;</code>
     * @return Whether the switchTransport field is set.
     */
    boolean hasSwitchTransport();
    /**
     * <code>.SwitchTransport switch_transport = 31;</code>
     * @return The switchTransport.
     */
    com.amazon.alexa.accessory.protocol.Transport.SwitchTransport getSwitchTransport();
    /**
     * <code>.SwitchTransport switch_transport = 31;</code>
     */
    com.amazon.alexa.accessory.protocol.Transport.SwitchTransportOrBuilder getSwitchTransportOrBuilder();

    /**
     * <code>.StartSpeech start_speech = 11;</code>
     * @return Whether the startSpeech field is set.
     */
    boolean hasStartSpeech();
    /**
     * <code>.StartSpeech start_speech = 11;</code>
     * @return The startSpeech.
     */
    com.amazon.alexa.accessory.protocol.Speech.StartSpeech getStartSpeech();
    /**
     * <code>.StartSpeech start_speech = 11;</code>
     */
    com.amazon.alexa.accessory.protocol.Speech.StartSpeechOrBuilder getStartSpeechOrBuilder();

    /**
     * <code>.ProvideSpeech provide_speech = 10;</code>
     * @return Whether the provideSpeech field is set.
     */
    boolean hasProvideSpeech();
    /**
     * <code>.ProvideSpeech provide_speech = 10;</code>
     * @return The provideSpeech.
     */
    com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech getProvideSpeech();
    /**
     * <code>.ProvideSpeech provide_speech = 10;</code>
     */
    com.amazon.alexa.accessory.protocol.Speech.ProvideSpeechOrBuilder getProvideSpeechOrBuilder();

    /**
     * <code>.StopSpeech stop_speech = 12;</code>
     * @return Whether the stopSpeech field is set.
     */
    boolean hasStopSpeech();
    /**
     * <code>.StopSpeech stop_speech = 12;</code>
     * @return The stopSpeech.
     */
    com.amazon.alexa.accessory.protocol.Speech.StopSpeech getStopSpeech();
    /**
     * <code>.StopSpeech stop_speech = 12;</code>
     */
    com.amazon.alexa.accessory.protocol.Speech.StopSpeechOrBuilder getStopSpeechOrBuilder();

    /**
     * <code>.EndpointSpeech endpoint_speech = 13;</code>
     * @return Whether the endpointSpeech field is set.
     */
    boolean hasEndpointSpeech();
    /**
     * <code>.EndpointSpeech endpoint_speech = 13;</code>
     * @return The endpointSpeech.
     */
    com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech getEndpointSpeech();
    /**
     * <code>.EndpointSpeech endpoint_speech = 13;</code>
     */
    com.amazon.alexa.accessory.protocol.Speech.EndpointSpeechOrBuilder getEndpointSpeechOrBuilder();

    /**
     * <code>.NotifySpeechState notify_speech_state = 14;</code>
     * @return Whether the notifySpeechState field is set.
     */
    boolean hasNotifySpeechState();
    /**
     * <code>.NotifySpeechState notify_speech_state = 14;</code>
     * @return The notifySpeechState.
     */
    com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState getNotifySpeechState();
    /**
     * <code>.NotifySpeechState notify_speech_state = 14;</code>
     */
    com.amazon.alexa.accessory.protocol.Speech.NotifySpeechStateOrBuilder getNotifySpeechStateOrBuilder();

    /**
     * <code>.ForwardATCommand forward_at_command = 40;</code>
     * @return Whether the forwardAtCommand field is set.
     */
    boolean hasForwardAtCommand();
    /**
     * <code>.ForwardATCommand forward_at_command = 40;</code>
     * @return The forwardAtCommand.
     */
    com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand getForwardAtCommand();
    /**
     * <code>.ForwardATCommand forward_at_command = 40;</code>
     */
    com.amazon.alexa.accessory.protocol.Calling.ForwardATCommandOrBuilder getForwardAtCommandOrBuilder();

    /**
     * <code>.IncomingCall incoming_call = 41;</code>
     * @return Whether the incomingCall field is set.
     */
    boolean hasIncomingCall();
    /**
     * <code>.IncomingCall incoming_call = 41;</code>
     * @return The incomingCall.
     */
    com.amazon.alexa.accessory.protocol.Calling.IncomingCall getIncomingCall();
    /**
     * <code>.IncomingCall incoming_call = 41;</code>
     */
    com.amazon.alexa.accessory.protocol.Calling.IncomingCallOrBuilder getIncomingCallOrBuilder();

    /**
     * <code>.GetCentralInformation get_central_information = 103;</code>
     * @return Whether the getCentralInformation field is set.
     */
    boolean hasGetCentralInformation();
    /**
     * <code>.GetCentralInformation get_central_information = 103;</code>
     * @return The getCentralInformation.
     */
    com.amazon.alexa.accessory.protocol.Central.GetCentralInformation getGetCentralInformation();
    /**
     * <code>.GetCentralInformation get_central_information = 103;</code>
     */
    com.amazon.alexa.accessory.protocol.Central.GetCentralInformationOrBuilder getGetCentralInformationOrBuilder();

    /**
     * <code>.GetDeviceInformation get_device_information = 20;</code>
     * @return Whether the getDeviceInformation field is set.
     */
    boolean hasGetDeviceInformation();
    /**
     * <code>.GetDeviceInformation get_device_information = 20;</code>
     * @return The getDeviceInformation.
     */
    com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation getGetDeviceInformation();
    /**
     * <code>.GetDeviceInformation get_device_information = 20;</code>
     */
    com.amazon.alexa.accessory.protocol.Device.GetDeviceInformationOrBuilder getGetDeviceInformationOrBuilder();

    /**
     * <code>.GetDeviceConfiguration get_device_configuration = 21;</code>
     * @return Whether the getDeviceConfiguration field is set.
     */
    boolean hasGetDeviceConfiguration();
    /**
     * <code>.GetDeviceConfiguration get_device_configuration = 21;</code>
     * @return The getDeviceConfiguration.
     */
    com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration getGetDeviceConfiguration();
    /**
     * <code>.GetDeviceConfiguration get_device_configuration = 21;</code>
     */
    com.amazon.alexa.accessory.protocol.Device.GetDeviceConfigurationOrBuilder getGetDeviceConfigurationOrBuilder();

    /**
     * <code>.OverrideAssistant override_assistant = 22;</code>
     * @return Whether the overrideAssistant field is set.
     */
    boolean hasOverrideAssistant();
    /**
     * <code>.OverrideAssistant override_assistant = 22;</code>
     * @return The overrideAssistant.
     */
    com.amazon.alexa.accessory.protocol.Device.OverrideAssistant getOverrideAssistant();
    /**
     * <code>.OverrideAssistant override_assistant = 22;</code>
     */
    com.amazon.alexa.accessory.protocol.Device.OverrideAssistantOrBuilder getOverrideAssistantOrBuilder();

    /**
     * <code>.StartSetup start_setup = 23;</code>
     * @return Whether the startSetup field is set.
     */
    boolean hasStartSetup();
    /**
     * <code>.StartSetup start_setup = 23;</code>
     * @return The startSetup.
     */
    com.amazon.alexa.accessory.protocol.Device.StartSetup getStartSetup();
    /**
     * <code>.StartSetup start_setup = 23;</code>
     */
    com.amazon.alexa.accessory.protocol.Device.StartSetupOrBuilder getStartSetupOrBuilder();

    /**
     * <code>.CompleteSetup complete_setup = 24;</code>
     * @return Whether the completeSetup field is set.
     */
    boolean hasCompleteSetup();
    /**
     * <code>.CompleteSetup complete_setup = 24;</code>
     * @return The completeSetup.
     */
    com.amazon.alexa.accessory.protocol.Device.CompleteSetup getCompleteSetup();
    /**
     * <code>.CompleteSetup complete_setup = 24;</code>
     */
    com.amazon.alexa.accessory.protocol.Device.CompleteSetupOrBuilder getCompleteSetupOrBuilder();

    /**
     * <code>.NotifyDeviceConfiguration notify_device_configuration = 25;</code>
     * @return Whether the notifyDeviceConfiguration field is set.
     */
    boolean hasNotifyDeviceConfiguration();
    /**
     * <code>.NotifyDeviceConfiguration notify_device_configuration = 25;</code>
     * @return The notifyDeviceConfiguration.
     */
    com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration getNotifyDeviceConfiguration();
    /**
     * <code>.NotifyDeviceConfiguration notify_device_configuration = 25;</code>
     */
    com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfigurationOrBuilder getNotifyDeviceConfigurationOrBuilder();

    /**
     * <code>.UpdateDeviceInformation update_device_information = 26;</code>
     * @return Whether the updateDeviceInformation field is set.
     */
    boolean hasUpdateDeviceInformation();
    /**
     * <code>.UpdateDeviceInformation update_device_information = 26;</code>
     * @return The updateDeviceInformation.
     */
    com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation getUpdateDeviceInformation();
    /**
     * <code>.UpdateDeviceInformation update_device_information = 26;</code>
     */
    com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformationOrBuilder getUpdateDeviceInformationOrBuilder();

    /**
     * <code>.NotifyDeviceInformation notify_device_information = 27;</code>
     * @return Whether the notifyDeviceInformation field is set.
     */
    boolean hasNotifyDeviceInformation();
    /**
     * <code>.NotifyDeviceInformation notify_device_information = 27;</code>
     * @return The notifyDeviceInformation.
     */
    com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation getNotifyDeviceInformation();
    /**
     * <code>.NotifyDeviceInformation notify_device_information = 27;</code>
     */
    com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformationOrBuilder getNotifyDeviceInformationOrBuilder();

    /**
     * <code>.GetDeviceFeatures get_device_features = 28;</code>
     * @return Whether the getDeviceFeatures field is set.
     */
    boolean hasGetDeviceFeatures();
    /**
     * <code>.GetDeviceFeatures get_device_features = 28;</code>
     * @return The getDeviceFeatures.
     */
    com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures getGetDeviceFeatures();
    /**
     * <code>.GetDeviceFeatures get_device_features = 28;</code>
     */
    com.amazon.alexa.accessory.protocol.Device.GetDeviceFeaturesOrBuilder getGetDeviceFeaturesOrBuilder();

    /**
     * <code>.IssueMediaControl issue_media_control = 60;</code>
     * @return Whether the issueMediaControl field is set.
     */
    boolean hasIssueMediaControl();
    /**
     * <code>.IssueMediaControl issue_media_control = 60;</code>
     * @return The issueMediaControl.
     */
    com.amazon.alexa.accessory.protocol.Media.IssueMediaControl getIssueMediaControl();
    /**
     * <code>.IssueMediaControl issue_media_control = 60;</code>
     */
    com.amazon.alexa.accessory.protocol.Media.IssueMediaControlOrBuilder getIssueMediaControlOrBuilder();

    /**
     * <code>.GetState get_state = 100;</code>
     * @return Whether the getState field is set.
     */
    boolean hasGetState();
    /**
     * <code>.GetState get_state = 100;</code>
     * @return The getState.
     */
    com.amazon.alexa.accessory.protocol.StateOuterClass.GetState getGetState();
    /**
     * <code>.GetState get_state = 100;</code>
     */
    com.amazon.alexa.accessory.protocol.StateOuterClass.GetStateOrBuilder getGetStateOrBuilder();

    /**
     * <code>.SetState set_state = 101;</code>
     * @return Whether the setState field is set.
     */
    boolean hasSetState();
    /**
     * <code>.SetState set_state = 101;</code>
     * @return The setState.
     */
    com.amazon.alexa.accessory.protocol.StateOuterClass.SetState getSetState();
    /**
     * <code>.SetState set_state = 101;</code>
     */
    com.amazon.alexa.accessory.protocol.StateOuterClass.SetStateOrBuilder getSetStateOrBuilder();

    /**
     * <code>.SynchronizeState synchronize_state = 102;</code>
     * @return Whether the synchronizeState field is set.
     */
    boolean hasSynchronizeState();
    /**
     * <code>.SynchronizeState synchronize_state = 102;</code>
     * @return The synchronizeState.
     */
    com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState getSynchronizeState();
    /**
     * <code>.SynchronizeState synchronize_state = 102;</code>
     */
    com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeStateOrBuilder getSynchronizeStateOrBuilder();

    public com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope.PayloadCase getPayloadCase();
  }
  /**
   * Protobuf type {@code ControlEnvelope}
   */
  public static final class ControlEnvelope extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ControlEnvelope)
      ControlEnvelopeOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ControlEnvelope.newBuilder() to construct.
    private ControlEnvelope(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ControlEnvelope() {
      command_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ControlEnvelope();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.amazon.alexa.accessory.protocol.Accessories.internal_static_ControlEnvelope_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.amazon.alexa.accessory.protocol.Accessories.internal_static_ControlEnvelope_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope.class, com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope.Builder.class);
    }

    private int payloadCase_ = 0;
    private java.lang.Object payload_;
    public enum PayloadCase
        implements com.google.protobuf.Internal.EnumLite,
            com.google.protobuf.AbstractMessage.InternalOneOfEnum {
      RESPONSE(9),
      RESET_CONNECTION(51),
      SYNCHRONIZE_SETTINGS(50),
      KEEP_ALIVE(55),
      REMOVE_DEVICE(56),
      GET_LOCALES(57),
      SET_LOCALE(58),
      LAUNCH_APP(59),
      UPGRADE_TRANSPORT(30),
      SWITCH_TRANSPORT(31),
      START_SPEECH(11),
      PROVIDE_SPEECH(10),
      STOP_SPEECH(12),
      ENDPOINT_SPEECH(13),
      NOTIFY_SPEECH_STATE(14),
      FORWARD_AT_COMMAND(40),
      INCOMING_CALL(41),
      GET_CENTRAL_INFORMATION(103),
      GET_DEVICE_INFORMATION(20),
      GET_DEVICE_CONFIGURATION(21),
      OVERRIDE_ASSISTANT(22),
      START_SETUP(23),
      COMPLETE_SETUP(24),
      NOTIFY_DEVICE_CONFIGURATION(25),
      UPDATE_DEVICE_INFORMATION(26),
      NOTIFY_DEVICE_INFORMATION(27),
      GET_DEVICE_FEATURES(28),
      ISSUE_MEDIA_CONTROL(60),
      GET_STATE(100),
      SET_STATE(101),
      SYNCHRONIZE_STATE(102),
      PAYLOAD_NOT_SET(0);
      private final int value;
      private PayloadCase(int value) {
        this.value = value;
      }
      /**
       * @param value The number of the enum to look for.
       * @return The enum associated with the given number.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static PayloadCase valueOf(int value) {
        return forNumber(value);
      }

      public static PayloadCase forNumber(int value) {
        switch (value) {
          case 9: return RESPONSE;
          case 51: return RESET_CONNECTION;
          case 50: return SYNCHRONIZE_SETTINGS;
          case 55: return KEEP_ALIVE;
          case 56: return REMOVE_DEVICE;
          case 57: return GET_LOCALES;
          case 58: return SET_LOCALE;
          case 59: return LAUNCH_APP;
          case 30: return UPGRADE_TRANSPORT;
          case 31: return SWITCH_TRANSPORT;
          case 11: return START_SPEECH;
          case 10: return PROVIDE_SPEECH;
          case 12: return STOP_SPEECH;
          case 13: return ENDPOINT_SPEECH;
          case 14: return NOTIFY_SPEECH_STATE;
          case 40: return FORWARD_AT_COMMAND;
          case 41: return INCOMING_CALL;
          case 103: return GET_CENTRAL_INFORMATION;
          case 20: return GET_DEVICE_INFORMATION;
          case 21: return GET_DEVICE_CONFIGURATION;
          case 22: return OVERRIDE_ASSISTANT;
          case 23: return START_SETUP;
          case 24: return COMPLETE_SETUP;
          case 25: return NOTIFY_DEVICE_CONFIGURATION;
          case 26: return UPDATE_DEVICE_INFORMATION;
          case 27: return NOTIFY_DEVICE_INFORMATION;
          case 28: return GET_DEVICE_FEATURES;
          case 60: return ISSUE_MEDIA_CONTROL;
          case 100: return GET_STATE;
          case 101: return SET_STATE;
          case 102: return SYNCHRONIZE_STATE;
          case 0: return PAYLOAD_NOT_SET;
          default: return null;
        }
      }
      public int getNumber() {
        return this.value;
      }
    };

    public PayloadCase
    getPayloadCase() {
      return PayloadCase.forNumber(
          payloadCase_);
    }

    public static final int COMMAND_FIELD_NUMBER = 1;
    private int command_;
    /**
     * <code>.Command command = 1;</code>
     * @return The enum numeric value on the wire for command.
     */
    @java.lang.Override public int getCommandValue() {
      return command_;
    }
    /**
     * <code>.Command command = 1;</code>
     * @return The command.
     */
    @java.lang.Override public com.amazon.alexa.accessory.protocol.Accessories.Command getCommand() {
      @SuppressWarnings("deprecation")
      com.amazon.alexa.accessory.protocol.Accessories.Command result = com.amazon.alexa.accessory.protocol.Accessories.Command.valueOf(command_);
      return result == null ? com.amazon.alexa.accessory.protocol.Accessories.Command.UNRECOGNIZED : result;
    }

    public static final int RESPONSE_FIELD_NUMBER = 9;
    /**
     * <code>.Response response = 9;</code>
     * @return Whether the response field is set.
     */
    @java.lang.Override
    public boolean hasResponse() {
      return payloadCase_ == 9;
    }
    /**
     * <code>.Response response = 9;</code>
     * @return The response.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Accessories.Response getResponse() {
      if (payloadCase_ == 9) {
         return (com.amazon.alexa.accessory.protocol.Accessories.Response) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Accessories.Response.getDefaultInstance();
    }
    /**
     * <code>.Response response = 9;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Accessories.ResponseOrBuilder getResponseOrBuilder() {
      if (payloadCase_ == 9) {
         return (com.amazon.alexa.accessory.protocol.Accessories.Response) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Accessories.Response.getDefaultInstance();
    }

    public static final int RESET_CONNECTION_FIELD_NUMBER = 51;
    /**
     * <code>.ResetConnection reset_connection = 51;</code>
     * @return Whether the resetConnection field is set.
     */
    @java.lang.Override
    public boolean hasResetConnection() {
      return payloadCase_ == 51;
    }
    /**
     * <code>.ResetConnection reset_connection = 51;</code>
     * @return The resetConnection.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.ResetConnection getResetConnection() {
      if (payloadCase_ == 51) {
         return (com.amazon.alexa.accessory.protocol.System.ResetConnection) payload_;
      }
      return com.amazon.alexa.accessory.protocol.System.ResetConnection.getDefaultInstance();
    }
    /**
     * <code>.ResetConnection reset_connection = 51;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.ResetConnectionOrBuilder getResetConnectionOrBuilder() {
      if (payloadCase_ == 51) {
         return (com.amazon.alexa.accessory.protocol.System.ResetConnection) payload_;
      }
      return com.amazon.alexa.accessory.protocol.System.ResetConnection.getDefaultInstance();
    }

    public static final int SYNCHRONIZE_SETTINGS_FIELD_NUMBER = 50;
    /**
     * <code>.SynchronizeSettings synchronize_settings = 50;</code>
     * @return Whether the synchronizeSettings field is set.
     */
    @java.lang.Override
    public boolean hasSynchronizeSettings() {
      return payloadCase_ == 50;
    }
    /**
     * <code>.SynchronizeSettings synchronize_settings = 50;</code>
     * @return The synchronizeSettings.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.SynchronizeSettings getSynchronizeSettings() {
      if (payloadCase_ == 50) {
         return (com.amazon.alexa.accessory.protocol.System.SynchronizeSettings) payload_;
      }
      return com.amazon.alexa.accessory.protocol.System.SynchronizeSettings.getDefaultInstance();
    }
    /**
     * <code>.SynchronizeSettings synchronize_settings = 50;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.SynchronizeSettingsOrBuilder getSynchronizeSettingsOrBuilder() {
      if (payloadCase_ == 50) {
         return (com.amazon.alexa.accessory.protocol.System.SynchronizeSettings) payload_;
      }
      return com.amazon.alexa.accessory.protocol.System.SynchronizeSettings.getDefaultInstance();
    }

    public static final int KEEP_ALIVE_FIELD_NUMBER = 55;
    /**
     * <code>.KeepAlive keep_alive = 55;</code>
     * @return Whether the keepAlive field is set.
     */
    @java.lang.Override
    public boolean hasKeepAlive() {
      return payloadCase_ == 55;
    }
    /**
     * <code>.KeepAlive keep_alive = 55;</code>
     * @return The keepAlive.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.KeepAlive getKeepAlive() {
      if (payloadCase_ == 55) {
         return (com.amazon.alexa.accessory.protocol.System.KeepAlive) payload_;
      }
      return com.amazon.alexa.accessory.protocol.System.KeepAlive.getDefaultInstance();
    }
    /**
     * <code>.KeepAlive keep_alive = 55;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.KeepAliveOrBuilder getKeepAliveOrBuilder() {
      if (payloadCase_ == 55) {
         return (com.amazon.alexa.accessory.protocol.System.KeepAlive) payload_;
      }
      return com.amazon.alexa.accessory.protocol.System.KeepAlive.getDefaultInstance();
    }

    public static final int REMOVE_DEVICE_FIELD_NUMBER = 56;
    /**
     * <code>.RemoveDevice remove_device = 56;</code>
     * @return Whether the removeDevice field is set.
     */
    @java.lang.Override
    public boolean hasRemoveDevice() {
      return payloadCase_ == 56;
    }
    /**
     * <code>.RemoveDevice remove_device = 56;</code>
     * @return The removeDevice.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.RemoveDevice getRemoveDevice() {
      if (payloadCase_ == 56) {
         return (com.amazon.alexa.accessory.protocol.System.RemoveDevice) payload_;
      }
      return com.amazon.alexa.accessory.protocol.System.RemoveDevice.getDefaultInstance();
    }
    /**
     * <code>.RemoveDevice remove_device = 56;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.RemoveDeviceOrBuilder getRemoveDeviceOrBuilder() {
      if (payloadCase_ == 56) {
         return (com.amazon.alexa.accessory.protocol.System.RemoveDevice) payload_;
      }
      return com.amazon.alexa.accessory.protocol.System.RemoveDevice.getDefaultInstance();
    }

    public static final int GET_LOCALES_FIELD_NUMBER = 57;
    /**
     * <code>.GetLocales get_locales = 57;</code>
     * @return Whether the getLocales field is set.
     */
    @java.lang.Override
    public boolean hasGetLocales() {
      return payloadCase_ == 57;
    }
    /**
     * <code>.GetLocales get_locales = 57;</code>
     * @return The getLocales.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.GetLocales getGetLocales() {
      if (payloadCase_ == 57) {
         return (com.amazon.alexa.accessory.protocol.System.GetLocales) payload_;
      }
      return com.amazon.alexa.accessory.protocol.System.GetLocales.getDefaultInstance();
    }
    /**
     * <code>.GetLocales get_locales = 57;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.GetLocalesOrBuilder getGetLocalesOrBuilder() {
      if (payloadCase_ == 57) {
         return (com.amazon.alexa.accessory.protocol.System.GetLocales) payload_;
      }
      return com.amazon.alexa.accessory.protocol.System.GetLocales.getDefaultInstance();
    }

    public static final int SET_LOCALE_FIELD_NUMBER = 58;
    /**
     * <code>.SetLocale set_locale = 58;</code>
     * @return Whether the setLocale field is set.
     */
    @java.lang.Override
    public boolean hasSetLocale() {
      return payloadCase_ == 58;
    }
    /**
     * <code>.SetLocale set_locale = 58;</code>
     * @return The setLocale.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.SetLocale getSetLocale() {
      if (payloadCase_ == 58) {
         return (com.amazon.alexa.accessory.protocol.System.SetLocale) payload_;
      }
      return com.amazon.alexa.accessory.protocol.System.SetLocale.getDefaultInstance();
    }
    /**
     * <code>.SetLocale set_locale = 58;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.SetLocaleOrBuilder getSetLocaleOrBuilder() {
      if (payloadCase_ == 58) {
         return (com.amazon.alexa.accessory.protocol.System.SetLocale) payload_;
      }
      return com.amazon.alexa.accessory.protocol.System.SetLocale.getDefaultInstance();
    }

    public static final int LAUNCH_APP_FIELD_NUMBER = 59;
    /**
     * <code>.LaunchApp launch_app = 59;</code>
     * @return Whether the launchApp field is set.
     */
    @java.lang.Override
    public boolean hasLaunchApp() {
      return payloadCase_ == 59;
    }
    /**
     * <code>.LaunchApp launch_app = 59;</code>
     * @return The launchApp.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.LaunchApp getLaunchApp() {
      if (payloadCase_ == 59) {
         return (com.amazon.alexa.accessory.protocol.System.LaunchApp) payload_;
      }
      return com.amazon.alexa.accessory.protocol.System.LaunchApp.getDefaultInstance();
    }
    /**
     * <code>.LaunchApp launch_app = 59;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.System.LaunchAppOrBuilder getLaunchAppOrBuilder() {
      if (payloadCase_ == 59) {
         return (com.amazon.alexa.accessory.protocol.System.LaunchApp) payload_;
      }
      return com.amazon.alexa.accessory.protocol.System.LaunchApp.getDefaultInstance();
    }

    public static final int UPGRADE_TRANSPORT_FIELD_NUMBER = 30;
    /**
     * <code>.UpgradeTransport upgrade_transport = 30;</code>
     * @return Whether the upgradeTransport field is set.
     */
    @java.lang.Override
    public boolean hasUpgradeTransport() {
      return payloadCase_ == 30;
    }
    /**
     * <code>.UpgradeTransport upgrade_transport = 30;</code>
     * @return The upgradeTransport.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport getUpgradeTransport() {
      if (payloadCase_ == 30) {
         return (com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport.getDefaultInstance();
    }
    /**
     * <code>.UpgradeTransport upgrade_transport = 30;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Transport.UpgradeTransportOrBuilder getUpgradeTransportOrBuilder() {
      if (payloadCase_ == 30) {
         return (com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport.getDefaultInstance();
    }

    public static final int SWITCH_TRANSPORT_FIELD_NUMBER = 31;
    /**
     * <code>.SwitchTransport switch_transport = 31;</code>
     * @return Whether the switchTransport field is set.
     */
    @java.lang.Override
    public boolean hasSwitchTransport() {
      return payloadCase_ == 31;
    }
    /**
     * <code>.SwitchTransport switch_transport = 31;</code>
     * @return The switchTransport.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Transport.SwitchTransport getSwitchTransport() {
      if (payloadCase_ == 31) {
         return (com.amazon.alexa.accessory.protocol.Transport.SwitchTransport) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Transport.SwitchTransport.getDefaultInstance();
    }
    /**
     * <code>.SwitchTransport switch_transport = 31;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Transport.SwitchTransportOrBuilder getSwitchTransportOrBuilder() {
      if (payloadCase_ == 31) {
         return (com.amazon.alexa.accessory.protocol.Transport.SwitchTransport) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Transport.SwitchTransport.getDefaultInstance();
    }

    public static final int START_SPEECH_FIELD_NUMBER = 11;
    /**
     * <code>.StartSpeech start_speech = 11;</code>
     * @return Whether the startSpeech field is set.
     */
    @java.lang.Override
    public boolean hasStartSpeech() {
      return payloadCase_ == 11;
    }
    /**
     * <code>.StartSpeech start_speech = 11;</code>
     * @return The startSpeech.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.StartSpeech getStartSpeech() {
      if (payloadCase_ == 11) {
         return (com.amazon.alexa.accessory.protocol.Speech.StartSpeech) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Speech.StartSpeech.getDefaultInstance();
    }
    /**
     * <code>.StartSpeech start_speech = 11;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.StartSpeechOrBuilder getStartSpeechOrBuilder() {
      if (payloadCase_ == 11) {
         return (com.amazon.alexa.accessory.protocol.Speech.StartSpeech) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Speech.StartSpeech.getDefaultInstance();
    }

    public static final int PROVIDE_SPEECH_FIELD_NUMBER = 10;
    /**
     * <code>.ProvideSpeech provide_speech = 10;</code>
     * @return Whether the provideSpeech field is set.
     */
    @java.lang.Override
    public boolean hasProvideSpeech() {
      return payloadCase_ == 10;
    }
    /**
     * <code>.ProvideSpeech provide_speech = 10;</code>
     * @return The provideSpeech.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech getProvideSpeech() {
      if (payloadCase_ == 10) {
         return (com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech.getDefaultInstance();
    }
    /**
     * <code>.ProvideSpeech provide_speech = 10;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.ProvideSpeechOrBuilder getProvideSpeechOrBuilder() {
      if (payloadCase_ == 10) {
         return (com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech.getDefaultInstance();
    }

    public static final int STOP_SPEECH_FIELD_NUMBER = 12;
    /**
     * <code>.StopSpeech stop_speech = 12;</code>
     * @return Whether the stopSpeech field is set.
     */
    @java.lang.Override
    public boolean hasStopSpeech() {
      return payloadCase_ == 12;
    }
    /**
     * <code>.StopSpeech stop_speech = 12;</code>
     * @return The stopSpeech.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.StopSpeech getStopSpeech() {
      if (payloadCase_ == 12) {
         return (com.amazon.alexa.accessory.protocol.Speech.StopSpeech) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Speech.StopSpeech.getDefaultInstance();
    }
    /**
     * <code>.StopSpeech stop_speech = 12;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.StopSpeechOrBuilder getStopSpeechOrBuilder() {
      if (payloadCase_ == 12) {
         return (com.amazon.alexa.accessory.protocol.Speech.StopSpeech) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Speech.StopSpeech.getDefaultInstance();
    }

    public static final int ENDPOINT_SPEECH_FIELD_NUMBER = 13;
    /**
     * <code>.EndpointSpeech endpoint_speech = 13;</code>
     * @return Whether the endpointSpeech field is set.
     */
    @java.lang.Override
    public boolean hasEndpointSpeech() {
      return payloadCase_ == 13;
    }
    /**
     * <code>.EndpointSpeech endpoint_speech = 13;</code>
     * @return The endpointSpeech.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech getEndpointSpeech() {
      if (payloadCase_ == 13) {
         return (com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech.getDefaultInstance();
    }
    /**
     * <code>.EndpointSpeech endpoint_speech = 13;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.EndpointSpeechOrBuilder getEndpointSpeechOrBuilder() {
      if (payloadCase_ == 13) {
         return (com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech.getDefaultInstance();
    }

    public static final int NOTIFY_SPEECH_STATE_FIELD_NUMBER = 14;
    /**
     * <code>.NotifySpeechState notify_speech_state = 14;</code>
     * @return Whether the notifySpeechState field is set.
     */
    @java.lang.Override
    public boolean hasNotifySpeechState() {
      return payloadCase_ == 14;
    }
    /**
     * <code>.NotifySpeechState notify_speech_state = 14;</code>
     * @return The notifySpeechState.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState getNotifySpeechState() {
      if (payloadCase_ == 14) {
         return (com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState.getDefaultInstance();
    }
    /**
     * <code>.NotifySpeechState notify_speech_state = 14;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Speech.NotifySpeechStateOrBuilder getNotifySpeechStateOrBuilder() {
      if (payloadCase_ == 14) {
         return (com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState.getDefaultInstance();
    }

    public static final int FORWARD_AT_COMMAND_FIELD_NUMBER = 40;
    /**
     * <code>.ForwardATCommand forward_at_command = 40;</code>
     * @return Whether the forwardAtCommand field is set.
     */
    @java.lang.Override
    public boolean hasForwardAtCommand() {
      return payloadCase_ == 40;
    }
    /**
     * <code>.ForwardATCommand forward_at_command = 40;</code>
     * @return The forwardAtCommand.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand getForwardAtCommand() {
      if (payloadCase_ == 40) {
         return (com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand.getDefaultInstance();
    }
    /**
     * <code>.ForwardATCommand forward_at_command = 40;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Calling.ForwardATCommandOrBuilder getForwardAtCommandOrBuilder() {
      if (payloadCase_ == 40) {
         return (com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand.getDefaultInstance();
    }

    public static final int INCOMING_CALL_FIELD_NUMBER = 41;
    /**
     * <code>.IncomingCall incoming_call = 41;</code>
     * @return Whether the incomingCall field is set.
     */
    @java.lang.Override
    public boolean hasIncomingCall() {
      return payloadCase_ == 41;
    }
    /**
     * <code>.IncomingCall incoming_call = 41;</code>
     * @return The incomingCall.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Calling.IncomingCall getIncomingCall() {
      if (payloadCase_ == 41) {
         return (com.amazon.alexa.accessory.protocol.Calling.IncomingCall) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Calling.IncomingCall.getDefaultInstance();
    }
    /**
     * <code>.IncomingCall incoming_call = 41;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Calling.IncomingCallOrBuilder getIncomingCallOrBuilder() {
      if (payloadCase_ == 41) {
         return (com.amazon.alexa.accessory.protocol.Calling.IncomingCall) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Calling.IncomingCall.getDefaultInstance();
    }

    public static final int GET_CENTRAL_INFORMATION_FIELD_NUMBER = 103;
    /**
     * <code>.GetCentralInformation get_central_information = 103;</code>
     * @return Whether the getCentralInformation field is set.
     */
    @java.lang.Override
    public boolean hasGetCentralInformation() {
      return payloadCase_ == 103;
    }
    /**
     * <code>.GetCentralInformation get_central_information = 103;</code>
     * @return The getCentralInformation.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Central.GetCentralInformation getGetCentralInformation() {
      if (payloadCase_ == 103) {
         return (com.amazon.alexa.accessory.protocol.Central.GetCentralInformation) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Central.GetCentralInformation.getDefaultInstance();
    }
    /**
     * <code>.GetCentralInformation get_central_information = 103;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Central.GetCentralInformationOrBuilder getGetCentralInformationOrBuilder() {
      if (payloadCase_ == 103) {
         return (com.amazon.alexa.accessory.protocol.Central.GetCentralInformation) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Central.GetCentralInformation.getDefaultInstance();
    }

    public static final int GET_DEVICE_INFORMATION_FIELD_NUMBER = 20;
    /**
     * <code>.GetDeviceInformation get_device_information = 20;</code>
     * @return Whether the getDeviceInformation field is set.
     */
    @java.lang.Override
    public boolean hasGetDeviceInformation() {
      return payloadCase_ == 20;
    }
    /**
     * <code>.GetDeviceInformation get_device_information = 20;</code>
     * @return The getDeviceInformation.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation getGetDeviceInformation() {
      if (payloadCase_ == 20) {
         return (com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation.getDefaultInstance();
    }
    /**
     * <code>.GetDeviceInformation get_device_information = 20;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.GetDeviceInformationOrBuilder getGetDeviceInformationOrBuilder() {
      if (payloadCase_ == 20) {
         return (com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation.getDefaultInstance();
    }

    public static final int GET_DEVICE_CONFIGURATION_FIELD_NUMBER = 21;
    /**
     * <code>.GetDeviceConfiguration get_device_configuration = 21;</code>
     * @return Whether the getDeviceConfiguration field is set.
     */
    @java.lang.Override
    public boolean hasGetDeviceConfiguration() {
      return payloadCase_ == 21;
    }
    /**
     * <code>.GetDeviceConfiguration get_device_configuration = 21;</code>
     * @return The getDeviceConfiguration.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration getGetDeviceConfiguration() {
      if (payloadCase_ == 21) {
         return (com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration.getDefaultInstance();
    }
    /**
     * <code>.GetDeviceConfiguration get_device_configuration = 21;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.GetDeviceConfigurationOrBuilder getGetDeviceConfigurationOrBuilder() {
      if (payloadCase_ == 21) {
         return (com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration.getDefaultInstance();
    }

    public static final int OVERRIDE_ASSISTANT_FIELD_NUMBER = 22;
    /**
     * <code>.OverrideAssistant override_assistant = 22;</code>
     * @return Whether the overrideAssistant field is set.
     */
    @java.lang.Override
    public boolean hasOverrideAssistant() {
      return payloadCase_ == 22;
    }
    /**
     * <code>.OverrideAssistant override_assistant = 22;</code>
     * @return The overrideAssistant.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.OverrideAssistant getOverrideAssistant() {
      if (payloadCase_ == 22) {
         return (com.amazon.alexa.accessory.protocol.Device.OverrideAssistant) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.OverrideAssistant.getDefaultInstance();
    }
    /**
     * <code>.OverrideAssistant override_assistant = 22;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.OverrideAssistantOrBuilder getOverrideAssistantOrBuilder() {
      if (payloadCase_ == 22) {
         return (com.amazon.alexa.accessory.protocol.Device.OverrideAssistant) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.OverrideAssistant.getDefaultInstance();
    }

    public static final int START_SETUP_FIELD_NUMBER = 23;
    /**
     * <code>.StartSetup start_setup = 23;</code>
     * @return Whether the startSetup field is set.
     */
    @java.lang.Override
    public boolean hasStartSetup() {
      return payloadCase_ == 23;
    }
    /**
     * <code>.StartSetup start_setup = 23;</code>
     * @return The startSetup.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.StartSetup getStartSetup() {
      if (payloadCase_ == 23) {
         return (com.amazon.alexa.accessory.protocol.Device.StartSetup) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.StartSetup.getDefaultInstance();
    }
    /**
     * <code>.StartSetup start_setup = 23;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.StartSetupOrBuilder getStartSetupOrBuilder() {
      if (payloadCase_ == 23) {
         return (com.amazon.alexa.accessory.protocol.Device.StartSetup) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.StartSetup.getDefaultInstance();
    }

    public static final int COMPLETE_SETUP_FIELD_NUMBER = 24;
    /**
     * <code>.CompleteSetup complete_setup = 24;</code>
     * @return Whether the completeSetup field is set.
     */
    @java.lang.Override
    public boolean hasCompleteSetup() {
      return payloadCase_ == 24;
    }
    /**
     * <code>.CompleteSetup complete_setup = 24;</code>
     * @return The completeSetup.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.CompleteSetup getCompleteSetup() {
      if (payloadCase_ == 24) {
         return (com.amazon.alexa.accessory.protocol.Device.CompleteSetup) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.CompleteSetup.getDefaultInstance();
    }
    /**
     * <code>.CompleteSetup complete_setup = 24;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.CompleteSetupOrBuilder getCompleteSetupOrBuilder() {
      if (payloadCase_ == 24) {
         return (com.amazon.alexa.accessory.protocol.Device.CompleteSetup) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.CompleteSetup.getDefaultInstance();
    }

    public static final int NOTIFY_DEVICE_CONFIGURATION_FIELD_NUMBER = 25;
    /**
     * <code>.NotifyDeviceConfiguration notify_device_configuration = 25;</code>
     * @return Whether the notifyDeviceConfiguration field is set.
     */
    @java.lang.Override
    public boolean hasNotifyDeviceConfiguration() {
      return payloadCase_ == 25;
    }
    /**
     * <code>.NotifyDeviceConfiguration notify_device_configuration = 25;</code>
     * @return The notifyDeviceConfiguration.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration getNotifyDeviceConfiguration() {
      if (payloadCase_ == 25) {
         return (com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration.getDefaultInstance();
    }
    /**
     * <code>.NotifyDeviceConfiguration notify_device_configuration = 25;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfigurationOrBuilder getNotifyDeviceConfigurationOrBuilder() {
      if (payloadCase_ == 25) {
         return (com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration.getDefaultInstance();
    }

    public static final int UPDATE_DEVICE_INFORMATION_FIELD_NUMBER = 26;
    /**
     * <code>.UpdateDeviceInformation update_device_information = 26;</code>
     * @return Whether the updateDeviceInformation field is set.
     */
    @java.lang.Override
    public boolean hasUpdateDeviceInformation() {
      return payloadCase_ == 26;
    }
    /**
     * <code>.UpdateDeviceInformation update_device_information = 26;</code>
     * @return The updateDeviceInformation.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation getUpdateDeviceInformation() {
      if (payloadCase_ == 26) {
         return (com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation.getDefaultInstance();
    }
    /**
     * <code>.UpdateDeviceInformation update_device_information = 26;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformationOrBuilder getUpdateDeviceInformationOrBuilder() {
      if (payloadCase_ == 26) {
         return (com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation.getDefaultInstance();
    }

    public static final int NOTIFY_DEVICE_INFORMATION_FIELD_NUMBER = 27;
    /**
     * <code>.NotifyDeviceInformation notify_device_information = 27;</code>
     * @return Whether the notifyDeviceInformation field is set.
     */
    @java.lang.Override
    public boolean hasNotifyDeviceInformation() {
      return payloadCase_ == 27;
    }
    /**
     * <code>.NotifyDeviceInformation notify_device_information = 27;</code>
     * @return The notifyDeviceInformation.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation getNotifyDeviceInformation() {
      if (payloadCase_ == 27) {
         return (com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation.getDefaultInstance();
    }
    /**
     * <code>.NotifyDeviceInformation notify_device_information = 27;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformationOrBuilder getNotifyDeviceInformationOrBuilder() {
      if (payloadCase_ == 27) {
         return (com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation.getDefaultInstance();
    }

    public static final int GET_DEVICE_FEATURES_FIELD_NUMBER = 28;
    /**
     * <code>.GetDeviceFeatures get_device_features = 28;</code>
     * @return Whether the getDeviceFeatures field is set.
     */
    @java.lang.Override
    public boolean hasGetDeviceFeatures() {
      return payloadCase_ == 28;
    }
    /**
     * <code>.GetDeviceFeatures get_device_features = 28;</code>
     * @return The getDeviceFeatures.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures getGetDeviceFeatures() {
      if (payloadCase_ == 28) {
         return (com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures.getDefaultInstance();
    }
    /**
     * <code>.GetDeviceFeatures get_device_features = 28;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Device.GetDeviceFeaturesOrBuilder getGetDeviceFeaturesOrBuilder() {
      if (payloadCase_ == 28) {
         return (com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures.getDefaultInstance();
    }

    public static final int ISSUE_MEDIA_CONTROL_FIELD_NUMBER = 60;
    /**
     * <code>.IssueMediaControl issue_media_control = 60;</code>
     * @return Whether the issueMediaControl field is set.
     */
    @java.lang.Override
    public boolean hasIssueMediaControl() {
      return payloadCase_ == 60;
    }
    /**
     * <code>.IssueMediaControl issue_media_control = 60;</code>
     * @return The issueMediaControl.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Media.IssueMediaControl getIssueMediaControl() {
      if (payloadCase_ == 60) {
         return (com.amazon.alexa.accessory.protocol.Media.IssueMediaControl) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Media.IssueMediaControl.getDefaultInstance();
    }
    /**
     * <code>.IssueMediaControl issue_media_control = 60;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Media.IssueMediaControlOrBuilder getIssueMediaControlOrBuilder() {
      if (payloadCase_ == 60) {
         return (com.amazon.alexa.accessory.protocol.Media.IssueMediaControl) payload_;
      }
      return com.amazon.alexa.accessory.protocol.Media.IssueMediaControl.getDefaultInstance();
    }

    public static final int GET_STATE_FIELD_NUMBER = 100;
    /**
     * <code>.GetState get_state = 100;</code>
     * @return Whether the getState field is set.
     */
    @java.lang.Override
    public boolean hasGetState() {
      return payloadCase_ == 100;
    }
    /**
     * <code>.GetState get_state = 100;</code>
     * @return The getState.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.StateOuterClass.GetState getGetState() {
      if (payloadCase_ == 100) {
         return (com.amazon.alexa.accessory.protocol.StateOuterClass.GetState) payload_;
      }
      return com.amazon.alexa.accessory.protocol.StateOuterClass.GetState.getDefaultInstance();
    }
    /**
     * <code>.GetState get_state = 100;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.StateOuterClass.GetStateOrBuilder getGetStateOrBuilder() {
      if (payloadCase_ == 100) {
         return (com.amazon.alexa.accessory.protocol.StateOuterClass.GetState) payload_;
      }
      return com.amazon.alexa.accessory.protocol.StateOuterClass.GetState.getDefaultInstance();
    }

    public static final int SET_STATE_FIELD_NUMBER = 101;
    /**
     * <code>.SetState set_state = 101;</code>
     * @return Whether the setState field is set.
     */
    @java.lang.Override
    public boolean hasSetState() {
      return payloadCase_ == 101;
    }
    /**
     * <code>.SetState set_state = 101;</code>
     * @return The setState.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.StateOuterClass.SetState getSetState() {
      if (payloadCase_ == 101) {
         return (com.amazon.alexa.accessory.protocol.StateOuterClass.SetState) payload_;
      }
      return com.amazon.alexa.accessory.protocol.StateOuterClass.SetState.getDefaultInstance();
    }
    /**
     * <code>.SetState set_state = 101;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.StateOuterClass.SetStateOrBuilder getSetStateOrBuilder() {
      if (payloadCase_ == 101) {
         return (com.amazon.alexa.accessory.protocol.StateOuterClass.SetState) payload_;
      }
      return com.amazon.alexa.accessory.protocol.StateOuterClass.SetState.getDefaultInstance();
    }

    public static final int SYNCHRONIZE_STATE_FIELD_NUMBER = 102;
    /**
     * <code>.SynchronizeState synchronize_state = 102;</code>
     * @return Whether the synchronizeState field is set.
     */
    @java.lang.Override
    public boolean hasSynchronizeState() {
      return payloadCase_ == 102;
    }
    /**
     * <code>.SynchronizeState synchronize_state = 102;</code>
     * @return The synchronizeState.
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState getSynchronizeState() {
      if (payloadCase_ == 102) {
         return (com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState) payload_;
      }
      return com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState.getDefaultInstance();
    }
    /**
     * <code>.SynchronizeState synchronize_state = 102;</code>
     */
    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeStateOrBuilder getSynchronizeStateOrBuilder() {
      if (payloadCase_ == 102) {
         return (com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState) payload_;
      }
      return com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState.getDefaultInstance();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (command_ != com.amazon.alexa.accessory.protocol.Accessories.Command.NONE.getNumber()) {
        output.writeEnum(1, command_);
      }
      if (payloadCase_ == 9) {
        output.writeMessage(9, (com.amazon.alexa.accessory.protocol.Accessories.Response) payload_);
      }
      if (payloadCase_ == 10) {
        output.writeMessage(10, (com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech) payload_);
      }
      if (payloadCase_ == 11) {
        output.writeMessage(11, (com.amazon.alexa.accessory.protocol.Speech.StartSpeech) payload_);
      }
      if (payloadCase_ == 12) {
        output.writeMessage(12, (com.amazon.alexa.accessory.protocol.Speech.StopSpeech) payload_);
      }
      if (payloadCase_ == 13) {
        output.writeMessage(13, (com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech) payload_);
      }
      if (payloadCase_ == 14) {
        output.writeMessage(14, (com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState) payload_);
      }
      if (payloadCase_ == 20) {
        output.writeMessage(20, (com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation) payload_);
      }
      if (payloadCase_ == 21) {
        output.writeMessage(21, (com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration) payload_);
      }
      if (payloadCase_ == 22) {
        output.writeMessage(22, (com.amazon.alexa.accessory.protocol.Device.OverrideAssistant) payload_);
      }
      if (payloadCase_ == 23) {
        output.writeMessage(23, (com.amazon.alexa.accessory.protocol.Device.StartSetup) payload_);
      }
      if (payloadCase_ == 24) {
        output.writeMessage(24, (com.amazon.alexa.accessory.protocol.Device.CompleteSetup) payload_);
      }
      if (payloadCase_ == 25) {
        output.writeMessage(25, (com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration) payload_);
      }
      if (payloadCase_ == 26) {
        output.writeMessage(26, (com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation) payload_);
      }
      if (payloadCase_ == 27) {
        output.writeMessage(27, (com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation) payload_);
      }
      if (payloadCase_ == 28) {
        output.writeMessage(28, (com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures) payload_);
      }
      if (payloadCase_ == 30) {
        output.writeMessage(30, (com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport) payload_);
      }
      if (payloadCase_ == 31) {
        output.writeMessage(31, (com.amazon.alexa.accessory.protocol.Transport.SwitchTransport) payload_);
      }
      if (payloadCase_ == 40) {
        output.writeMessage(40, (com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand) payload_);
      }
      if (payloadCase_ == 41) {
        output.writeMessage(41, (com.amazon.alexa.accessory.protocol.Calling.IncomingCall) payload_);
      }
      if (payloadCase_ == 50) {
        output.writeMessage(50, (com.amazon.alexa.accessory.protocol.System.SynchronizeSettings) payload_);
      }
      if (payloadCase_ == 51) {
        output.writeMessage(51, (com.amazon.alexa.accessory.protocol.System.ResetConnection) payload_);
      }
      if (payloadCase_ == 55) {
        output.writeMessage(55, (com.amazon.alexa.accessory.protocol.System.KeepAlive) payload_);
      }
      if (payloadCase_ == 56) {
        output.writeMessage(56, (com.amazon.alexa.accessory.protocol.System.RemoveDevice) payload_);
      }
      if (payloadCase_ == 57) {
        output.writeMessage(57, (com.amazon.alexa.accessory.protocol.System.GetLocales) payload_);
      }
      if (payloadCase_ == 58) {
        output.writeMessage(58, (com.amazon.alexa.accessory.protocol.System.SetLocale) payload_);
      }
      if (payloadCase_ == 59) {
        output.writeMessage(59, (com.amazon.alexa.accessory.protocol.System.LaunchApp) payload_);
      }
      if (payloadCase_ == 60) {
        output.writeMessage(60, (com.amazon.alexa.accessory.protocol.Media.IssueMediaControl) payload_);
      }
      if (payloadCase_ == 100) {
        output.writeMessage(100, (com.amazon.alexa.accessory.protocol.StateOuterClass.GetState) payload_);
      }
      if (payloadCase_ == 101) {
        output.writeMessage(101, (com.amazon.alexa.accessory.protocol.StateOuterClass.SetState) payload_);
      }
      if (payloadCase_ == 102) {
        output.writeMessage(102, (com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState) payload_);
      }
      if (payloadCase_ == 103) {
        output.writeMessage(103, (com.amazon.alexa.accessory.protocol.Central.GetCentralInformation) payload_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (command_ != com.amazon.alexa.accessory.protocol.Accessories.Command.NONE.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, command_);
      }
      if (payloadCase_ == 9) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(9, (com.amazon.alexa.accessory.protocol.Accessories.Response) payload_);
      }
      if (payloadCase_ == 10) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(10, (com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech) payload_);
      }
      if (payloadCase_ == 11) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(11, (com.amazon.alexa.accessory.protocol.Speech.StartSpeech) payload_);
      }
      if (payloadCase_ == 12) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(12, (com.amazon.alexa.accessory.protocol.Speech.StopSpeech) payload_);
      }
      if (payloadCase_ == 13) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(13, (com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech) payload_);
      }
      if (payloadCase_ == 14) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(14, (com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState) payload_);
      }
      if (payloadCase_ == 20) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(20, (com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation) payload_);
      }
      if (payloadCase_ == 21) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(21, (com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration) payload_);
      }
      if (payloadCase_ == 22) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(22, (com.amazon.alexa.accessory.protocol.Device.OverrideAssistant) payload_);
      }
      if (payloadCase_ == 23) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(23, (com.amazon.alexa.accessory.protocol.Device.StartSetup) payload_);
      }
      if (payloadCase_ == 24) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(24, (com.amazon.alexa.accessory.protocol.Device.CompleteSetup) payload_);
      }
      if (payloadCase_ == 25) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(25, (com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration) payload_);
      }
      if (payloadCase_ == 26) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(26, (com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation) payload_);
      }
      if (payloadCase_ == 27) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(27, (com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation) payload_);
      }
      if (payloadCase_ == 28) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(28, (com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures) payload_);
      }
      if (payloadCase_ == 30) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(30, (com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport) payload_);
      }
      if (payloadCase_ == 31) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(31, (com.amazon.alexa.accessory.protocol.Transport.SwitchTransport) payload_);
      }
      if (payloadCase_ == 40) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(40, (com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand) payload_);
      }
      if (payloadCase_ == 41) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(41, (com.amazon.alexa.accessory.protocol.Calling.IncomingCall) payload_);
      }
      if (payloadCase_ == 50) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(50, (com.amazon.alexa.accessory.protocol.System.SynchronizeSettings) payload_);
      }
      if (payloadCase_ == 51) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(51, (com.amazon.alexa.accessory.protocol.System.ResetConnection) payload_);
      }
      if (payloadCase_ == 55) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(55, (com.amazon.alexa.accessory.protocol.System.KeepAlive) payload_);
      }
      if (payloadCase_ == 56) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(56, (com.amazon.alexa.accessory.protocol.System.RemoveDevice) payload_);
      }
      if (payloadCase_ == 57) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(57, (com.amazon.alexa.accessory.protocol.System.GetLocales) payload_);
      }
      if (payloadCase_ == 58) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(58, (com.amazon.alexa.accessory.protocol.System.SetLocale) payload_);
      }
      if (payloadCase_ == 59) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(59, (com.amazon.alexa.accessory.protocol.System.LaunchApp) payload_);
      }
      if (payloadCase_ == 60) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(60, (com.amazon.alexa.accessory.protocol.Media.IssueMediaControl) payload_);
      }
      if (payloadCase_ == 100) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(100, (com.amazon.alexa.accessory.protocol.StateOuterClass.GetState) payload_);
      }
      if (payloadCase_ == 101) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(101, (com.amazon.alexa.accessory.protocol.StateOuterClass.SetState) payload_);
      }
      if (payloadCase_ == 102) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(102, (com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState) payload_);
      }
      if (payloadCase_ == 103) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(103, (com.amazon.alexa.accessory.protocol.Central.GetCentralInformation) payload_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope)) {
        return super.equals(obj);
      }
      com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope other = (com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope) obj;

      if (command_ != other.command_) return false;
      if (!getPayloadCase().equals(other.getPayloadCase())) return false;
      switch (payloadCase_) {
        case 9:
          if (!getResponse()
              .equals(other.getResponse())) return false;
          break;
        case 51:
          if (!getResetConnection()
              .equals(other.getResetConnection())) return false;
          break;
        case 50:
          if (!getSynchronizeSettings()
              .equals(other.getSynchronizeSettings())) return false;
          break;
        case 55:
          if (!getKeepAlive()
              .equals(other.getKeepAlive())) return false;
          break;
        case 56:
          if (!getRemoveDevice()
              .equals(other.getRemoveDevice())) return false;
          break;
        case 57:
          if (!getGetLocales()
              .equals(other.getGetLocales())) return false;
          break;
        case 58:
          if (!getSetLocale()
              .equals(other.getSetLocale())) return false;
          break;
        case 59:
          if (!getLaunchApp()
              .equals(other.getLaunchApp())) return false;
          break;
        case 30:
          if (!getUpgradeTransport()
              .equals(other.getUpgradeTransport())) return false;
          break;
        case 31:
          if (!getSwitchTransport()
              .equals(other.getSwitchTransport())) return false;
          break;
        case 11:
          if (!getStartSpeech()
              .equals(other.getStartSpeech())) return false;
          break;
        case 10:
          if (!getProvideSpeech()
              .equals(other.getProvideSpeech())) return false;
          break;
        case 12:
          if (!getStopSpeech()
              .equals(other.getStopSpeech())) return false;
          break;
        case 13:
          if (!getEndpointSpeech()
              .equals(other.getEndpointSpeech())) return false;
          break;
        case 14:
          if (!getNotifySpeechState()
              .equals(other.getNotifySpeechState())) return false;
          break;
        case 40:
          if (!getForwardAtCommand()
              .equals(other.getForwardAtCommand())) return false;
          break;
        case 41:
          if (!getIncomingCall()
              .equals(other.getIncomingCall())) return false;
          break;
        case 103:
          if (!getGetCentralInformation()
              .equals(other.getGetCentralInformation())) return false;
          break;
        case 20:
          if (!getGetDeviceInformation()
              .equals(other.getGetDeviceInformation())) return false;
          break;
        case 21:
          if (!getGetDeviceConfiguration()
              .equals(other.getGetDeviceConfiguration())) return false;
          break;
        case 22:
          if (!getOverrideAssistant()
              .equals(other.getOverrideAssistant())) return false;
          break;
        case 23:
          if (!getStartSetup()
              .equals(other.getStartSetup())) return false;
          break;
        case 24:
          if (!getCompleteSetup()
              .equals(other.getCompleteSetup())) return false;
          break;
        case 25:
          if (!getNotifyDeviceConfiguration()
              .equals(other.getNotifyDeviceConfiguration())) return false;
          break;
        case 26:
          if (!getUpdateDeviceInformation()
              .equals(other.getUpdateDeviceInformation())) return false;
          break;
        case 27:
          if (!getNotifyDeviceInformation()
              .equals(other.getNotifyDeviceInformation())) return false;
          break;
        case 28:
          if (!getGetDeviceFeatures()
              .equals(other.getGetDeviceFeatures())) return false;
          break;
        case 60:
          if (!getIssueMediaControl()
              .equals(other.getIssueMediaControl())) return false;
          break;
        case 100:
          if (!getGetState()
              .equals(other.getGetState())) return false;
          break;
        case 101:
          if (!getSetState()
              .equals(other.getSetState())) return false;
          break;
        case 102:
          if (!getSynchronizeState()
              .equals(other.getSynchronizeState())) return false;
          break;
        case 0:
        default:
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + COMMAND_FIELD_NUMBER;
      hash = (53 * hash) + command_;
      switch (payloadCase_) {
        case 9:
          hash = (37 * hash) + RESPONSE_FIELD_NUMBER;
          hash = (53 * hash) + getResponse().hashCode();
          break;
        case 51:
          hash = (37 * hash) + RESET_CONNECTION_FIELD_NUMBER;
          hash = (53 * hash) + getResetConnection().hashCode();
          break;
        case 50:
          hash = (37 * hash) + SYNCHRONIZE_SETTINGS_FIELD_NUMBER;
          hash = (53 * hash) + getSynchronizeSettings().hashCode();
          break;
        case 55:
          hash = (37 * hash) + KEEP_ALIVE_FIELD_NUMBER;
          hash = (53 * hash) + getKeepAlive().hashCode();
          break;
        case 56:
          hash = (37 * hash) + REMOVE_DEVICE_FIELD_NUMBER;
          hash = (53 * hash) + getRemoveDevice().hashCode();
          break;
        case 57:
          hash = (37 * hash) + GET_LOCALES_FIELD_NUMBER;
          hash = (53 * hash) + getGetLocales().hashCode();
          break;
        case 58:
          hash = (37 * hash) + SET_LOCALE_FIELD_NUMBER;
          hash = (53 * hash) + getSetLocale().hashCode();
          break;
        case 59:
          hash = (37 * hash) + LAUNCH_APP_FIELD_NUMBER;
          hash = (53 * hash) + getLaunchApp().hashCode();
          break;
        case 30:
          hash = (37 * hash) + UPGRADE_TRANSPORT_FIELD_NUMBER;
          hash = (53 * hash) + getUpgradeTransport().hashCode();
          break;
        case 31:
          hash = (37 * hash) + SWITCH_TRANSPORT_FIELD_NUMBER;
          hash = (53 * hash) + getSwitchTransport().hashCode();
          break;
        case 11:
          hash = (37 * hash) + START_SPEECH_FIELD_NUMBER;
          hash = (53 * hash) + getStartSpeech().hashCode();
          break;
        case 10:
          hash = (37 * hash) + PROVIDE_SPEECH_FIELD_NUMBER;
          hash = (53 * hash) + getProvideSpeech().hashCode();
          break;
        case 12:
          hash = (37 * hash) + STOP_SPEECH_FIELD_NUMBER;
          hash = (53 * hash) + getStopSpeech().hashCode();
          break;
        case 13:
          hash = (37 * hash) + ENDPOINT_SPEECH_FIELD_NUMBER;
          hash = (53 * hash) + getEndpointSpeech().hashCode();
          break;
        case 14:
          hash = (37 * hash) + NOTIFY_SPEECH_STATE_FIELD_NUMBER;
          hash = (53 * hash) + getNotifySpeechState().hashCode();
          break;
        case 40:
          hash = (37 * hash) + FORWARD_AT_COMMAND_FIELD_NUMBER;
          hash = (53 * hash) + getForwardAtCommand().hashCode();
          break;
        case 41:
          hash = (37 * hash) + INCOMING_CALL_FIELD_NUMBER;
          hash = (53 * hash) + getIncomingCall().hashCode();
          break;
        case 103:
          hash = (37 * hash) + GET_CENTRAL_INFORMATION_FIELD_NUMBER;
          hash = (53 * hash) + getGetCentralInformation().hashCode();
          break;
        case 20:
          hash = (37 * hash) + GET_DEVICE_INFORMATION_FIELD_NUMBER;
          hash = (53 * hash) + getGetDeviceInformation().hashCode();
          break;
        case 21:
          hash = (37 * hash) + GET_DEVICE_CONFIGURATION_FIELD_NUMBER;
          hash = (53 * hash) + getGetDeviceConfiguration().hashCode();
          break;
        case 22:
          hash = (37 * hash) + OVERRIDE_ASSISTANT_FIELD_NUMBER;
          hash = (53 * hash) + getOverrideAssistant().hashCode();
          break;
        case 23:
          hash = (37 * hash) + START_SETUP_FIELD_NUMBER;
          hash = (53 * hash) + getStartSetup().hashCode();
          break;
        case 24:
          hash = (37 * hash) + COMPLETE_SETUP_FIELD_NUMBER;
          hash = (53 * hash) + getCompleteSetup().hashCode();
          break;
        case 25:
          hash = (37 * hash) + NOTIFY_DEVICE_CONFIGURATION_FIELD_NUMBER;
          hash = (53 * hash) + getNotifyDeviceConfiguration().hashCode();
          break;
        case 26:
          hash = (37 * hash) + UPDATE_DEVICE_INFORMATION_FIELD_NUMBER;
          hash = (53 * hash) + getUpdateDeviceInformation().hashCode();
          break;
        case 27:
          hash = (37 * hash) + NOTIFY_DEVICE_INFORMATION_FIELD_NUMBER;
          hash = (53 * hash) + getNotifyDeviceInformation().hashCode();
          break;
        case 28:
          hash = (37 * hash) + GET_DEVICE_FEATURES_FIELD_NUMBER;
          hash = (53 * hash) + getGetDeviceFeatures().hashCode();
          break;
        case 60:
          hash = (37 * hash) + ISSUE_MEDIA_CONTROL_FIELD_NUMBER;
          hash = (53 * hash) + getIssueMediaControl().hashCode();
          break;
        case 100:
          hash = (37 * hash) + GET_STATE_FIELD_NUMBER;
          hash = (53 * hash) + getGetState().hashCode();
          break;
        case 101:
          hash = (37 * hash) + SET_STATE_FIELD_NUMBER;
          hash = (53 * hash) + getSetState().hashCode();
          break;
        case 102:
          hash = (37 * hash) + SYNCHRONIZE_STATE_FIELD_NUMBER;
          hash = (53 * hash) + getSynchronizeState().hashCode();
          break;
        case 0:
        default:
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code ControlEnvelope}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ControlEnvelope)
        com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelopeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.amazon.alexa.accessory.protocol.Accessories.internal_static_ControlEnvelope_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.amazon.alexa.accessory.protocol.Accessories.internal_static_ControlEnvelope_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope.class, com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope.Builder.class);
      }

      // Construct using com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        command_ = 0;

        if (responseBuilder_ != null) {
          responseBuilder_.clear();
        }
        if (resetConnectionBuilder_ != null) {
          resetConnectionBuilder_.clear();
        }
        if (synchronizeSettingsBuilder_ != null) {
          synchronizeSettingsBuilder_.clear();
        }
        if (keepAliveBuilder_ != null) {
          keepAliveBuilder_.clear();
        }
        if (removeDeviceBuilder_ != null) {
          removeDeviceBuilder_.clear();
        }
        if (getLocalesBuilder_ != null) {
          getLocalesBuilder_.clear();
        }
        if (setLocaleBuilder_ != null) {
          setLocaleBuilder_.clear();
        }
        if (launchAppBuilder_ != null) {
          launchAppBuilder_.clear();
        }
        if (upgradeTransportBuilder_ != null) {
          upgradeTransportBuilder_.clear();
        }
        if (switchTransportBuilder_ != null) {
          switchTransportBuilder_.clear();
        }
        if (startSpeechBuilder_ != null) {
          startSpeechBuilder_.clear();
        }
        if (provideSpeechBuilder_ != null) {
          provideSpeechBuilder_.clear();
        }
        if (stopSpeechBuilder_ != null) {
          stopSpeechBuilder_.clear();
        }
        if (endpointSpeechBuilder_ != null) {
          endpointSpeechBuilder_.clear();
        }
        if (notifySpeechStateBuilder_ != null) {
          notifySpeechStateBuilder_.clear();
        }
        if (forwardAtCommandBuilder_ != null) {
          forwardAtCommandBuilder_.clear();
        }
        if (incomingCallBuilder_ != null) {
          incomingCallBuilder_.clear();
        }
        if (getCentralInformationBuilder_ != null) {
          getCentralInformationBuilder_.clear();
        }
        if (getDeviceInformationBuilder_ != null) {
          getDeviceInformationBuilder_.clear();
        }
        if (getDeviceConfigurationBuilder_ != null) {
          getDeviceConfigurationBuilder_.clear();
        }
        if (overrideAssistantBuilder_ != null) {
          overrideAssistantBuilder_.clear();
        }
        if (startSetupBuilder_ != null) {
          startSetupBuilder_.clear();
        }
        if (completeSetupBuilder_ != null) {
          completeSetupBuilder_.clear();
        }
        if (notifyDeviceConfigurationBuilder_ != null) {
          notifyDeviceConfigurationBuilder_.clear();
        }
        if (updateDeviceInformationBuilder_ != null) {
          updateDeviceInformationBuilder_.clear();
        }
        if (notifyDeviceInformationBuilder_ != null) {
          notifyDeviceInformationBuilder_.clear();
        }
        if (getDeviceFeaturesBuilder_ != null) {
          getDeviceFeaturesBuilder_.clear();
        }
        if (issueMediaControlBuilder_ != null) {
          issueMediaControlBuilder_.clear();
        }
        if (getStateBuilder_ != null) {
          getStateBuilder_.clear();
        }
        if (setStateBuilder_ != null) {
          setStateBuilder_.clear();
        }
        if (synchronizeStateBuilder_ != null) {
          synchronizeStateBuilder_.clear();
        }
        payloadCase_ = 0;
        payload_ = null;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.amazon.alexa.accessory.protocol.Accessories.internal_static_ControlEnvelope_descriptor;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope getDefaultInstanceForType() {
        return com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope.getDefaultInstance();
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope build() {
        com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope buildPartial() {
        com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope result = new com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope(this);
        result.command_ = command_;
        if (payloadCase_ == 9) {
          if (responseBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = responseBuilder_.build();
          }
        }
        if (payloadCase_ == 51) {
          if (resetConnectionBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = resetConnectionBuilder_.build();
          }
        }
        if (payloadCase_ == 50) {
          if (synchronizeSettingsBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = synchronizeSettingsBuilder_.build();
          }
        }
        if (payloadCase_ == 55) {
          if (keepAliveBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = keepAliveBuilder_.build();
          }
        }
        if (payloadCase_ == 56) {
          if (removeDeviceBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = removeDeviceBuilder_.build();
          }
        }
        if (payloadCase_ == 57) {
          if (getLocalesBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = getLocalesBuilder_.build();
          }
        }
        if (payloadCase_ == 58) {
          if (setLocaleBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = setLocaleBuilder_.build();
          }
        }
        if (payloadCase_ == 59) {
          if (launchAppBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = launchAppBuilder_.build();
          }
        }
        if (payloadCase_ == 30) {
          if (upgradeTransportBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = upgradeTransportBuilder_.build();
          }
        }
        if (payloadCase_ == 31) {
          if (switchTransportBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = switchTransportBuilder_.build();
          }
        }
        if (payloadCase_ == 11) {
          if (startSpeechBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = startSpeechBuilder_.build();
          }
        }
        if (payloadCase_ == 10) {
          if (provideSpeechBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = provideSpeechBuilder_.build();
          }
        }
        if (payloadCase_ == 12) {
          if (stopSpeechBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = stopSpeechBuilder_.build();
          }
        }
        if (payloadCase_ == 13) {
          if (endpointSpeechBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = endpointSpeechBuilder_.build();
          }
        }
        if (payloadCase_ == 14) {
          if (notifySpeechStateBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = notifySpeechStateBuilder_.build();
          }
        }
        if (payloadCase_ == 40) {
          if (forwardAtCommandBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = forwardAtCommandBuilder_.build();
          }
        }
        if (payloadCase_ == 41) {
          if (incomingCallBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = incomingCallBuilder_.build();
          }
        }
        if (payloadCase_ == 103) {
          if (getCentralInformationBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = getCentralInformationBuilder_.build();
          }
        }
        if (payloadCase_ == 20) {
          if (getDeviceInformationBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = getDeviceInformationBuilder_.build();
          }
        }
        if (payloadCase_ == 21) {
          if (getDeviceConfigurationBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = getDeviceConfigurationBuilder_.build();
          }
        }
        if (payloadCase_ == 22) {
          if (overrideAssistantBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = overrideAssistantBuilder_.build();
          }
        }
        if (payloadCase_ == 23) {
          if (startSetupBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = startSetupBuilder_.build();
          }
        }
        if (payloadCase_ == 24) {
          if (completeSetupBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = completeSetupBuilder_.build();
          }
        }
        if (payloadCase_ == 25) {
          if (notifyDeviceConfigurationBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = notifyDeviceConfigurationBuilder_.build();
          }
        }
        if (payloadCase_ == 26) {
          if (updateDeviceInformationBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = updateDeviceInformationBuilder_.build();
          }
        }
        if (payloadCase_ == 27) {
          if (notifyDeviceInformationBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = notifyDeviceInformationBuilder_.build();
          }
        }
        if (payloadCase_ == 28) {
          if (getDeviceFeaturesBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = getDeviceFeaturesBuilder_.build();
          }
        }
        if (payloadCase_ == 60) {
          if (issueMediaControlBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = issueMediaControlBuilder_.build();
          }
        }
        if (payloadCase_ == 100) {
          if (getStateBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = getStateBuilder_.build();
          }
        }
        if (payloadCase_ == 101) {
          if (setStateBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = setStateBuilder_.build();
          }
        }
        if (payloadCase_ == 102) {
          if (synchronizeStateBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = synchronizeStateBuilder_.build();
          }
        }
        result.payloadCase_ = payloadCase_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope) {
          return mergeFrom((com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope other) {
        if (other == com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope.getDefaultInstance()) return this;
        if (other.command_ != 0) {
          setCommandValue(other.getCommandValue());
        }
        switch (other.getPayloadCase()) {
          case RESPONSE: {
            mergeResponse(other.getResponse());
            break;
          }
          case RESET_CONNECTION: {
            mergeResetConnection(other.getResetConnection());
            break;
          }
          case SYNCHRONIZE_SETTINGS: {
            mergeSynchronizeSettings(other.getSynchronizeSettings());
            break;
          }
          case KEEP_ALIVE: {
            mergeKeepAlive(other.getKeepAlive());
            break;
          }
          case REMOVE_DEVICE: {
            mergeRemoveDevice(other.getRemoveDevice());
            break;
          }
          case GET_LOCALES: {
            mergeGetLocales(other.getGetLocales());
            break;
          }
          case SET_LOCALE: {
            mergeSetLocale(other.getSetLocale());
            break;
          }
          case LAUNCH_APP: {
            mergeLaunchApp(other.getLaunchApp());
            break;
          }
          case UPGRADE_TRANSPORT: {
            mergeUpgradeTransport(other.getUpgradeTransport());
            break;
          }
          case SWITCH_TRANSPORT: {
            mergeSwitchTransport(other.getSwitchTransport());
            break;
          }
          case START_SPEECH: {
            mergeStartSpeech(other.getStartSpeech());
            break;
          }
          case PROVIDE_SPEECH: {
            mergeProvideSpeech(other.getProvideSpeech());
            break;
          }
          case STOP_SPEECH: {
            mergeStopSpeech(other.getStopSpeech());
            break;
          }
          case ENDPOINT_SPEECH: {
            mergeEndpointSpeech(other.getEndpointSpeech());
            break;
          }
          case NOTIFY_SPEECH_STATE: {
            mergeNotifySpeechState(other.getNotifySpeechState());
            break;
          }
          case FORWARD_AT_COMMAND: {
            mergeForwardAtCommand(other.getForwardAtCommand());
            break;
          }
          case INCOMING_CALL: {
            mergeIncomingCall(other.getIncomingCall());
            break;
          }
          case GET_CENTRAL_INFORMATION: {
            mergeGetCentralInformation(other.getGetCentralInformation());
            break;
          }
          case GET_DEVICE_INFORMATION: {
            mergeGetDeviceInformation(other.getGetDeviceInformation());
            break;
          }
          case GET_DEVICE_CONFIGURATION: {
            mergeGetDeviceConfiguration(other.getGetDeviceConfiguration());
            break;
          }
          case OVERRIDE_ASSISTANT: {
            mergeOverrideAssistant(other.getOverrideAssistant());
            break;
          }
          case START_SETUP: {
            mergeStartSetup(other.getStartSetup());
            break;
          }
          case COMPLETE_SETUP: {
            mergeCompleteSetup(other.getCompleteSetup());
            break;
          }
          case NOTIFY_DEVICE_CONFIGURATION: {
            mergeNotifyDeviceConfiguration(other.getNotifyDeviceConfiguration());
            break;
          }
          case UPDATE_DEVICE_INFORMATION: {
            mergeUpdateDeviceInformation(other.getUpdateDeviceInformation());
            break;
          }
          case NOTIFY_DEVICE_INFORMATION: {
            mergeNotifyDeviceInformation(other.getNotifyDeviceInformation());
            break;
          }
          case GET_DEVICE_FEATURES: {
            mergeGetDeviceFeatures(other.getGetDeviceFeatures());
            break;
          }
          case ISSUE_MEDIA_CONTROL: {
            mergeIssueMediaControl(other.getIssueMediaControl());
            break;
          }
          case GET_STATE: {
            mergeGetState(other.getGetState());
            break;
          }
          case SET_STATE: {
            mergeSetState(other.getSetState());
            break;
          }
          case SYNCHRONIZE_STATE: {
            mergeSynchronizeState(other.getSynchronizeState());
            break;
          }
          case PAYLOAD_NOT_SET: {
            break;
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                command_ = input.readEnum();

                break;
              } // case 8
              case 74: {
                input.readMessage(
                    getResponseFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 9;
                break;
              } // case 74
              case 82: {
                input.readMessage(
                    getProvideSpeechFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 10;
                break;
              } // case 82
              case 90: {
                input.readMessage(
                    getStartSpeechFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 11;
                break;
              } // case 90
              case 98: {
                input.readMessage(
                    getStopSpeechFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 12;
                break;
              } // case 98
              case 106: {
                input.readMessage(
                    getEndpointSpeechFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 13;
                break;
              } // case 106
              case 114: {
                input.readMessage(
                    getNotifySpeechStateFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 14;
                break;
              } // case 114
              case 162: {
                input.readMessage(
                    getGetDeviceInformationFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 20;
                break;
              } // case 162
              case 170: {
                input.readMessage(
                    getGetDeviceConfigurationFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 21;
                break;
              } // case 170
              case 178: {
                input.readMessage(
                    getOverrideAssistantFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 22;
                break;
              } // case 178
              case 186: {
                input.readMessage(
                    getStartSetupFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 23;
                break;
              } // case 186
              case 194: {
                input.readMessage(
                    getCompleteSetupFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 24;
                break;
              } // case 194
              case 202: {
                input.readMessage(
                    getNotifyDeviceConfigurationFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 25;
                break;
              } // case 202
              case 210: {
                input.readMessage(
                    getUpdateDeviceInformationFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 26;
                break;
              } // case 210
              case 218: {
                input.readMessage(
                    getNotifyDeviceInformationFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 27;
                break;
              } // case 218
              case 226: {
                input.readMessage(
                    getGetDeviceFeaturesFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 28;
                break;
              } // case 226
              case 242: {
                input.readMessage(
                    getUpgradeTransportFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 30;
                break;
              } // case 242
              case 250: {
                input.readMessage(
                    getSwitchTransportFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 31;
                break;
              } // case 250
              case 322: {
                input.readMessage(
                    getForwardAtCommandFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 40;
                break;
              } // case 322
              case 330: {
                input.readMessage(
                    getIncomingCallFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 41;
                break;
              } // case 330
              case 402: {
                input.readMessage(
                    getSynchronizeSettingsFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 50;
                break;
              } // case 402
              case 410: {
                input.readMessage(
                    getResetConnectionFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 51;
                break;
              } // case 410
              case 442: {
                input.readMessage(
                    getKeepAliveFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 55;
                break;
              } // case 442
              case 450: {
                input.readMessage(
                    getRemoveDeviceFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 56;
                break;
              } // case 450
              case 458: {
                input.readMessage(
                    getGetLocalesFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 57;
                break;
              } // case 458
              case 466: {
                input.readMessage(
                    getSetLocaleFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 58;
                break;
              } // case 466
              case 474: {
                input.readMessage(
                    getLaunchAppFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 59;
                break;
              } // case 474
              case 482: {
                input.readMessage(
                    getIssueMediaControlFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 60;
                break;
              } // case 482
              case 802: {
                input.readMessage(
                    getGetStateFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 100;
                break;
              } // case 802
              case 810: {
                input.readMessage(
                    getSetStateFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 101;
                break;
              } // case 810
              case 818: {
                input.readMessage(
                    getSynchronizeStateFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 102;
                break;
              } // case 818
              case 826: {
                input.readMessage(
                    getGetCentralInformationFieldBuilder().getBuilder(),
                    extensionRegistry);
                payloadCase_ = 103;
                break;
              } // case 826
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int payloadCase_ = 0;
      private java.lang.Object payload_;
      public PayloadCase
          getPayloadCase() {
        return PayloadCase.forNumber(
            payloadCase_);
      }

      public Builder clearPayload() {
        payloadCase_ = 0;
        payload_ = null;
        onChanged();
        return this;
      }


      private int command_ = 0;
      /**
       * <code>.Command command = 1;</code>
       * @return The enum numeric value on the wire for command.
       */
      @java.lang.Override public int getCommandValue() {
        return command_;
      }
      /**
       * <code>.Command command = 1;</code>
       * @param value The enum numeric value on the wire for command to set.
       * @return This builder for chaining.
       */
      public Builder setCommandValue(int value) {
        
        command_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>.Command command = 1;</code>
       * @return The command.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Accessories.Command getCommand() {
        @SuppressWarnings("deprecation")
        com.amazon.alexa.accessory.protocol.Accessories.Command result = com.amazon.alexa.accessory.protocol.Accessories.Command.valueOf(command_);
        return result == null ? com.amazon.alexa.accessory.protocol.Accessories.Command.UNRECOGNIZED : result;
      }
      /**
       * <code>.Command command = 1;</code>
       * @param value The command to set.
       * @return This builder for chaining.
       */
      public Builder setCommand(com.amazon.alexa.accessory.protocol.Accessories.Command value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        command_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.Command command = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommand() {
        
        command_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Accessories.Response, com.amazon.alexa.accessory.protocol.Accessories.Response.Builder, com.amazon.alexa.accessory.protocol.Accessories.ResponseOrBuilder> responseBuilder_;
      /**
       * <code>.Response response = 9;</code>
       * @return Whether the response field is set.
       */
      @java.lang.Override
      public boolean hasResponse() {
        return payloadCase_ == 9;
      }
      /**
       * <code>.Response response = 9;</code>
       * @return The response.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Accessories.Response getResponse() {
        if (responseBuilder_ == null) {
          if (payloadCase_ == 9) {
            return (com.amazon.alexa.accessory.protocol.Accessories.Response) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Accessories.Response.getDefaultInstance();
        } else {
          if (payloadCase_ == 9) {
            return responseBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Accessories.Response.getDefaultInstance();
        }
      }
      /**
       * <code>.Response response = 9;</code>
       */
      public Builder setResponse(com.amazon.alexa.accessory.protocol.Accessories.Response value) {
        if (responseBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          responseBuilder_.setMessage(value);
        }
        payloadCase_ = 9;
        return this;
      }
      /**
       * <code>.Response response = 9;</code>
       */
      public Builder setResponse(
          com.amazon.alexa.accessory.protocol.Accessories.Response.Builder builderForValue) {
        if (responseBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          responseBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 9;
        return this;
      }
      /**
       * <code>.Response response = 9;</code>
       */
      public Builder mergeResponse(com.amazon.alexa.accessory.protocol.Accessories.Response value) {
        if (responseBuilder_ == null) {
          if (payloadCase_ == 9 &&
              payload_ != com.amazon.alexa.accessory.protocol.Accessories.Response.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Accessories.Response.newBuilder((com.amazon.alexa.accessory.protocol.Accessories.Response) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 9) {
            responseBuilder_.mergeFrom(value);
          } else {
            responseBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 9;
        return this;
      }
      /**
       * <code>.Response response = 9;</code>
       */
      public Builder clearResponse() {
        if (responseBuilder_ == null) {
          if (payloadCase_ == 9) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 9) {
            payloadCase_ = 0;
            payload_ = null;
          }
          responseBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.Response response = 9;</code>
       */
      public com.amazon.alexa.accessory.protocol.Accessories.Response.Builder getResponseBuilder() {
        return getResponseFieldBuilder().getBuilder();
      }
      /**
       * <code>.Response response = 9;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Accessories.ResponseOrBuilder getResponseOrBuilder() {
        if ((payloadCase_ == 9) && (responseBuilder_ != null)) {
          return responseBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 9) {
            return (com.amazon.alexa.accessory.protocol.Accessories.Response) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Accessories.Response.getDefaultInstance();
        }
      }
      /**
       * <code>.Response response = 9;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Accessories.Response, com.amazon.alexa.accessory.protocol.Accessories.Response.Builder, com.amazon.alexa.accessory.protocol.Accessories.ResponseOrBuilder> 
          getResponseFieldBuilder() {
        if (responseBuilder_ == null) {
          if (!(payloadCase_ == 9)) {
            payload_ = com.amazon.alexa.accessory.protocol.Accessories.Response.getDefaultInstance();
          }
          responseBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Accessories.Response, com.amazon.alexa.accessory.protocol.Accessories.Response.Builder, com.amazon.alexa.accessory.protocol.Accessories.ResponseOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Accessories.Response) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 9;
        onChanged();;
        return responseBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.ResetConnection, com.amazon.alexa.accessory.protocol.System.ResetConnection.Builder, com.amazon.alexa.accessory.protocol.System.ResetConnectionOrBuilder> resetConnectionBuilder_;
      /**
       * <code>.ResetConnection reset_connection = 51;</code>
       * @return Whether the resetConnection field is set.
       */
      @java.lang.Override
      public boolean hasResetConnection() {
        return payloadCase_ == 51;
      }
      /**
       * <code>.ResetConnection reset_connection = 51;</code>
       * @return The resetConnection.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.ResetConnection getResetConnection() {
        if (resetConnectionBuilder_ == null) {
          if (payloadCase_ == 51) {
            return (com.amazon.alexa.accessory.protocol.System.ResetConnection) payload_;
          }
          return com.amazon.alexa.accessory.protocol.System.ResetConnection.getDefaultInstance();
        } else {
          if (payloadCase_ == 51) {
            return resetConnectionBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.System.ResetConnection.getDefaultInstance();
        }
      }
      /**
       * <code>.ResetConnection reset_connection = 51;</code>
       */
      public Builder setResetConnection(com.amazon.alexa.accessory.protocol.System.ResetConnection value) {
        if (resetConnectionBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          resetConnectionBuilder_.setMessage(value);
        }
        payloadCase_ = 51;
        return this;
      }
      /**
       * <code>.ResetConnection reset_connection = 51;</code>
       */
      public Builder setResetConnection(
          com.amazon.alexa.accessory.protocol.System.ResetConnection.Builder builderForValue) {
        if (resetConnectionBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          resetConnectionBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 51;
        return this;
      }
      /**
       * <code>.ResetConnection reset_connection = 51;</code>
       */
      public Builder mergeResetConnection(com.amazon.alexa.accessory.protocol.System.ResetConnection value) {
        if (resetConnectionBuilder_ == null) {
          if (payloadCase_ == 51 &&
              payload_ != com.amazon.alexa.accessory.protocol.System.ResetConnection.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.System.ResetConnection.newBuilder((com.amazon.alexa.accessory.protocol.System.ResetConnection) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 51) {
            resetConnectionBuilder_.mergeFrom(value);
          } else {
            resetConnectionBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 51;
        return this;
      }
      /**
       * <code>.ResetConnection reset_connection = 51;</code>
       */
      public Builder clearResetConnection() {
        if (resetConnectionBuilder_ == null) {
          if (payloadCase_ == 51) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 51) {
            payloadCase_ = 0;
            payload_ = null;
          }
          resetConnectionBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.ResetConnection reset_connection = 51;</code>
       */
      public com.amazon.alexa.accessory.protocol.System.ResetConnection.Builder getResetConnectionBuilder() {
        return getResetConnectionFieldBuilder().getBuilder();
      }
      /**
       * <code>.ResetConnection reset_connection = 51;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.ResetConnectionOrBuilder getResetConnectionOrBuilder() {
        if ((payloadCase_ == 51) && (resetConnectionBuilder_ != null)) {
          return resetConnectionBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 51) {
            return (com.amazon.alexa.accessory.protocol.System.ResetConnection) payload_;
          }
          return com.amazon.alexa.accessory.protocol.System.ResetConnection.getDefaultInstance();
        }
      }
      /**
       * <code>.ResetConnection reset_connection = 51;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.ResetConnection, com.amazon.alexa.accessory.protocol.System.ResetConnection.Builder, com.amazon.alexa.accessory.protocol.System.ResetConnectionOrBuilder> 
          getResetConnectionFieldBuilder() {
        if (resetConnectionBuilder_ == null) {
          if (!(payloadCase_ == 51)) {
            payload_ = com.amazon.alexa.accessory.protocol.System.ResetConnection.getDefaultInstance();
          }
          resetConnectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.System.ResetConnection, com.amazon.alexa.accessory.protocol.System.ResetConnection.Builder, com.amazon.alexa.accessory.protocol.System.ResetConnectionOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.System.ResetConnection) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 51;
        onChanged();;
        return resetConnectionBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.SynchronizeSettings, com.amazon.alexa.accessory.protocol.System.SynchronizeSettings.Builder, com.amazon.alexa.accessory.protocol.System.SynchronizeSettingsOrBuilder> synchronizeSettingsBuilder_;
      /**
       * <code>.SynchronizeSettings synchronize_settings = 50;</code>
       * @return Whether the synchronizeSettings field is set.
       */
      @java.lang.Override
      public boolean hasSynchronizeSettings() {
        return payloadCase_ == 50;
      }
      /**
       * <code>.SynchronizeSettings synchronize_settings = 50;</code>
       * @return The synchronizeSettings.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.SynchronizeSettings getSynchronizeSettings() {
        if (synchronizeSettingsBuilder_ == null) {
          if (payloadCase_ == 50) {
            return (com.amazon.alexa.accessory.protocol.System.SynchronizeSettings) payload_;
          }
          return com.amazon.alexa.accessory.protocol.System.SynchronizeSettings.getDefaultInstance();
        } else {
          if (payloadCase_ == 50) {
            return synchronizeSettingsBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.System.SynchronizeSettings.getDefaultInstance();
        }
      }
      /**
       * <code>.SynchronizeSettings synchronize_settings = 50;</code>
       */
      public Builder setSynchronizeSettings(com.amazon.alexa.accessory.protocol.System.SynchronizeSettings value) {
        if (synchronizeSettingsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          synchronizeSettingsBuilder_.setMessage(value);
        }
        payloadCase_ = 50;
        return this;
      }
      /**
       * <code>.SynchronizeSettings synchronize_settings = 50;</code>
       */
      public Builder setSynchronizeSettings(
          com.amazon.alexa.accessory.protocol.System.SynchronizeSettings.Builder builderForValue) {
        if (synchronizeSettingsBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          synchronizeSettingsBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 50;
        return this;
      }
      /**
       * <code>.SynchronizeSettings synchronize_settings = 50;</code>
       */
      public Builder mergeSynchronizeSettings(com.amazon.alexa.accessory.protocol.System.SynchronizeSettings value) {
        if (synchronizeSettingsBuilder_ == null) {
          if (payloadCase_ == 50 &&
              payload_ != com.amazon.alexa.accessory.protocol.System.SynchronizeSettings.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.System.SynchronizeSettings.newBuilder((com.amazon.alexa.accessory.protocol.System.SynchronizeSettings) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 50) {
            synchronizeSettingsBuilder_.mergeFrom(value);
          } else {
            synchronizeSettingsBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 50;
        return this;
      }
      /**
       * <code>.SynchronizeSettings synchronize_settings = 50;</code>
       */
      public Builder clearSynchronizeSettings() {
        if (synchronizeSettingsBuilder_ == null) {
          if (payloadCase_ == 50) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 50) {
            payloadCase_ = 0;
            payload_ = null;
          }
          synchronizeSettingsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.SynchronizeSettings synchronize_settings = 50;</code>
       */
      public com.amazon.alexa.accessory.protocol.System.SynchronizeSettings.Builder getSynchronizeSettingsBuilder() {
        return getSynchronizeSettingsFieldBuilder().getBuilder();
      }
      /**
       * <code>.SynchronizeSettings synchronize_settings = 50;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.SynchronizeSettingsOrBuilder getSynchronizeSettingsOrBuilder() {
        if ((payloadCase_ == 50) && (synchronizeSettingsBuilder_ != null)) {
          return synchronizeSettingsBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 50) {
            return (com.amazon.alexa.accessory.protocol.System.SynchronizeSettings) payload_;
          }
          return com.amazon.alexa.accessory.protocol.System.SynchronizeSettings.getDefaultInstance();
        }
      }
      /**
       * <code>.SynchronizeSettings synchronize_settings = 50;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.SynchronizeSettings, com.amazon.alexa.accessory.protocol.System.SynchronizeSettings.Builder, com.amazon.alexa.accessory.protocol.System.SynchronizeSettingsOrBuilder> 
          getSynchronizeSettingsFieldBuilder() {
        if (synchronizeSettingsBuilder_ == null) {
          if (!(payloadCase_ == 50)) {
            payload_ = com.amazon.alexa.accessory.protocol.System.SynchronizeSettings.getDefaultInstance();
          }
          synchronizeSettingsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.System.SynchronizeSettings, com.amazon.alexa.accessory.protocol.System.SynchronizeSettings.Builder, com.amazon.alexa.accessory.protocol.System.SynchronizeSettingsOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.System.SynchronizeSettings) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 50;
        onChanged();;
        return synchronizeSettingsBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.KeepAlive, com.amazon.alexa.accessory.protocol.System.KeepAlive.Builder, com.amazon.alexa.accessory.protocol.System.KeepAliveOrBuilder> keepAliveBuilder_;
      /**
       * <code>.KeepAlive keep_alive = 55;</code>
       * @return Whether the keepAlive field is set.
       */
      @java.lang.Override
      public boolean hasKeepAlive() {
        return payloadCase_ == 55;
      }
      /**
       * <code>.KeepAlive keep_alive = 55;</code>
       * @return The keepAlive.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.KeepAlive getKeepAlive() {
        if (keepAliveBuilder_ == null) {
          if (payloadCase_ == 55) {
            return (com.amazon.alexa.accessory.protocol.System.KeepAlive) payload_;
          }
          return com.amazon.alexa.accessory.protocol.System.KeepAlive.getDefaultInstance();
        } else {
          if (payloadCase_ == 55) {
            return keepAliveBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.System.KeepAlive.getDefaultInstance();
        }
      }
      /**
       * <code>.KeepAlive keep_alive = 55;</code>
       */
      public Builder setKeepAlive(com.amazon.alexa.accessory.protocol.System.KeepAlive value) {
        if (keepAliveBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          keepAliveBuilder_.setMessage(value);
        }
        payloadCase_ = 55;
        return this;
      }
      /**
       * <code>.KeepAlive keep_alive = 55;</code>
       */
      public Builder setKeepAlive(
          com.amazon.alexa.accessory.protocol.System.KeepAlive.Builder builderForValue) {
        if (keepAliveBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          keepAliveBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 55;
        return this;
      }
      /**
       * <code>.KeepAlive keep_alive = 55;</code>
       */
      public Builder mergeKeepAlive(com.amazon.alexa.accessory.protocol.System.KeepAlive value) {
        if (keepAliveBuilder_ == null) {
          if (payloadCase_ == 55 &&
              payload_ != com.amazon.alexa.accessory.protocol.System.KeepAlive.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.System.KeepAlive.newBuilder((com.amazon.alexa.accessory.protocol.System.KeepAlive) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 55) {
            keepAliveBuilder_.mergeFrom(value);
          } else {
            keepAliveBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 55;
        return this;
      }
      /**
       * <code>.KeepAlive keep_alive = 55;</code>
       */
      public Builder clearKeepAlive() {
        if (keepAliveBuilder_ == null) {
          if (payloadCase_ == 55) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 55) {
            payloadCase_ = 0;
            payload_ = null;
          }
          keepAliveBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.KeepAlive keep_alive = 55;</code>
       */
      public com.amazon.alexa.accessory.protocol.System.KeepAlive.Builder getKeepAliveBuilder() {
        return getKeepAliveFieldBuilder().getBuilder();
      }
      /**
       * <code>.KeepAlive keep_alive = 55;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.KeepAliveOrBuilder getKeepAliveOrBuilder() {
        if ((payloadCase_ == 55) && (keepAliveBuilder_ != null)) {
          return keepAliveBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 55) {
            return (com.amazon.alexa.accessory.protocol.System.KeepAlive) payload_;
          }
          return com.amazon.alexa.accessory.protocol.System.KeepAlive.getDefaultInstance();
        }
      }
      /**
       * <code>.KeepAlive keep_alive = 55;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.KeepAlive, com.amazon.alexa.accessory.protocol.System.KeepAlive.Builder, com.amazon.alexa.accessory.protocol.System.KeepAliveOrBuilder> 
          getKeepAliveFieldBuilder() {
        if (keepAliveBuilder_ == null) {
          if (!(payloadCase_ == 55)) {
            payload_ = com.amazon.alexa.accessory.protocol.System.KeepAlive.getDefaultInstance();
          }
          keepAliveBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.System.KeepAlive, com.amazon.alexa.accessory.protocol.System.KeepAlive.Builder, com.amazon.alexa.accessory.protocol.System.KeepAliveOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.System.KeepAlive) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 55;
        onChanged();;
        return keepAliveBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.RemoveDevice, com.amazon.alexa.accessory.protocol.System.RemoveDevice.Builder, com.amazon.alexa.accessory.protocol.System.RemoveDeviceOrBuilder> removeDeviceBuilder_;
      /**
       * <code>.RemoveDevice remove_device = 56;</code>
       * @return Whether the removeDevice field is set.
       */
      @java.lang.Override
      public boolean hasRemoveDevice() {
        return payloadCase_ == 56;
      }
      /**
       * <code>.RemoveDevice remove_device = 56;</code>
       * @return The removeDevice.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.RemoveDevice getRemoveDevice() {
        if (removeDeviceBuilder_ == null) {
          if (payloadCase_ == 56) {
            return (com.amazon.alexa.accessory.protocol.System.RemoveDevice) payload_;
          }
          return com.amazon.alexa.accessory.protocol.System.RemoveDevice.getDefaultInstance();
        } else {
          if (payloadCase_ == 56) {
            return removeDeviceBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.System.RemoveDevice.getDefaultInstance();
        }
      }
      /**
       * <code>.RemoveDevice remove_device = 56;</code>
       */
      public Builder setRemoveDevice(com.amazon.alexa.accessory.protocol.System.RemoveDevice value) {
        if (removeDeviceBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          removeDeviceBuilder_.setMessage(value);
        }
        payloadCase_ = 56;
        return this;
      }
      /**
       * <code>.RemoveDevice remove_device = 56;</code>
       */
      public Builder setRemoveDevice(
          com.amazon.alexa.accessory.protocol.System.RemoveDevice.Builder builderForValue) {
        if (removeDeviceBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          removeDeviceBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 56;
        return this;
      }
      /**
       * <code>.RemoveDevice remove_device = 56;</code>
       */
      public Builder mergeRemoveDevice(com.amazon.alexa.accessory.protocol.System.RemoveDevice value) {
        if (removeDeviceBuilder_ == null) {
          if (payloadCase_ == 56 &&
              payload_ != com.amazon.alexa.accessory.protocol.System.RemoveDevice.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.System.RemoveDevice.newBuilder((com.amazon.alexa.accessory.protocol.System.RemoveDevice) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 56) {
            removeDeviceBuilder_.mergeFrom(value);
          } else {
            removeDeviceBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 56;
        return this;
      }
      /**
       * <code>.RemoveDevice remove_device = 56;</code>
       */
      public Builder clearRemoveDevice() {
        if (removeDeviceBuilder_ == null) {
          if (payloadCase_ == 56) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 56) {
            payloadCase_ = 0;
            payload_ = null;
          }
          removeDeviceBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.RemoveDevice remove_device = 56;</code>
       */
      public com.amazon.alexa.accessory.protocol.System.RemoveDevice.Builder getRemoveDeviceBuilder() {
        return getRemoveDeviceFieldBuilder().getBuilder();
      }
      /**
       * <code>.RemoveDevice remove_device = 56;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.RemoveDeviceOrBuilder getRemoveDeviceOrBuilder() {
        if ((payloadCase_ == 56) && (removeDeviceBuilder_ != null)) {
          return removeDeviceBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 56) {
            return (com.amazon.alexa.accessory.protocol.System.RemoveDevice) payload_;
          }
          return com.amazon.alexa.accessory.protocol.System.RemoveDevice.getDefaultInstance();
        }
      }
      /**
       * <code>.RemoveDevice remove_device = 56;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.RemoveDevice, com.amazon.alexa.accessory.protocol.System.RemoveDevice.Builder, com.amazon.alexa.accessory.protocol.System.RemoveDeviceOrBuilder> 
          getRemoveDeviceFieldBuilder() {
        if (removeDeviceBuilder_ == null) {
          if (!(payloadCase_ == 56)) {
            payload_ = com.amazon.alexa.accessory.protocol.System.RemoveDevice.getDefaultInstance();
          }
          removeDeviceBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.System.RemoveDevice, com.amazon.alexa.accessory.protocol.System.RemoveDevice.Builder, com.amazon.alexa.accessory.protocol.System.RemoveDeviceOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.System.RemoveDevice) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 56;
        onChanged();;
        return removeDeviceBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.GetLocales, com.amazon.alexa.accessory.protocol.System.GetLocales.Builder, com.amazon.alexa.accessory.protocol.System.GetLocalesOrBuilder> getLocalesBuilder_;
      /**
       * <code>.GetLocales get_locales = 57;</code>
       * @return Whether the getLocales field is set.
       */
      @java.lang.Override
      public boolean hasGetLocales() {
        return payloadCase_ == 57;
      }
      /**
       * <code>.GetLocales get_locales = 57;</code>
       * @return The getLocales.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.GetLocales getGetLocales() {
        if (getLocalesBuilder_ == null) {
          if (payloadCase_ == 57) {
            return (com.amazon.alexa.accessory.protocol.System.GetLocales) payload_;
          }
          return com.amazon.alexa.accessory.protocol.System.GetLocales.getDefaultInstance();
        } else {
          if (payloadCase_ == 57) {
            return getLocalesBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.System.GetLocales.getDefaultInstance();
        }
      }
      /**
       * <code>.GetLocales get_locales = 57;</code>
       */
      public Builder setGetLocales(com.amazon.alexa.accessory.protocol.System.GetLocales value) {
        if (getLocalesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          getLocalesBuilder_.setMessage(value);
        }
        payloadCase_ = 57;
        return this;
      }
      /**
       * <code>.GetLocales get_locales = 57;</code>
       */
      public Builder setGetLocales(
          com.amazon.alexa.accessory.protocol.System.GetLocales.Builder builderForValue) {
        if (getLocalesBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          getLocalesBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 57;
        return this;
      }
      /**
       * <code>.GetLocales get_locales = 57;</code>
       */
      public Builder mergeGetLocales(com.amazon.alexa.accessory.protocol.System.GetLocales value) {
        if (getLocalesBuilder_ == null) {
          if (payloadCase_ == 57 &&
              payload_ != com.amazon.alexa.accessory.protocol.System.GetLocales.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.System.GetLocales.newBuilder((com.amazon.alexa.accessory.protocol.System.GetLocales) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 57) {
            getLocalesBuilder_.mergeFrom(value);
          } else {
            getLocalesBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 57;
        return this;
      }
      /**
       * <code>.GetLocales get_locales = 57;</code>
       */
      public Builder clearGetLocales() {
        if (getLocalesBuilder_ == null) {
          if (payloadCase_ == 57) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 57) {
            payloadCase_ = 0;
            payload_ = null;
          }
          getLocalesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.GetLocales get_locales = 57;</code>
       */
      public com.amazon.alexa.accessory.protocol.System.GetLocales.Builder getGetLocalesBuilder() {
        return getGetLocalesFieldBuilder().getBuilder();
      }
      /**
       * <code>.GetLocales get_locales = 57;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.GetLocalesOrBuilder getGetLocalesOrBuilder() {
        if ((payloadCase_ == 57) && (getLocalesBuilder_ != null)) {
          return getLocalesBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 57) {
            return (com.amazon.alexa.accessory.protocol.System.GetLocales) payload_;
          }
          return com.amazon.alexa.accessory.protocol.System.GetLocales.getDefaultInstance();
        }
      }
      /**
       * <code>.GetLocales get_locales = 57;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.GetLocales, com.amazon.alexa.accessory.protocol.System.GetLocales.Builder, com.amazon.alexa.accessory.protocol.System.GetLocalesOrBuilder> 
          getGetLocalesFieldBuilder() {
        if (getLocalesBuilder_ == null) {
          if (!(payloadCase_ == 57)) {
            payload_ = com.amazon.alexa.accessory.protocol.System.GetLocales.getDefaultInstance();
          }
          getLocalesBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.System.GetLocales, com.amazon.alexa.accessory.protocol.System.GetLocales.Builder, com.amazon.alexa.accessory.protocol.System.GetLocalesOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.System.GetLocales) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 57;
        onChanged();;
        return getLocalesBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.SetLocale, com.amazon.alexa.accessory.protocol.System.SetLocale.Builder, com.amazon.alexa.accessory.protocol.System.SetLocaleOrBuilder> setLocaleBuilder_;
      /**
       * <code>.SetLocale set_locale = 58;</code>
       * @return Whether the setLocale field is set.
       */
      @java.lang.Override
      public boolean hasSetLocale() {
        return payloadCase_ == 58;
      }
      /**
       * <code>.SetLocale set_locale = 58;</code>
       * @return The setLocale.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.SetLocale getSetLocale() {
        if (setLocaleBuilder_ == null) {
          if (payloadCase_ == 58) {
            return (com.amazon.alexa.accessory.protocol.System.SetLocale) payload_;
          }
          return com.amazon.alexa.accessory.protocol.System.SetLocale.getDefaultInstance();
        } else {
          if (payloadCase_ == 58) {
            return setLocaleBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.System.SetLocale.getDefaultInstance();
        }
      }
      /**
       * <code>.SetLocale set_locale = 58;</code>
       */
      public Builder setSetLocale(com.amazon.alexa.accessory.protocol.System.SetLocale value) {
        if (setLocaleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          setLocaleBuilder_.setMessage(value);
        }
        payloadCase_ = 58;
        return this;
      }
      /**
       * <code>.SetLocale set_locale = 58;</code>
       */
      public Builder setSetLocale(
          com.amazon.alexa.accessory.protocol.System.SetLocale.Builder builderForValue) {
        if (setLocaleBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          setLocaleBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 58;
        return this;
      }
      /**
       * <code>.SetLocale set_locale = 58;</code>
       */
      public Builder mergeSetLocale(com.amazon.alexa.accessory.protocol.System.SetLocale value) {
        if (setLocaleBuilder_ == null) {
          if (payloadCase_ == 58 &&
              payload_ != com.amazon.alexa.accessory.protocol.System.SetLocale.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.System.SetLocale.newBuilder((com.amazon.alexa.accessory.protocol.System.SetLocale) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 58) {
            setLocaleBuilder_.mergeFrom(value);
          } else {
            setLocaleBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 58;
        return this;
      }
      /**
       * <code>.SetLocale set_locale = 58;</code>
       */
      public Builder clearSetLocale() {
        if (setLocaleBuilder_ == null) {
          if (payloadCase_ == 58) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 58) {
            payloadCase_ = 0;
            payload_ = null;
          }
          setLocaleBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.SetLocale set_locale = 58;</code>
       */
      public com.amazon.alexa.accessory.protocol.System.SetLocale.Builder getSetLocaleBuilder() {
        return getSetLocaleFieldBuilder().getBuilder();
      }
      /**
       * <code>.SetLocale set_locale = 58;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.SetLocaleOrBuilder getSetLocaleOrBuilder() {
        if ((payloadCase_ == 58) && (setLocaleBuilder_ != null)) {
          return setLocaleBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 58) {
            return (com.amazon.alexa.accessory.protocol.System.SetLocale) payload_;
          }
          return com.amazon.alexa.accessory.protocol.System.SetLocale.getDefaultInstance();
        }
      }
      /**
       * <code>.SetLocale set_locale = 58;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.SetLocale, com.amazon.alexa.accessory.protocol.System.SetLocale.Builder, com.amazon.alexa.accessory.protocol.System.SetLocaleOrBuilder> 
          getSetLocaleFieldBuilder() {
        if (setLocaleBuilder_ == null) {
          if (!(payloadCase_ == 58)) {
            payload_ = com.amazon.alexa.accessory.protocol.System.SetLocale.getDefaultInstance();
          }
          setLocaleBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.System.SetLocale, com.amazon.alexa.accessory.protocol.System.SetLocale.Builder, com.amazon.alexa.accessory.protocol.System.SetLocaleOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.System.SetLocale) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 58;
        onChanged();;
        return setLocaleBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.LaunchApp, com.amazon.alexa.accessory.protocol.System.LaunchApp.Builder, com.amazon.alexa.accessory.protocol.System.LaunchAppOrBuilder> launchAppBuilder_;
      /**
       * <code>.LaunchApp launch_app = 59;</code>
       * @return Whether the launchApp field is set.
       */
      @java.lang.Override
      public boolean hasLaunchApp() {
        return payloadCase_ == 59;
      }
      /**
       * <code>.LaunchApp launch_app = 59;</code>
       * @return The launchApp.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.LaunchApp getLaunchApp() {
        if (launchAppBuilder_ == null) {
          if (payloadCase_ == 59) {
            return (com.amazon.alexa.accessory.protocol.System.LaunchApp) payload_;
          }
          return com.amazon.alexa.accessory.protocol.System.LaunchApp.getDefaultInstance();
        } else {
          if (payloadCase_ == 59) {
            return launchAppBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.System.LaunchApp.getDefaultInstance();
        }
      }
      /**
       * <code>.LaunchApp launch_app = 59;</code>
       */
      public Builder setLaunchApp(com.amazon.alexa.accessory.protocol.System.LaunchApp value) {
        if (launchAppBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          launchAppBuilder_.setMessage(value);
        }
        payloadCase_ = 59;
        return this;
      }
      /**
       * <code>.LaunchApp launch_app = 59;</code>
       */
      public Builder setLaunchApp(
          com.amazon.alexa.accessory.protocol.System.LaunchApp.Builder builderForValue) {
        if (launchAppBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          launchAppBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 59;
        return this;
      }
      /**
       * <code>.LaunchApp launch_app = 59;</code>
       */
      public Builder mergeLaunchApp(com.amazon.alexa.accessory.protocol.System.LaunchApp value) {
        if (launchAppBuilder_ == null) {
          if (payloadCase_ == 59 &&
              payload_ != com.amazon.alexa.accessory.protocol.System.LaunchApp.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.System.LaunchApp.newBuilder((com.amazon.alexa.accessory.protocol.System.LaunchApp) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 59) {
            launchAppBuilder_.mergeFrom(value);
          } else {
            launchAppBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 59;
        return this;
      }
      /**
       * <code>.LaunchApp launch_app = 59;</code>
       */
      public Builder clearLaunchApp() {
        if (launchAppBuilder_ == null) {
          if (payloadCase_ == 59) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 59) {
            payloadCase_ = 0;
            payload_ = null;
          }
          launchAppBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.LaunchApp launch_app = 59;</code>
       */
      public com.amazon.alexa.accessory.protocol.System.LaunchApp.Builder getLaunchAppBuilder() {
        return getLaunchAppFieldBuilder().getBuilder();
      }
      /**
       * <code>.LaunchApp launch_app = 59;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.System.LaunchAppOrBuilder getLaunchAppOrBuilder() {
        if ((payloadCase_ == 59) && (launchAppBuilder_ != null)) {
          return launchAppBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 59) {
            return (com.amazon.alexa.accessory.protocol.System.LaunchApp) payload_;
          }
          return com.amazon.alexa.accessory.protocol.System.LaunchApp.getDefaultInstance();
        }
      }
      /**
       * <code>.LaunchApp launch_app = 59;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.System.LaunchApp, com.amazon.alexa.accessory.protocol.System.LaunchApp.Builder, com.amazon.alexa.accessory.protocol.System.LaunchAppOrBuilder> 
          getLaunchAppFieldBuilder() {
        if (launchAppBuilder_ == null) {
          if (!(payloadCase_ == 59)) {
            payload_ = com.amazon.alexa.accessory.protocol.System.LaunchApp.getDefaultInstance();
          }
          launchAppBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.System.LaunchApp, com.amazon.alexa.accessory.protocol.System.LaunchApp.Builder, com.amazon.alexa.accessory.protocol.System.LaunchAppOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.System.LaunchApp) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 59;
        onChanged();;
        return launchAppBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport, com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport.Builder, com.amazon.alexa.accessory.protocol.Transport.UpgradeTransportOrBuilder> upgradeTransportBuilder_;
      /**
       * <code>.UpgradeTransport upgrade_transport = 30;</code>
       * @return Whether the upgradeTransport field is set.
       */
      @java.lang.Override
      public boolean hasUpgradeTransport() {
        return payloadCase_ == 30;
      }
      /**
       * <code>.UpgradeTransport upgrade_transport = 30;</code>
       * @return The upgradeTransport.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport getUpgradeTransport() {
        if (upgradeTransportBuilder_ == null) {
          if (payloadCase_ == 30) {
            return (com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport.getDefaultInstance();
        } else {
          if (payloadCase_ == 30) {
            return upgradeTransportBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport.getDefaultInstance();
        }
      }
      /**
       * <code>.UpgradeTransport upgrade_transport = 30;</code>
       */
      public Builder setUpgradeTransport(com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport value) {
        if (upgradeTransportBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          upgradeTransportBuilder_.setMessage(value);
        }
        payloadCase_ = 30;
        return this;
      }
      /**
       * <code>.UpgradeTransport upgrade_transport = 30;</code>
       */
      public Builder setUpgradeTransport(
          com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport.Builder builderForValue) {
        if (upgradeTransportBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          upgradeTransportBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 30;
        return this;
      }
      /**
       * <code>.UpgradeTransport upgrade_transport = 30;</code>
       */
      public Builder mergeUpgradeTransport(com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport value) {
        if (upgradeTransportBuilder_ == null) {
          if (payloadCase_ == 30 &&
              payload_ != com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport.newBuilder((com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 30) {
            upgradeTransportBuilder_.mergeFrom(value);
          } else {
            upgradeTransportBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 30;
        return this;
      }
      /**
       * <code>.UpgradeTransport upgrade_transport = 30;</code>
       */
      public Builder clearUpgradeTransport() {
        if (upgradeTransportBuilder_ == null) {
          if (payloadCase_ == 30) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 30) {
            payloadCase_ = 0;
            payload_ = null;
          }
          upgradeTransportBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.UpgradeTransport upgrade_transport = 30;</code>
       */
      public com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport.Builder getUpgradeTransportBuilder() {
        return getUpgradeTransportFieldBuilder().getBuilder();
      }
      /**
       * <code>.UpgradeTransport upgrade_transport = 30;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Transport.UpgradeTransportOrBuilder getUpgradeTransportOrBuilder() {
        if ((payloadCase_ == 30) && (upgradeTransportBuilder_ != null)) {
          return upgradeTransportBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 30) {
            return (com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport.getDefaultInstance();
        }
      }
      /**
       * <code>.UpgradeTransport upgrade_transport = 30;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport, com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport.Builder, com.amazon.alexa.accessory.protocol.Transport.UpgradeTransportOrBuilder> 
          getUpgradeTransportFieldBuilder() {
        if (upgradeTransportBuilder_ == null) {
          if (!(payloadCase_ == 30)) {
            payload_ = com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport.getDefaultInstance();
          }
          upgradeTransportBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport, com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport.Builder, com.amazon.alexa.accessory.protocol.Transport.UpgradeTransportOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Transport.UpgradeTransport) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 30;
        onChanged();;
        return upgradeTransportBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Transport.SwitchTransport, com.amazon.alexa.accessory.protocol.Transport.SwitchTransport.Builder, com.amazon.alexa.accessory.protocol.Transport.SwitchTransportOrBuilder> switchTransportBuilder_;
      /**
       * <code>.SwitchTransport switch_transport = 31;</code>
       * @return Whether the switchTransport field is set.
       */
      @java.lang.Override
      public boolean hasSwitchTransport() {
        return payloadCase_ == 31;
      }
      /**
       * <code>.SwitchTransport switch_transport = 31;</code>
       * @return The switchTransport.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Transport.SwitchTransport getSwitchTransport() {
        if (switchTransportBuilder_ == null) {
          if (payloadCase_ == 31) {
            return (com.amazon.alexa.accessory.protocol.Transport.SwitchTransport) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Transport.SwitchTransport.getDefaultInstance();
        } else {
          if (payloadCase_ == 31) {
            return switchTransportBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Transport.SwitchTransport.getDefaultInstance();
        }
      }
      /**
       * <code>.SwitchTransport switch_transport = 31;</code>
       */
      public Builder setSwitchTransport(com.amazon.alexa.accessory.protocol.Transport.SwitchTransport value) {
        if (switchTransportBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          switchTransportBuilder_.setMessage(value);
        }
        payloadCase_ = 31;
        return this;
      }
      /**
       * <code>.SwitchTransport switch_transport = 31;</code>
       */
      public Builder setSwitchTransport(
          com.amazon.alexa.accessory.protocol.Transport.SwitchTransport.Builder builderForValue) {
        if (switchTransportBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          switchTransportBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 31;
        return this;
      }
      /**
       * <code>.SwitchTransport switch_transport = 31;</code>
       */
      public Builder mergeSwitchTransport(com.amazon.alexa.accessory.protocol.Transport.SwitchTransport value) {
        if (switchTransportBuilder_ == null) {
          if (payloadCase_ == 31 &&
              payload_ != com.amazon.alexa.accessory.protocol.Transport.SwitchTransport.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Transport.SwitchTransport.newBuilder((com.amazon.alexa.accessory.protocol.Transport.SwitchTransport) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 31) {
            switchTransportBuilder_.mergeFrom(value);
          } else {
            switchTransportBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 31;
        return this;
      }
      /**
       * <code>.SwitchTransport switch_transport = 31;</code>
       */
      public Builder clearSwitchTransport() {
        if (switchTransportBuilder_ == null) {
          if (payloadCase_ == 31) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 31) {
            payloadCase_ = 0;
            payload_ = null;
          }
          switchTransportBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.SwitchTransport switch_transport = 31;</code>
       */
      public com.amazon.alexa.accessory.protocol.Transport.SwitchTransport.Builder getSwitchTransportBuilder() {
        return getSwitchTransportFieldBuilder().getBuilder();
      }
      /**
       * <code>.SwitchTransport switch_transport = 31;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Transport.SwitchTransportOrBuilder getSwitchTransportOrBuilder() {
        if ((payloadCase_ == 31) && (switchTransportBuilder_ != null)) {
          return switchTransportBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 31) {
            return (com.amazon.alexa.accessory.protocol.Transport.SwitchTransport) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Transport.SwitchTransport.getDefaultInstance();
        }
      }
      /**
       * <code>.SwitchTransport switch_transport = 31;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Transport.SwitchTransport, com.amazon.alexa.accessory.protocol.Transport.SwitchTransport.Builder, com.amazon.alexa.accessory.protocol.Transport.SwitchTransportOrBuilder> 
          getSwitchTransportFieldBuilder() {
        if (switchTransportBuilder_ == null) {
          if (!(payloadCase_ == 31)) {
            payload_ = com.amazon.alexa.accessory.protocol.Transport.SwitchTransport.getDefaultInstance();
          }
          switchTransportBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Transport.SwitchTransport, com.amazon.alexa.accessory.protocol.Transport.SwitchTransport.Builder, com.amazon.alexa.accessory.protocol.Transport.SwitchTransportOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Transport.SwitchTransport) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 31;
        onChanged();;
        return switchTransportBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.StartSpeech, com.amazon.alexa.accessory.protocol.Speech.StartSpeech.Builder, com.amazon.alexa.accessory.protocol.Speech.StartSpeechOrBuilder> startSpeechBuilder_;
      /**
       * <code>.StartSpeech start_speech = 11;</code>
       * @return Whether the startSpeech field is set.
       */
      @java.lang.Override
      public boolean hasStartSpeech() {
        return payloadCase_ == 11;
      }
      /**
       * <code>.StartSpeech start_speech = 11;</code>
       * @return The startSpeech.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.StartSpeech getStartSpeech() {
        if (startSpeechBuilder_ == null) {
          if (payloadCase_ == 11) {
            return (com.amazon.alexa.accessory.protocol.Speech.StartSpeech) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Speech.StartSpeech.getDefaultInstance();
        } else {
          if (payloadCase_ == 11) {
            return startSpeechBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Speech.StartSpeech.getDefaultInstance();
        }
      }
      /**
       * <code>.StartSpeech start_speech = 11;</code>
       */
      public Builder setStartSpeech(com.amazon.alexa.accessory.protocol.Speech.StartSpeech value) {
        if (startSpeechBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          startSpeechBuilder_.setMessage(value);
        }
        payloadCase_ = 11;
        return this;
      }
      /**
       * <code>.StartSpeech start_speech = 11;</code>
       */
      public Builder setStartSpeech(
          com.amazon.alexa.accessory.protocol.Speech.StartSpeech.Builder builderForValue) {
        if (startSpeechBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          startSpeechBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 11;
        return this;
      }
      /**
       * <code>.StartSpeech start_speech = 11;</code>
       */
      public Builder mergeStartSpeech(com.amazon.alexa.accessory.protocol.Speech.StartSpeech value) {
        if (startSpeechBuilder_ == null) {
          if (payloadCase_ == 11 &&
              payload_ != com.amazon.alexa.accessory.protocol.Speech.StartSpeech.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Speech.StartSpeech.newBuilder((com.amazon.alexa.accessory.protocol.Speech.StartSpeech) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 11) {
            startSpeechBuilder_.mergeFrom(value);
          } else {
            startSpeechBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 11;
        return this;
      }
      /**
       * <code>.StartSpeech start_speech = 11;</code>
       */
      public Builder clearStartSpeech() {
        if (startSpeechBuilder_ == null) {
          if (payloadCase_ == 11) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 11) {
            payloadCase_ = 0;
            payload_ = null;
          }
          startSpeechBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.StartSpeech start_speech = 11;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.StartSpeech.Builder getStartSpeechBuilder() {
        return getStartSpeechFieldBuilder().getBuilder();
      }
      /**
       * <code>.StartSpeech start_speech = 11;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.StartSpeechOrBuilder getStartSpeechOrBuilder() {
        if ((payloadCase_ == 11) && (startSpeechBuilder_ != null)) {
          return startSpeechBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 11) {
            return (com.amazon.alexa.accessory.protocol.Speech.StartSpeech) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Speech.StartSpeech.getDefaultInstance();
        }
      }
      /**
       * <code>.StartSpeech start_speech = 11;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.StartSpeech, com.amazon.alexa.accessory.protocol.Speech.StartSpeech.Builder, com.amazon.alexa.accessory.protocol.Speech.StartSpeechOrBuilder> 
          getStartSpeechFieldBuilder() {
        if (startSpeechBuilder_ == null) {
          if (!(payloadCase_ == 11)) {
            payload_ = com.amazon.alexa.accessory.protocol.Speech.StartSpeech.getDefaultInstance();
          }
          startSpeechBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Speech.StartSpeech, com.amazon.alexa.accessory.protocol.Speech.StartSpeech.Builder, com.amazon.alexa.accessory.protocol.Speech.StartSpeechOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Speech.StartSpeech) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 11;
        onChanged();;
        return startSpeechBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech, com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech.Builder, com.amazon.alexa.accessory.protocol.Speech.ProvideSpeechOrBuilder> provideSpeechBuilder_;
      /**
       * <code>.ProvideSpeech provide_speech = 10;</code>
       * @return Whether the provideSpeech field is set.
       */
      @java.lang.Override
      public boolean hasProvideSpeech() {
        return payloadCase_ == 10;
      }
      /**
       * <code>.ProvideSpeech provide_speech = 10;</code>
       * @return The provideSpeech.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech getProvideSpeech() {
        if (provideSpeechBuilder_ == null) {
          if (payloadCase_ == 10) {
            return (com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech.getDefaultInstance();
        } else {
          if (payloadCase_ == 10) {
            return provideSpeechBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech.getDefaultInstance();
        }
      }
      /**
       * <code>.ProvideSpeech provide_speech = 10;</code>
       */
      public Builder setProvideSpeech(com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech value) {
        if (provideSpeechBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          provideSpeechBuilder_.setMessage(value);
        }
        payloadCase_ = 10;
        return this;
      }
      /**
       * <code>.ProvideSpeech provide_speech = 10;</code>
       */
      public Builder setProvideSpeech(
          com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech.Builder builderForValue) {
        if (provideSpeechBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          provideSpeechBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 10;
        return this;
      }
      /**
       * <code>.ProvideSpeech provide_speech = 10;</code>
       */
      public Builder mergeProvideSpeech(com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech value) {
        if (provideSpeechBuilder_ == null) {
          if (payloadCase_ == 10 &&
              payload_ != com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech.newBuilder((com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 10) {
            provideSpeechBuilder_.mergeFrom(value);
          } else {
            provideSpeechBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 10;
        return this;
      }
      /**
       * <code>.ProvideSpeech provide_speech = 10;</code>
       */
      public Builder clearProvideSpeech() {
        if (provideSpeechBuilder_ == null) {
          if (payloadCase_ == 10) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 10) {
            payloadCase_ = 0;
            payload_ = null;
          }
          provideSpeechBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.ProvideSpeech provide_speech = 10;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech.Builder getProvideSpeechBuilder() {
        return getProvideSpeechFieldBuilder().getBuilder();
      }
      /**
       * <code>.ProvideSpeech provide_speech = 10;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.ProvideSpeechOrBuilder getProvideSpeechOrBuilder() {
        if ((payloadCase_ == 10) && (provideSpeechBuilder_ != null)) {
          return provideSpeechBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 10) {
            return (com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech.getDefaultInstance();
        }
      }
      /**
       * <code>.ProvideSpeech provide_speech = 10;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech, com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech.Builder, com.amazon.alexa.accessory.protocol.Speech.ProvideSpeechOrBuilder> 
          getProvideSpeechFieldBuilder() {
        if (provideSpeechBuilder_ == null) {
          if (!(payloadCase_ == 10)) {
            payload_ = com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech.getDefaultInstance();
          }
          provideSpeechBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech, com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech.Builder, com.amazon.alexa.accessory.protocol.Speech.ProvideSpeechOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Speech.ProvideSpeech) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 10;
        onChanged();;
        return provideSpeechBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.StopSpeech, com.amazon.alexa.accessory.protocol.Speech.StopSpeech.Builder, com.amazon.alexa.accessory.protocol.Speech.StopSpeechOrBuilder> stopSpeechBuilder_;
      /**
       * <code>.StopSpeech stop_speech = 12;</code>
       * @return Whether the stopSpeech field is set.
       */
      @java.lang.Override
      public boolean hasStopSpeech() {
        return payloadCase_ == 12;
      }
      /**
       * <code>.StopSpeech stop_speech = 12;</code>
       * @return The stopSpeech.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.StopSpeech getStopSpeech() {
        if (stopSpeechBuilder_ == null) {
          if (payloadCase_ == 12) {
            return (com.amazon.alexa.accessory.protocol.Speech.StopSpeech) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Speech.StopSpeech.getDefaultInstance();
        } else {
          if (payloadCase_ == 12) {
            return stopSpeechBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Speech.StopSpeech.getDefaultInstance();
        }
      }
      /**
       * <code>.StopSpeech stop_speech = 12;</code>
       */
      public Builder setStopSpeech(com.amazon.alexa.accessory.protocol.Speech.StopSpeech value) {
        if (stopSpeechBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          stopSpeechBuilder_.setMessage(value);
        }
        payloadCase_ = 12;
        return this;
      }
      /**
       * <code>.StopSpeech stop_speech = 12;</code>
       */
      public Builder setStopSpeech(
          com.amazon.alexa.accessory.protocol.Speech.StopSpeech.Builder builderForValue) {
        if (stopSpeechBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          stopSpeechBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 12;
        return this;
      }
      /**
       * <code>.StopSpeech stop_speech = 12;</code>
       */
      public Builder mergeStopSpeech(com.amazon.alexa.accessory.protocol.Speech.StopSpeech value) {
        if (stopSpeechBuilder_ == null) {
          if (payloadCase_ == 12 &&
              payload_ != com.amazon.alexa.accessory.protocol.Speech.StopSpeech.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Speech.StopSpeech.newBuilder((com.amazon.alexa.accessory.protocol.Speech.StopSpeech) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 12) {
            stopSpeechBuilder_.mergeFrom(value);
          } else {
            stopSpeechBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 12;
        return this;
      }
      /**
       * <code>.StopSpeech stop_speech = 12;</code>
       */
      public Builder clearStopSpeech() {
        if (stopSpeechBuilder_ == null) {
          if (payloadCase_ == 12) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 12) {
            payloadCase_ = 0;
            payload_ = null;
          }
          stopSpeechBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.StopSpeech stop_speech = 12;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.StopSpeech.Builder getStopSpeechBuilder() {
        return getStopSpeechFieldBuilder().getBuilder();
      }
      /**
       * <code>.StopSpeech stop_speech = 12;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.StopSpeechOrBuilder getStopSpeechOrBuilder() {
        if ((payloadCase_ == 12) && (stopSpeechBuilder_ != null)) {
          return stopSpeechBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 12) {
            return (com.amazon.alexa.accessory.protocol.Speech.StopSpeech) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Speech.StopSpeech.getDefaultInstance();
        }
      }
      /**
       * <code>.StopSpeech stop_speech = 12;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.StopSpeech, com.amazon.alexa.accessory.protocol.Speech.StopSpeech.Builder, com.amazon.alexa.accessory.protocol.Speech.StopSpeechOrBuilder> 
          getStopSpeechFieldBuilder() {
        if (stopSpeechBuilder_ == null) {
          if (!(payloadCase_ == 12)) {
            payload_ = com.amazon.alexa.accessory.protocol.Speech.StopSpeech.getDefaultInstance();
          }
          stopSpeechBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Speech.StopSpeech, com.amazon.alexa.accessory.protocol.Speech.StopSpeech.Builder, com.amazon.alexa.accessory.protocol.Speech.StopSpeechOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Speech.StopSpeech) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 12;
        onChanged();;
        return stopSpeechBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech, com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech.Builder, com.amazon.alexa.accessory.protocol.Speech.EndpointSpeechOrBuilder> endpointSpeechBuilder_;
      /**
       * <code>.EndpointSpeech endpoint_speech = 13;</code>
       * @return Whether the endpointSpeech field is set.
       */
      @java.lang.Override
      public boolean hasEndpointSpeech() {
        return payloadCase_ == 13;
      }
      /**
       * <code>.EndpointSpeech endpoint_speech = 13;</code>
       * @return The endpointSpeech.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech getEndpointSpeech() {
        if (endpointSpeechBuilder_ == null) {
          if (payloadCase_ == 13) {
            return (com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech.getDefaultInstance();
        } else {
          if (payloadCase_ == 13) {
            return endpointSpeechBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech.getDefaultInstance();
        }
      }
      /**
       * <code>.EndpointSpeech endpoint_speech = 13;</code>
       */
      public Builder setEndpointSpeech(com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech value) {
        if (endpointSpeechBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          endpointSpeechBuilder_.setMessage(value);
        }
        payloadCase_ = 13;
        return this;
      }
      /**
       * <code>.EndpointSpeech endpoint_speech = 13;</code>
       */
      public Builder setEndpointSpeech(
          com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech.Builder builderForValue) {
        if (endpointSpeechBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          endpointSpeechBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 13;
        return this;
      }
      /**
       * <code>.EndpointSpeech endpoint_speech = 13;</code>
       */
      public Builder mergeEndpointSpeech(com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech value) {
        if (endpointSpeechBuilder_ == null) {
          if (payloadCase_ == 13 &&
              payload_ != com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech.newBuilder((com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 13) {
            endpointSpeechBuilder_.mergeFrom(value);
          } else {
            endpointSpeechBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 13;
        return this;
      }
      /**
       * <code>.EndpointSpeech endpoint_speech = 13;</code>
       */
      public Builder clearEndpointSpeech() {
        if (endpointSpeechBuilder_ == null) {
          if (payloadCase_ == 13) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 13) {
            payloadCase_ = 0;
            payload_ = null;
          }
          endpointSpeechBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.EndpointSpeech endpoint_speech = 13;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech.Builder getEndpointSpeechBuilder() {
        return getEndpointSpeechFieldBuilder().getBuilder();
      }
      /**
       * <code>.EndpointSpeech endpoint_speech = 13;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.EndpointSpeechOrBuilder getEndpointSpeechOrBuilder() {
        if ((payloadCase_ == 13) && (endpointSpeechBuilder_ != null)) {
          return endpointSpeechBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 13) {
            return (com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech.getDefaultInstance();
        }
      }
      /**
       * <code>.EndpointSpeech endpoint_speech = 13;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech, com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech.Builder, com.amazon.alexa.accessory.protocol.Speech.EndpointSpeechOrBuilder> 
          getEndpointSpeechFieldBuilder() {
        if (endpointSpeechBuilder_ == null) {
          if (!(payloadCase_ == 13)) {
            payload_ = com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech.getDefaultInstance();
          }
          endpointSpeechBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech, com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech.Builder, com.amazon.alexa.accessory.protocol.Speech.EndpointSpeechOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Speech.EndpointSpeech) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 13;
        onChanged();;
        return endpointSpeechBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState, com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState.Builder, com.amazon.alexa.accessory.protocol.Speech.NotifySpeechStateOrBuilder> notifySpeechStateBuilder_;
      /**
       * <code>.NotifySpeechState notify_speech_state = 14;</code>
       * @return Whether the notifySpeechState field is set.
       */
      @java.lang.Override
      public boolean hasNotifySpeechState() {
        return payloadCase_ == 14;
      }
      /**
       * <code>.NotifySpeechState notify_speech_state = 14;</code>
       * @return The notifySpeechState.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState getNotifySpeechState() {
        if (notifySpeechStateBuilder_ == null) {
          if (payloadCase_ == 14) {
            return (com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState.getDefaultInstance();
        } else {
          if (payloadCase_ == 14) {
            return notifySpeechStateBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState.getDefaultInstance();
        }
      }
      /**
       * <code>.NotifySpeechState notify_speech_state = 14;</code>
       */
      public Builder setNotifySpeechState(com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState value) {
        if (notifySpeechStateBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          notifySpeechStateBuilder_.setMessage(value);
        }
        payloadCase_ = 14;
        return this;
      }
      /**
       * <code>.NotifySpeechState notify_speech_state = 14;</code>
       */
      public Builder setNotifySpeechState(
          com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState.Builder builderForValue) {
        if (notifySpeechStateBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          notifySpeechStateBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 14;
        return this;
      }
      /**
       * <code>.NotifySpeechState notify_speech_state = 14;</code>
       */
      public Builder mergeNotifySpeechState(com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState value) {
        if (notifySpeechStateBuilder_ == null) {
          if (payloadCase_ == 14 &&
              payload_ != com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState.newBuilder((com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 14) {
            notifySpeechStateBuilder_.mergeFrom(value);
          } else {
            notifySpeechStateBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 14;
        return this;
      }
      /**
       * <code>.NotifySpeechState notify_speech_state = 14;</code>
       */
      public Builder clearNotifySpeechState() {
        if (notifySpeechStateBuilder_ == null) {
          if (payloadCase_ == 14) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 14) {
            payloadCase_ = 0;
            payload_ = null;
          }
          notifySpeechStateBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.NotifySpeechState notify_speech_state = 14;</code>
       */
      public com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState.Builder getNotifySpeechStateBuilder() {
        return getNotifySpeechStateFieldBuilder().getBuilder();
      }
      /**
       * <code>.NotifySpeechState notify_speech_state = 14;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Speech.NotifySpeechStateOrBuilder getNotifySpeechStateOrBuilder() {
        if ((payloadCase_ == 14) && (notifySpeechStateBuilder_ != null)) {
          return notifySpeechStateBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 14) {
            return (com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState.getDefaultInstance();
        }
      }
      /**
       * <code>.NotifySpeechState notify_speech_state = 14;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState, com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState.Builder, com.amazon.alexa.accessory.protocol.Speech.NotifySpeechStateOrBuilder> 
          getNotifySpeechStateFieldBuilder() {
        if (notifySpeechStateBuilder_ == null) {
          if (!(payloadCase_ == 14)) {
            payload_ = com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState.getDefaultInstance();
          }
          notifySpeechStateBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState, com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState.Builder, com.amazon.alexa.accessory.protocol.Speech.NotifySpeechStateOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Speech.NotifySpeechState) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 14;
        onChanged();;
        return notifySpeechStateBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand, com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand.Builder, com.amazon.alexa.accessory.protocol.Calling.ForwardATCommandOrBuilder> forwardAtCommandBuilder_;
      /**
       * <code>.ForwardATCommand forward_at_command = 40;</code>
       * @return Whether the forwardAtCommand field is set.
       */
      @java.lang.Override
      public boolean hasForwardAtCommand() {
        return payloadCase_ == 40;
      }
      /**
       * <code>.ForwardATCommand forward_at_command = 40;</code>
       * @return The forwardAtCommand.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand getForwardAtCommand() {
        if (forwardAtCommandBuilder_ == null) {
          if (payloadCase_ == 40) {
            return (com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand.getDefaultInstance();
        } else {
          if (payloadCase_ == 40) {
            return forwardAtCommandBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand.getDefaultInstance();
        }
      }
      /**
       * <code>.ForwardATCommand forward_at_command = 40;</code>
       */
      public Builder setForwardAtCommand(com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand value) {
        if (forwardAtCommandBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          forwardAtCommandBuilder_.setMessage(value);
        }
        payloadCase_ = 40;
        return this;
      }
      /**
       * <code>.ForwardATCommand forward_at_command = 40;</code>
       */
      public Builder setForwardAtCommand(
          com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand.Builder builderForValue) {
        if (forwardAtCommandBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          forwardAtCommandBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 40;
        return this;
      }
      /**
       * <code>.ForwardATCommand forward_at_command = 40;</code>
       */
      public Builder mergeForwardAtCommand(com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand value) {
        if (forwardAtCommandBuilder_ == null) {
          if (payloadCase_ == 40 &&
              payload_ != com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand.newBuilder((com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 40) {
            forwardAtCommandBuilder_.mergeFrom(value);
          } else {
            forwardAtCommandBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 40;
        return this;
      }
      /**
       * <code>.ForwardATCommand forward_at_command = 40;</code>
       */
      public Builder clearForwardAtCommand() {
        if (forwardAtCommandBuilder_ == null) {
          if (payloadCase_ == 40) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 40) {
            payloadCase_ = 0;
            payload_ = null;
          }
          forwardAtCommandBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.ForwardATCommand forward_at_command = 40;</code>
       */
      public com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand.Builder getForwardAtCommandBuilder() {
        return getForwardAtCommandFieldBuilder().getBuilder();
      }
      /**
       * <code>.ForwardATCommand forward_at_command = 40;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Calling.ForwardATCommandOrBuilder getForwardAtCommandOrBuilder() {
        if ((payloadCase_ == 40) && (forwardAtCommandBuilder_ != null)) {
          return forwardAtCommandBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 40) {
            return (com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand.getDefaultInstance();
        }
      }
      /**
       * <code>.ForwardATCommand forward_at_command = 40;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand, com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand.Builder, com.amazon.alexa.accessory.protocol.Calling.ForwardATCommandOrBuilder> 
          getForwardAtCommandFieldBuilder() {
        if (forwardAtCommandBuilder_ == null) {
          if (!(payloadCase_ == 40)) {
            payload_ = com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand.getDefaultInstance();
          }
          forwardAtCommandBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand, com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand.Builder, com.amazon.alexa.accessory.protocol.Calling.ForwardATCommandOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Calling.ForwardATCommand) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 40;
        onChanged();;
        return forwardAtCommandBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Calling.IncomingCall, com.amazon.alexa.accessory.protocol.Calling.IncomingCall.Builder, com.amazon.alexa.accessory.protocol.Calling.IncomingCallOrBuilder> incomingCallBuilder_;
      /**
       * <code>.IncomingCall incoming_call = 41;</code>
       * @return Whether the incomingCall field is set.
       */
      @java.lang.Override
      public boolean hasIncomingCall() {
        return payloadCase_ == 41;
      }
      /**
       * <code>.IncomingCall incoming_call = 41;</code>
       * @return The incomingCall.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Calling.IncomingCall getIncomingCall() {
        if (incomingCallBuilder_ == null) {
          if (payloadCase_ == 41) {
            return (com.amazon.alexa.accessory.protocol.Calling.IncomingCall) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Calling.IncomingCall.getDefaultInstance();
        } else {
          if (payloadCase_ == 41) {
            return incomingCallBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Calling.IncomingCall.getDefaultInstance();
        }
      }
      /**
       * <code>.IncomingCall incoming_call = 41;</code>
       */
      public Builder setIncomingCall(com.amazon.alexa.accessory.protocol.Calling.IncomingCall value) {
        if (incomingCallBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          incomingCallBuilder_.setMessage(value);
        }
        payloadCase_ = 41;
        return this;
      }
      /**
       * <code>.IncomingCall incoming_call = 41;</code>
       */
      public Builder setIncomingCall(
          com.amazon.alexa.accessory.protocol.Calling.IncomingCall.Builder builderForValue) {
        if (incomingCallBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          incomingCallBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 41;
        return this;
      }
      /**
       * <code>.IncomingCall incoming_call = 41;</code>
       */
      public Builder mergeIncomingCall(com.amazon.alexa.accessory.protocol.Calling.IncomingCall value) {
        if (incomingCallBuilder_ == null) {
          if (payloadCase_ == 41 &&
              payload_ != com.amazon.alexa.accessory.protocol.Calling.IncomingCall.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Calling.IncomingCall.newBuilder((com.amazon.alexa.accessory.protocol.Calling.IncomingCall) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 41) {
            incomingCallBuilder_.mergeFrom(value);
          } else {
            incomingCallBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 41;
        return this;
      }
      /**
       * <code>.IncomingCall incoming_call = 41;</code>
       */
      public Builder clearIncomingCall() {
        if (incomingCallBuilder_ == null) {
          if (payloadCase_ == 41) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 41) {
            payloadCase_ = 0;
            payload_ = null;
          }
          incomingCallBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.IncomingCall incoming_call = 41;</code>
       */
      public com.amazon.alexa.accessory.protocol.Calling.IncomingCall.Builder getIncomingCallBuilder() {
        return getIncomingCallFieldBuilder().getBuilder();
      }
      /**
       * <code>.IncomingCall incoming_call = 41;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Calling.IncomingCallOrBuilder getIncomingCallOrBuilder() {
        if ((payloadCase_ == 41) && (incomingCallBuilder_ != null)) {
          return incomingCallBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 41) {
            return (com.amazon.alexa.accessory.protocol.Calling.IncomingCall) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Calling.IncomingCall.getDefaultInstance();
        }
      }
      /**
       * <code>.IncomingCall incoming_call = 41;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Calling.IncomingCall, com.amazon.alexa.accessory.protocol.Calling.IncomingCall.Builder, com.amazon.alexa.accessory.protocol.Calling.IncomingCallOrBuilder> 
          getIncomingCallFieldBuilder() {
        if (incomingCallBuilder_ == null) {
          if (!(payloadCase_ == 41)) {
            payload_ = com.amazon.alexa.accessory.protocol.Calling.IncomingCall.getDefaultInstance();
          }
          incomingCallBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Calling.IncomingCall, com.amazon.alexa.accessory.protocol.Calling.IncomingCall.Builder, com.amazon.alexa.accessory.protocol.Calling.IncomingCallOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Calling.IncomingCall) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 41;
        onChanged();;
        return incomingCallBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Central.GetCentralInformation, com.amazon.alexa.accessory.protocol.Central.GetCentralInformation.Builder, com.amazon.alexa.accessory.protocol.Central.GetCentralInformationOrBuilder> getCentralInformationBuilder_;
      /**
       * <code>.GetCentralInformation get_central_information = 103;</code>
       * @return Whether the getCentralInformation field is set.
       */
      @java.lang.Override
      public boolean hasGetCentralInformation() {
        return payloadCase_ == 103;
      }
      /**
       * <code>.GetCentralInformation get_central_information = 103;</code>
       * @return The getCentralInformation.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Central.GetCentralInformation getGetCentralInformation() {
        if (getCentralInformationBuilder_ == null) {
          if (payloadCase_ == 103) {
            return (com.amazon.alexa.accessory.protocol.Central.GetCentralInformation) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Central.GetCentralInformation.getDefaultInstance();
        } else {
          if (payloadCase_ == 103) {
            return getCentralInformationBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Central.GetCentralInformation.getDefaultInstance();
        }
      }
      /**
       * <code>.GetCentralInformation get_central_information = 103;</code>
       */
      public Builder setGetCentralInformation(com.amazon.alexa.accessory.protocol.Central.GetCentralInformation value) {
        if (getCentralInformationBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          getCentralInformationBuilder_.setMessage(value);
        }
        payloadCase_ = 103;
        return this;
      }
      /**
       * <code>.GetCentralInformation get_central_information = 103;</code>
       */
      public Builder setGetCentralInformation(
          com.amazon.alexa.accessory.protocol.Central.GetCentralInformation.Builder builderForValue) {
        if (getCentralInformationBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          getCentralInformationBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 103;
        return this;
      }
      /**
       * <code>.GetCentralInformation get_central_information = 103;</code>
       */
      public Builder mergeGetCentralInformation(com.amazon.alexa.accessory.protocol.Central.GetCentralInformation value) {
        if (getCentralInformationBuilder_ == null) {
          if (payloadCase_ == 103 &&
              payload_ != com.amazon.alexa.accessory.protocol.Central.GetCentralInformation.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Central.GetCentralInformation.newBuilder((com.amazon.alexa.accessory.protocol.Central.GetCentralInformation) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 103) {
            getCentralInformationBuilder_.mergeFrom(value);
          } else {
            getCentralInformationBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 103;
        return this;
      }
      /**
       * <code>.GetCentralInformation get_central_information = 103;</code>
       */
      public Builder clearGetCentralInformation() {
        if (getCentralInformationBuilder_ == null) {
          if (payloadCase_ == 103) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 103) {
            payloadCase_ = 0;
            payload_ = null;
          }
          getCentralInformationBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.GetCentralInformation get_central_information = 103;</code>
       */
      public com.amazon.alexa.accessory.protocol.Central.GetCentralInformation.Builder getGetCentralInformationBuilder() {
        return getGetCentralInformationFieldBuilder().getBuilder();
      }
      /**
       * <code>.GetCentralInformation get_central_information = 103;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Central.GetCentralInformationOrBuilder getGetCentralInformationOrBuilder() {
        if ((payloadCase_ == 103) && (getCentralInformationBuilder_ != null)) {
          return getCentralInformationBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 103) {
            return (com.amazon.alexa.accessory.protocol.Central.GetCentralInformation) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Central.GetCentralInformation.getDefaultInstance();
        }
      }
      /**
       * <code>.GetCentralInformation get_central_information = 103;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Central.GetCentralInformation, com.amazon.alexa.accessory.protocol.Central.GetCentralInformation.Builder, com.amazon.alexa.accessory.protocol.Central.GetCentralInformationOrBuilder> 
          getGetCentralInformationFieldBuilder() {
        if (getCentralInformationBuilder_ == null) {
          if (!(payloadCase_ == 103)) {
            payload_ = com.amazon.alexa.accessory.protocol.Central.GetCentralInformation.getDefaultInstance();
          }
          getCentralInformationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Central.GetCentralInformation, com.amazon.alexa.accessory.protocol.Central.GetCentralInformation.Builder, com.amazon.alexa.accessory.protocol.Central.GetCentralInformationOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Central.GetCentralInformation) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 103;
        onChanged();;
        return getCentralInformationBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation, com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation.Builder, com.amazon.alexa.accessory.protocol.Device.GetDeviceInformationOrBuilder> getDeviceInformationBuilder_;
      /**
       * <code>.GetDeviceInformation get_device_information = 20;</code>
       * @return Whether the getDeviceInformation field is set.
       */
      @java.lang.Override
      public boolean hasGetDeviceInformation() {
        return payloadCase_ == 20;
      }
      /**
       * <code>.GetDeviceInformation get_device_information = 20;</code>
       * @return The getDeviceInformation.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation getGetDeviceInformation() {
        if (getDeviceInformationBuilder_ == null) {
          if (payloadCase_ == 20) {
            return (com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation.getDefaultInstance();
        } else {
          if (payloadCase_ == 20) {
            return getDeviceInformationBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation.getDefaultInstance();
        }
      }
      /**
       * <code>.GetDeviceInformation get_device_information = 20;</code>
       */
      public Builder setGetDeviceInformation(com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation value) {
        if (getDeviceInformationBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          getDeviceInformationBuilder_.setMessage(value);
        }
        payloadCase_ = 20;
        return this;
      }
      /**
       * <code>.GetDeviceInformation get_device_information = 20;</code>
       */
      public Builder setGetDeviceInformation(
          com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation.Builder builderForValue) {
        if (getDeviceInformationBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          getDeviceInformationBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 20;
        return this;
      }
      /**
       * <code>.GetDeviceInformation get_device_information = 20;</code>
       */
      public Builder mergeGetDeviceInformation(com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation value) {
        if (getDeviceInformationBuilder_ == null) {
          if (payloadCase_ == 20 &&
              payload_ != com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation.newBuilder((com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 20) {
            getDeviceInformationBuilder_.mergeFrom(value);
          } else {
            getDeviceInformationBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 20;
        return this;
      }
      /**
       * <code>.GetDeviceInformation get_device_information = 20;</code>
       */
      public Builder clearGetDeviceInformation() {
        if (getDeviceInformationBuilder_ == null) {
          if (payloadCase_ == 20) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 20) {
            payloadCase_ = 0;
            payload_ = null;
          }
          getDeviceInformationBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.GetDeviceInformation get_device_information = 20;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation.Builder getGetDeviceInformationBuilder() {
        return getGetDeviceInformationFieldBuilder().getBuilder();
      }
      /**
       * <code>.GetDeviceInformation get_device_information = 20;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.GetDeviceInformationOrBuilder getGetDeviceInformationOrBuilder() {
        if ((payloadCase_ == 20) && (getDeviceInformationBuilder_ != null)) {
          return getDeviceInformationBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 20) {
            return (com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation.getDefaultInstance();
        }
      }
      /**
       * <code>.GetDeviceInformation get_device_information = 20;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation, com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation.Builder, com.amazon.alexa.accessory.protocol.Device.GetDeviceInformationOrBuilder> 
          getGetDeviceInformationFieldBuilder() {
        if (getDeviceInformationBuilder_ == null) {
          if (!(payloadCase_ == 20)) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation.getDefaultInstance();
          }
          getDeviceInformationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation, com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation.Builder, com.amazon.alexa.accessory.protocol.Device.GetDeviceInformationOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Device.GetDeviceInformation) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 20;
        onChanged();;
        return getDeviceInformationBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration, com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration.Builder, com.amazon.alexa.accessory.protocol.Device.GetDeviceConfigurationOrBuilder> getDeviceConfigurationBuilder_;
      /**
       * <code>.GetDeviceConfiguration get_device_configuration = 21;</code>
       * @return Whether the getDeviceConfiguration field is set.
       */
      @java.lang.Override
      public boolean hasGetDeviceConfiguration() {
        return payloadCase_ == 21;
      }
      /**
       * <code>.GetDeviceConfiguration get_device_configuration = 21;</code>
       * @return The getDeviceConfiguration.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration getGetDeviceConfiguration() {
        if (getDeviceConfigurationBuilder_ == null) {
          if (payloadCase_ == 21) {
            return (com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration.getDefaultInstance();
        } else {
          if (payloadCase_ == 21) {
            return getDeviceConfigurationBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration.getDefaultInstance();
        }
      }
      /**
       * <code>.GetDeviceConfiguration get_device_configuration = 21;</code>
       */
      public Builder setGetDeviceConfiguration(com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration value) {
        if (getDeviceConfigurationBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          getDeviceConfigurationBuilder_.setMessage(value);
        }
        payloadCase_ = 21;
        return this;
      }
      /**
       * <code>.GetDeviceConfiguration get_device_configuration = 21;</code>
       */
      public Builder setGetDeviceConfiguration(
          com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration.Builder builderForValue) {
        if (getDeviceConfigurationBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          getDeviceConfigurationBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 21;
        return this;
      }
      /**
       * <code>.GetDeviceConfiguration get_device_configuration = 21;</code>
       */
      public Builder mergeGetDeviceConfiguration(com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration value) {
        if (getDeviceConfigurationBuilder_ == null) {
          if (payloadCase_ == 21 &&
              payload_ != com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration.newBuilder((com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 21) {
            getDeviceConfigurationBuilder_.mergeFrom(value);
          } else {
            getDeviceConfigurationBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 21;
        return this;
      }
      /**
       * <code>.GetDeviceConfiguration get_device_configuration = 21;</code>
       */
      public Builder clearGetDeviceConfiguration() {
        if (getDeviceConfigurationBuilder_ == null) {
          if (payloadCase_ == 21) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 21) {
            payloadCase_ = 0;
            payload_ = null;
          }
          getDeviceConfigurationBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.GetDeviceConfiguration get_device_configuration = 21;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration.Builder getGetDeviceConfigurationBuilder() {
        return getGetDeviceConfigurationFieldBuilder().getBuilder();
      }
      /**
       * <code>.GetDeviceConfiguration get_device_configuration = 21;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.GetDeviceConfigurationOrBuilder getGetDeviceConfigurationOrBuilder() {
        if ((payloadCase_ == 21) && (getDeviceConfigurationBuilder_ != null)) {
          return getDeviceConfigurationBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 21) {
            return (com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration.getDefaultInstance();
        }
      }
      /**
       * <code>.GetDeviceConfiguration get_device_configuration = 21;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration, com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration.Builder, com.amazon.alexa.accessory.protocol.Device.GetDeviceConfigurationOrBuilder> 
          getGetDeviceConfigurationFieldBuilder() {
        if (getDeviceConfigurationBuilder_ == null) {
          if (!(payloadCase_ == 21)) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration.getDefaultInstance();
          }
          getDeviceConfigurationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration, com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration.Builder, com.amazon.alexa.accessory.protocol.Device.GetDeviceConfigurationOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Device.GetDeviceConfiguration) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 21;
        onChanged();;
        return getDeviceConfigurationBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.OverrideAssistant, com.amazon.alexa.accessory.protocol.Device.OverrideAssistant.Builder, com.amazon.alexa.accessory.protocol.Device.OverrideAssistantOrBuilder> overrideAssistantBuilder_;
      /**
       * <code>.OverrideAssistant override_assistant = 22;</code>
       * @return Whether the overrideAssistant field is set.
       */
      @java.lang.Override
      public boolean hasOverrideAssistant() {
        return payloadCase_ == 22;
      }
      /**
       * <code>.OverrideAssistant override_assistant = 22;</code>
       * @return The overrideAssistant.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.OverrideAssistant getOverrideAssistant() {
        if (overrideAssistantBuilder_ == null) {
          if (payloadCase_ == 22) {
            return (com.amazon.alexa.accessory.protocol.Device.OverrideAssistant) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.OverrideAssistant.getDefaultInstance();
        } else {
          if (payloadCase_ == 22) {
            return overrideAssistantBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Device.OverrideAssistant.getDefaultInstance();
        }
      }
      /**
       * <code>.OverrideAssistant override_assistant = 22;</code>
       */
      public Builder setOverrideAssistant(com.amazon.alexa.accessory.protocol.Device.OverrideAssistant value) {
        if (overrideAssistantBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          overrideAssistantBuilder_.setMessage(value);
        }
        payloadCase_ = 22;
        return this;
      }
      /**
       * <code>.OverrideAssistant override_assistant = 22;</code>
       */
      public Builder setOverrideAssistant(
          com.amazon.alexa.accessory.protocol.Device.OverrideAssistant.Builder builderForValue) {
        if (overrideAssistantBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          overrideAssistantBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 22;
        return this;
      }
      /**
       * <code>.OverrideAssistant override_assistant = 22;</code>
       */
      public Builder mergeOverrideAssistant(com.amazon.alexa.accessory.protocol.Device.OverrideAssistant value) {
        if (overrideAssistantBuilder_ == null) {
          if (payloadCase_ == 22 &&
              payload_ != com.amazon.alexa.accessory.protocol.Device.OverrideAssistant.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.OverrideAssistant.newBuilder((com.amazon.alexa.accessory.protocol.Device.OverrideAssistant) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 22) {
            overrideAssistantBuilder_.mergeFrom(value);
          } else {
            overrideAssistantBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 22;
        return this;
      }
      /**
       * <code>.OverrideAssistant override_assistant = 22;</code>
       */
      public Builder clearOverrideAssistant() {
        if (overrideAssistantBuilder_ == null) {
          if (payloadCase_ == 22) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 22) {
            payloadCase_ = 0;
            payload_ = null;
          }
          overrideAssistantBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.OverrideAssistant override_assistant = 22;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.OverrideAssistant.Builder getOverrideAssistantBuilder() {
        return getOverrideAssistantFieldBuilder().getBuilder();
      }
      /**
       * <code>.OverrideAssistant override_assistant = 22;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.OverrideAssistantOrBuilder getOverrideAssistantOrBuilder() {
        if ((payloadCase_ == 22) && (overrideAssistantBuilder_ != null)) {
          return overrideAssistantBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 22) {
            return (com.amazon.alexa.accessory.protocol.Device.OverrideAssistant) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.OverrideAssistant.getDefaultInstance();
        }
      }
      /**
       * <code>.OverrideAssistant override_assistant = 22;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.OverrideAssistant, com.amazon.alexa.accessory.protocol.Device.OverrideAssistant.Builder, com.amazon.alexa.accessory.protocol.Device.OverrideAssistantOrBuilder> 
          getOverrideAssistantFieldBuilder() {
        if (overrideAssistantBuilder_ == null) {
          if (!(payloadCase_ == 22)) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.OverrideAssistant.getDefaultInstance();
          }
          overrideAssistantBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Device.OverrideAssistant, com.amazon.alexa.accessory.protocol.Device.OverrideAssistant.Builder, com.amazon.alexa.accessory.protocol.Device.OverrideAssistantOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Device.OverrideAssistant) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 22;
        onChanged();;
        return overrideAssistantBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.StartSetup, com.amazon.alexa.accessory.protocol.Device.StartSetup.Builder, com.amazon.alexa.accessory.protocol.Device.StartSetupOrBuilder> startSetupBuilder_;
      /**
       * <code>.StartSetup start_setup = 23;</code>
       * @return Whether the startSetup field is set.
       */
      @java.lang.Override
      public boolean hasStartSetup() {
        return payloadCase_ == 23;
      }
      /**
       * <code>.StartSetup start_setup = 23;</code>
       * @return The startSetup.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.StartSetup getStartSetup() {
        if (startSetupBuilder_ == null) {
          if (payloadCase_ == 23) {
            return (com.amazon.alexa.accessory.protocol.Device.StartSetup) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.StartSetup.getDefaultInstance();
        } else {
          if (payloadCase_ == 23) {
            return startSetupBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Device.StartSetup.getDefaultInstance();
        }
      }
      /**
       * <code>.StartSetup start_setup = 23;</code>
       */
      public Builder setStartSetup(com.amazon.alexa.accessory.protocol.Device.StartSetup value) {
        if (startSetupBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          startSetupBuilder_.setMessage(value);
        }
        payloadCase_ = 23;
        return this;
      }
      /**
       * <code>.StartSetup start_setup = 23;</code>
       */
      public Builder setStartSetup(
          com.amazon.alexa.accessory.protocol.Device.StartSetup.Builder builderForValue) {
        if (startSetupBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          startSetupBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 23;
        return this;
      }
      /**
       * <code>.StartSetup start_setup = 23;</code>
       */
      public Builder mergeStartSetup(com.amazon.alexa.accessory.protocol.Device.StartSetup value) {
        if (startSetupBuilder_ == null) {
          if (payloadCase_ == 23 &&
              payload_ != com.amazon.alexa.accessory.protocol.Device.StartSetup.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.StartSetup.newBuilder((com.amazon.alexa.accessory.protocol.Device.StartSetup) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 23) {
            startSetupBuilder_.mergeFrom(value);
          } else {
            startSetupBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 23;
        return this;
      }
      /**
       * <code>.StartSetup start_setup = 23;</code>
       */
      public Builder clearStartSetup() {
        if (startSetupBuilder_ == null) {
          if (payloadCase_ == 23) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 23) {
            payloadCase_ = 0;
            payload_ = null;
          }
          startSetupBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.StartSetup start_setup = 23;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.StartSetup.Builder getStartSetupBuilder() {
        return getStartSetupFieldBuilder().getBuilder();
      }
      /**
       * <code>.StartSetup start_setup = 23;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.StartSetupOrBuilder getStartSetupOrBuilder() {
        if ((payloadCase_ == 23) && (startSetupBuilder_ != null)) {
          return startSetupBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 23) {
            return (com.amazon.alexa.accessory.protocol.Device.StartSetup) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.StartSetup.getDefaultInstance();
        }
      }
      /**
       * <code>.StartSetup start_setup = 23;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.StartSetup, com.amazon.alexa.accessory.protocol.Device.StartSetup.Builder, com.amazon.alexa.accessory.protocol.Device.StartSetupOrBuilder> 
          getStartSetupFieldBuilder() {
        if (startSetupBuilder_ == null) {
          if (!(payloadCase_ == 23)) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.StartSetup.getDefaultInstance();
          }
          startSetupBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Device.StartSetup, com.amazon.alexa.accessory.protocol.Device.StartSetup.Builder, com.amazon.alexa.accessory.protocol.Device.StartSetupOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Device.StartSetup) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 23;
        onChanged();;
        return startSetupBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.CompleteSetup, com.amazon.alexa.accessory.protocol.Device.CompleteSetup.Builder, com.amazon.alexa.accessory.protocol.Device.CompleteSetupOrBuilder> completeSetupBuilder_;
      /**
       * <code>.CompleteSetup complete_setup = 24;</code>
       * @return Whether the completeSetup field is set.
       */
      @java.lang.Override
      public boolean hasCompleteSetup() {
        return payloadCase_ == 24;
      }
      /**
       * <code>.CompleteSetup complete_setup = 24;</code>
       * @return The completeSetup.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.CompleteSetup getCompleteSetup() {
        if (completeSetupBuilder_ == null) {
          if (payloadCase_ == 24) {
            return (com.amazon.alexa.accessory.protocol.Device.CompleteSetup) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.CompleteSetup.getDefaultInstance();
        } else {
          if (payloadCase_ == 24) {
            return completeSetupBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Device.CompleteSetup.getDefaultInstance();
        }
      }
      /**
       * <code>.CompleteSetup complete_setup = 24;</code>
       */
      public Builder setCompleteSetup(com.amazon.alexa.accessory.protocol.Device.CompleteSetup value) {
        if (completeSetupBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          completeSetupBuilder_.setMessage(value);
        }
        payloadCase_ = 24;
        return this;
      }
      /**
       * <code>.CompleteSetup complete_setup = 24;</code>
       */
      public Builder setCompleteSetup(
          com.amazon.alexa.accessory.protocol.Device.CompleteSetup.Builder builderForValue) {
        if (completeSetupBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          completeSetupBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 24;
        return this;
      }
      /**
       * <code>.CompleteSetup complete_setup = 24;</code>
       */
      public Builder mergeCompleteSetup(com.amazon.alexa.accessory.protocol.Device.CompleteSetup value) {
        if (completeSetupBuilder_ == null) {
          if (payloadCase_ == 24 &&
              payload_ != com.amazon.alexa.accessory.protocol.Device.CompleteSetup.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.CompleteSetup.newBuilder((com.amazon.alexa.accessory.protocol.Device.CompleteSetup) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 24) {
            completeSetupBuilder_.mergeFrom(value);
          } else {
            completeSetupBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 24;
        return this;
      }
      /**
       * <code>.CompleteSetup complete_setup = 24;</code>
       */
      public Builder clearCompleteSetup() {
        if (completeSetupBuilder_ == null) {
          if (payloadCase_ == 24) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 24) {
            payloadCase_ = 0;
            payload_ = null;
          }
          completeSetupBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.CompleteSetup complete_setup = 24;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.CompleteSetup.Builder getCompleteSetupBuilder() {
        return getCompleteSetupFieldBuilder().getBuilder();
      }
      /**
       * <code>.CompleteSetup complete_setup = 24;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.CompleteSetupOrBuilder getCompleteSetupOrBuilder() {
        if ((payloadCase_ == 24) && (completeSetupBuilder_ != null)) {
          return completeSetupBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 24) {
            return (com.amazon.alexa.accessory.protocol.Device.CompleteSetup) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.CompleteSetup.getDefaultInstance();
        }
      }
      /**
       * <code>.CompleteSetup complete_setup = 24;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.CompleteSetup, com.amazon.alexa.accessory.protocol.Device.CompleteSetup.Builder, com.amazon.alexa.accessory.protocol.Device.CompleteSetupOrBuilder> 
          getCompleteSetupFieldBuilder() {
        if (completeSetupBuilder_ == null) {
          if (!(payloadCase_ == 24)) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.CompleteSetup.getDefaultInstance();
          }
          completeSetupBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Device.CompleteSetup, com.amazon.alexa.accessory.protocol.Device.CompleteSetup.Builder, com.amazon.alexa.accessory.protocol.Device.CompleteSetupOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Device.CompleteSetup) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 24;
        onChanged();;
        return completeSetupBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration, com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration.Builder, com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfigurationOrBuilder> notifyDeviceConfigurationBuilder_;
      /**
       * <code>.NotifyDeviceConfiguration notify_device_configuration = 25;</code>
       * @return Whether the notifyDeviceConfiguration field is set.
       */
      @java.lang.Override
      public boolean hasNotifyDeviceConfiguration() {
        return payloadCase_ == 25;
      }
      /**
       * <code>.NotifyDeviceConfiguration notify_device_configuration = 25;</code>
       * @return The notifyDeviceConfiguration.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration getNotifyDeviceConfiguration() {
        if (notifyDeviceConfigurationBuilder_ == null) {
          if (payloadCase_ == 25) {
            return (com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration.getDefaultInstance();
        } else {
          if (payloadCase_ == 25) {
            return notifyDeviceConfigurationBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration.getDefaultInstance();
        }
      }
      /**
       * <code>.NotifyDeviceConfiguration notify_device_configuration = 25;</code>
       */
      public Builder setNotifyDeviceConfiguration(com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration value) {
        if (notifyDeviceConfigurationBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          notifyDeviceConfigurationBuilder_.setMessage(value);
        }
        payloadCase_ = 25;
        return this;
      }
      /**
       * <code>.NotifyDeviceConfiguration notify_device_configuration = 25;</code>
       */
      public Builder setNotifyDeviceConfiguration(
          com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration.Builder builderForValue) {
        if (notifyDeviceConfigurationBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          notifyDeviceConfigurationBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 25;
        return this;
      }
      /**
       * <code>.NotifyDeviceConfiguration notify_device_configuration = 25;</code>
       */
      public Builder mergeNotifyDeviceConfiguration(com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration value) {
        if (notifyDeviceConfigurationBuilder_ == null) {
          if (payloadCase_ == 25 &&
              payload_ != com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration.newBuilder((com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 25) {
            notifyDeviceConfigurationBuilder_.mergeFrom(value);
          } else {
            notifyDeviceConfigurationBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 25;
        return this;
      }
      /**
       * <code>.NotifyDeviceConfiguration notify_device_configuration = 25;</code>
       */
      public Builder clearNotifyDeviceConfiguration() {
        if (notifyDeviceConfigurationBuilder_ == null) {
          if (payloadCase_ == 25) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 25) {
            payloadCase_ = 0;
            payload_ = null;
          }
          notifyDeviceConfigurationBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.NotifyDeviceConfiguration notify_device_configuration = 25;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration.Builder getNotifyDeviceConfigurationBuilder() {
        return getNotifyDeviceConfigurationFieldBuilder().getBuilder();
      }
      /**
       * <code>.NotifyDeviceConfiguration notify_device_configuration = 25;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfigurationOrBuilder getNotifyDeviceConfigurationOrBuilder() {
        if ((payloadCase_ == 25) && (notifyDeviceConfigurationBuilder_ != null)) {
          return notifyDeviceConfigurationBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 25) {
            return (com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration.getDefaultInstance();
        }
      }
      /**
       * <code>.NotifyDeviceConfiguration notify_device_configuration = 25;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration, com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration.Builder, com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfigurationOrBuilder> 
          getNotifyDeviceConfigurationFieldBuilder() {
        if (notifyDeviceConfigurationBuilder_ == null) {
          if (!(payloadCase_ == 25)) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration.getDefaultInstance();
          }
          notifyDeviceConfigurationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration, com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration.Builder, com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfigurationOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Device.NotifyDeviceConfiguration) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 25;
        onChanged();;
        return notifyDeviceConfigurationBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation, com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation.Builder, com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformationOrBuilder> updateDeviceInformationBuilder_;
      /**
       * <code>.UpdateDeviceInformation update_device_information = 26;</code>
       * @return Whether the updateDeviceInformation field is set.
       */
      @java.lang.Override
      public boolean hasUpdateDeviceInformation() {
        return payloadCase_ == 26;
      }
      /**
       * <code>.UpdateDeviceInformation update_device_information = 26;</code>
       * @return The updateDeviceInformation.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation getUpdateDeviceInformation() {
        if (updateDeviceInformationBuilder_ == null) {
          if (payloadCase_ == 26) {
            return (com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation.getDefaultInstance();
        } else {
          if (payloadCase_ == 26) {
            return updateDeviceInformationBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation.getDefaultInstance();
        }
      }
      /**
       * <code>.UpdateDeviceInformation update_device_information = 26;</code>
       */
      public Builder setUpdateDeviceInformation(com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation value) {
        if (updateDeviceInformationBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          updateDeviceInformationBuilder_.setMessage(value);
        }
        payloadCase_ = 26;
        return this;
      }
      /**
       * <code>.UpdateDeviceInformation update_device_information = 26;</code>
       */
      public Builder setUpdateDeviceInformation(
          com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation.Builder builderForValue) {
        if (updateDeviceInformationBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          updateDeviceInformationBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 26;
        return this;
      }
      /**
       * <code>.UpdateDeviceInformation update_device_information = 26;</code>
       */
      public Builder mergeUpdateDeviceInformation(com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation value) {
        if (updateDeviceInformationBuilder_ == null) {
          if (payloadCase_ == 26 &&
              payload_ != com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation.newBuilder((com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 26) {
            updateDeviceInformationBuilder_.mergeFrom(value);
          } else {
            updateDeviceInformationBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 26;
        return this;
      }
      /**
       * <code>.UpdateDeviceInformation update_device_information = 26;</code>
       */
      public Builder clearUpdateDeviceInformation() {
        if (updateDeviceInformationBuilder_ == null) {
          if (payloadCase_ == 26) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 26) {
            payloadCase_ = 0;
            payload_ = null;
          }
          updateDeviceInformationBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.UpdateDeviceInformation update_device_information = 26;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation.Builder getUpdateDeviceInformationBuilder() {
        return getUpdateDeviceInformationFieldBuilder().getBuilder();
      }
      /**
       * <code>.UpdateDeviceInformation update_device_information = 26;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformationOrBuilder getUpdateDeviceInformationOrBuilder() {
        if ((payloadCase_ == 26) && (updateDeviceInformationBuilder_ != null)) {
          return updateDeviceInformationBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 26) {
            return (com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation.getDefaultInstance();
        }
      }
      /**
       * <code>.UpdateDeviceInformation update_device_information = 26;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation, com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation.Builder, com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformationOrBuilder> 
          getUpdateDeviceInformationFieldBuilder() {
        if (updateDeviceInformationBuilder_ == null) {
          if (!(payloadCase_ == 26)) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation.getDefaultInstance();
          }
          updateDeviceInformationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation, com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation.Builder, com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformationOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Device.UpdateDeviceInformation) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 26;
        onChanged();;
        return updateDeviceInformationBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation, com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation.Builder, com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformationOrBuilder> notifyDeviceInformationBuilder_;
      /**
       * <code>.NotifyDeviceInformation notify_device_information = 27;</code>
       * @return Whether the notifyDeviceInformation field is set.
       */
      @java.lang.Override
      public boolean hasNotifyDeviceInformation() {
        return payloadCase_ == 27;
      }
      /**
       * <code>.NotifyDeviceInformation notify_device_information = 27;</code>
       * @return The notifyDeviceInformation.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation getNotifyDeviceInformation() {
        if (notifyDeviceInformationBuilder_ == null) {
          if (payloadCase_ == 27) {
            return (com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation.getDefaultInstance();
        } else {
          if (payloadCase_ == 27) {
            return notifyDeviceInformationBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation.getDefaultInstance();
        }
      }
      /**
       * <code>.NotifyDeviceInformation notify_device_information = 27;</code>
       */
      public Builder setNotifyDeviceInformation(com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation value) {
        if (notifyDeviceInformationBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          notifyDeviceInformationBuilder_.setMessage(value);
        }
        payloadCase_ = 27;
        return this;
      }
      /**
       * <code>.NotifyDeviceInformation notify_device_information = 27;</code>
       */
      public Builder setNotifyDeviceInformation(
          com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation.Builder builderForValue) {
        if (notifyDeviceInformationBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          notifyDeviceInformationBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 27;
        return this;
      }
      /**
       * <code>.NotifyDeviceInformation notify_device_information = 27;</code>
       */
      public Builder mergeNotifyDeviceInformation(com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation value) {
        if (notifyDeviceInformationBuilder_ == null) {
          if (payloadCase_ == 27 &&
              payload_ != com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation.newBuilder((com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 27) {
            notifyDeviceInformationBuilder_.mergeFrom(value);
          } else {
            notifyDeviceInformationBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 27;
        return this;
      }
      /**
       * <code>.NotifyDeviceInformation notify_device_information = 27;</code>
       */
      public Builder clearNotifyDeviceInformation() {
        if (notifyDeviceInformationBuilder_ == null) {
          if (payloadCase_ == 27) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 27) {
            payloadCase_ = 0;
            payload_ = null;
          }
          notifyDeviceInformationBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.NotifyDeviceInformation notify_device_information = 27;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation.Builder getNotifyDeviceInformationBuilder() {
        return getNotifyDeviceInformationFieldBuilder().getBuilder();
      }
      /**
       * <code>.NotifyDeviceInformation notify_device_information = 27;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformationOrBuilder getNotifyDeviceInformationOrBuilder() {
        if ((payloadCase_ == 27) && (notifyDeviceInformationBuilder_ != null)) {
          return notifyDeviceInformationBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 27) {
            return (com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation.getDefaultInstance();
        }
      }
      /**
       * <code>.NotifyDeviceInformation notify_device_information = 27;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation, com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation.Builder, com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformationOrBuilder> 
          getNotifyDeviceInformationFieldBuilder() {
        if (notifyDeviceInformationBuilder_ == null) {
          if (!(payloadCase_ == 27)) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation.getDefaultInstance();
          }
          notifyDeviceInformationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation, com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation.Builder, com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformationOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Device.NotifyDeviceInformation) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 27;
        onChanged();;
        return notifyDeviceInformationBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures, com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures.Builder, com.amazon.alexa.accessory.protocol.Device.GetDeviceFeaturesOrBuilder> getDeviceFeaturesBuilder_;
      /**
       * <code>.GetDeviceFeatures get_device_features = 28;</code>
       * @return Whether the getDeviceFeatures field is set.
       */
      @java.lang.Override
      public boolean hasGetDeviceFeatures() {
        return payloadCase_ == 28;
      }
      /**
       * <code>.GetDeviceFeatures get_device_features = 28;</code>
       * @return The getDeviceFeatures.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures getGetDeviceFeatures() {
        if (getDeviceFeaturesBuilder_ == null) {
          if (payloadCase_ == 28) {
            return (com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures.getDefaultInstance();
        } else {
          if (payloadCase_ == 28) {
            return getDeviceFeaturesBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures.getDefaultInstance();
        }
      }
      /**
       * <code>.GetDeviceFeatures get_device_features = 28;</code>
       */
      public Builder setGetDeviceFeatures(com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures value) {
        if (getDeviceFeaturesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          getDeviceFeaturesBuilder_.setMessage(value);
        }
        payloadCase_ = 28;
        return this;
      }
      /**
       * <code>.GetDeviceFeatures get_device_features = 28;</code>
       */
      public Builder setGetDeviceFeatures(
          com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures.Builder builderForValue) {
        if (getDeviceFeaturesBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          getDeviceFeaturesBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 28;
        return this;
      }
      /**
       * <code>.GetDeviceFeatures get_device_features = 28;</code>
       */
      public Builder mergeGetDeviceFeatures(com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures value) {
        if (getDeviceFeaturesBuilder_ == null) {
          if (payloadCase_ == 28 &&
              payload_ != com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures.newBuilder((com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 28) {
            getDeviceFeaturesBuilder_.mergeFrom(value);
          } else {
            getDeviceFeaturesBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 28;
        return this;
      }
      /**
       * <code>.GetDeviceFeatures get_device_features = 28;</code>
       */
      public Builder clearGetDeviceFeatures() {
        if (getDeviceFeaturesBuilder_ == null) {
          if (payloadCase_ == 28) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 28) {
            payloadCase_ = 0;
            payload_ = null;
          }
          getDeviceFeaturesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.GetDeviceFeatures get_device_features = 28;</code>
       */
      public com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures.Builder getGetDeviceFeaturesBuilder() {
        return getGetDeviceFeaturesFieldBuilder().getBuilder();
      }
      /**
       * <code>.GetDeviceFeatures get_device_features = 28;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Device.GetDeviceFeaturesOrBuilder getGetDeviceFeaturesOrBuilder() {
        if ((payloadCase_ == 28) && (getDeviceFeaturesBuilder_ != null)) {
          return getDeviceFeaturesBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 28) {
            return (com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures.getDefaultInstance();
        }
      }
      /**
       * <code>.GetDeviceFeatures get_device_features = 28;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures, com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures.Builder, com.amazon.alexa.accessory.protocol.Device.GetDeviceFeaturesOrBuilder> 
          getGetDeviceFeaturesFieldBuilder() {
        if (getDeviceFeaturesBuilder_ == null) {
          if (!(payloadCase_ == 28)) {
            payload_ = com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures.getDefaultInstance();
          }
          getDeviceFeaturesBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures, com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures.Builder, com.amazon.alexa.accessory.protocol.Device.GetDeviceFeaturesOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Device.GetDeviceFeatures) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 28;
        onChanged();;
        return getDeviceFeaturesBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Media.IssueMediaControl, com.amazon.alexa.accessory.protocol.Media.IssueMediaControl.Builder, com.amazon.alexa.accessory.protocol.Media.IssueMediaControlOrBuilder> issueMediaControlBuilder_;
      /**
       * <code>.IssueMediaControl issue_media_control = 60;</code>
       * @return Whether the issueMediaControl field is set.
       */
      @java.lang.Override
      public boolean hasIssueMediaControl() {
        return payloadCase_ == 60;
      }
      /**
       * <code>.IssueMediaControl issue_media_control = 60;</code>
       * @return The issueMediaControl.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Media.IssueMediaControl getIssueMediaControl() {
        if (issueMediaControlBuilder_ == null) {
          if (payloadCase_ == 60) {
            return (com.amazon.alexa.accessory.protocol.Media.IssueMediaControl) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Media.IssueMediaControl.getDefaultInstance();
        } else {
          if (payloadCase_ == 60) {
            return issueMediaControlBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.Media.IssueMediaControl.getDefaultInstance();
        }
      }
      /**
       * <code>.IssueMediaControl issue_media_control = 60;</code>
       */
      public Builder setIssueMediaControl(com.amazon.alexa.accessory.protocol.Media.IssueMediaControl value) {
        if (issueMediaControlBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          issueMediaControlBuilder_.setMessage(value);
        }
        payloadCase_ = 60;
        return this;
      }
      /**
       * <code>.IssueMediaControl issue_media_control = 60;</code>
       */
      public Builder setIssueMediaControl(
          com.amazon.alexa.accessory.protocol.Media.IssueMediaControl.Builder builderForValue) {
        if (issueMediaControlBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          issueMediaControlBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 60;
        return this;
      }
      /**
       * <code>.IssueMediaControl issue_media_control = 60;</code>
       */
      public Builder mergeIssueMediaControl(com.amazon.alexa.accessory.protocol.Media.IssueMediaControl value) {
        if (issueMediaControlBuilder_ == null) {
          if (payloadCase_ == 60 &&
              payload_ != com.amazon.alexa.accessory.protocol.Media.IssueMediaControl.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.Media.IssueMediaControl.newBuilder((com.amazon.alexa.accessory.protocol.Media.IssueMediaControl) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 60) {
            issueMediaControlBuilder_.mergeFrom(value);
          } else {
            issueMediaControlBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 60;
        return this;
      }
      /**
       * <code>.IssueMediaControl issue_media_control = 60;</code>
       */
      public Builder clearIssueMediaControl() {
        if (issueMediaControlBuilder_ == null) {
          if (payloadCase_ == 60) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 60) {
            payloadCase_ = 0;
            payload_ = null;
          }
          issueMediaControlBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.IssueMediaControl issue_media_control = 60;</code>
       */
      public com.amazon.alexa.accessory.protocol.Media.IssueMediaControl.Builder getIssueMediaControlBuilder() {
        return getIssueMediaControlFieldBuilder().getBuilder();
      }
      /**
       * <code>.IssueMediaControl issue_media_control = 60;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.Media.IssueMediaControlOrBuilder getIssueMediaControlOrBuilder() {
        if ((payloadCase_ == 60) && (issueMediaControlBuilder_ != null)) {
          return issueMediaControlBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 60) {
            return (com.amazon.alexa.accessory.protocol.Media.IssueMediaControl) payload_;
          }
          return com.amazon.alexa.accessory.protocol.Media.IssueMediaControl.getDefaultInstance();
        }
      }
      /**
       * <code>.IssueMediaControl issue_media_control = 60;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.Media.IssueMediaControl, com.amazon.alexa.accessory.protocol.Media.IssueMediaControl.Builder, com.amazon.alexa.accessory.protocol.Media.IssueMediaControlOrBuilder> 
          getIssueMediaControlFieldBuilder() {
        if (issueMediaControlBuilder_ == null) {
          if (!(payloadCase_ == 60)) {
            payload_ = com.amazon.alexa.accessory.protocol.Media.IssueMediaControl.getDefaultInstance();
          }
          issueMediaControlBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.Media.IssueMediaControl, com.amazon.alexa.accessory.protocol.Media.IssueMediaControl.Builder, com.amazon.alexa.accessory.protocol.Media.IssueMediaControlOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.Media.IssueMediaControl) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 60;
        onChanged();;
        return issueMediaControlBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.StateOuterClass.GetState, com.amazon.alexa.accessory.protocol.StateOuterClass.GetState.Builder, com.amazon.alexa.accessory.protocol.StateOuterClass.GetStateOrBuilder> getStateBuilder_;
      /**
       * <code>.GetState get_state = 100;</code>
       * @return Whether the getState field is set.
       */
      @java.lang.Override
      public boolean hasGetState() {
        return payloadCase_ == 100;
      }
      /**
       * <code>.GetState get_state = 100;</code>
       * @return The getState.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.StateOuterClass.GetState getGetState() {
        if (getStateBuilder_ == null) {
          if (payloadCase_ == 100) {
            return (com.amazon.alexa.accessory.protocol.StateOuterClass.GetState) payload_;
          }
          return com.amazon.alexa.accessory.protocol.StateOuterClass.GetState.getDefaultInstance();
        } else {
          if (payloadCase_ == 100) {
            return getStateBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.StateOuterClass.GetState.getDefaultInstance();
        }
      }
      /**
       * <code>.GetState get_state = 100;</code>
       */
      public Builder setGetState(com.amazon.alexa.accessory.protocol.StateOuterClass.GetState value) {
        if (getStateBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          getStateBuilder_.setMessage(value);
        }
        payloadCase_ = 100;
        return this;
      }
      /**
       * <code>.GetState get_state = 100;</code>
       */
      public Builder setGetState(
          com.amazon.alexa.accessory.protocol.StateOuterClass.GetState.Builder builderForValue) {
        if (getStateBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          getStateBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 100;
        return this;
      }
      /**
       * <code>.GetState get_state = 100;</code>
       */
      public Builder mergeGetState(com.amazon.alexa.accessory.protocol.StateOuterClass.GetState value) {
        if (getStateBuilder_ == null) {
          if (payloadCase_ == 100 &&
              payload_ != com.amazon.alexa.accessory.protocol.StateOuterClass.GetState.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.StateOuterClass.GetState.newBuilder((com.amazon.alexa.accessory.protocol.StateOuterClass.GetState) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 100) {
            getStateBuilder_.mergeFrom(value);
          } else {
            getStateBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 100;
        return this;
      }
      /**
       * <code>.GetState get_state = 100;</code>
       */
      public Builder clearGetState() {
        if (getStateBuilder_ == null) {
          if (payloadCase_ == 100) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 100) {
            payloadCase_ = 0;
            payload_ = null;
          }
          getStateBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.GetState get_state = 100;</code>
       */
      public com.amazon.alexa.accessory.protocol.StateOuterClass.GetState.Builder getGetStateBuilder() {
        return getGetStateFieldBuilder().getBuilder();
      }
      /**
       * <code>.GetState get_state = 100;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.StateOuterClass.GetStateOrBuilder getGetStateOrBuilder() {
        if ((payloadCase_ == 100) && (getStateBuilder_ != null)) {
          return getStateBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 100) {
            return (com.amazon.alexa.accessory.protocol.StateOuterClass.GetState) payload_;
          }
          return com.amazon.alexa.accessory.protocol.StateOuterClass.GetState.getDefaultInstance();
        }
      }
      /**
       * <code>.GetState get_state = 100;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.StateOuterClass.GetState, com.amazon.alexa.accessory.protocol.StateOuterClass.GetState.Builder, com.amazon.alexa.accessory.protocol.StateOuterClass.GetStateOrBuilder> 
          getGetStateFieldBuilder() {
        if (getStateBuilder_ == null) {
          if (!(payloadCase_ == 100)) {
            payload_ = com.amazon.alexa.accessory.protocol.StateOuterClass.GetState.getDefaultInstance();
          }
          getStateBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.StateOuterClass.GetState, com.amazon.alexa.accessory.protocol.StateOuterClass.GetState.Builder, com.amazon.alexa.accessory.protocol.StateOuterClass.GetStateOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.StateOuterClass.GetState) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 100;
        onChanged();;
        return getStateBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.StateOuterClass.SetState, com.amazon.alexa.accessory.protocol.StateOuterClass.SetState.Builder, com.amazon.alexa.accessory.protocol.StateOuterClass.SetStateOrBuilder> setStateBuilder_;
      /**
       * <code>.SetState set_state = 101;</code>
       * @return Whether the setState field is set.
       */
      @java.lang.Override
      public boolean hasSetState() {
        return payloadCase_ == 101;
      }
      /**
       * <code>.SetState set_state = 101;</code>
       * @return The setState.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.StateOuterClass.SetState getSetState() {
        if (setStateBuilder_ == null) {
          if (payloadCase_ == 101) {
            return (com.amazon.alexa.accessory.protocol.StateOuterClass.SetState) payload_;
          }
          return com.amazon.alexa.accessory.protocol.StateOuterClass.SetState.getDefaultInstance();
        } else {
          if (payloadCase_ == 101) {
            return setStateBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.StateOuterClass.SetState.getDefaultInstance();
        }
      }
      /**
       * <code>.SetState set_state = 101;</code>
       */
      public Builder setSetState(com.amazon.alexa.accessory.protocol.StateOuterClass.SetState value) {
        if (setStateBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          setStateBuilder_.setMessage(value);
        }
        payloadCase_ = 101;
        return this;
      }
      /**
       * <code>.SetState set_state = 101;</code>
       */
      public Builder setSetState(
          com.amazon.alexa.accessory.protocol.StateOuterClass.SetState.Builder builderForValue) {
        if (setStateBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          setStateBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 101;
        return this;
      }
      /**
       * <code>.SetState set_state = 101;</code>
       */
      public Builder mergeSetState(com.amazon.alexa.accessory.protocol.StateOuterClass.SetState value) {
        if (setStateBuilder_ == null) {
          if (payloadCase_ == 101 &&
              payload_ != com.amazon.alexa.accessory.protocol.StateOuterClass.SetState.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.StateOuterClass.SetState.newBuilder((com.amazon.alexa.accessory.protocol.StateOuterClass.SetState) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 101) {
            setStateBuilder_.mergeFrom(value);
          } else {
            setStateBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 101;
        return this;
      }
      /**
       * <code>.SetState set_state = 101;</code>
       */
      public Builder clearSetState() {
        if (setStateBuilder_ == null) {
          if (payloadCase_ == 101) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 101) {
            payloadCase_ = 0;
            payload_ = null;
          }
          setStateBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.SetState set_state = 101;</code>
       */
      public com.amazon.alexa.accessory.protocol.StateOuterClass.SetState.Builder getSetStateBuilder() {
        return getSetStateFieldBuilder().getBuilder();
      }
      /**
       * <code>.SetState set_state = 101;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.StateOuterClass.SetStateOrBuilder getSetStateOrBuilder() {
        if ((payloadCase_ == 101) && (setStateBuilder_ != null)) {
          return setStateBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 101) {
            return (com.amazon.alexa.accessory.protocol.StateOuterClass.SetState) payload_;
          }
          return com.amazon.alexa.accessory.protocol.StateOuterClass.SetState.getDefaultInstance();
        }
      }
      /**
       * <code>.SetState set_state = 101;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.StateOuterClass.SetState, com.amazon.alexa.accessory.protocol.StateOuterClass.SetState.Builder, com.amazon.alexa.accessory.protocol.StateOuterClass.SetStateOrBuilder> 
          getSetStateFieldBuilder() {
        if (setStateBuilder_ == null) {
          if (!(payloadCase_ == 101)) {
            payload_ = com.amazon.alexa.accessory.protocol.StateOuterClass.SetState.getDefaultInstance();
          }
          setStateBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.StateOuterClass.SetState, com.amazon.alexa.accessory.protocol.StateOuterClass.SetState.Builder, com.amazon.alexa.accessory.protocol.StateOuterClass.SetStateOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.StateOuterClass.SetState) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 101;
        onChanged();;
        return setStateBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState, com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState.Builder, com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeStateOrBuilder> synchronizeStateBuilder_;
      /**
       * <code>.SynchronizeState synchronize_state = 102;</code>
       * @return Whether the synchronizeState field is set.
       */
      @java.lang.Override
      public boolean hasSynchronizeState() {
        return payloadCase_ == 102;
      }
      /**
       * <code>.SynchronizeState synchronize_state = 102;</code>
       * @return The synchronizeState.
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState getSynchronizeState() {
        if (synchronizeStateBuilder_ == null) {
          if (payloadCase_ == 102) {
            return (com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState) payload_;
          }
          return com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState.getDefaultInstance();
        } else {
          if (payloadCase_ == 102) {
            return synchronizeStateBuilder_.getMessage();
          }
          return com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState.getDefaultInstance();
        }
      }
      /**
       * <code>.SynchronizeState synchronize_state = 102;</code>
       */
      public Builder setSynchronizeState(com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState value) {
        if (synchronizeStateBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          synchronizeStateBuilder_.setMessage(value);
        }
        payloadCase_ = 102;
        return this;
      }
      /**
       * <code>.SynchronizeState synchronize_state = 102;</code>
       */
      public Builder setSynchronizeState(
          com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState.Builder builderForValue) {
        if (synchronizeStateBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          synchronizeStateBuilder_.setMessage(builderForValue.build());
        }
        payloadCase_ = 102;
        return this;
      }
      /**
       * <code>.SynchronizeState synchronize_state = 102;</code>
       */
      public Builder mergeSynchronizeState(com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState value) {
        if (synchronizeStateBuilder_ == null) {
          if (payloadCase_ == 102 &&
              payload_ != com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState.getDefaultInstance()) {
            payload_ = com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState.newBuilder((com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState) payload_)
                .mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          if (payloadCase_ == 102) {
            synchronizeStateBuilder_.mergeFrom(value);
          } else {
            synchronizeStateBuilder_.setMessage(value);
          }
        }
        payloadCase_ = 102;
        return this;
      }
      /**
       * <code>.SynchronizeState synchronize_state = 102;</code>
       */
      public Builder clearSynchronizeState() {
        if (synchronizeStateBuilder_ == null) {
          if (payloadCase_ == 102) {
            payloadCase_ = 0;
            payload_ = null;
            onChanged();
          }
        } else {
          if (payloadCase_ == 102) {
            payloadCase_ = 0;
            payload_ = null;
          }
          synchronizeStateBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.SynchronizeState synchronize_state = 102;</code>
       */
      public com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState.Builder getSynchronizeStateBuilder() {
        return getSynchronizeStateFieldBuilder().getBuilder();
      }
      /**
       * <code>.SynchronizeState synchronize_state = 102;</code>
       */
      @java.lang.Override
      public com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeStateOrBuilder getSynchronizeStateOrBuilder() {
        if ((payloadCase_ == 102) && (synchronizeStateBuilder_ != null)) {
          return synchronizeStateBuilder_.getMessageOrBuilder();
        } else {
          if (payloadCase_ == 102) {
            return (com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState) payload_;
          }
          return com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState.getDefaultInstance();
        }
      }
      /**
       * <code>.SynchronizeState synchronize_state = 102;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState, com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState.Builder, com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeStateOrBuilder> 
          getSynchronizeStateFieldBuilder() {
        if (synchronizeStateBuilder_ == null) {
          if (!(payloadCase_ == 102)) {
            payload_ = com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState.getDefaultInstance();
          }
          synchronizeStateBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState, com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState.Builder, com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeStateOrBuilder>(
                  (com.amazon.alexa.accessory.protocol.StateOuterClass.SynchronizeState) payload_,
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        payloadCase_ = 102;
        onChanged();;
        return synchronizeStateBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ControlEnvelope)
    }

    // @@protoc_insertion_point(class_scope:ControlEnvelope)
    private static final com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope();
    }

    public static com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ControlEnvelope>
        PARSER = new com.google.protobuf.AbstractParser<ControlEnvelope>() {
      @java.lang.Override
      public ControlEnvelope parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ControlEnvelope> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ControlEnvelope> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.amazon.alexa.accessory.protocol.Accessories.ControlEnvelope getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Response_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Response_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ControlEnvelope_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ControlEnvelope_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\021accessories.proto\032\014common.proto\032\014syste" +
      "m.proto\032\017transport.proto\032\014speech.proto\032\r" +
      "calling.proto\032\rcentral.proto\032\014device.pro" +
      "to\032\013media.proto\032\013state.proto\"\254\003\n\010Respons" +
      "e\022\036\n\nerror_code\030\001 \001(\0162\n.ErrorCode\022\033\n\007loc" +
      "ales\030\025 \001(\0132\010.LocalesH\000\0220\n\022connection_det" +
      "ails\030\010 \001(\0132\022.ConnectionDetailsH\000\022\031\n\006dial" +
      "og\030\016 \001(\0132\007.DialogH\000\022*\n\017speech_provider\030\017" +
      " \001(\0132\017.SpeechProviderH\000\0222\n\023central_infor" +
      "mation\030\r \001(\0132\023.CentralInformationH\000\0220\n\022d" +
      "evice_information\030\003 \001(\0132\022.DeviceInformat" +
      "ionH\000\0224\n\024device_configuration\030\n \001(\0132\024.De" +
      "viceConfigurationH\000\022*\n\017device_features\030\034" +
      " \001(\0132\017.DeviceFeaturesH\000\022\027\n\005state\030\007 \001(\0132\006" +
      ".StateH\000B\t\n\007payload\"\276\013\n\017ControlEnvelope\022" +
      "\031\n\007command\030\001 \001(\0162\010.Command\022\035\n\010response\030\t" +
      " \001(\0132\t.ResponseH\000\022,\n\020reset_connection\0303 " +
      "\001(\0132\020.ResetConnectionH\000\0224\n\024synchronize_s" +
      "ettings\0302 \001(\0132\024.SynchronizeSettingsH\000\022 \n" +
      "\nkeep_alive\0307 \001(\0132\n.KeepAliveH\000\022&\n\rremov" +
      "e_device\0308 \001(\0132\r.RemoveDeviceH\000\022\"\n\013get_l" +
      "ocales\0309 \001(\0132\013.GetLocalesH\000\022 \n\nset_local" +
      "e\030: \001(\0132\n.SetLocaleH\000\022 \n\nlaunch_app\030; \001(" +
      "\0132\n.LaunchAppH\000\022.\n\021upgrade_transport\030\036 \001" +
      "(\0132\021.UpgradeTransportH\000\022,\n\020switch_transp" +
      "ort\030\037 \001(\0132\020.SwitchTransportH\000\022$\n\014start_s" +
      "peech\030\013 \001(\0132\014.StartSpeechH\000\022(\n\016provide_s" +
      "peech\030\n \001(\0132\016.ProvideSpeechH\000\022\"\n\013stop_sp" +
      "eech\030\014 \001(\0132\013.StopSpeechH\000\022*\n\017endpoint_sp" +
      "eech\030\r \001(\0132\017.EndpointSpeechH\000\0221\n\023notify_" +
      "speech_state\030\016 \001(\0132\022.NotifySpeechStateH\000" +
      "\022/\n\022forward_at_command\030( \001(\0132\021.ForwardAT" +
      "CommandH\000\022&\n\rincoming_call\030) \001(\0132\r.Incom" +
      "ingCallH\000\0229\n\027get_central_information\030g \001" +
      "(\0132\026.GetCentralInformationH\000\0227\n\026get_devi" +
      "ce_information\030\024 \001(\0132\025.GetDeviceInformat" +
      "ionH\000\022;\n\030get_device_configuration\030\025 \001(\0132" +
      "\027.GetDeviceConfigurationH\000\0220\n\022override_a" +
      "ssistant\030\026 \001(\0132\022.OverrideAssistantH\000\022\"\n\013" +
      "start_setup\030\027 \001(\0132\013.StartSetupH\000\022(\n\016comp" +
      "lete_setup\030\030 \001(\0132\016.CompleteSetupH\000\022A\n\033no" +
      "tify_device_configuration\030\031 \001(\0132\032.Notify" +
      "DeviceConfigurationH\000\022=\n\031update_device_i" +
      "nformation\030\032 \001(\0132\030.UpdateDeviceInformati" +
      "onH\000\022=\n\031notify_device_information\030\033 \001(\0132" +
      "\030.NotifyDeviceInformationH\000\0221\n\023get_devic" +
      "e_features\030\034 \001(\0132\022.GetDeviceFeaturesH\000\0221" +
      "\n\023issue_media_control\030< \001(\0132\022.IssueMedia" +
      "ControlH\000\022\036\n\tget_state\030d \001(\0132\t.GetStateH" +
      "\000\022\036\n\tset_state\030e \001(\0132\t.SetStateH\000\022.\n\021syn" +
      "chronize_state\030f \001(\0132\021.SynchronizeStateH" +
      "\000B\t\n\007payload*\256\005\n\007Command\022\010\n\004NONE\020\000\022\024\n\020RE" +
      "SET_CONNECTION\0203\022\030\n\024SYNCHRONIZE_SETTINGS" +
      "\0202\022\016\n\nKEEP_ALIVE\0207\022\021\n\rREMOVE_DEVICE\0208\022\017\n" +
      "\013GET_LOCALES\0209\022\016\n\nSET_LOCALE\020:\022\016\n\nLAUNCH" +
      "_APP\020;\022\025\n\021UPGRADE_TRANSPORT\020\036\022\024\n\020SWITCH_" +
      "TRANSPORT\020\037\022\020\n\014START_SPEECH\020\013\022\022\n\016PROVIDE" +
      "_SPEECH\020\n\022\017\n\013STOP_SPEECH\020\014\022\023\n\017ENDPOINT_S" +
      "PEECH\020\r\022\027\n\023NOTIFY_SPEECH_STATE\020\016\022\026\n\022FORW" +
      "ARD_AT_COMMAND\020(\022\021\n\rINCOMING_CALL\020)\022\033\n\027G" +
      "ET_CENTRAL_INFORMATION\020g\022\032\n\026GET_DEVICE_I" +
      "NFORMATION\020\024\022\034\n\030GET_DEVICE_CONFIGURATION" +
      "\020\025\022\026\n\022OVERRIDE_ASSISTANT\020\026\022\017\n\013START_SETU" +
      "P\020\027\022\022\n\016COMPLETE_SETUP\020\030\022\037\n\033NOTIFY_DEVICE" +
      "_CONFIGURATION\020\031\022\035\n\031UPDATE_DEVICE_INFORM" +
      "ATION\020\032\022\035\n\031NOTIFY_DEVICE_INFORMATION\020\033\022\027" +
      "\n\023GET_DEVICE_FEATURES\020\034\022\027\n\023ISSUE_MEDIA_C" +
      "ONTROL\020<\022\r\n\tGET_STATE\020d\022\r\n\tSET_STATE\020e\022\025" +
      "\n\021SYNCHRONIZE_STATE\020fB0\n#com.amazon.alex" +
      "a.accessory.protocolH\003\240\001\001\242\002\003AACb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.amazon.alexa.accessory.protocol.Common.getDescriptor(),
          com.amazon.alexa.accessory.protocol.System.getDescriptor(),
          com.amazon.alexa.accessory.protocol.Transport.getDescriptor(),
          com.amazon.alexa.accessory.protocol.Speech.getDescriptor(),
          com.amazon.alexa.accessory.protocol.Calling.getDescriptor(),
          com.amazon.alexa.accessory.protocol.Central.getDescriptor(),
          com.amazon.alexa.accessory.protocol.Device.getDescriptor(),
          com.amazon.alexa.accessory.protocol.Media.getDescriptor(),
          com.amazon.alexa.accessory.protocol.StateOuterClass.getDescriptor(),
        });
    internal_static_Response_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Response_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Response_descriptor,
        new java.lang.String[] { "ErrorCode", "Locales", "ConnectionDetails", "Dialog", "SpeechProvider", "CentralInformation", "DeviceInformation", "DeviceConfiguration", "DeviceFeatures", "State", "Payload", });
    internal_static_ControlEnvelope_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_ControlEnvelope_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ControlEnvelope_descriptor,
        new java.lang.String[] { "Command", "Response", "ResetConnection", "SynchronizeSettings", "KeepAlive", "RemoveDevice", "GetLocales", "SetLocale", "LaunchApp", "UpgradeTransport", "SwitchTransport", "StartSpeech", "ProvideSpeech", "StopSpeech", "EndpointSpeech", "NotifySpeechState", "ForwardAtCommand", "IncomingCall", "GetCentralInformation", "GetDeviceInformation", "GetDeviceConfiguration", "OverrideAssistant", "StartSetup", "CompleteSetup", "NotifyDeviceConfiguration", "UpdateDeviceInformation", "NotifyDeviceInformation", "GetDeviceFeatures", "IssueMediaControl", "GetState", "SetState", "SynchronizeState", "Payload", });
    com.amazon.alexa.accessory.protocol.Common.getDescriptor();
    com.amazon.alexa.accessory.protocol.System.getDescriptor();
    com.amazon.alexa.accessory.protocol.Transport.getDescriptor();
    com.amazon.alexa.accessory.protocol.Speech.getDescriptor();
    com.amazon.alexa.accessory.protocol.Calling.getDescriptor();
    com.amazon.alexa.accessory.protocol.Central.getDescriptor();
    com.amazon.alexa.accessory.protocol.Device.getDescriptor();
    com.amazon.alexa.accessory.protocol.Media.getDescriptor();
    com.amazon.alexa.accessory.protocol.StateOuterClass.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
