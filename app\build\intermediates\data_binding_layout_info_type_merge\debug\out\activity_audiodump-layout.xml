<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_audiodump" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_audiodump.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_audiodump_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="315" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/activity_audiodump_0" include="toolbar"><Expressions/><location startLine="10" startOffset="4" endLine="13" endOffset="9"/></Target><Target id="@+id/loginfo" tag="layout/activity_audiodump_0" include="logview"><Expressions/><location startLine="15" startOffset="4" endLine="18" endOffset="34"/></Target><Target id="@+id/recordtime" view="Chronometer"><Expressions/><location startLine="20" startOffset="4" endLine="29" endOffset="9"/></Target><Target id="@+id/iv" view="ImageView"><Expressions/><location startLine="31" startOffset="4" endLine="36" endOffset="38"/></Target><Target id="@+id/package_receive_text" view="TextView"><Expressions/><location startLine="49" startOffset="8" endLine="56" endOffset="43"/></Target><Target id="@+id/package_lost_text" view="TextView"><Expressions/><location startLine="58" startOffset="8" endLine="64" endOffset="43"/></Target><Target id="@+id/device_text" view="TextView"><Expressions/><location startLine="69" startOffset="4" endLine="78" endOffset="38"/></Target><Target id="@+id/save_file_type" view="RadioGroup"><Expressions/><location startLine="99" startOffset="12" endLine="172" endOffset="24"/></Target><Target id="@+id/file_type_pcm" view="RadioButton"><Expressions/><location startLine="116" startOffset="16" endLine="121" endOffset="21"/></Target><Target id="@+id/file_type_wav" view="RadioButton"><Expressions/><location startLine="134" startOffset="16" endLine="138" endOffset="21"/></Target><Target id="@+id/file_type_flac" view="RadioButton"><Expressions/><location startLine="150" startOffset="16" endLine="154" endOffset="21"/></Target><Target id="@+id/file_type_custom" view="RadioButton"><Expressions/><location startLine="167" startOffset="16" endLine="171" endOffset="21"/></Target><Target id="@+id/et_file_type" view="EditText"><Expressions/><location startLine="174" startOffset="12" endLine="180" endOffset="49"/></Target><Target id="@+id/insert_data" view="RadioGroup"><Expressions/><location startLine="200" startOffset="8" endLine="255" endOffset="20"/></Target><Target id="@+id/insert_data_max" view="RadioButton"><Expressions/><location startLine="216" startOffset="12" endLine="221" endOffset="17"/></Target><Target id="@+id/insert_data_zero" view="RadioButton"><Expressions/><location startLine="234" startOffset="12" endLine="238" endOffset="17"/></Target><Target id="@+id/insert_data_lost" view="RadioButton"><Expressions/><location startLine="250" startOffset="12" endLine="254" endOffset="17"/></Target><Target id="@+id/stream_start_text" view="EditText"><Expressions/><location startLine="271" startOffset="8" endLine="279" endOffset="36"/></Target><Target id="@+id/start_audio_dump" view="Button"><Expressions/><location startLine="289" startOffset="8" endLine="294" endOffset="57"/></Target><Target id="@+id/stop_audio_dump" view="Button"><Expressions/><location startLine="296" startOffset="8" endLine="301" endOffset="61"/></Target><Target id="@+id/spp_stop" view="Button"><Expressions/><location startLine="304" startOffset="4" endLine="313" endOffset="34"/></Target></Targets></Layout>