// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentActionsBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final Button typeBle;

  @NonNull
  public final LinearLayout typeChoose;

  @NonNull
  public final Button typeSpp;

  private FragmentActionsBinding(@NonNull FrameLayout rootView, @NonNull Button typeBle,
      @NonNull LinearLayout typeChoose, @NonNull Button typeSpp) {
    this.rootView = rootView;
    this.typeBle = typeBle;
    this.typeChoose = typeChoose;
    this.typeSpp = typeSpp;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentActionsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentActionsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_actions, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentActionsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.type_ble;
      Button typeBle = rootView.findViewById(id);
      if (typeBle == null) {
        break missingId;
      }

      id = R.id.type_choose;
      LinearLayout typeChoose = rootView.findViewById(id);
      if (typeChoose == null) {
        break missingId;
      }

      id = R.id.type_spp;
      Button typeSpp = rootView.findViewById(id);
      if (typeSpp == null) {
        break missingId;
      }

      return new FragmentActionsBinding((FrameLayout) rootView, typeBle, typeChoose, typeSpp);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
