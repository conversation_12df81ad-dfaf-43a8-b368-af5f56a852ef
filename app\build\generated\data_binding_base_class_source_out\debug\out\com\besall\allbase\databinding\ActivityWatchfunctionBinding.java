// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityWatchfunctionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button findMy;

  @NonNull
  public final ToolbarBinding tool;

  @NonNull
  public final Button watchAvs;

  @NonNull
  public final Button watchDial;

  @NonNull
  public final Button wearableDevice;

  private ActivityWatchfunctionBinding(@NonNull LinearLayout rootView, @NonNull Button findMy,
      @NonNull ToolbarBinding tool, @NonNull Button watchAvs, @NonNull Button watchDial,
      @NonNull Button wearableDevice) {
    this.rootView = rootView;
    this.findMy = findMy;
    this.tool = tool;
    this.watchAvs = watchAvs;
    this.watchDial = watchDial;
    this.wearableDevice = wearableDevice;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityWatchfunctionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityWatchfunctionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_watchfunction, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityWatchfunctionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.find_my;
      Button findMy = rootView.findViewById(id);
      if (findMy == null) {
        break missingId;
      }

      id = R.id.tool;
      View tool = rootView.findViewById(id);
      if (tool == null) {
        break missingId;
      }
      ToolbarBinding binding_tool = ToolbarBinding.bind(tool);

      id = R.id.watch_avs;
      Button watchAvs = rootView.findViewById(id);
      if (watchAvs == null) {
        break missingId;
      }

      id = R.id.watch_dial;
      Button watchDial = rootView.findViewById(id);
      if (watchDial == null) {
        break missingId;
      }

      id = R.id.wearable_device;
      Button wearableDevice = rootView.findViewById(id);
      if (wearableDevice == null) {
        break missingId;
      }

      return new ActivityWatchfunctionBinding((LinearLayout) rootView, findMy, binding_tool,
          watchAvs, watchDial, wearableDevice);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
