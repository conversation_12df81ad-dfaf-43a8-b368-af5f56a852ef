// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button agree;

  @NonNull
  public final TextView agreeTV;

  @NonNull
  public final LinearLayout agreeView;

  @NonNull
  public final Button disagree;

  @NonNull
  public final Button entryBtn;

  @NonNull
  public final Button privacyLink;

  @NonNull
  public final Button privacyPolicy;

  @NonNull
  public final TextView versionText;

  private ActivityMainBinding(@NonNull LinearLayout rootView, @NonNull Button agree,
      @NonNull TextView agreeTV, @NonNull LinearLayout agreeView, @NonNull Button disagree,
      @NonNull Button entryBtn, @NonNull Button privacyLink, @NonNull Button privacyPolicy,
      @NonNull TextView versionText) {
    this.rootView = rootView;
    this.agree = agree;
    this.agreeTV = agreeTV;
    this.agreeView = agreeView;
    this.disagree = disagree;
    this.entryBtn = entryBtn;
    this.privacyLink = privacyLink;
    this.privacyPolicy = privacyPolicy;
    this.versionText = versionText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.agree;
      Button agree = rootView.findViewById(id);
      if (agree == null) {
        break missingId;
      }

      id = R.id.agreeTV;
      TextView agreeTV = rootView.findViewById(id);
      if (agreeTV == null) {
        break missingId;
      }

      id = R.id.agree_view;
      LinearLayout agreeView = rootView.findViewById(id);
      if (agreeView == null) {
        break missingId;
      }

      id = R.id.disagree;
      Button disagree = rootView.findViewById(id);
      if (disagree == null) {
        break missingId;
      }

      id = R.id.entry_btn;
      Button entryBtn = rootView.findViewById(id);
      if (entryBtn == null) {
        break missingId;
      }

      id = R.id.privacy_link;
      Button privacyLink = rootView.findViewById(id);
      if (privacyLink == null) {
        break missingId;
      }

      id = R.id.privacy_policy;
      Button privacyPolicy = rootView.findViewById(id);
      if (privacyPolicy == null) {
        break missingId;
      }

      id = R.id.version_text;
      TextView versionText = rootView.findViewById(id);
      if (versionText == null) {
        break missingId;
      }

      return new ActivityMainBinding((LinearLayout) rootView, agree, agreeTV, agreeView, disagree,
          entryBtn, privacyLink, privacyPolicy, versionText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
