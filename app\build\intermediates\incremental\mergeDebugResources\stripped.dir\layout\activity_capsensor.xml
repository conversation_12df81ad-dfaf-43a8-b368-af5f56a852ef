<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:tc="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >
    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <include
        android:id="@+id/loginfo"
        layout="@layout/logview"
        android:visibility="gone"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="50dp"
        android:layout_marginTop="20dp"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/capsensor_send_btn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="Send"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    />
                <TextView
                    android:layout_width="20dp"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="20dp"
                    android:gravity="center"
                    android:textSize="18sp"
                    android:textAlignment="center"
                    android:text="0x"/>

                <EditText
                    android:id="@+id/capsensor_send_text"
                    android:layout_width="100dp"
                    android:layout_height="match_parent"
                    android:hint="0000"
                    android:textSize="20sp"
                    android:digits="0123456789abcdefABCDEF,"
                    />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.suke.widget.SwitchButton
                    android:id="@+id/capsensor_show_touch_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="20dp"
                    app:sb_show_indicator="false"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="20dp"
                    android:textSize="16sp"
                    android:textColor="@color/black"
                    android:text="Show Touch Event?"
                    android:gravity="center"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="20dp"
                    android:textSize="16sp"
                    android:textColor="@color/black"
                    android:text="Multiple"
                    android:gravity="center"/>

                <EditText
                    android:id="@+id/et_data_multiple"
                    android:layout_width="50dp"
                    android:layout_height="match_parent"
                    android:hint="1"
                    android:text="1"
                    android:textAlignment="center"
                    android:inputType="number"/>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">
                <Button
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="Ear State"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    />

                <TextView
                    android:id="@+id/ear_state_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="20sp"
                    android:textAlignment="center"
                    android:text="On Ear"/>

            </LinearLayout>

            <TextView
                android:id="@+id/touch_event_text"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:textColor="@color/light_blue_600"
                android:text="Touch Event"
                android:textSize="30sp"
                android:textAlignment="center"
                />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:orientation="horizontal"
                android:weightSum="10"
                >

                <Button
                    android:id="@+id/start"
                    android:layout_weight="2.5"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:text="start">

                </Button>

                <Button
                    android:id="@+id/stop"
                    android:layout_weight="2.5"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:text="stop">

                </Button>

                <Button
                    android:id="@+id/continues"
                    android:layout_weight="2.5"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:text="continue">

                </Button>

                <EditText
                    android:id="@+id/saveName"
                    android:layout_weight="2.5"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:hint="log name">

                </EditText>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:orientation="horizontal">
                <CheckBox
                    android:id="@+id/cn1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="true"
                    android:text="1">
                </CheckBox>

                <CheckBox
                    android:id="@+id/cn2"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="true"
                    android:text="2">
                </CheckBox>

                <CheckBox
                    android:id="@+id/cn3"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="true"
                    android:text="3">
                </CheckBox>

                <CheckBox
                    android:id="@+id/cn4"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="true"
                    android:text="4">
                </CheckBox>

                <CheckBox
                    android:id="@+id/cn5"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="true"
                    android:text="5">
                </CheckBox>

                <CheckBox
                    android:id="@+id/cn6"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="true"
                    android:text="6"
                    >
                </CheckBox>

                <CheckBox
                    android:id="@+id/cn7"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="true"
                    android:text="7"
                    >
                </CheckBox>

                <CheckBox
                    android:id="@+id/cn8"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="true"
                    android:text="8"
                    >
                </CheckBox>

            </LinearLayout>

            <TextView
                android:id="@+id/capsensor_data"
                android:layout_marginLeft="6dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <com.github.mikephil.charting.charts.LineChart
                android:id="@+id/capsensor_chart"
                android:layout_width="match_parent"
                android:layout_height="400dp"/>


        </LinearLayout>

    </ScrollView>

</LinearLayout>



