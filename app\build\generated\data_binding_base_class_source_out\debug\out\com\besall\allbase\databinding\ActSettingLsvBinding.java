// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActSettingLsvBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button deleteButton;

  @NonNull
  public final Button pathButton;

  @NonNull
  public final TextView settingPath;

  @NonNull
  public final TextView settingSize;

  private ActSettingLsvBinding(@NonNull ConstraintLayout rootView, @NonNull Button deleteButton,
      @NonNull Button pathButton, @NonNull TextView settingPath, @NonNull TextView settingSize) {
    this.rootView = rootView;
    this.deleteButton = deleteButton;
    this.pathButton = pathButton;
    this.settingPath = settingPath;
    this.settingSize = settingSize;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActSettingLsvBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActSettingLsvBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.act_setting_lsv, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActSettingLsvBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.delete_button;
      Button deleteButton = rootView.findViewById(id);
      if (deleteButton == null) {
        break missingId;
      }

      id = R.id.path_button;
      Button pathButton = rootView.findViewById(id);
      if (pathButton == null) {
        break missingId;
      }

      id = R.id.setting_path;
      TextView settingPath = rootView.findViewById(id);
      if (settingPath == null) {
        break missingId;
      }

      id = R.id.setting_size;
      TextView settingSize = rootView.findViewById(id);
      if (settingSize == null) {
        break missingId;
      }

      return new ActSettingLsvBinding((ConstraintLayout) rootView, deleteButton, pathButton,
          settingPath, settingSize);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
