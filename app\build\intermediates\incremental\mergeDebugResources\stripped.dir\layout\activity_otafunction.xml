<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@mipmap/bes_bg6"
    android:orientation="vertical"
    >

    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />
    <ScrollView
        android:layout_marginTop="10dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="20dp">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            >

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginTop="20dp"
                />

            <Button
                android:id="@+id/usb_tota_ota"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:padding="20dp"
                android:gravity="left|center_vertical"
                android:background="@drawable/rectangle_longbtn"
                android:drawableLeft="@drawable/ota_icon_spp_ota2"
                android:drawableRight="@drawable/home_icon_arrow"
                android:textColor="@color/ff2c4662"
                android:text="    USB TOTA OTA"
                android:textAllCaps="false"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />


            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginTop="20dp"
                />

            <Button
                android:id="@+id/spp_ota_v1_old"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:padding="20dp"
                android:gravity="left|center_vertical"
                android:background="@drawable/rectangle_longbtn"
                android:drawableLeft="@drawable/ota_icon_spp_ota1"
                android:drawableRight="@drawable/home_icon_arrow"
                android:textColor="@color/ff2c4662"
                android:text="    SPP OTA OLD SERVICE"
                android:textAllCaps="false"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />
            <Button
                android:id="@+id/spp_ota_v1"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:padding="20dp"
                android:gravity="left|center_vertical"
                android:background="@drawable/rectangle_longbtn"
                android:drawableLeft="@drawable/ota_icon_spp_ota1"
                android:drawableRight="@drawable/home_icon_arrow"
                android:textColor="@color/ff2c4662"
                android:text="    SPP OTA"
                android:textAllCaps="false"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <Button
                android:id="@+id/ble_ota_v1"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:padding="20dp"
                android:gravity="left|center_vertical"
                android:background="@drawable/rectangle_longbtn"
                android:drawableLeft="@drawable/ota_icon_spp_ota1"
                android:drawableRight="@drawable/home_icon_arrow"
                android:textColor="@color/ff2c4662"
                android:text="    BLE OTA"
                android:textAllCaps="false"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <Button
                android:id="@+id/spp_totaota_v1"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:padding="20dp"
                android:gravity="left|center_vertical"
                android:background="@drawable/rectangle_longbtn"
                android:drawableLeft="@drawable/ota_icon_spp_ota1"
                android:drawableRight="@drawable/home_icon_arrow"
                android:textColor="@color/ff2c4662"
                android:text="    SPP TOTA OTA"
                android:textAllCaps="false"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <Button
                android:id="@+id/ble_totaota_v1"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:padding="20dp"
                android:gravity="left|center_vertical"
                android:background="@drawable/rectangle_longbtn"
                android:drawableLeft="@drawable/ota_icon_spp_ota1"
                android:drawableRight="@drawable/home_icon_arrow"
                android:textColor="@color/ff2c4662"
                android:text="    BLE TOTA OTA"
                android:textAllCaps="false"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginTop="20dp"
                />

            <Button
                android:id="@+id/spp_ota_v2"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:gravity="left|center_vertical"
                android:padding="20dp"
                android:background="@drawable/rectangle_longbtn"
                android:drawableLeft="@drawable/ota_icon_spp_ota2"
                android:drawableRight="@drawable/home_icon_arrow"
                android:textColor="@color/ff2c4662"
                android:text="    SPP OTA"
                android:textAllCaps="false"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <Button
                android:id="@+id/ble_ota_v2"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:padding="20dp"
                android:gravity="left|center_vertical"
                android:background="@drawable/rectangle_longbtn"
                android:drawableLeft="@drawable/ota_icon_spp_ota2"
                android:drawableRight="@drawable/home_icon_arrow"
                android:textColor="@color/ff2c4662"
                android:text="    BLE OTA"
                android:textAllCaps="false"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <Button
                android:id="@+id/spp_totaota_v2"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:gravity="left|center_vertical"
                android:padding="20dp"
                android:background="@drawable/rectangle_longbtn"
                android:drawableLeft="@drawable/ota_icon_spp_ota2"
                android:drawableRight="@drawable/home_icon_arrow"
                android:textColor="@color/ff2c4662"
                android:text="    SPP TOTA OTA"
                android:textAllCaps="false"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <Button
                android:id="@+id/ble_totaota_v2"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:padding="20dp"
                android:gravity="left|center_vertical"
                android:background="@drawable/rectangle_longbtn"
                android:drawableLeft="@drawable/ota_icon_spp_ota2"
                android:drawableRight="@drawable/home_icon_arrow"
                android:textColor="@color/ff2c4662"
                android:text="    BLE TOTA OTA"
                android:textAllCaps="false"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                />



        </LinearLayout>



    </ScrollView>

</LinearLayout>