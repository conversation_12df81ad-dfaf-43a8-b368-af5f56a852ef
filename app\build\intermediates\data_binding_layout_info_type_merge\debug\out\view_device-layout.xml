<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="view_device" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\view_device.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/view_device_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="73" endOffset="51"/></Target><Target id="@+id/view_device_support" view="LinearLayout"><Expressions/><location startLine="19" startOffset="8" endLine="25" endOffset="22"/></Target><Target id="@+id/view_device_bg" view="LinearLayout"><Expressions/><location startLine="27" startOffset="8" endLine="70" endOffset="22"/></Target><Target id="@+id/device_title" view="TextView"><Expressions/><location startLine="42" startOffset="12" endLine="53" endOffset="22"/></Target><Target id="@+id/device_name" view="EditText"><Expressions/><location startLine="54" startOffset="12" endLine="69" endOffset="22"/></Target></Targets></Layout>