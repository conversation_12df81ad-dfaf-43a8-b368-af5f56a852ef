<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_eq_drc" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\item_eq_drc.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_eq_drc_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="100" endOffset="14"/></Target><Target id="@+id/seekbar_radioview" view="View"><Expressions/><location startLine="17" startOffset="8" endLine="24" endOffset="13"/></Target><Target id="@+id/seekbar_min" view="TextView"><Expressions/><location startLine="31" startOffset="8" endLine="37" endOffset="37"/></Target><Target id="@+id/seekbar_real" view="TextView"><Expressions/><location startLine="39" startOffset="8" endLine="45" endOffset="37"/></Target><Target id="@+id/seekbar_max" view="TextView"><Expressions/><location startLine="47" startOffset="8" endLine="54" endOffset="13"/></Target><Target id="@+id/seekbar_radio" view="RadioButton"><Expressions/><location startLine="63" startOffset="8" endLine="70" endOffset="13"/></Target><Target id="@+id/seekbar_title" view="TextView"><Expressions/><location startLine="72" startOffset="8" endLine="82" endOffset="13"/></Target><Target id="@+id/seekbar" view="SeekBar"><Expressions/><location startLine="84" startOffset="8" endLine="91" endOffset="14"/></Target></Targets></Layout>