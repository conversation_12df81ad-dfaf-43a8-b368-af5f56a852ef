// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LogviewBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button done;

  @NonNull
  public final TextView logV;

  @NonNull
  public final LinearLayout loginfo;

  @NonNull
  public final ScrollView scrPolicy;

  private LogviewBinding(@NonNull LinearLayout rootView, @NonNull Button done,
      @NonNull TextView logV, @NonNull LinearLayout loginfo, @NonNull ScrollView scrPolicy) {
    this.rootView = rootView;
    this.done = done;
    this.logV = logV;
    this.loginfo = loginfo;
    this.scrPolicy = scrPolicy;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LogviewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LogviewBinding inflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup parent,
      boolean attachToParent) {
    View root = inflater.inflate(R.layout.logview, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LogviewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.done;
      Button done = rootView.findViewById(id);
      if (done == null) {
        break missingId;
      }

      id = R.id.logV;
      TextView logV = rootView.findViewById(id);
      if (logV == null) {
        break missingId;
      }

      LinearLayout loginfo = (LinearLayout) rootView;

      id = R.id.scr_policy;
      ScrollView scrPolicy = rootView.findViewById(id);
      if (scrPolicy == null) {
        break missingId;
      }

      return new LogviewBinding((LinearLayout) rootView, done, logV, loginfo, scrPolicy);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
