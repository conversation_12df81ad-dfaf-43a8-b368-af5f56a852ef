<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="5dp"
    android:background="@color/white"
    >

    <ImageView
        android:id="@+id/select_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_gravity="center"
        android:background="@drawable/ota_top_nor"></ImageView>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:layout_marginLeft="20dp"
        >

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:textColor="@color/title_color_light"
            android:textSize="13sp"

            />

        <TextView
            android:id="@+id/address"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:textColor="@color/title_color_light"
            android:textSize="13sp"
            />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:layout_marginRight="20dp"
        android:visibility="visible">

        <ImageView
            android:id="@+id/rssi_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            />

        <TextView
            android:id="@+id/rssi_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/title_color_light"
            android:textSize="12sp"
            />
        <Button
            android:id="@+id/disconnect_btn"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:text="@string/disconnect_device"
            android:textColor="@color/white"
            android:textSize="13sp"
            android:background="@color/ffff5d5d"
            android:focusable="false"
            android:visibility="gone"
            >

        </Button>

    </LinearLayout>
</LinearLayout>