<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    >
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="45.5dp"
        android:background="@color/white"
        >
        <ImageView
            android:id="@+id/account_image"
            android:layout_width="17.78dp"
            android:layout_height="17.78dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="8.89dp"
            android:visibility="gone"
            />
        <TextView
            android:id="@+id/account_textview"
            android:layout_width="wrap_content"
            android:layout_height="23dp"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/account_image"
            android:paddingLeft="15dp"
            android:text="cmd"
            android:fontFamily="@font/sanshan_normal"
            android:textSize="14sp"
            android:textColor="#ffafb8c3"
            android:layout_marginRight="50dp"
            />
        <RelativeLayout
            android:id="@+id/delete_account_rl"
            android:layout_width="53.33dp"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            >

            <ImageView
                android:id="@+id/delete_account_image"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="15dp"
                android:src="@drawable/delete_account" />
        </RelativeLayout>
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="-0.5dp"
        android:background="@color/ffe1e6eb"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp">

    </View>
</LinearLayout>