package com.besall.allbase.bluetooth.service.capsensor;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;
import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_SUCCESS;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOR_DATA_MULTIPLE;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOR_DATA_MULTIPLE_VALUE;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOR_ISBTH_KEY;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.BES_CAPSENSOR_ISBTH_VALUE;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_DATA;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.CAPSENSOR_DATA_LOG;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.OP_TOTA_SENSOE_HUB_DUMP_SET;
import static com.besall.allbase.bluetooth.service.capsensor.CapSensorConstants.SPEED_TEST_SEND_DATA;

import android.content.Context;
import android.util.Log;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.base.BesBaseService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.CmdInfo;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.message.BaseMessage;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.bluetooth.service.audiodump.AudioDumpCMD;

public class CapSensorService extends BesBaseService {
    private int dataLength = -1;
    private int curMultiple = 1;

    public CapSensorService(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        super(serviceConfig, listener, context);
        SPHelper.putPreference(mContext, BesSdkConstants.BES_CONNECT_SERVICE, BesSdkConstants.BES_SPP_CONNECT.toString());
        startConnect(serviceConfig);
    }

    @Override
    public void onStatusChanged(HmDevice device, int status, DeviceProtocol protocol) {
        super.onStatusChanged(device, status, protocol);
        if (status == BES_CONNECT_ERROR) {
            callBackStateChangedMessage(BES_CONNECT_ERROR, "");
        } else if (!mConfig.getTotaConnect() && status == BES_CONNECT_SUCCESS) {
            callBackStateChangedMessage(BES_CONNECT_SUCCESS, "");
        }
    }

    @Override
    public void onDataReceived(BaseMessage deviceMessage) {
        super.onDataReceived(deviceMessage);
        Log.i(TAG, "onDataReceived: -----" + ArrayUtil.toHex((byte[]) deviceMessage.getMsgContent()));
        if (mConfig.getTotaConnect() && !totauccess) {
            Log.i(TAG, "onDataReceived: ----tota no connect");
            return;
        }
//        callBackStateChangedMessage(CAPSENSOR_DATA_LOG, "after decode:" + ArrayUtil.toHex((byte[]) deviceMessage.getMsgContent()));
//        int result = CapSensorCMD.receiveData(mContext, (byte[]) deviceMessage.getMsgContent(), mConfig.getTotaConnect());
//        if (result == CAPSENSOR_DATA) {
//            callBackStateChangedMessage(CAPSENSOR_DATA, CapSensorCMD.getCapsensorData());
//        } else if (result == SPEED_TEST_SEND_DATA) {
//            callBackStateChangedMessage(CAPSENSOR_DATA, CapSensorCMD.getSSSpeedTestIntervel());
//            //speedTestSendTestData((byte[]) deviceMessage.getMsgContent());
//        }
        byte[] allData = (byte[]) deviceMessage.getMsgContent();
        if (dataLength == -1) {
            dataLength = allData.length;
        }
        for (int i = 0; i < allData.length / dataLength * curMultiple; i ++) {
            int l = dataLength / curMultiple;
            byte[] data = new byte[l];
            System.arraycopy(allData, i * l, data, 0, l);
            String[] result = CapSensorCMD.receiveData(mContext, data, mConfig.getTotaConnect());
            callBackStateChangedMessage(CAPSENSOR_DATA, result[1] + "besmask" + result[0]);
//            if (result == CAPSENSOR_DATA) {
//                callBackStateChangedMessage(CAPSENSOR_DATA, CapSensorCMD.getCapsensorData());
//            } else if (result == SPEED_TEST_SEND_DATA) {
//                callBackStateChangedMessage(CAPSENSOR_DATA, CapSensorCMD.getSSSpeedTestIntervel());
//                //speedTestSendTestData((byte[]) deviceMessage.getMsgContent());
//            }
        }

    }

    public void test() {
//        Log.i(TAG, "test: --------");
//        //test
//        String test = "43,41,50,53,45,4e,53,4f,52,30,00,00,00,00,00,1f,fd,00,00,01,00,00,00,7d,f2,00,00,02,00,00,00,ae,f0,00,00,03,00,00,00,07,f7,00,6d,0c,01,00,20,01,01,00,f2,0a,00,00,5b,06,00,00,01,00,00,00,";
//        byte[] data = ArrayUtil.toBytes(test);
//        int result = CapSensorCMD.receiveData(mContext, data, false);
//        if (result == CAPSENSOR_DATA) {
//            callBackStateChangedMessage(CAPSENSOR_DATA, CapSensorCMD.getCapsensorData());
//        }

    }

    public String getChannelData() {
        return CapSensorCMD.getChannelData();
    }

    public void getSensorData() {
        curMultiple = (int) SPHelper.getPreference(mContext, BES_CAPSENSOR_DATA_MULTIPLE, BES_CAPSENSOR_DATA_MULTIPLE_VALUE);
        dataLength = -1;
        if (mConfig.getTotaConnect()) {
            CmdInfo sensor = new CmdInfo(OP_TOTA_SENSOE_HUB_DUMP_SET, new byte[]{0x01});
            sendData(sensor.toBytes());
            return;
        }
        sendData(CapSensorCMD.getNormalStartOrStopCMD(true));
    }

    public static String getExtraParamData() {
        return CapSensorCMD.getExtraParamData();
    }

    public void stopSensorData() {
        if (mConfig.getTotaConnect()) {
            CmdInfo sensor = new CmdInfo(OP_TOTA_SENSOE_HUB_DUMP_SET, new byte[]{0x00});
            sendData(sensor.toBytes());
            return;
        }
        sendData(CapSensorCMD.getNormalStartOrStopCMD(false));
    }


}
