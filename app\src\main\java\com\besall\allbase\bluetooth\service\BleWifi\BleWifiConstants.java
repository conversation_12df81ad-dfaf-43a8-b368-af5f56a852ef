package com.besall.allbase.bluetooth.service.BleWifi;

import com.bes.bessdk.utils.ArrayUtil;

public class BleWifiConstants {

    public static final String                    WIFI_NAME = "WIFI NAME";
    public static final String                WIFA_PASSWORD = "WIFI PASSWORD";

    public static final int              CHOOSE_WIFI_RESULT = 100;
    public static final String       CHOOSE_WIFI_RESULT_KEY = "CHOOSE_WIFI_RESULT_KEY";



    public final static String                        STR_HASNEWFILE = "hasnewfile";
    public final static String                        STR_TOOPENWIFI = "toopenwifi";
    public final static String                        STR_WIFICONFIG = "wificonfig";
    public final static String                        STR_TRANSFEROK = "transferok";
    public final static String                        STR_DELETEFILE = "deletefile";
    public final static String                        STR_GETWORKPAT = "getworkpat";
    public final static String                               STR_END = "END";
    public final static String                              STR_PATH = "path";


    public final static byte[]                     HEADER_HASNEWFILE = ArrayUtil.toBytes(ArrayUtil.str2HexStr(STR_HASNEWFILE));
    public final static byte[]                     HEADER_TOOPENWIFI = ArrayUtil.toBytes(ArrayUtil.str2HexStr(STR_TOOPENWIFI));
    public final static byte[]                     HEADER_WIFICONFIG = ArrayUtil.toBytes(ArrayUtil.str2HexStr(STR_WIFICONFIG));
    public final static byte[]                     HEADER_TRANSFEROK = ArrayUtil.toBytes(ArrayUtil.str2HexStr(STR_TRANSFEROK));
    public final static byte[]                     HEADER_DELETEFILE = ArrayUtil.toBytes(ArrayUtil.str2HexStr(STR_DELETEFILE));
    public final static byte[]                     HEADER_GETWORKPAT = ArrayUtil.toBytes(ArrayUtil.str2HexStr(STR_GETWORKPAT));
    public final static byte[]                              TAIL_END = ArrayUtil.toBytes(ArrayUtil.str2HexStr(STR_END));
    public final static byte[]                        STR_PATH_BYTES = ArrayUtil.toBytes(ArrayUtil.str2HexStr(STR_PATH));



    public final static byte                        BES_BLE_WIFI_CMD = 0x00;
    public final static byte                   BES_BLE_WIFI_RESPONSE = 0x01;
    public final static byte             BES_BLE_WIFI_OPEN_WIFI_FAIL = 0x02;

    public final static byte                  BES_BLE_WIFI_FILE_PATH = 0x01;
    public final static byte                   BES_BLE_WIFI_OTA_PATH = 0x02;


    public final static int                BES_BLE_WIFI_HAS_NEW_FILE = 0x00000700;
    public final static int            BES_BLE_WIFI_HAS_NEW_FILE_END = 0x00000701;
    public final static int                 BES_BLE_WIFI_RECEIVE_URL = 0x00000702;
    public final static int               BES_BLE_WIFI_OPEN_WIFI_RSP = 0x00000703;
    public final static int      BES_BLE_RECEIVE_WIFI_OPEN_WIFI_FAIL = 0x00000704;
    public final static int              BES_BLE_RECEIVE_WIFI_CONFIG = 0x00000705;
    public final static int                BES_BLE_RECEIVE_DELETE_OK = 0x00000706;
    public final static int              BES_BLE_RECEIVE_DELETE_FAIL = 0x00000707;
    public final static int                BES_BLE_RECEIVE_FILE_PATH = 0x00000708;
    public final static int                 BES_BLE_RECEIVE_OTA_PATH = 0x00000709;

    public final static int                BES_BLE_WIFI_RECEIVE_DATA = 0x00000720;
    public final static int                   BES_BLE_WIFI_SEND_DATA = 0x00000721;


    //error
    public final static int             BES_BLE_WIFI_FILE_NAME_ERROR = 0x00000750;
}
