// Generated by view binder compiler. Do not edit!
package com.besall.allbase.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckedTextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.besall.allbase.R;
import java.lang.NullPointerException;
import java.lang.Override;

public final class SimpleSpinnerItemBinding implements ViewBinding {
  @NonNull
  private final CheckedTextView rootView;

  @NonNull
  public final CheckedTextView text1;

  private SimpleSpinnerItemBinding(@NonNull CheckedTextView rootView,
      @NonNull CheckedTextView text1) {
    this.rootView = rootView;
    this.text1 = text1;
  }

  @Override
  @NonNull
  public CheckedTextView getRoot() {
    return rootView;
  }

  @NonNull
  public static SimpleSpinnerItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SimpleSpinnerItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.simple_spinner_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SimpleSpinnerItemBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    CheckedTextView text1 = (CheckedTextView) rootView;

    return new SimpleSpinnerItemBinding((CheckedTextView) rootView, text1);
  }
}
