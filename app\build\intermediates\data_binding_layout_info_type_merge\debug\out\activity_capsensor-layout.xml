<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_capsensor" modulePackage="com.besall.allbase" filePath="app\src\main\res\layout\activity_capsensor.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_capsensor_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="281" endOffset="14"/></Target><Target id="@+id/tool" tag="layout/activity_capsensor_0" include="toolbar"><Expressions/><location startLine="10" startOffset="4" endLine="13" endOffset="9"/></Target><Target id="@+id/loginfo" tag="layout/activity_capsensor_0" include="logview"><Expressions/><location startLine="15" startOffset="4" endLine="18" endOffset="34"/></Target><Target id="@+id/capsensor_send_btn" view="Button"><Expressions/><location startLine="36" startOffset="16" endLine="45" endOffset="21"/></Target><Target id="@+id/capsensor_send_text" view="EditText"><Expressions/><location startLine="55" startOffset="16" endLine="62" endOffset="21"/></Target><Target id="@+id/capsensor_show_touch_switch" view="com.suke.widget.SwitchButton"><Expressions/><location startLine="71" startOffset="16" endLine="77" endOffset="50"/></Target><Target id="@+id/et_data_multiple" view="EditText"><Expressions/><location startLine="97" startOffset="16" endLine="104" endOffset="47"/></Target><Target id="@+id/ear_state_text" view="TextView"><Expressions/><location startLine="123" startOffset="16" endLine="129" endOffset="42"/></Target><Target id="@+id/touch_event_text" view="TextView"><Expressions/><location startLine="133" startOffset="12" endLine="141" endOffset="17"/></Target><Target id="@+id/start" view="Button"><Expressions/><location startLine="150" startOffset="16" endLine="158" endOffset="24"/></Target><Target id="@+id/stop" view="Button"><Expressions/><location startLine="160" startOffset="16" endLine="168" endOffset="24"/></Target><Target id="@+id/continues" view="Button"><Expressions/><location startLine="170" startOffset="16" endLine="178" endOffset="24"/></Target><Target id="@+id/saveName" view="EditText"><Expressions/><location startLine="180" startOffset="16" endLine="188" endOffset="26"/></Target><Target id="@+id/cn1" view="CheckBox"><Expressions/><location startLine="196" startOffset="16" endLine="202" endOffset="26"/></Target><Target id="@+id/cn2" view="CheckBox"><Expressions/><location startLine="204" startOffset="16" endLine="210" endOffset="26"/></Target><Target id="@+id/cn3" view="CheckBox"><Expressions/><location startLine="212" startOffset="16" endLine="218" endOffset="26"/></Target><Target id="@+id/cn4" view="CheckBox"><Expressions/><location startLine="220" startOffset="16" endLine="226" endOffset="26"/></Target><Target id="@+id/cn5" view="CheckBox"><Expressions/><location startLine="228" startOffset="16" endLine="234" endOffset="26"/></Target><Target id="@+id/cn6" view="CheckBox"><Expressions/><location startLine="236" startOffset="16" endLine="243" endOffset="26"/></Target><Target id="@+id/cn7" view="CheckBox"><Expressions/><location startLine="245" startOffset="16" endLine="252" endOffset="26"/></Target><Target id="@+id/cn8" view="CheckBox"><Expressions/><location startLine="254" startOffset="16" endLine="261" endOffset="26"/></Target><Target id="@+id/capsensor_data" view="TextView"><Expressions/><location startLine="265" startOffset="12" endLine="269" endOffset="53"/></Target><Target id="@+id/capsensor_chart" view="com.github.mikephil.charting.charts.LineChart"><Expressions/><location startLine="271" startOffset="12" endLine="274" endOffset="46"/></Target></Targets></Layout>